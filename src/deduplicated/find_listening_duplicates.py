import os
import glob
import json
import re
from tqdm import tqdm
from sentence_transformers import SentenceTransformer, util
import numpy as np

# Settings
DATA_DIRS = [
    'data/scraped/scraped_listening_cleaned',
    'data/scraped/scraped_listening_cleaned_free'
]
THRESHOLD = 0.80
MODEL_NAME = 'all-MiniLM-L6-v2'
OUTPUT_DIR = 'data/deduplicated/listening/'
os.makedirs(OUTPUT_DIR, exist_ok=True)
GROUPS = [
    (1, 4, 'listening_group1.json'),
    (5, 10, 'listening_group2.json'),
    (11, 19, 'listening_group3.json'),
    (20, 29, 'listening_group4.json'),
    (30, 35, 'listening_group5.json'),
    (36, 39, 'listening_group6.json'),
]
ANSWER_FILES = {
    'regular': 'data/scraped/scraped_answer/listening_correct_answer.json',
    'free': 'data/scraped/scraped_answer/listening_correct_answer_free.json',
}

# 1. Load all questions
questions = []
for d in DATA_DIRS:
    is_free = d.endswith('_free')
    for path in glob.glob(os.path.join(d, 'test*.json')):
        base = os.path.splitext(os.path.basename(path))[0]  # e.g., test15
        test_id = base + ('_gr' if is_free else '')
        print(f"Checking file: {path}")  # Already present
        with open(path, encoding='utf-8') as f:
            data = json.load(f)
            for idx, q in enumerate(data):
                if 'extracted_text' not in q:
                    print(f"❌ MISSING 'extracted_text' in {path} (question {idx+1}, question_number={q.get('question_number')})")
                q_copy = dict(q)
                q_copy['test_id'] = test_id
                q_copy['question_number'] = int(q_copy['question_number'])
                questions.append(q_copy)

# 2. Load correct answers
with open(ANSWER_FILES['regular'], encoding='utf-8') as f:
    correct_regular = json.load(f)
with open(ANSWER_FILES['free'], encoding='utf-8') as f:
    correct_free = json.load(f)

# 2.5. Load analysis data (future use)
ANALYSIS_DIRS = {
    'regular': 'data/analysis/listening_analysis',
    'free': 'data/analysis/listening_analysis_free'
}
analysis_data = {}

for category, dir_path in ANALYSIS_DIRS.items():
    if not os.path.exists(dir_path):
        print(f"[INFO] {dir_path} not found (listening analysis not available yet)")
        continue
    for json_file in glob.glob(os.path.join(dir_path, 'test*.json')):
        test_id = os.path.splitext(os.path.basename(json_file))[0]  # e.g., test1
        test_key = test_id + ('_gr' if category == 'free' else '')
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            analysis_data[test_key] = {str(q['question_number']): q for q in data}
        print(f"Loaded {category} analysis for {test_id}: {len(data)} questions")

def get_analysis_for_location(location):
    """Get analysis for a specific location like 'test15 Q12' or 'test15_gr Q12'"""
    test_part, q_part = location.split()
    q_num = q_part.replace('Q', '')
    return analysis_data.get(test_part, {}).get(q_num)

def get_correct_answer(location):
    # location: 'test15 Q12' or 'test15_gr Q12'
    test_part, q_part = location.split()
    is_free = test_part.endswith('_gr')
    test_key = test_part.replace('_gr', '')
    q_num = q_part.replace('Q', '')
    if is_free:
        return correct_free.get(test_key, {}).get(q_num)
    else:
        return correct_regular.get(test_key, {}).get(q_num)

def has_choices_text(question):
    """Check if a question has non-empty choices text."""
    choices = question.get('choices', {})
    return any(choice.strip() for choice in choices.values())

def get_choices_text(question):
    """Get concatenated choices text for similarity comparison."""
    choices = question.get('choices', {})
    # Sort by key (A, B, C, D) to ensure consistent ordering
    sorted_choices = sorted(choices.items())
    return ' '.join(f"{key}: {value}" for key, value in sorted_choices if value.strip())

def compute_similarity_matrix(questions, model):
    """Compute similarity matrix using appropriate method for each question pair."""
    N = len(questions)
    similarity_matrix = np.zeros((N, N))

    # Separate questions by type
    questions_with_choices = []
    questions_without_choices = []
    choice_indices = []
    no_choice_indices = []

    for i, q in enumerate(questions):
        if has_choices_text(q):
            questions_with_choices.append(get_choices_text(q))
            choice_indices.append(i)
        else:
            questions_without_choices.append(q['extracted_text'])
            no_choice_indices.append(i)

    print(f"Questions with choices text: {len(questions_with_choices)}")
    print(f"Questions without choices text: {len(questions_without_choices)}")

    # Compute embeddings for each type
    if questions_with_choices:
        print("Computing embeddings for questions with choices...")
        choice_embeddings = model.encode(questions_with_choices, convert_to_tensor=True, show_progress_bar=True)

    if questions_without_choices:
        print("Computing embeddings for questions without choices...")
        no_choice_embeddings = model.encode(questions_without_choices, convert_to_tensor=True, show_progress_bar=True)

    # Fill similarity matrix
    print("Computing similarity matrix...")

    # Same type comparisons (choices with choices)
    if questions_with_choices:
        choice_sims = util.pytorch_cos_sim(choice_embeddings, choice_embeddings).cpu().numpy()
        for i, idx_i in enumerate(choice_indices):
            for j, idx_j in enumerate(choice_indices):
                similarity_matrix[idx_i][idx_j] = choice_sims[i][j]

    # Same type comparisons (no choices with no choices)
    if questions_without_choices:
        no_choice_sims = util.pytorch_cos_sim(no_choice_embeddings, no_choice_embeddings).cpu().numpy()
        for i, idx_i in enumerate(no_choice_indices):
            for j, idx_j in enumerate(no_choice_indices):
                similarity_matrix[idx_i][idx_j] = no_choice_sims[i][j]

    # Cross-type comparisons (choices vs no choices) - use extracted_text for both
    if questions_with_choices and questions_without_choices:
        print("Computing cross-type similarities...")
        choice_extracted_texts = [questions[i]['extracted_text'] for i in choice_indices]
        choice_extracted_embeddings = model.encode(choice_extracted_texts, convert_to_tensor=True, show_progress_bar=False)

        cross_sims = util.pytorch_cos_sim(choice_extracted_embeddings, no_choice_embeddings).cpu().numpy()
        for i, idx_i in enumerate(choice_indices):
            for j, idx_j in enumerate(no_choice_indices):
                similarity_matrix[idx_i][idx_j] = cross_sims[i][j]
                similarity_matrix[idx_j][idx_i] = cross_sims[i][j]  # Symmetric

    return similarity_matrix

# 3. Compute similarity matrix using improved method
model = SentenceTransformer(MODEL_NAME)
similarity_matrix = compute_similarity_matrix(questions, model)

# Helper functions for sorting
def parse_location(location):
    """Parse a location string like 'test12 Q5' or 'test1_gr Q10' into components."""
    match = re.match(r'(test\d+)(_gr)?\s+Q(\d+)', location)
    if match:
        test_base = match.group(1)  # e.g., 'test12'
        is_free = match.group(2) is not None  # True if '_gr' suffix exists
        test_num = int(test_base.replace('test', ''))  # e.g., 12
        question_num = int(match.group(3))  # e.g., 5
        return (is_free, test_num, question_num)
    return (False, 999999, 999999)  # fallback for unparseable locations

def sort_locations(locations):
    """Sort locations with free tests first, then normal tests, ordered by test number then question number."""
    def location_sort_key(location):
        is_free, test_num, question_num = parse_location(location)
        # Free tests (is_free=True) should come first, so we use (not is_free) as the primary key
        # This makes free tests have key (False, ...) and normal tests have key (True, ...)
        return (not is_free, test_num, question_num)

    return sorted(locations, key=location_sort_key)

def get_primary_location_sort_key(locations):
    """Get the sort key for the first (primary) location in a sorted locations list."""
    if not locations:
        return (True, 999999, 999999)  # fallback
    sorted_locs = sort_locations(locations)
    is_free, test_num, question_num = parse_location(sorted_locs[0])
    # Return the same key format as location_sort_key for consistency
    return (not is_free, test_num, question_num)

# 4. Cluster by similarity (greedy) using similarity matrix
N = len(questions)
assigned = np.zeros(N, dtype=bool)
clusters = []
for i in tqdm(range(N)):
    if assigned[i]:
        continue
    cluster = [i]
    assigned[i] = True
    sims = similarity_matrix[i]  # Get similarities for question i
    for j in range(i+1, N):
        if not assigned[j] and sims[j] >= THRESHOLD:
            cluster.append(j)
            assigned[j] = True
    clusters.append(cluster)

# 5. For each cluster, pick the best representative with complete data
rep_questions = []
clusters_with_choices = 0
clusters_without_choices = 0
mixed_clusters = 0

for cluster in clusters:
    locs = [f"{questions[idx]['test_id']} Q{questions[idx]['question_number']}" for idx in cluster]

    # Analyze cluster composition for debugging
    has_choices_count = sum(1 for idx in cluster if has_choices_text(questions[idx]))
    no_choices_count = len(cluster) - has_choices_count

    if has_choices_count > 0 and no_choices_count > 0:
        mixed_clusters += 1
        print(f"Mixed cluster found: {locs} (choices: {has_choices_count}, no choices: {no_choices_count})")
    elif has_choices_count > 0:
        clusters_with_choices += 1
    else:
        clusters_without_choices += 1

    # Sort locations: free tests first, then normal tests, ordered by test number then question number
    sorted_locs = sort_locations(locs)

    # Find the first location that has both question data AND correct answer
    # This ensures we use a complete set from the same source
    best_question = None
    best_answer = None
    best_location = None

    for loc in sorted_locs:
        # Find the question data for this location
        for idx in cluster:
            q = questions[idx]
            q_loc = f"{q['test_id']} Q{q['question_number']}"
            if q_loc == loc:
                # Check if this location has a correct answer
                ans = get_correct_answer(loc)
                if ans:
                    # Found a complete set - use this as our representative
                    best_question = q.copy()
                    best_answer = ans
                    best_location = loc
                    break
        if best_question and best_answer:
            break

    # Fallback: if no location has both, use the first question and first available answer
    if not best_question:
        best_question = questions[cluster[0]].copy()
        for loc in sorted_locs:
            ans = get_correct_answer(loc)
            if ans:
                best_answer = ans
                break

    # Remove question_number and extracted_text (listening-specific cleanup)
    best_question.pop('question_number', None)
    best_question.pop('extracted_text', None)
    best_question['locations'] = sorted_locs
    best_question['correct_answer'] = best_answer
    best_question['source_location'] = best_location  # Track which source we used
    rep_questions.append(best_question)

print(f"\nCluster analysis:")
print(f"Clusters with choices text: {clusters_with_choices}")
print(f"Clusters without choices text: {clusters_without_choices}")
print(f"Mixed clusters (both types): {mixed_clusters}")
print(f"Total clusters: {len(clusters)}")
print(f"Total representative questions: {len(rep_questions)}")

# 6. Assign to groups and write output
for start, end, out_file in GROUPS:
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # Sort questions within each group by their primary location (first location in sorted list)
    # Free tests first, then normal tests, ordered by test number then question number
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    out_path = os.path.join(OUTPUT_DIR, out_file)
    with open(out_path, 'w', encoding='utf-8') as f:
        json.dump(group, f, ensure_ascii=False, indent=2)
    print(f"Wrote {len(group)} questions to {out_path}")

# 7. Export correct answers by group
correct_by_group = {}
for idx, (start, end, out_file) in enumerate(GROUPS, 1):
    group_key = f"group{idx}"
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # IMPORTANT: Sort questions the same way as in the question files
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    correct_by_group[group_key] = {}
    for i, q in enumerate(group, 1):
        if q.get('correct_answer'):
            correct_by_group[group_key][str(i)] = q['correct_answer']

correct_group_path = 'data/scraped/scraped_answer/listening_correct_answer_by_group.json'
with open(correct_group_path, 'w', encoding='utf-8') as f:
    json.dump(correct_by_group, f, ensure_ascii=False, indent=2)
print(f"Wrote group correct answers to {correct_group_path}")

# 8. Export analysis by group (future use)
analysis_by_group = {}
for idx, (start, end, out_file) in enumerate(GROUPS, 1):
    group_key = f"group{idx}"
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # IMPORTANT: Sort questions the same way as in the question files
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    analysis_by_group[group_key] = {}
    for i, q in enumerate(group, 1):
        # Use analysis from the same source location as the question/answer if available
        analysis = None
        source_location = q.get('source_location')

        if source_location:
            # Try to get analysis from the same source first
            analysis = get_analysis_for_location(source_location)

        # If no analysis from source location, try other locations as fallback
        if not analysis:
            for location in q.get('locations', []):
                analysis = get_analysis_for_location(location)
                if analysis:
                    break

        if analysis:
            analysis_by_group[group_key][str(i)] = analysis
        # else: skip for now (no analysis data)

analysis_output_dir = 'data/analysis/listening_analysis_by_group/'
os.makedirs(analysis_output_dir, exist_ok=True)
for group_key, group_analysis in analysis_by_group.items():
    group_num = group_key.replace('group', '')
    output_file = os.path.join(analysis_output_dir, f'listening_group{group_num}_analysis.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(group_analysis, f, ensure_ascii=False, indent=2)
    print(f"[INFO] Wrote {len(group_analysis)} analysis entries to {output_file} (may be empty if no analysis data)") 