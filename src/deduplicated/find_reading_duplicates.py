import os
import glob
import json
import re
from tqdm import tqdm
from sentence_transformers import SentenceTransformer, util
import numpy as np


# Settings
DATA_DIRS = [
    'data/scraped/scraped_reading_cleaned',
    'data/scraped/scraped_reading_cleaned_free'
]
THRESHOLD = 0.80
MODEL_NAME = 'all-MiniLM-L6-v2'
OUTPUT_DIR = 'data/deduplicated/reading/'
os.makedirs(OUTPUT_DIR, exist_ok=True)
GROUPS = [
    (1, 4, 'reading_group1.json'),
    (5, 10, 'reading_group2.json'),
    (11, 19, 'reading_group3.json'),
    (20, 29, 'reading_group4.json'),
    (30, 35, 'reading_group5.json'),
    (36, 39, 'reading_group6.json'),
]
ANSWER_FILES = {
    'regular': 'data/scraped/scraped_answer/reading_correct_answer.json',
    'free': 'data/scraped/scraped_answer/reading_correct_answer_free.json',
    'by_group': 'data/scraped/scraped_answer/reading_correct_answer_by_group.json',
}

ANALYSIS_DIR = 'data/analysis/reading_analysis_by_group/'

# 1. Load all questions
questions = []
for d in DATA_DIRS:
    is_free = d.endswith('_free')
    for path in glob.glob(os.path.join(d, 'test*.json')):
        base = os.path.splitext(os.path.basename(path))[0]  # e.g., test15
        test_id = base + ('_gr' if is_free else '')
        with open(path, encoding='utf-8') as f:
            data = json.load(f)
            for q in data:
                questions.append({
                    'test_id': test_id,
                    'question_number': int(q.get('question_number')),
                    'extracted_text': q.get('extracted_text', ''),
                    'question_text': q.get('question_text', ''),
                    'choices': q.get('choices', {})
                })

# 2. Load correct answers
with open(ANSWER_FILES['regular'], encoding='utf-8') as f:
    correct_regular = json.load(f)
with open(ANSWER_FILES['free'], encoding='utf-8') as f:
    correct_free = json.load(f)

# Load existing group-based answers
with open(ANSWER_FILES['by_group'], encoding='utf-8') as f:
    existing_group_answers = json.load(f)

# 2.5. Load analysis data
ANALYSIS_DIRS = {
    'regular': 'data/analysis/reading_analysis',
    'free': 'data/analysis/reading_analysis_free'
}
analysis_data = {}

for category, dir_path in ANALYSIS_DIRS.items():
    if not os.path.exists(dir_path):
        print(f"Warning: {dir_path} not found")
        continue
        
    for json_file in glob.glob(os.path.join(dir_path, 'test*.json')):
        test_id = os.path.splitext(os.path.basename(json_file))[0]  # e.g., test1
        test_key = test_id + ('_gr' if category == 'free' else '')
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            # Map by question_number for fast lookup
            analysis_data[test_key] = {str(q['question_number']): q for q in data}
        
        print(f"Loaded {category} analysis for {test_id}: {len(data)} questions")

# Load existing group analysis files
existing_group_analysis = {}
for group_num in range(1, 7):  # groups 1-6
    analysis_file = os.path.join(ANALYSIS_DIR, f'reading_group{group_num}_analysis.json')
    if os.path.exists(analysis_file):
        with open(analysis_file, 'r', encoding='utf-8') as f:
            existing_group_analysis[f'group{group_num}'] = json.load(f)
        print(f"Loaded existing analysis for group{group_num}")
    else:
        print(f"Warning: {analysis_file} not found")

def get_correct_answer(location):
    # location: 'test15 Q12' or 'test15_gr Q12'
    test_part, q_part = location.split()
    is_free = test_part.endswith('_gr')
    test_key = test_part.replace('_gr', '')
    q_num = q_part.replace('Q', '')
    if is_free:
        return correct_free.get(test_key, {}).get(q_num)
    else:
        return correct_regular.get(test_key, {}).get(q_num)

def get_analysis_for_location(location):
    """Get analysis for a specific location like 'test12 Q1' or 'test12_gr Q1'"""
    test_part, q_part = location.split()
    q_num = q_part.replace('Q', '')
    return analysis_data.get(test_part, {}).get(q_num)

# 3. Compute embeddings
model = SentenceTransformer(MODEL_NAME)
texts = [q['extracted_text'] for q in questions]
embeddings = model.encode(texts, convert_to_tensor=True, show_progress_bar=True)

# Helper functions for sorting
def parse_location(location):
    """Parse a location string like 'test12 Q5' or 'test1_gr Q10' into components."""
    match = re.match(r'(test\d+)(_gr)?\s+Q(\d+)', location)
    if match:
        test_base = match.group(1)  # e.g., 'test12'
        is_free = match.group(2) is not None  # True if '_gr' suffix exists
        test_num = int(test_base.replace('test', ''))  # e.g., 12
        question_num = int(match.group(3))  # e.g., 5
        return (is_free, test_num, question_num)
    return (False, 999999, 999999)  # fallback for unparseable locations

def sort_locations(locations):
    """Sort locations with free tests first, then normal tests, ordered by test number then question number."""
    def location_sort_key(location):
        is_free, test_num, question_num = parse_location(location)
        # Free tests (is_free=True) should come first, so we use (not is_free) as the primary key
        # This makes free tests have key (False, ...) and normal tests have key (True, ...)
        return (not is_free, test_num, question_num)

    return sorted(locations, key=location_sort_key)

def get_primary_location_sort_key(locations):
    """Get the sort key for the first (primary) location in a sorted locations list."""
    if not locations:
        return (True, 999999, 999999)  # fallback
    sorted_locs = sort_locations(locations)
    is_free, test_num, question_num = parse_location(sorted_locs[0])
    # Return the same key format as location_sort_key for consistency
    return (not is_free, test_num, question_num)

# 4. Cluster by similarity (greedy)
N = len(questions)
assigned = np.zeros(N, dtype=bool)
clusters = []
for i in tqdm(range(N)):
    if assigned[i]:
        continue
    cluster = [i]
    assigned[i] = True
    sims = util.pytorch_cos_sim(embeddings[i], embeddings)[0].cpu().numpy()
    for j in range(i+1, N):
        if not assigned[j] and sims[j] >= THRESHOLD:
            cluster.append(j)
            assigned[j] = True
    clusters.append(cluster)

# 5. For each cluster, pick the best representative with complete data
rep_questions = []
for cluster in clusters:
    locs = [f"{questions[idx]['test_id']} Q{questions[idx]['question_number']}" for idx in cluster]
    # Sort locations: free tests first, then normal tests, ordered by test number then question number
    sorted_locs = sort_locations(locs)

    # Find the first location that has both question data AND correct answer
    # This ensures we use a complete set from the same source
    best_question = None
    best_answer = None
    best_location = None

    for loc in sorted_locs:
        # Find the question data for this location
        for idx in cluster:
            q = questions[idx]
            q_loc = f"{q['test_id']} Q{q['question_number']}"
            if q_loc == loc:
                # Check if this location has a correct answer
                ans = get_correct_answer(loc)
                if ans:
                    # Found a complete set - use this as our representative
                    best_question = q
                    best_answer = ans
                    best_location = loc
                    break
        if best_question and best_answer:
            break

    # Fallback: if no location has both, use the first question and first available answer
    if not best_question:
        best_question = questions[cluster[0]]
        for loc in sorted_locs:
            ans = get_correct_answer(loc)
            if ans:
                best_answer = ans
                break

    rep_questions.append({
        'extracted_text': best_question['extracted_text'],
        'question_text': best_question['question_text'],
        'choices': best_question['choices'],
        'locations': sorted_locs,
        'correct_answer': best_answer,
        'source_location': best_location  # Track which source we used
    })

# 6. Assign to groups and write output
for start, end, out_file in GROUPS:
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # Sort questions within each group by their primary location (first location in sorted list)
    # Free tests first, then normal tests, ordered by test number then question number
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    out_path = os.path.join(OUTPUT_DIR, out_file)
    with open(out_path, 'w', encoding='utf-8') as f:
        json.dump(group, f, ensure_ascii=False, indent=2)
    print(f"Wrote {len(group)} questions to {out_path}")

# 7. Export correct answers by group
correct_by_group = {}
for idx, (start, end, out_file) in enumerate(GROUPS, 1):
    group_key = f"group{idx}"
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # IMPORTANT: Sort questions the same way as in the question files
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    correct_by_group[group_key] = {}
    for i, q in enumerate(group, 1):
        if q.get('correct_answer'):
            correct_by_group[group_key][str(i)] = q['correct_answer']

correct_group_path = 'data/scraped/scraped_answer/reading_correct_answer_by_group.json'
with open(correct_group_path, 'w', encoding='utf-8') as f:
    json.dump(correct_by_group, f, ensure_ascii=False, indent=2)
print(f"Wrote group correct answers to {correct_group_path}")

# 8. Export analysis by group
analysis_by_group = {}
for idx, (start, end, out_file) in enumerate(GROUPS, 1):
    group_key = f"group{idx}"
    group = [
        q for q in rep_questions if start <= questions[clusters[rep_questions.index(q)][0]]['question_number'] <= end
    ]
    # IMPORTANT: Sort questions the same way as in the question files
    group.sort(key=lambda q: get_primary_location_sort_key(q['locations']))

    analysis_by_group[group_key] = {}
    for i, q in enumerate(group, 1):
        # Use analysis from the same source location as the question/answer if available
        analysis = None
        source_location = q.get('source_location')

        if source_location:
            # Try to get analysis from the same source first
            analysis = get_analysis_for_location(source_location)

        # If no analysis from source location, try other locations as fallback
        if not analysis:
            for location in q.get('locations', []):
                analysis = get_analysis_for_location(location)
                if analysis:
                    break

        if analysis:
            analysis_by_group[group_key][str(i)] = analysis
        else:
            print(f"  ⚠️  No analysis found for {group_key} question {i} (locations: {q.get('locations', [])})")

# Create output directory for analysis files
analysis_output_dir = 'data/analysis/reading_analysis_by_group/'
os.makedirs(analysis_output_dir, exist_ok=True)

# Write individual group analysis files
for group_key, group_analysis in analysis_by_group.items():
    group_num = group_key.replace('group', '')
    output_file = os.path.join(analysis_output_dir, f'reading_group{group_num}_analysis.json')
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(group_analysis, f, ensure_ascii=False, indent=2)
    print(f"Wrote {len(group_analysis)} analysis entries to {output_file}")

print(f"✅ Analysis by group files created in {analysis_output_dir}") 