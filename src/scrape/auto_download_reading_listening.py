#!/usr/bin/env python3
"""
Automate answering multiple TCF Canada “Compréhension Écrite” quizzes and saving each result.

Usage:
  python auto_click.py <startID>-<endID>
  python auto_click.py all

Examples:
  python auto_click.py 3-5   # tests 3,4,5
  python auto_click.py all    # tests 1 through 39
"""
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException

print("auto_click.py loaded, args:", sys.argv)

# Argument handling
if len(sys.argv) != 2:
    sys.exit("Usage: python auto_click.py <startID>-<endID> or 'all'")
arg = sys.argv[1]
if arg.lower() == 'all':
    test_ids = list(range(1, 38))
elif '-' in arg:
    start, end = arg.split('-', 1)
    test_ids = list(range(int(start), int(end) + 1))
else:
    test_ids = [int(arg)]

LOGIN_URL = 'https://examens.preptcfcanada.com/'
BASE_URL = 'https://examens.preptcfcanada.com/comprehension-oral-test-'
SAVE_DIR = '/Users/<USER>/Git_repo/fun/TCF-Canada/temp1'

# Main automation
def main():
    print("Starting automation for tests:", test_ids)
    driver = webdriver.Chrome()
    wait = WebDriverWait(driver, 10)

    # 1) Manual login
    print(f"Navigate to login: {LOGIN_URL}")
    driver.get(LOGIN_URL)
    input("Please log in in the browser, then press Enter to continue...")

    # Loop through each test
    for test_id in test_ids:
        url = f"{BASE_URL}{test_id}/"
        print(f"\nProcessing Test {test_id}: {url}")
        driver.get(url)

        # 2) Start Quiz if needed
        try:
            btn = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='startQuiz']")))
            btn.click()
        except TimeoutException:
            pass

        # 3) Answer all questions
        q_count = 0
        while True:
            try:
                li = wait.until(EC.visibility_of_element_located((
                    By.CSS_SELECTOR,
                    "li.wpProQuiz_listItem:not([style*='display: none'])"
                )))
            except TimeoutException:
                print(f"Test {test_id}: completed {q_count} questions.")
                break

            # select first radio
            try:
                radio = li.find_element(By.CSS_SELECTOR, "input[type='radio']")
                driver.execute_script("arguments[0].scrollIntoView(true);", radio)
                try:
                    radio.click()
                except ElementClickInterceptedException:
                    driver.execute_script("arguments[0].click();", radio)
                q_count += 1
            except Exception as e:
                print(f"Test {test_id}: radio click failed: {e}")
                break

            # click next
            try:
                nxt = li.find_element(By.CSS_SELECTOR, "input[name='next']")
                driver.execute_script("arguments[0].scrollIntoView(true);", nxt)
                try:
                    nxt.click()
                except ElementClickInterceptedException:
                    driver.execute_script("arguments[0].click();", nxt)
            except Exception as e:
                print(f"Test {test_id}: next click failed: {e}")
                break

            time.sleep(1)

        # 4) Save page
        html_file = f"{SAVE_DIR}/comprehension-oral-test-{test_id}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"Saved HTML: {html_file}")


    print("All tests processed. Browser remains open.")

if __name__ == '__main__':
    main()
