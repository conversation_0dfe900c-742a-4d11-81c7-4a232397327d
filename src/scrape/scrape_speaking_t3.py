import os
import json
import re
from pathlib import Path
from bs4 import BeautifulSoup
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_formatted_text(element):
    """
    Extract text from HTML element while preserving formatting like indentation, 
    spacing, and line breaks without keeping HTML tags.
    """
    if not element:
        return ""
    
    # Replace various HTML elements with appropriate text formatting
    # Create a copy to avoid modifying the original
    soup = BeautifulSoup(str(element), 'html.parser')
    
    # Replace <br> tags with line breaks
    for br in soup.find_all('br'):
        br.replace_with('\n')
    
    # Replace </p> tags with double line breaks (paragraph separation)
    for p in soup.find_all('p'):
        p.insert_after('\n\n')
    
    # Replace </div> tags with line breaks
    for div in soup.find_all('div'):
        div.insert_after('\n')
    
    # Replace </li> tags with line breaks
    for li in soup.find_all('li'):
        li.insert_after('\n')
    
    # Get the text while preserving whitespace structure
    text = soup.get_text()
    
    # Clean up excessive line breaks while preserving intentional formatting
    # Replace multiple consecutive line breaks with maximum of 2
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    # Remove leading/trailing whitespace but preserve internal structure
    text = text.strip()
    
    return text

def extract_topic_from_filename(filename):
    """Extract topic from filename like 'Santé _ Correction Expression Orale Tâche 3 Pro.html'"""
    # Remove file extension and split by underscore
    base_name = filename.replace('.html', '')
    
    # Extract topic (first part before _)
    if '_' in base_name:
        topic = base_name.split('_')[0].strip()
        return topic
    
    return base_name.strip()

def parse_speaking_t3_html(file_path):
    """
    Parse a speaking task 3 HTML file and extract the structure:
    - Multiple taches (tasks/questions)
    - Each tache has an exemplary answer
    """
    logger.info(f"Processing file: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Initialize result structure
    result = {
        "taches": []
    }
    
    # Find all accordion items that contain the tasks
    accordion_items = soup.find_all('div', class_='elementor-accordion-item')
    
    if not accordion_items:
        logger.warning(f"No accordion items found in {file_path}")
        return result
    
    logger.info(f"Found {len(accordion_items)} accordion items")
    
    for i, item in enumerate(accordion_items):
        # Find the task title/question
        title_element = item.find('a', class_='elementor-accordion-title')
        if not title_element:
            logger.warning(f"No title found for item {i+1}")
            continue
            
        # Use formatted text extraction to preserve structure
        task_question = extract_formatted_text(title_element)
        
        # Find the exemplary answer content
        content_element = item.find('div', class_='elementor-tab-content')
        if not content_element:
            logger.warning(f"No content found for item {i+1}")
            continue
        
        # Extract the exemplary answer text with preserved formatting
        exemplary_answer_formatted = extract_formatted_text(content_element)
        
        # Also extract individual paragraphs with preserved formatting
        paragraphs = []
        for p in content_element.find_all('p'):
            paragraph_text = extract_formatted_text(p)
            if paragraph_text:
                paragraphs.append(paragraph_text)
        
        if task_question and (exemplary_answer_formatted or paragraphs):
            tache_data = {
                "task_number": i + 1,
                "question": task_question,
                "exemplary_answer": {
                    "full_text": exemplary_answer_formatted,
                    "paragraphs": paragraphs,
                    "paragraph_count": len(paragraphs)
                },
                "word_count": len(exemplary_answer_formatted.split()) if exemplary_answer_formatted else 0
            }
            
            result["taches"].append(tache_data)
            logger.info(f"  - Found task {i+1}: {len(exemplary_answer_formatted.split())} words")
    
    logger.info(f"Successfully parsed {len(result['taches'])} tasks")
    return result

def scrape_all_speaking_t3_files():
    """Scrape all speaking task 3 HTML files and organize by topic"""
    
    # Define paths
    input_dir = Path("/Users/<USER>/Git_repo/fun/TCF-Canada/data/raw/Expression-Orale/La_tâche_3")
    output_dir = Path("/Users/<USER>/Git_repo/fun/TCF-Canada/data/scraped/scraped_speaking/La_tâche_3")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all HTML files
    html_files = list(input_dir.glob("*.html"))
    html_files = [f for f in html_files if f.name != ".DS_Store"]
    
    logger.info(f"Found {len(html_files)} HTML files to process")
    
    # Process each file
    all_topics_data = {}
    
    for html_file in html_files:
        topic = extract_topic_from_filename(html_file.name)
        
        if not topic:
            logger.warning(f"Could not extract topic from filename: {html_file.name}")
            continue
        
        # Parse the HTML file
        parsed_data = parse_speaking_t3_html(html_file)
        
        if parsed_data["taches"]:
            # Calculate totals
            total_tasks = len(parsed_data["taches"])
            total_words = sum(tache["word_count"] for tache in parsed_data["taches"])
            total_paragraphs = sum(tache["exemplary_answer"]["paragraph_count"] for tache in parsed_data["taches"])
            
            # Create topic data structure
            topic_data = {
                "topic": topic,
                "taches": parsed_data["taches"],
                "statistics": {
                    "total_tasks": total_tasks,
                    "total_words": total_words,
                    "total_paragraphs": total_paragraphs,
                    "average_words_per_task": round(total_words / total_tasks, 1) if total_tasks > 0 else 0
                }
            }
            
            all_topics_data[topic] = topic_data
            
            # Save individual topic file
            output_file = output_dir / f"{topic}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(topic_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved {topic}: {total_tasks} tasks, {total_words} words, {total_paragraphs} paragraphs")
    
    # Save summary file with all topics
    summary_file = output_dir / "all_speaking_t3.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(all_topics_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Saved summary file with {len(all_topics_data)} topics")
    
    # Print summary statistics
    total_files = len(all_topics_data)
    total_tasks = sum(data["statistics"]["total_tasks"] for data in all_topics_data.values())
    total_words = sum(data["statistics"]["total_words"] for data in all_topics_data.values())
    total_paragraphs = sum(data["statistics"]["total_paragraphs"] for data in all_topics_data.values())
    
    print(f"\n📊 SCRAPING SUMMARY:")
    print(f"✅ Processed {total_files} topic files")
    print(f"📋 Total tasks: {total_tasks}")
    print(f"📝 Total words: {total_words:,}")
    print(f"📄 Total paragraphs: {total_paragraphs}")
    print(f"📁 Output directory: {output_dir}")
    
    # Print topic breakdown
    print(f"\n📚 TOPICS BREAKDOWN:")
    for topic, data in all_topics_data.items():
        stats = data["statistics"]
        print(f"  • {topic}: {stats['total_tasks']} tasks, {stats['total_words']:,} words ({stats['average_words_per_task']} avg/task)")

if __name__ == "__main__":
    scrape_all_speaking_t3_files()
