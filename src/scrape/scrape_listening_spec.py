#!/usr/bin/env python3
"""
Scrape a local TCF oral-quiz HTML file into JSON and media folder.
"""
from __future__ import annotations

import argparse
import json
import re
import time
import requests
import subprocess
import sys
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# ──────────────────────────────────────────────────────────────────────────────
# Constants & Defaults
# ──────────────────────────────────────────────────────────────────────────────
DEFAULT_OUTPUT_DIR = Path("scraped_listening")
DEFAULT_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

DEFAULT_BASE_URL = "https://examens.preptcfcanada.com/"

# ──────────────────────────────────────────────────────────────────────────────
# Helpers
# ──────────────────────────────────────────────────────────────────────────────

def download_file(url: str, dest: Path):
    """Download a file if it doesn't already exist."""
    if not dest.exists():
        resp = requests.get(url, timeout=30)
        resp.raise_for_status()
        dest.write_bytes(resp.content)


def clean_choice(txt: str) -> str:
    """Strip leading numbering/‘A 88’ etc. from a choice label."""
    return re.sub(r'^(?:\d+\.\s*)?[A-D]\s*88\s*', '', txt).strip()


def parse_question(li, base_url: str) -> dict:
    # Determine question number
    data_id = li.get("data-question_id")
    if data_id:
        qnum = data_id
    else:
        ul = li.find("ul", class_="wpProQuiz_questionList")
        if ul and ul.has_attr("data-question_id"):
            qnum = ul["data-question_id"]
        else:
            raw = ""
            page = li.find("div", class_="wpProQuiz_question_page")
            if page:
                raw = page.text.strip()
            match = re.search(r"\d+", raw)
            qnum = match.group(0) if match else "?"

    # Audio: <audio src> or nested <source>
    audio_tag = li.find("audio")
    audio_path = None
    if audio_tag:
        src = audio_tag.get("src")
        if not src:
            src_tag = audio_tag.find("source")
            src = src_tag["src"] if src_tag and src_tag.get("src") else None
        if src:
            audio_url = urljoin(base_url, src)
            audio_path = audio_tag._save_dir / f"Q{qnum}.mp3"
            download_file(audio_url, audio_path)

    # Image
    img_tag = li.find("img")
    image_path = None
    if img_tag and img_tag.get("src"):
        img_url = urljoin(base_url, img_tag["src"])
        ext = Path(img_url).suffix or ".webp"
        image_path = img_tag._save_dir / f"Q{qnum}{ext}"
        download_file(img_url, image_path)

    # Choices
    choices = {}
    ul = li.find("ul", class_="wpProQuiz_questionList")
    if ul:
        for i, choice_li in enumerate(ul.find_all("li"), start=1):
            label = choice_li.find("label")
            if label:
                text = clean_choice(label.get_text(" ", strip=True))
                letter = chr(64 + i)
                choices[letter] = text

    return {
        "question_number": qnum,
        "audio_path": str(audio_path) if audio_path else None,
        "image_path": str(image_path) if image_path else None,
        "choices": choices or None,
    }


def scrape_oral_quiz(html_file: str, base_url: str):
    """Parse the HTML file, download media into quiz-specific folder, and write JSON."""
    m = re.search(r"test[-_]?(\d+)", html_file, re.I)
    quiz_id = m.group(1) if m else Path(html_file).stem

    save_dir = Path("listening_asset") / f"media_test{quiz_id}"
    save_dir.mkdir(parents=True, exist_ok=True)

    html = Path(html_file).read_text(encoding="utf-8")
    soup = BeautifulSoup(html, "html.parser")
    items = soup.select("li.wpProQuiz_listItem")

    for li in items:
        for tag in (li.find_all("audio") + li.find_all("img")):
            tag._save_dir = save_dir  # type: ignore

    results = []
    for i, li in enumerate(items, start=1):
        entry = parse_question(li, base_url)
        # Override question_number to be sequential
        entry['question_number'] = str(i)
        results.append(entry)

    out_path = DEFAULT_OUTPUT_DIR / f"tcf_test{quiz_id}.json"
    out_path.write_text(
        json.dumps(results, ensure_ascii=False, indent=2),
        encoding="utf-8"
    )

    print(f"Saved {len(results)} questions → {out_path}")
    print(f"Downloaded media into     → {save_dir}")


def main():
    parser = argparse.ArgumentParser(
        description="Scrape a local TCF oral-quiz HTML file into JSON and media folder."
    )
    parser.add_argument(
        "html_file",
        help="Path to the downloaded HTML file (e.g. comprehension-oral-test-1.html)"
    )
    parser.add_argument(
        "base_url",
        nargs="?",
        default=DEFAULT_BASE_URL,
        help=f"OPTIONAL: base URL to resolve relative media paths (default: {DEFAULT_BASE_URL!r})"
    )
    args = parser.parse_args()

    html_path = Path(args.html_file)
    if not html_path.exists():
        parser.error(f"File not found: {html_path}")

    scrape_oral_quiz(str(html_path), args.base_url)


if __name__ == "__main__":
    main()
