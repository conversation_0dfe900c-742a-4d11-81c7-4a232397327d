from __future__ import annotations

import subprocess, sys, time, pathlib, re
from typing import Optional, Iterable

# ──────────────────────────────────────────────────────────────────────────────
SCRAPER = pathlib.Path(__file__).with_name("scrape_listening.py")
BASE_URL = "https://examens.preptcfcanada.com/comprehension-oral-test-{}/"
PAUSE = 1.0  # seconds between successive scrapes (be polite to the Vision API)
HTML_RE = re.compile(r"comprehension[-_]?oral[-_]?test[-–—]?(\d+)", re.I)

# ──────────────────────────────────────────────────────────────────────────────

def run_scraper(target: str | pathlib.Path) -> None:
    """Invoke `scrape_tcf.py <target>` and stream its output."""
    try:
        subprocess.run([sys.executable, str(SCRAPER), str(target)], check=True)
    except subprocess.CalledProcessError as err:
        print(f"{target} failed (exit {err.returncode}). Continuing…")


def iter_urls(start: int, end: int) -> Iterable[str]:
    for n in range(start, end + 1):
        yield BASE_URL.format(n)


def iter_html_files(folder: pathlib.Path) -> Iterable[pathlib.Path]:
    for path in sorted(folder.glob("*.html")):
        yield path

# ──────────────────────────────────────────────────────────────────────────────
# argument parsing helpers
# ──────────────────────────────────────────────────────────────────────────────

def parse_range_args(argv: list[str]) -> tuple[int, int]:
    if len(argv) == 0:
        return 37, 37
    if len(argv) == 1 or len(argv) > 2:
        sys.exit("Give *both* start and end, or none.")
    try:
        s, e = int(argv[0]), int(argv[1])
    except ValueError:
        sys.exit("Start/end must be integers.")
    if s < 1 or e < s:
        sys.exit("Invalid range: start ≥ 1 and end ≥ start.")
    return s, e

# ──────────────────────────────────────────────────────────────────────────────

def main(argv: Optional[list[str]] = None) -> None:
    argv = (argv or sys.argv)[1:]  # strip script name

    if not SCRAPER.exists():
        sys.exit(f"Cannot find {SCRAPER} — place this script next to scrape_tcf.py.")

    # ── Folder mode ─────────────────────────────────────────────────────────
    if argv and pathlib.Path(argv[0]).is_dir():
        folder = pathlib.Path(argv[0]).resolve()
        print(f"Folder mode → processing all *.html in {folder}\n")
        for html_path in iter_html_files(folder):
            print(f"{html_path.name}")
            run_scraper(html_path)
            time.sleep(PAUSE)
        print("\n All local files done.")
        return

    # ── URL mode ────────────────────────────────────────────────────────────
    start, end = parse_range_args(argv)
    print(f"URL mode → scraping tests {start}-{end}\n")
    for url in iter_urls(start, end):
        print(f"{url}")
        run_scraper(url)
        time.sleep(PAUSE)
    print("\nAll remote tests done.")


if __name__ == "__main__":
    main()
