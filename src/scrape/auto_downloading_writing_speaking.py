#!/usr/bin/env python3
"""
Download all 36 “correction expression orale – tâche 2 pro” pages for 2023–2025.

Usage:
  python download_all_orale_corrections.py
"""

import time
from selenium import webdriver
from pathlib import Path

# --- SETTINGS ---
LOGIN_URL = 'https://examens.preptcfcanada.com/'
BASE_URL  = 'https://reussir-tcfcanada.com/'
SAVE_DIR  = Path("downloaded_orale_corrections_2023_2025")
SAVE_DIR.mkdir(exist_ok=True)

# French month names, lowercase, no accents
months = [
    "janvier", "fevrier", "mars", "avril", "mai", "juin",
    "juillet", "aout", "septembre", "octobre", "novembre", "decembre"
]
years = [2024, 2025]

def make_slug(month: str, year: int) -> str:
    return f"{month}-{year}-correction-expression-ecrite"

def main():
    driver = webdriver.Chrome()
    driver.maximize_window()

    # 1) Manual login
    driver.get(LOGIN_URL)
    input("Please log in on the TCF Canada site, then press Enter here to continue…")

    # 2) Loop over all months & years
    for year in years:
        for month in months:
            slug = make_slug(month, year)
            url = f"{BASE_URL}{slug}/"
            print(f"→ Downloading {url}")
            driver.get(url)
            time.sleep(2)  # wait for JS/dynamic content if any

            out_file = SAVE_DIR / f"{slug}.html"
            out_file.write_text(driver.page_source, encoding="utf-8")
            print(f"   • Saved → {out_file}")

    print(" Done downloading all 36 pages. Browser remains open.")
    # driver.quit()

if __name__ == '__main__':
    main()