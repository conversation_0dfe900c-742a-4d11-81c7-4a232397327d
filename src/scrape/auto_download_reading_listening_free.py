#!/usr/bin/env python3
"""
Automate answering three specific TCF Canada "Compréhension Écrite" quizzes and saving each result.

Usage:
  python auto_download_reading_speaking_listening_free.py
"""
import sys
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, ElementClickInterceptedException

print("auto_download_reading_speaking_listening_free.py loaded")

test_ids = [1, 2, 3]
# The URL suffix contains the special character %ef%bf%bc
URL_SUFFIX = '-gr%ef%bf%bc/'
BASE_URL = 'https://examens.preptcfcanada.com/comprehension-orale-test-'
SAVE_DIR = '/Users/<USER>/Git_repo/fun/TCF-Canada/temp1'
LOGIN_URL = 'https://examens.preptcfcanada.com/'

def main():
    print("Starting automation for tests:", test_ids)
    driver = webdriver.Chrome()
    wait = WebDriverWait(driver, 10)

    # 1) Manual login
    print(f"Navigate to login: {LOGIN_URL}")
    driver.get(LOGIN_URL)
    input("Please log in in the browser, then press Enter to continue...")

    # Loop through each test
    for test_id in test_ids:
        url = f"{BASE_URL}{test_id}{URL_SUFFIX}"
        print(f"\nProcessing Test {test_id}: {url}")
        driver.get(url)

        # 2) Start Quiz if needed
        try:
            btn = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, "input[name='startQuiz']")))
            btn.click()
        except TimeoutException:
            pass

        # 3) Answer all questions
        q_count = 0
        while True:
            try:
                li = wait.until(EC.visibility_of_element_located((
                    By.CSS_SELECTOR,
                    "li.wpProQuiz_listItem:not([style*='display: none'])"
                )))
            except TimeoutException:
                print(f"Test {test_id}: completed {q_count} questions.")
                break

            # select first radio
            try:
                radio = li.find_element(By.CSS_SELECTOR, "input[type='radio']")
                driver.execute_script("arguments[0].scrollIntoView(true);", radio)
                try:
                    radio.click()
                except ElementClickInterceptedException:
                    driver.execute_script("arguments[0].click();", radio)
                q_count += 1
            except Exception as e:
                print(f"Test {test_id}: radio click failed: {e}")
                break

            # click next
            try:
                nxt = li.find_element(By.CSS_SELECTOR, "input[name='next']")
                driver.execute_script("arguments[0].scrollIntoView(true);", nxt)
                try:
                    nxt.click()
                except ElementClickInterceptedException:
                    driver.execute_script("arguments[0].click();", nxt)
            except Exception as e:
                print(f"Test {test_id}: next click failed: {e}")
                break

            time.sleep(1)

        # 4) Save page
        html_file = f"{SAVE_DIR}/comprehension-orale-test-{test_id}-gr.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(driver.page_source)
        print(f"Saved HTML: {html_file}")

    print("All tests processed. Browser remains open.")

if __name__ == '__main__':
    main()
