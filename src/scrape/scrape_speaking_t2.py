import os
import json
import re
from pathlib import Path
from bs4 import BeautifulSoup
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_formatted_text(element):
    """
    Extract text from HTML element while preserving formatting like indentation, 
    spacing, and line breaks without keeping HTML tags.
    """
    if not element:
        return ""
    
    # Replace various HTML elements with appropriate text formatting
    # Create a copy to avoid modifying the original
    soup = BeautifulSoup(str(element), 'html.parser')
    
    # Replace <br> tags with line breaks
    for br in soup.find_all('br'):
        br.replace_with('\n')
    
    # Replace </p> tags with double line breaks (paragraph separation)
    for p in soup.find_all('p'):
        p.insert_after('\n\n')
    
    # Replace </div> tags with line breaks
    for div in soup.find_all('div'):
        div.insert_after('\n')
    
    # Replace </li> tags with line breaks
    for li in soup.find_all('li'):
        li.insert_after('\n')
    
    # Get the text while preserving whitespace structure
    text = soup.get_text()
    
    # Clean up excessive line breaks while preserving intentional formatting
    # Replace multiple consecutive line breaks with maximum of 2
    text = re.sub(r'\n{3,}', '\n\n', text)
    
    # Remove leading/trailing whitespace but preserve internal structure
    text = text.strip()
    
    return text

def extract_month_from_filename(filename):
    """Extract month and year from filename like 'mai-2025-correction-expression-orale-tache-2-pro.html'"""
    # Remove file extension
    base_name = filename.replace('.html', '')
    
    # Extract month-year pattern
    match = re.match(r'(\w+)-(2023|2024|2025)', base_name)
    if match:
        month_fr, year = match.groups()
        return f"{month_fr}-{year}"
    
    return None

def parse_speaking_html(file_path):
    """
    Parse a speaking HTML file and extract the structure:
    - Multiple parties (sections)
    - Each partie contains multiple taches (scenarios)
    - Each tache has example questions
    """
    logger.info(f"Processing file: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    soup = BeautifulSoup(content, 'html.parser')
    
    # Initialize result structure
    result = {
        "parties": []
    }
    
    # Find all "Partie X" dividers
    partie_dividers = soup.find_all('span', class_='elementor-divider__text', string=re.compile(r'Partie \d+'))
    
    if not partie_dividers:
        logger.warning(f"No partie dividers found in {file_path}")
        return result
    
    logger.info(f"Found {len(partie_dividers)} parties")
    
    for i, partie_divider in enumerate(partie_dividers):
        partie_text = partie_divider.get_text().strip()
        partie_number = re.search(r'Partie (\d+)', partie_text).group(1)
        logger.info(f"Processing Partie {partie_number}")
        
        # Find the accordion section that comes after this partie divider
        # Start from the partie divider and look for the next accordion element
        accordion = None
        
        # Get the container div of the partie divider
        divider_container = partie_divider.find_parent('div', class_='elementor-widget elementor-widget-divider')
        
        if divider_container:
            # Look for the next sibling that contains an accordion
            current_element = divider_container
            while current_element:
                current_element = current_element.find_next_sibling()
                if current_element:
                    # Look for accordion class patterns
                    accordion_widget = current_element.find('div', class_='elementor-widget-accordion')
                    if accordion_widget:
                        accordion = accordion_widget.find('div', class_='elementor-accordion')
                        if accordion:
                            break
        
        # Alternative method: use find_next to search forward in the DOM
        if not accordion:
            accordion_search = partie_divider.find_next('div', class_='elementor-accordion')
            if accordion_search:
                # Verify this is the next accordion (not too far away)
                accordion = accordion_search
        
        if not accordion:
            logger.warning(f"No accordion found for Partie {partie_number}")
            continue
        
        logger.info(f"Found accordion for Partie {partie_number}")
        
        # Extract all taches (scenarios) from this partie
        taches = []
        accordion_items = accordion.find_all('div', class_='elementor-accordion-item')
        
        for item in accordion_items:
            # Find the scenario title (tache description)
            title_element = item.find('a', class_='elementor-accordion-title')
            if not title_element:
                continue
                
            # Use formatted text extraction to preserve structure
            scenario = extract_formatted_text(title_element)
            
            # Find the content with example questions
            content_element = item.find('div', class_='elementor-tab-content')
            if not content_element:
                continue
            
            # Extract the numbered list of questions
            questions = []
            
            # Try the old format first: <ol> with <li> elements
            ol_element = content_element.find('ol')
            if ol_element:
                for li in ol_element.find_all('li'):
                    # Use formatted text extraction to preserve question structure
                    question_text = extract_formatted_text(li)
                    if question_text:
                        questions.append(question_text)
            else:
                # Try the new 2025 format: <p> with <strong> elements separated by <br>
                p_elements = content_element.find_all('p')
                for p in p_elements:
                    # Get all strong elements within this paragraph
                    strong_elements = p.find_all('strong')
                    for strong in strong_elements:
                        question_text = extract_formatted_text(strong)
                        if question_text and question_text.strip():
                            # Remove numbering if it exists (e.g., "1. Question" -> "Question")
                            cleaned_question = re.sub(r'^\d+\.\s*', '', question_text.strip())
                            if cleaned_question:
                                questions.append(cleaned_question)
            
            if scenario and questions:
                taches.append({
                    "scenario": scenario,
                    "example_questions": questions,
                    "question_count": len(questions)
                })
                logger.info(f"  - Found scenario with {len(questions)} questions")
        
        if taches:
            result["parties"].append({
                "partie_number": int(partie_number),
                "taches": taches,
                "tache_count": len(taches)
            })
    
    logger.info(f"Successfully parsed {len(result['parties'])} parties with total scenarios")
    return result

def scrape_all_speaking_files():
    """Scrape all speaking HTML files and organize by month"""
    
    # Define paths
    input_dir = Path("/Users/<USER>/Git_repo/fun/TCF-Canada/data/raw/Expression-Orale/La_tâche_2")
    output_dir = Path("/Users/<USER>/Git_repo/fun/TCF-Canada/data/scraped/scraped_speaking/La_tache_2")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all HTML files
    html_files = list(input_dir.glob("*.html"))
    html_files = [f for f in html_files if f.name != ".DS_Store"]
    
    logger.info(f"Found {len(html_files)} HTML files to process")
    
    # Process each file
    all_months_data = {}
    
    for html_file in html_files:
        month_year = extract_month_from_filename(html_file.name)
        
        if not month_year:
            logger.warning(f"Could not extract month from filename: {html_file.name}")
            continue
        
        # Parse the HTML file
        parsed_data = parse_speaking_html(html_file)
        
        if parsed_data["parties"]:
            # Calculate totals
            total_taches = sum(partie["tache_count"] for partie in parsed_data["parties"])
            total_questions = sum(
                sum(len(tache["example_questions"]) for tache in partie["taches"]) 
                for partie in parsed_data["parties"]
            )
            
            # Create month data structure
            month_data = {
                "month_year": month_year,
                "parties": parsed_data["parties"],
                "statistics": {
                    "total_parties": len(parsed_data["parties"]),
                    "total_scenarios": total_taches,
                    "total_questions": total_questions
                }
            }
            
            all_months_data[month_year] = month_data
            
            # Save individual month file
            output_file = output_dir / f"{month_year}.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(month_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Saved {month_year}: {len(parsed_data['parties'])} parties, {total_taches} scenarios, {total_questions} questions")
    
    # Save summary file with all months
    summary_file = output_dir / "all_speaking_t2.json"
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(all_months_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"Saved summary file with {len(all_months_data)} months")
    
    # Print summary statistics
    total_files = len(all_months_data)
    total_parties = sum(data["statistics"]["total_parties"] for data in all_months_data.values())
    total_scenarios = sum(data["statistics"]["total_scenarios"] for data in all_months_data.values())
    total_questions = sum(data["statistics"]["total_questions"] for data in all_months_data.values())
    
    print(f"\n📊 SCRAPING SUMMARY:")
    print(f"✅ Processed {total_files} month files")
    print(f"📋 Total parties: {total_parties}")
    print(f"🎭 Total scenarios: {total_scenarios}")
    print(f"❓ Total example questions: {total_questions}")
    print(f"📁 Output directory: {output_dir}")
    
    # Check for any skipped files
    processed_files = set(all_months_data.keys())
    all_files = set(extract_month_from_filename(f.name) for f in html_files if extract_month_from_filename(f.name))
    skipped_files = all_files - processed_files
    
    if skipped_files:
        print(f"\n❌ SKIPPED FILES ({len(skipped_files)}):")
        for file in sorted(skipped_files):
            print(f"  • {file}")
    else:
        print(f"\n✅ All {len(html_files)} files were processed successfully!")

if __name__ == "__main__":
    scrape_all_speaking_files()
