from bs4 import BeautifulSoup
import json
import os
from pathlib import Path

def extract_tasks_and_corrections(html_content):
    """Extract tasks and corrections from the HTML content."""
    soup = BeautifulSoup(html_content, 'html.parser')
    combinations = []
    
    # Find all divider elements that contain combination numbers
    dividers = soup.find_all('div', class_='elementor-divider')
    
    for i, divider in enumerate(dividers):
        # Extract combination number - find span with both classes
        combination_text = divider.find('span', class_=['elementor-divider__text', 'elementor-divider__element'])
        if not combination_text or 'Combinaison' not in combination_text.text:
            continue
            
        combination_number = combination_text.text.strip().replace('Combinaison ', '')
        
        current_combination = {
            'combination_number': combination_number,
            'tasks': []
        }
        
        # Find the top-level section containing this combination
        top_section = divider.find_parent('section')
        if not top_section:
            continue
            
        # Instead of trying to find boundaries, just process all elements in the top section
        # and use the section as the boundary
        elements_in_combination = [top_section]
        
        # Process tasks in this combination
        tasks = extract_tasks_from_elements(elements_in_combination)
        current_combination['tasks'] = tasks
        
        if tasks:  # Only add combination if it has tasks
            combinations.append(current_combination)
    
    # Sort combinations by number
    combinations.sort(key=lambda x: int(x['combination_number']))
    return combinations

def extract_tasks_from_elements(elements):
    """Extract tasks from a list of elements."""
    tasks = []
    task_data = {}
    
    for element in elements:
        # Find all heading titles in this element
        headings = element.find_all('span', class_='elementor-heading-title')
        
        for heading in headings:
            heading_text = heading.text.strip()
            
            # Check if it's a task title
            if heading_text.startswith('Tâche ') and 'Correction' not in heading_text:
                try:
                    task_number = int(heading_text.replace('Tâche ', ''))
                    if task_number not in task_data:
                        task_data[task_number] = {'task_number': task_number}
                    
                    # Find task content (next section with text editor)
                    task_content = find_next_text_content(heading)
                    if task_content:
                        task_data[task_number]['task_content'] = task_content
                        
                except ValueError:
                    continue
                
            # Check if it's a correction title
            elif heading_text.startswith('Correction Tâche '):
                try:
                    task_number = int(heading_text.replace('Correction Tâche ', ''))
                    if task_number not in task_data:
                        task_data[task_number] = {'task_number': task_number}
                    
                    # Find correction content (next section with text editor)
                    correction_content = find_next_text_content(heading)
                    if correction_content:
                        task_data[task_number]['correction_content'] = correction_content
                        
                except ValueError:
                    continue
                
    # Convert task_data dict to sorted list
    for task_num in sorted(task_data.keys()):
        task = task_data[task_num]
        # Only add task if it has content
        if 'task_content' in task:
            if 'correction_content' not in task:
                task['correction_content'] = None
            tasks.append(task)
    
    return tasks

def is_footer_content(text):
    """Check if text content is footer/navigation content that should be filtered out."""
    if not text:
        return False
    
    text_lower = text.lower().strip()
    
    # Exact footer/navigation patterns to filter out
    exact_footer_patterns = [
        "réussir tcf canada",
        "tous les droits sont réservés",
        "nos contacts", 
        "les méthodologies",
        "à propos de nous",
        "nous acceptons",
        "plateforme spécialisée dans la préparation",
        "pour partager les sujets de votre session"
    ]
    
    # Check if text contains any exact footer patterns
    for pattern in exact_footer_patterns:
        if pattern in text_lower:
            return True
    
    # Check for standalone navigation words (but not when part of longer content)
    if len(text_lower) < 30:
        standalone_patterns = ["les pages", "contacts", "méthodologies"]
        for pattern in standalone_patterns:
            if text_lower == pattern or text_lower.startswith(pattern + " ") or text_lower.endswith(" " + pattern):
                return True
    
    return False

def is_task_content(text):
    """Check if text content looks like task instructions that shouldn't be in corrections."""
    if not text:
        return False
    
    text_stripped = text.strip()
    text_lower = text_stripped.lower()
    
    # Task instruction patterns
    task_patterns = [
        "vous répondez à",
        "rédigez votre",
        "rédiger un message",
        "rédiger un article", 
        "rédiger une lettre",
        "vous écrivez un message",
        "vous décrivez",
        "vous donnez vos impressions",
        "faire des témoignages",
        "un site internet recherche",
        "mots minimum",
        "mots maximum"
    ]
    
    # Check for task instruction patterns
    for pattern in task_patterns:
        if pattern in text_lower:
            return True
    
    # Check for greeting patterns that indicate task content
    greeting_patterns = ["salut,", "cher ", "chère ", "bonjour,"]
    if any(text_lower.startswith(pattern) for pattern in greeting_patterns):
        return True
    
    # Check for short task-like questions
    if (len(text_stripped) < 100 and 
        text_stripped.endswith("?") and 
        any(word in text_lower for word in ["est-ce", "comment", "pourquoi", "que faire"])):
        return True
    
    return False

def contains_task_patterns(text):
    """Check if text contains patterns that indicate it's task content and should stop correction processing."""
    if not text:
        return False
    
    text_lower = text.strip().lower()
    
    # Strong indicators that this is task instruction content (not correction examples)
    strong_task_patterns = [
        "vous avez assisté à un événement",
        "racontez votre expérience et donnez votre impression",
        "décrivez le déroulement de l'événement",
        "une semaine sans voiture",
        "faire des témoignages sur",
        "un site internet recherche des gens",
        "rédigez votre expérience pour faire part"
    ]
    
    # Check for strong task patterns
    for pattern in strong_task_patterns:
        if pattern in text_lower:
            return True
    
    # Check for task instruction sentences (not just words)
    task_instruction_patterns = [
        "vous répondez à votre ami",
        "rédigez un message à",
        "vous écrivez un message à",
        "dans votre message, vous décrivez"
    ]
    
    for pattern in task_instruction_patterns:
        if pattern in text_lower:
            return True
    
    # Check for word count requirements (clear sign of task instructions)
    if ("mots minimum" in text_lower and "mots maximum" in text_lower):
        return True
    
    return False

def find_next_text_content(heading_element):
    """Find all unique content after a heading until the next task/correction heading, maintaining proper order."""
    all_content = []
    seen_content = set()  # Track content we've already captured
    
    # Determine if this is Task 3 by checking the heading text
    is_task3 = False
    heading_text = heading_element.text.strip()
    if heading_text.startswith('Tâche ') and '3' in heading_text:
        is_task3 = True
    
    # Find the next task/correction heading to establish boundary
    next_heading = None
    current = heading_element.find_next()
    
    while current:
        if (current.name == 'span' and 
            current.get('class') and 
            'elementor-heading-title' in current.get('class', [])):
            heading_text_check = current.text.strip()
            if (heading_text_check.startswith('Tâche ') or 
                heading_text_check.startswith('Correction Tâche ') or
                heading_text_check.startswith('Combinaison ')):
                next_heading = current
                break
        current = current.find_next()
    
    # For corrections, be more restrictive - stop at first major section boundary
    is_correction = heading_text.startswith('Correction')
    content_count = 0
    max_content_pieces = 3 if is_correction else 10  # Limit content pieces for corrections
    
    # Now collect all content between our heading and the next heading in order
    current_element = heading_element.find_next()
    
    while current_element and current_element != next_heading and content_count < max_content_pieces:
        # Early termination for corrections if we hit a section boundary
        if is_correction and current_element.name == 'section':
            # Check if this section might contain a new task
            section_headings = current_element.find_all('span', class_='elementor-heading-title')
            for section_heading in section_headings:
                section_text = section_heading.text.strip()
                if (section_text.startswith('Tâche ') or 
                    section_text.startswith('Correction') or
                    any(pattern in section_text.lower() for pattern in ['répondez', 'rédigez', 'vous avez assisté'])):
                    # This section contains a new task, stop here
                    return '\n\n'.join(all_content) if all_content else None
        
        # Check if this element is a heading title (h2, h3, or span with elementor-heading-title class)
        if ((current_element.name in ['h2', 'h3', 'span']) and 
            current_element.get('class') and 
            'elementor-heading-title' in current_element.get('class', [])):
            heading_text = current_element.text.strip()
            # Skip if it's a task/correction heading (should be caught by boundary)
            if not (heading_text.startswith('Tâche ') or 
                   heading_text.startswith('Correction Tâche ') or
                   heading_text.startswith('Combinaison ')):
                # Check if it's not footer content
                if not is_footer_content(heading_text):
                    if heading_text not in seen_content:
                        seen_content.add(heading_text)
                        all_content.append(heading_text)
                        content_count += 1
        
        # Check if this element is a text editor
        elif (current_element.name == 'div' and 
              current_element.get('class') and 
              'elementor-widget-text-editor' in current_element.get('class', [])):
            content = clean_text_content(current_element)
            if content and content.strip():
                content_cleaned = content.strip()
                # Check if it's not footer content or task content
                if not is_footer_content(content_cleaned) and not is_task_content(content_cleaned):
                    # For corrections, be extra strict about task-like content
                    if is_correction and contains_task_patterns(content_cleaned):
                        break  # Stop processing if we hit task-like content in corrections
                    
                    if content_cleaned not in seen_content:
                        seen_content.add(content_cleaned)
                        all_content.append(content_cleaned)
                        content_count += 1
        
        # Also check for content within this element (but process in order)
        elif current_element.name in ['div', 'section']:
            # Get all child elements in order
            child_elements = current_element.find_all(['h2', 'h3', 'span', 'div'], recursive=False)
            
            for child in child_elements:
                if content_count >= max_content_pieces:
                    break
                    
                # Check if it's a heading
                if (child.name in ['h2', 'h3', 'span'] and 
                    child.get('class') and 
                    'elementor-heading-title' in child.get('class', [])):
                    heading_text = child.text.strip()
                    if not (heading_text.startswith('Tâche ') or 
                           heading_text.startswith('Correction Tâche ') or
                           heading_text.startswith('Combinaison ')):
                        # Check if it's not footer content
                        if not is_footer_content(heading_text):
                            if heading_text not in seen_content:
                                seen_content.add(heading_text)
                                all_content.append(heading_text)
                                content_count += 1
                
                # Check if it's a text editor
                elif (child.name == 'div' and 
                      child.get('class') and 
                      'elementor-widget-text-editor' in child.get('class', [])):
                    content = clean_text_content(child)
                    if content and content.strip():
                        content_cleaned = content.strip()
                        # Check if it's not footer content or task content
                        if not is_footer_content(content_cleaned) and not is_task_content(content_cleaned):
                            # For corrections, be extra strict about task-like content
                            if is_correction and contains_task_patterns(content_cleaned):
                                break  # Stop processing if we hit task-like content
                            
                            if content_cleaned not in seen_content:
                                seen_content.add(content_cleaned)
                                all_content.append(content_cleaned)
                                content_count += 1
                
                # If it's a container, look for text editors within it
                elif child.name == 'div':
                    text_editors = child.find_all('div', class_='elementor-widget-text-editor')
                    for text_editor in text_editors:
                        if content_count >= max_content_pieces:
                            break
                        content = clean_text_content(text_editor)
                        if content and content.strip():
                            content_cleaned = content.strip()
                            # Check if it's not footer content or task content
                            if not is_footer_content(content_cleaned) and not is_task_content(content_cleaned):
                                # For corrections, be extra strict about task-like content
                                if is_correction and contains_task_patterns(content_cleaned):
                                    break  # Stop processing if we hit task-like content
                                
                                if content_cleaned not in seen_content:
                                    seen_content.add(content_cleaned)
                                    all_content.append(content_cleaned)
                                    content_count += 1
        
        current_element = current_element.find_next()
    
    # Post-process to fix content ordering issues - ONLY for Task 3
    if all_content:
        if is_task3:
            all_content = fix_task3_content_order(all_content)
        return '\n\n'.join(all_content)
    
    # Fallback to original simple logic if nothing found
    section = heading_element.find_next('section', class_='elementor-section')
    if section:
        text_editor = section.find('div', class_='elementor-widget-text-editor')
        if text_editor:
            return clean_text_content(text_editor)
    
    text_editor = heading_element.find_next('div', class_='elementor-widget-text-editor')
    if text_editor:
        return clean_text_content(text_editor)
    
    return None

def fix_task3_content_order(content_list):
    """Fix the content order for Task 3 to ensure proper structure: Topic → Document 1: → Content 1 → Document 2: → Content 2"""
    if len(content_list) < 3:
        return content_list
    
    # Debug: Print what we're working with
    # print(f"DEBUG - Original content list ({len(content_list)} items):")
    # for i, item in enumerate(content_list):
    #     print(f"  {i}: '{item[:80]}...' (length: {len(item)})")
    # print("---")
    
    # Look for the pattern: Content1, Content2, Topic, "Document 1:", "Document 2:"
    topic_title = None
    doc1_label = None
    doc2_label = None
    content_pieces = []
    
    for i, item in enumerate(content_list):
        item_stripped = item.strip()
        if item_stripped == "Document 1 :":
            doc1_label = i
            # print(f"DEBUG - Found Document 1 label at position {i}")
        elif item_stripped == "Document 2 :":
            doc2_label = i
            # print(f"DEBUG - Found Document 2 label at position {i}")
        # Check for topic title FIRST (before content detection)
        elif (len(item_stripped) > 10 and 
              len(item_stripped) < 200 and  # Not too long (likely not content)
              not item_stripped.startswith("Document ") and 
              not item_stripped.endswith(":") and
              i < len(content_list) - 2 and  # Not one of the last items
              # Additional criteria to identify topic titles vs content
              (item_stripped.endswith("?") or  # Questions are often topics
               ":" in item_stripped or         # Titles often have colons
               not item_stripped.startswith(("Jean", "Sara", "Bonjour", "Salut", "Cher", "Chère")) and  # Not dialogue/letters
               not any(word in item_stripped.lower() for word in ["je suis", "nous", "mon", "ma", "mes", "vous"]))):  # Not personal content
            # This might be a topic title
            if topic_title is None:  # Take the first one we find
                topic_title = (i, item)
                # print(f"DEBUG - Found potential topic title at position {i}: '{item_stripped}'")
        elif (not item_stripped.startswith("Document ") and 
              len(item_stripped) > 20 and 
              not item_stripped.endswith(":")):
            # This looks like content (long text, not a label)
            content_pieces.append((i, item))
            # print(f"DEBUG - Found content piece at position {i}: '{item_stripped[:50]}...'")
    
    # print(f"DEBUG - Analysis: topic_title={topic_title}, doc1_label={doc1_label}, doc2_label={doc2_label}, content_pieces={len(content_pieces)}")
    
    # Check if we have the problematic pattern: content before labels
    if (doc1_label is not None and doc2_label is not None and 
        len(content_pieces) >= 2):
        
        # Check if content appears before labels (problematic pattern)
        content_before_labels = (content_pieces[0][0] < doc1_label and 
                               content_pieces[1][0] < doc2_label)
        
        # Also check if labels appear without content after them (empty pattern)
        labels_without_content = True
        if doc1_label < len(content_list) - 1:
            next_after_doc1 = content_list[doc1_label + 1].strip()
            if len(next_after_doc1) > 20:  # Has substantial content after Doc 1
                labels_without_content = False
        
        # print(f"DEBUG - content_before_labels={content_before_labels}, labels_without_content={labels_without_content}")
        
        if content_before_labels or labels_without_content:
            # We have a problematic pattern, let's reorder
            reordered = []
            
            # Add topic title first if we found one
            if topic_title:
                reordered.append(topic_title[1])
                # print(f"DEBUG - Adding topic title: '{topic_title[1][:50]}...'")
            
            # Add Document 1 label and content
            reordered.append("Document 1 :")
            if len(content_pieces) >= 1:
                reordered.append(content_pieces[0][1])
                # print(f"DEBUG - Adding Document 1 content: '{content_pieces[0][1][:50]}...'")
            
            # Add Document 2 label and content  
            reordered.append("Document 2 :")
            if len(content_pieces) >= 2:
                reordered.append(content_pieces[1][1])
                # print(f"DEBUG - Adding Document 2 content: '{content_pieces[1][1][:50]}...'")
            
            # print(f"DEBUG - Returning reordered content with {len(reordered)} items")
            return reordered
    
    # If no problematic pattern detected, check if we should still add topic at beginning
    if (topic_title and doc1_label is not None and doc2_label is not None and
        topic_title[0] > doc1_label):  # Topic appears after Document labels
        
        reordered = []
        
        # Add topic title first
        reordered.append(topic_title[1])
        
        # Add the rest in order, excluding the topic from its original position
        for i, item in enumerate(content_list):
            if i != topic_title[0]:  # Skip the topic from its original position
                reordered.append(item)
        
        # print(f"DEBUG - Reordering to put topic first, returning {len(reordered)} items")
        return reordered
    
    # If no problematic pattern detected, return as-is
    # print("DEBUG - No reordering needed, returning original")
    return content_list

def clean_text_content(text_editor_element):
    """Clean and extract text content from text editor element, preserving spacing and indentation."""
    if not text_editor_element:
        return None
    
    # Find the actual content container
    content_div = text_editor_element.find('div', class_='elementor-text-editor')
    if not content_div:
        content_div = text_editor_element
    
    # Extract text while preserving structure
    formatted_text = extract_formatted_text(content_div)
    
    if formatted_text:
        # Remove redundant footer content
        formatted_text = remove_footer_content(formatted_text)
    
    return formatted_text.strip() if formatted_text else None

def remove_footer_content(text):
    """Remove redundant footer and navigation content from scraped text."""
    if not text:
        return text
    
    # List of footer/navigation patterns to remove
    footer_patterns = [
        "Réussir TCF Canada LTD. © 2025",
        "Tous les droits sont réservés",
        "Les pages",
        "Nos Contacts",
        "les méthodologies", 
        "À propos de nous",
        "Nous acceptons",
        "Les Pages",
        "Plateforme spécialisée dans la préparation au TCF Canada",
        "Pour partager les sujets de votre session:"
    ]
    
    # Split text into lines for processing
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line_stripped = line.strip()
        
        # Skip empty lines
        if not line_stripped:
            continue
            
        # Check if line contains footer content
        is_footer = False
        for pattern in footer_patterns:
            if pattern in line_stripped:
                is_footer = True
                break
        
        # If not footer content, keep the line
        if not is_footer:
            cleaned_lines.append(line)
    
    # Rejoin the cleaned lines
    cleaned_text = '\n'.join(cleaned_lines)
    
    # Additional cleanup: remove trailing footer sections
    # Look for patterns that indicate start of footer section
    footer_start_patterns = [
        "Pour partager les sujets",
        "Plateforme spécialisée dans la préparation",
        "Réussir TCF Canada"
    ]
    
    for pattern in footer_start_patterns:
        if pattern in cleaned_text:
            # Split at the pattern and keep only the part before it
            parts = cleaned_text.split(pattern)
            if len(parts) > 1:
                cleaned_text = parts[0].rstrip()
    
    return cleaned_text

def extract_formatted_text(element):
    """Extract text from HTML element while preserving formatting structure."""
    if not element:
        return ""
    
    text_parts = []
    
    for child in element.children:
        if child.name is None:  # Text node
            text = str(child).strip()
            if text:
                text_parts.append(text)
        elif child.name == 'p':
            # Handle paragraphs with proper spacing
            p_text = extract_formatted_text(child)
            if p_text.strip():
                text_parts.append(p_text)
                text_parts.append("\n")  # Add line break after paragraph
        elif child.name == 'strong':
            # Extract bold text content without tags
            strong_text = extract_formatted_text(child)
            if strong_text.strip():
                text_parts.append(strong_text)
        elif child.name == 'span':
            # Handle spans (like colored text) - extract content only
            span_text = extract_formatted_text(child)
            if span_text.strip():
                text_parts.append(span_text)
        elif child.name == 'br':
            # Handle line breaks
            text_parts.append("\n")
        elif child.name in ['div', 'section']:
            # Handle containers recursively
            div_text = extract_formatted_text(child)
            if div_text.strip():
                text_parts.append(div_text)
        else:
            # Handle other elements recursively
            other_text = extract_formatted_text(child)
            if other_text.strip():
                text_parts.append(other_text)
    
    # Join parts and clean up spacing
    result = "".join(text_parts)
    
    # Clean up multiple consecutive newlines but preserve intentional spacing
    import re
    result = re.sub(r'\n\s*\n\s*\n+', '\n\n', result)  # Max 2 consecutive newlines
    result = re.sub(r'[ \t]+', ' ', result)  # Normalize spaces but keep single spaces
    
    return result

def preview_scraped_content(output_dir, max_files=2):
    """Preview the scraped content to show formatting preservation."""
    print("\n=== PREVIEW OF SCRAPED CONTENT ===")
    
    json_files = list(output_dir.glob('*.json'))
    preview_files = json_files[:max_files]
    
    for json_file in preview_files:
        if json_file.name == 'all_expression_ecrite.json':
            continue
            
        print(f"\n📄 File: {json_file.name}")
        
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for combination in data.get('combinations', [])[:1]:  # Show first combination only
            print(f"\n🔢 Combination {combination['combination_number']}")
            
            for task in combination.get('tasks', [])[:1]:  # Show first task only
                print(f"\n📝 Task {task['task_number']}")
                
                # Show task content
                if 'task_content' in task and task['task_content']:
                    content = task['task_content']
                    if isinstance(content, str):
                        print("✅ FORMATTED CONTENT (with spacing preserved):")
                        preview_text = content[:300] + "..." if len(content) > 300 else content
                        print(f"'{preview_text}'")
                    else:
                        print("⚠️  OLD FORMAT:")
                        print(str(content)[:200] + "..." if len(str(content)) > 200 else str(content))
                
                # Show correction content if available
                if 'correction_content' in task and task['correction_content']:
                    print(f"\n🔧 Correction for Task {task['task_number']}")
                    content = task['correction_content']
                    if isinstance(content, str):
                        print("✅ FORMATTED CORRECTION (with spacing preserved):")
                        preview_text = content[:300] + "..." if len(content) > 300 else content
                        print(f"'{preview_text}'")
                    else:
                        print("⚠️  OLD FORMAT:")
                        print(str(content)[:200] + "..." if len(str(content)) > 200 else str(content))
                
                break  # Only show first task
            break  # Only show first combination
        break  # Only show first file

def scrape_expression_ecrite():
    """Main function to scrape expression écrite content."""
    # Get the workspace root directory
    workspace_root = Path(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    input_dir = workspace_root / 'data' / 'raw' / 'Expression-écrite'
    output_dir = workspace_root / 'data' / 'scraped' / 'scraped_writing'
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    
    all_data = []
    
    # Process each HTML file
    for html_file in input_dir.glob('*.html'):
        print(f"Processing {html_file.name}...")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
            
        # Extract month and year from filename
        filename = html_file.stem
        month_year = filename.split('-correction')[0]
        
        # Extract tasks and corrections
        combinations = extract_tasks_and_corrections(html_content)
        
        file_data = {
            'month_year': month_year,
            'combinations': combinations
        }
        
        all_data.append(file_data)
        
        # Save individual file data
        output_file = output_dir / f'{month_year}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(file_data, f, ensure_ascii=False, indent=2)
            
        print(f"Found {len(combinations)} combinations with tasks")
    
    # Save combined data
    combined_output = output_dir / 'all_expression_ecrite.json'
    with open(combined_output, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=2)
    
    print(f"Scraping complete! Processed {len(all_data)} files.")
    print(f"Output saved to: {output_dir}")
    
    # Preview the results
    preview_scraped_content(output_dir)

if __name__ == '__main__':
    scrape_expression_ecrite() 