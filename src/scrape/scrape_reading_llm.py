#!/usr/bin/env python3
import os
import re
import sys
import json
import time
import base64
import requests
from pathlib import Path
from bs4 import BeautifulSoup, Tag
from PIL import Image
from openai import OpenAI
from api import myapi  # Your secure API key

# ─────────────────────── OpenAI Setup ───────────────────────
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", myapi)
client = OpenAI(api_key=OPENAI_API_KEY)

def clean(txt: str) -> str:
    return re.sub(r"\s+", " ", txt).strip()

def clean_question_text(q: str) -> str:
    return re.sub(r"^```|```$", "", q.strip())


from PIL import ImageEnhance, Image

def prepare_image_for_llm(img_path: Path) -> str:
    """
    Open the image, flatten it onto a white background, optionally brighten it,
    and return a base64 data URL.
    """
    img = Image.open(img_path).convert("RGBA")

    bg = Image.new("RGBA", img.size, (255, 255, 255, 255))
    flat = Image.alpha_composite(bg, img)

    flat = flat.convert("RGB")
    enhancer = ImageEnhance.Brightness(flat)
    flat = enhancer.enhance(1.15)

    enhancer = ImageEnhance.Contrast(flat)
    flat = enhancer.enhance(1.3)

    from io import BytesIO
    import base64
    buf = BytesIO()
    flat.save(buf, format="JPEG")
    b64 = base64.b64encode(buf.getvalue()).decode("utf-8")
    return f"data:image/jpeg;base64,{b64}"


def do_llm_ocr(img_path: Path) -> tuple[str, str, str]:
    """Use GPT-4o Vision to extract formatted text and bottom question from image."""
    try:
        image_url = prepare_image_for_llm(img_path)

        print(f"GPT-4 Vision on: {img_path.name}")
        resp = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": (
                        "You are an expert at reading French text from images. "
                        "Preserve the original layout and structure (including line breaks, bullet points, etc.)."
                        "Ignore the watermark such as 'www.reussir-tcfcanada.com', 'CANADA' and any 'TCF' related."
                    )
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": (
                                "This image shows a French message or menu or letter with a question at the bottom.\n\n"
                                "1. Extract all the visible French text and preserve its formatting using line breaks.\n"
                                "2. Then extract only the final question at the bottom.\n\n"
                                "Format your response as:\n"
                                "- Text:\n```\n<structured text>\n```\n"
                                "- Question: <bottom question only>"
                            )
                        },
                        {
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        }
                    ]
                }
            ]
        )

        content = resp.choices[0].message.content.strip()
        if "Text:" in content and "Question:" in content:
            text_match = re.search(r"Text:\s*```(.*?)```", content, re.S)
            question_match = re.search(r"Question:\s*(.*)", content, re.S)
            body = text_match.group(1).strip() if text_match else ""
            question = clean(question_match.group(1)) if question_match else ""
        else:
            body, question = "", content.strip()
        return content, body, question
    except Exception as e:
        print("LLM OCR failed:", e)
        return "", "", ""

def parse_one(li: Tag, test_id: str, img_dir: Path) -> dict:
    qnum = li.find("div", class_="lqc-question-list-1").span.text.strip()
    a_tag = li.select_one("div.wpProQuiz_question_text a")
    img_url = a_tag["href"] if a_tag else None
    img_path = None
    ocr_raw = ""
    body = ""
    question = ""

    if img_url:
        img_path = img_dir / f"Q{qnum}_test{test_id}{Path(img_url).suffix or '.jpg'}"
        if not img_path.exists():
            r = requests.get(img_url, timeout=30)
            r.raise_for_status()
            img_path.write_bytes(r.content)
        ocr_raw, body, question = do_llm_ocr(img_path)
        time.sleep(0.5)

    choices = {}
    for itm in li.select("ul.wpProQuiz_questionList > li.wpProQuiz_questionListItem"):
        label = itm.find("label")
        spans = label.find_all("span") if label else []
        if len(spans) < 1:
            continue
        letter = spans[0].get_text(strip=True)
        txt_node = spans[1].next_sibling.strip() if len(spans) > 1 and isinstance(spans[1].next_sibling, str) else ""
        if not txt_node:
            full = clean(label.get_text(" "))
            txt_node = re.sub(rf"^{letter}\s*\d*\s*", "", full)
        choices[letter] = txt_node

    return {
        "question_number": qnum,
        "image_url": img_url,
        "image_path": str(img_path) if img_path else None,
        "ocr_text": ocr_raw,
        "text": body,
        "question_text": clean_question_text(question),
        "choices": choices,
    }

def main() -> None:
    if len(sys.argv) < 2:
        print("Usage: python scrape_llm_structured.py <URL-or-HTML-file>")
        sys.exit(1)

    src = sys.argv[1]
    is_url = bool(re.match(r"https?://", src, flags=re.I))
    html = requests.get(src, timeout=30).text if is_url else Path(src).read_text(encoding="utf-8")
    src_id = src if is_url else Path(src).stem
    m = re.search(r"test[-–—](\d+)", src_id, flags=re.I)
    if not m:
        sys.exit("Couldn’t find “test-<number>” in source.")
    test_id = m.group(1)

    out_dir = Path("scraped-llm-structured"); out_dir.mkdir(exist_ok=True)
    img_dir = Path(f"downloaded_images_{test_id}"); img_dir.mkdir(exist_ok=True)

    soup = BeautifulSoup(html, "html.parser")
    items = soup.select("li.wpProQuiz_listItem")
    if not items:
        sys.exit("No quiz items found – page layout may have changed.")

    data = [parse_one(li, test_id, img_dir) for li in items]
    out = out_dir / f"tcf_test{test_id}.json"
    out.write_text(json.dumps(data, ensure_ascii=False, indent=2), encoding="utf-8")

    print(f"Saved {len(data)} questions → {out.resolve()}")
    print("Images stored in:", img_dir.resolve())

if __name__ == "__main__":
    main()
