#!/usr/bin/env python3
"""
Collect the *correct‑answer letters* from **every** TCF‑Canada quiz HTML
file in a folder and dump them into **one combined JSON** structure:

```json
{
  "test1": { "1": "B", "2": "D", … },
  "test2": { "1": "C", "2": "A", … },
  …
}
```

Usage
-----
```bash
python extract_all_correct.py ./html_folder/          # walks every *.html
python extract_all_correct.py one_page.html           # single file still OK
```

*The script auto‑detects the test ID (`test-<n>`) from each filename.*
"""
from __future__ import annotations

import argparse, json, re, sys, pathlib
from bs4 import BeautifulSoup, Tag
from typing import Dict, Tuple

TEST_RE = re.compile(r"comprehension[-_]?ecrite[-_]?test[-–—]?(\d+)", re.I)

# ── core helpers ─────────────────────────────────────────────────────────────

def extract_one(html_path: pathlib.Path) -> Tuple[str, Dict[str, str]]:
    """Return (test_id, {question_number: correct_letter}) from one file."""
    html = html_path.read_text(encoding="utf-8", errors="ignore")
    soup = BeautifulSoup(html, "html.parser")

    # find test ID from filename or <title>
    m = TEST_RE.search(html_path.name) or (soup.title and TEST_RE.search(soup.title.string or ""))
    if not m:
        raise ValueError("test id not found")
    test_id = f"test{m.group(1)}"

    answers: Dict[str, str] = {}
    for li in soup.select("li.wpProQuiz_questionListItem[class*='answerCorrect']"):
        label = li.find("label"); span = label and label.find("span")
        letter = span.get_text(strip=True) if span else None
        qblock: Tag | None = li.find_parent("li", class_="wpProQuiz_listItem")
        numdiv  = qblock and qblock.find("div", class_="lqc-question-list-1")
        qnum    = numdiv and numdiv.span and numdiv.span.get_text(strip=True)
        if qnum and letter:
            answers[qnum] = letter
    return test_id, answers

# ── main ─────────────────────────────────────────────────────────────────────

def main() -> None:
    p = argparse.ArgumentParser(description="Extract all correct answers into one JSON file.")
    p.add_argument("path", help="HTML file or folder containing *.html files")
    p.add_argument("-o", "--out", default="correct_answers_all.json", help="output JSON file")
    args = p.parse_args()

    root = pathlib.Path(args.path).expanduser().resolve()
    html_files = [root] if root.is_file() else sorted(root.glob("*.html"))
    if not html_files:
        sys.exit("No HTML files found.")

    all_answers: Dict[str, Dict[str, str]] = {}

    for html_path in html_files:
        try:
            test_id, answers = extract_one(html_path)
        except Exception as e:
            print(f"{html_path.name} skipped ({e})")
            continue
        all_answers[test_id] = answers
        print(f"✔{html_path.name}: {len(answers)} answers")

    out_path = pathlib.Path(args.out)
    out_path.write_text(json.dumps(all_answers, ensure_ascii=False, indent=2), encoding="utf-8")
    print(f"\n Saved {len(all_answers)} tests → {out_path.resolve()}")

if __name__ == "__main__":
    main()
