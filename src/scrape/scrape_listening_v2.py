#!/usr/bin/env python3
from __future__ import annotations

import argparse
import json
import re
import time
import requests
import subprocess
import sys
from pathlib import Path
from bs4 import BeautifulSoup
from urllib.parse import urljoin

# ──────────────────────────────────────────────────────────────────────────────
# Constants & Defaults
# ──────────────────────────────────────────────────────────────────────────────
DEFAULT_OUTPUT_DIR = Path("scraped_listening")
DEFAULT_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

DEFAULT_BASE_URL = "https://examens.preptcfcanada.com/"

# ──────────────────────────────────────────────────────────────────────────────
# Helpers
# ──────────────────────────────────────────────────────────────────────────────
def download_file(url: str, dest: Path):
    """Download a file if it doesn't already exist."""
    if not dest.exists():
        resp = requests.get(url, timeout=30)
        resp.raise_for_status()
        dest.write_bytes(resp.content)

def clean_choice(txt: str) -> str:
    """Strip leading numbering/‘A 88’ etc. from a choice label."""
    return re.sub(r'^(?:\d+\.\s*)?[A-D]\s*88\s*', '', txt).strip()

def parse_question(li, base_url: str) -> dict:
    # Question number
    data_id = li.get("data-question_id")
    if data_id:
        qnum = data_id
    else:
        raw = ""
        page = li.find("div", class_="wpProQuiz_question_page")
        if page:
            raw = page.text.strip()
        match = re.search(r"\d+", raw)
        qnum = match.group(0) if match else "?"

    # Audio
    audio_tag = li.find("audio")
    audio_path = None
    if audio_tag and audio_tag.get("src"):
        audio_url = urljoin(base_url, audio_tag["src"])
        audio_path = audio_tag._save_dir / f"Q{qnum}.mp3"  # see below
        download_file(audio_url, audio_path)

    # Image
    img_tag = li.find("img")
    image_path = None
    if img_tag and img_tag.get("src"):
        img_url = urljoin(base_url, img_tag["src"])
        ext = Path(img_url).suffix or ".webp"
        image_path = img_tag._save_dir / f"Q{qnum}{ext}"
        download_file(img_url, image_path)

    # Choices
    choices = {}
    ul = li.find("ul", class_="wpProQuiz_questionList")
    if ul:
        for i, choice_li in enumerate(ul.find_all("li"), start=1):
            label = choice_li.find("label")
            if label:
                text = clean_choice(label.get_text(" ", strip=True))
                letter = chr(64 + i)  # A, B, C, D...
                choices[letter] = text

    return {
        "question_number": qnum,
        "audio_path": str(audio_path) if audio_path else None,
        "image_path": str(image_path) if image_path else None,
        "choices": choices or None,
    }

def scrape_oral_quiz(html_file: str, base_url: str):
    """Parse the HTML file, download all audio into quiz-specific folder, and write JSON."""
    m = re.search(r"test[-_]?(\d+)", html_file, re.I)
    quiz_id = m.group(1) if m else Path(html_file).stem
    save_dir = Path("listening_asset") / f"media_test{quiz_id}"
    save_dir.mkdir(parents=True, exist_ok=True)

    html = Path(html_file).read_text(encoding="utf-8")
    soup = BeautifulSoup(html, "html.parser")

    # 1. Collect all audio URLs and download them
    audio_tags = soup.find_all("audio")
    audio_paths = []
    for idx, audio in enumerate(audio_tags, start=1):
        source = audio.find("source")
        if source and source.get("src"):
            audio_url = urljoin(base_url, source["src"].split("?")[0])
            audio_name = f"Q{idx}.mp3"
            audio_path = save_dir / audio_name
            print(f"Downloading {audio_url} → {audio_path}")
            download_file(audio_url, audio_path)
            audio_paths.append(str(audio_path))
        else:
            audio_paths.append(None)

    # 2. Parse questions and assign audio_path by order
    items = soup.select("li.wpProQuiz_listItem")
    for li in items:
        for tag in (li.find_all("audio") + li.find_all("img")):
            tag._save_dir = save_dir  # type: ignore

    results = []
    for idx, li in enumerate(items):
        q = parse_question(li, base_url)
        # Assign audio_path by order if not found in parse_question
        if (not q.get("audio_path") or q["audio_path"] == "None") and idx < len(audio_paths):
            q["audio_path"] = audio_paths[idx]
        results.append(q)

    out_path = DEFAULT_OUTPUT_DIR / f"tcf_test{quiz_id}.json"
    out_path.write_text(
        json.dumps(results, ensure_ascii=False, indent=2),
        encoding="utf-8"
    )

    print(f"Saved {len(results)} questions → {out_path}")
    print(f"Downloaded {len(audio_tags)} audio files into → {save_dir}")

# ──────────────────────────────────────────────────────────────────────────────
# Entrypoint
# ──────────────────────────────────────────────────────────────────────────────
def main():
    parser = argparse.ArgumentParser(
        description="Scrape a local TCF oral-quiz HTML file into JSON and media folder."
    )
    parser.add_argument(
        "html_file",
        help="Path to the downloaded HTML file (e.g. comprehension-oral-test-1.html)"
    )
    parser.add_argument(
        "base_url",
        nargs="?",
        default=DEFAULT_BASE_URL,
        help=f"OPTIONAL: base URL to resolve relative media paths (default: {DEFAULT_BASE_URL!r})"
    )
    args = parser.parse_args()

    html_path = Path(args.html_file)
    if not html_path.exists():
        parser.error(f"File not found: {html_path}")

    scrape_oral_quiz(str(html_path), args.base_url)

if __name__ == "__main__":
    main()