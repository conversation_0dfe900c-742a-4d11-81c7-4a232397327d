# pip install pillow
from PIL import Image, ImageDraw, ImageFont
import os

def get_font_size(draw, image_width, image_height, text):
    """Dynamically calculate font size to span diagonal"""
    diag_length = int((image_width**2 + image_height**2) ** 0.5)
    font_size = diag_length // len(text) * 3  # heuristic adjustment

    # Binary search for maximum font size that fits
    low, high = 1, font_size * 3
    best_size = 1
    while low <= high:
        mid = (low + high) // 2
        try:
            test_font = ImageFont.truetype("arial.ttf", mid)  # Use custom font if available
        except:
            test_font = ImageFont.load_default(mid)
        bbox = draw.textbbox((0, 0), text, font=test_font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        if text_width < image_width and text_height < image_height:
            best_size = mid
            low = mid + 1
        else:
            high = mid - 1
    return best_size

def add_watermark(image_path, output_path, watermark_text, text_opacity):
    base_image = Image.open(image_path).convert("RGBA")
    width, height = base_image.size

    txt_layer = Image.new("RGBA", base_image.size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(txt_layer)

    # Dynamically determine font size
    font_size = get_font_size(draw, width, height, watermark_text)

    # Load font (use system font if available, else default)
    try:
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        font = ImageFont.load_default(font_size)

    # Set fill color with opacity (RGBA)
    fill_color = (255, 255, 255, text_opacity)

    # Position text in center
    bbox = draw.textbbox((0, 0), watermark_text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    position = ((width - text_width) // 2, (height - text_height) // 2)

    # Draw text at center
    draw.text(position, watermark_text, font=font, fill=fill_color)

    # Rotate watermark layer by 45 degrees
    txt_layer = txt_layer.rotate(45, expand=False, resample=Image.BICUBIC)

    # Combine layers
    watermarked = Image.alpha_composite(base_image, txt_layer)

    # Convert back to RGB to save as JPEG or other format
    watermarked = watermarked.convert("RGB")

    # Save result
    watermarked.save(output_path)

def add_watermark_folder(input_folder, output_folder, watermark_text, text_opacity):
    # Process all images in input folder
    for filename in os.listdir(input_folder):
        if filename.lower().endswith((".png", ".jpg", ".jpeg", ".bmp", ".gif", ".webp")):
            image_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename)
            print(f"Adding watermark to {filename}")
            add_watermark(image_path, output_path, watermark_text, text_opacity)

    print("Watermarking complete.")


if __name__ == "__main__":
    # Configuration
    input_folder = "/Users/<USER>/Downloads/photo"
    output_folder = "/Users/<USER>/Downloads/photo_watermarked"
    watermark_text = "tcf-canada.site"
    text_opacity = 50  # maximum is 255

    os.makedirs(output_folder, exist_ok=True)
    add_watermark_folder(input_folder, output_folder, watermark_text, text_opacity)