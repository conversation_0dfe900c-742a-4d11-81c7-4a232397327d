import os
import json


class DataIterator:
    """
    An iterator that reads JSON files from one folder,
    and returns each item along with its corresponding answer
    from a provided answer file.

    Each JSON file is expected to be named like 'test1.json', 'test2.json', etc.,
    and contain a list of items (e.g., questions). The answer file should map
    test file names to question-answer pairs.

    Yields:
        tuple: (item, answer, source_file)
    """

    def __init__(self, answer_file_path, question_folder_path):
        self.all_files = []

        # Load answer data
        if not os.path.isfile(answer_file_path):
            raise FileNotFoundError(f"Answer file not found: {answer_file_path}")
        with open(answer_file_path, "r") as af:
            self.answers = json.load(af)

        # Collect all JSON files
        if not os.path.isdir(question_folder_path):
            raise FileNotFoundError(f"Folder not found: {question_folder_path}")
        for filename in sorted(os.listdir(question_folder_path)):
            if filename.startswith("test") and filename.endswith(".json"):
                full_path = os.path.abspath(os.path.join(question_folder_path, filename))
                self.all_files.append(full_path)

        self.file_index = 0
        self.item_index = 0
        self.current_data = []
        self.current_file = None
        self.current_answers = []

    def get_last_k_dirs(self, file_path, k):
        dir_path = os.path.dirname(file_path)
        base_name = os.path.basename(file_path)
        parts = []
        while len(parts) < k:
            dir_path, tail = os.path.split(dir_path)
            if not tail:
                break
            parts.insert(0, tail)
        return os.path.join(*parts, base_name)

    def __iter__(self):
        return self

    def __next__(self):
        while self.file_index < len(self.all_files):
            if not self.current_data:
                current_file_path = self.all_files[self.file_index]
                file_name = os.path.splitext(os.path.basename(current_file_path))[0]  # e.g., 'test1'

                # Load JSON content
                with open(current_file_path, "r") as f:
                    self.current_data = json.load(f)
                    self.item_index = 0

                # Get answers for this test file
                if file_name not in self.answers:
                    raise KeyError(f"No answers found for '{file_name}' in answer file.")
                self.current_answers = [self.answers[file_name].get(str(i + 1)) for i in range(len(self.current_data))]

                # Store truncated file path
                self.current_file = self.get_last_k_dirs(current_file_path, 1)

            if self.item_index < len(self.current_data):
                item = self.current_data[self.item_index]
                answer = self.current_answers[self.item_index]
                self.item_index += 1
                return item, answer, self.current_file
            else:
                # Move to next file
                self.file_index += 1
                self.current_data = []
                self.current_answers = []
                self.current_file = None

        raise StopIteration