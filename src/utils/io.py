import json
import os

def append_item_to_json(file_path, new_item, verbose=True):
    """
    Appends a new item to a JSON file containing a list.

    Args:
        file_path (str): Path to the JSON file.
        new_item (dict or list): The item to append.
    """
    # Step 1: Check if file exists and load data
    if not os.path.isfile(file_path):
        # If file doesn't exist, create one with an empty list
        with open(file_path, 'w') as f:
            json.dump([], f)

    with open(file_path, 'r') as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError:
            # Handle corrupted or empty file
            data = []

    # Step 2: Ensure data is a list
    if not isinstance(data, list):
        raise ValueError("JSON file does not contain a list.")

    # Step 3: Append new item
    data.append(new_item)

    # Step 4: Write updated data back to file
    # with open(file_path, 'w') as f:
    #     json.dump(data, f, indent=4)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    if verbose:
        print(f"Successfully appended item to {file_path}")