from data_iterator import DataIterator

reading_iterator = DataIterator("../../data/scraped/scraped_answer/reading_correct_answer_free.json",
                                "../../data/scraped/scraped_reading_cleaned_free")
listening_iterator = DataIterator("../../data/scraped/scraped_answer/listening_correct_answer_free.json",
                                  "../../data/scraped/scraped_listening_cleaned_free")

count = 0
for item, answer, source_file in listening_iterator:
    question_number = item["question_number"]
    print(f"Item: {question_number} | Source File: {source_file} | Answer: {answer} | Image: {item['image_path'] is not None}")
    count += 1

print(f"{count} items processed")