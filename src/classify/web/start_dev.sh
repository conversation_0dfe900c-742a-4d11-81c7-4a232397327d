#!/bin/bash

# TCF Classification Development Server Starter
# Starts both Flask backend (port 5001) and React frontend (port 3000)

echo "🚀 Starting TCF Classification Development Environment"
echo "=" * 60

# Check if we're in the web directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the src/classify/web directory"
    exit 1
fi

# Function to check if port is in use
check_port() {
    lsof -i :$1 > /dev/null 2>&1
    return $?
}

# Function to stop existing servers
stop_servers() {
    echo "🛑 Stopping existing servers..."
    pkill -f "python3 app.py" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    sleep 2
}

# Function to start Flask backend
start_flask() {
    echo "🐍 Starting Flask backend on port 5001..."
    python3 app.py &
    FLASK_PID=$!
    
    # Wait for Flask to start
    echo "⏳ Waiting for Flask to start..."
    for i in {1..10}; do
        if curl -s http://localhost:5001/api/health > /dev/null 2>&1; then
            echo "✅ Flask backend is ready!"
            break
        fi
        sleep 1
    done
    
    if ! curl -s http://localhost:5001/api/health > /dev/null 2>&1; then
        echo "❌ Flask backend failed to start"
        return 1
    fi
}

# Function to start React frontend
start_react() {
    echo "⚛️  Starting React frontend on port 3000..."
    npm start &
    REACT_PID=$!
    echo "✅ React frontend starting..."
    echo "🌐 Frontend: http://localhost:3000"
    echo "🔧 Backend API: http://localhost:5001"
}

# Cleanup function
cleanup() {
    echo -e "\n🛑 Shutting down servers..."
    kill $FLASK_PID 2>/dev/null || true
    kill $REACT_PID 2>/dev/null || true
    pkill -f "python3 app.py" 2>/dev/null || true
    pkill -f "react-scripts start" 2>/dev/null || true
    echo "👋 Goodbye!"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Main execution
echo "🔍 Checking for existing servers..."
if check_port 5001; then
    echo "⚠️  Port 5001 is busy, stopping existing Flask server..."
    stop_servers
fi

if check_port 3000; then
    echo "⚠️  Port 3000 is busy, stopping existing React server..."
    stop_servers
fi

# Start servers
start_flask
if [ $? -eq 0 ]; then
    start_react
    
    echo -e "\n🎉 Development environment ready!"
    echo "📋 Available endpoints:"
    echo "   • Health Check: http://localhost:5001/api/health"
    echo "   • Main Interface: http://localhost:3000"
    echo -e "\n⌨️  Press Ctrl+C to stop both servers"
    
    # Keep script running
    wait
else
    echo "❌ Failed to start Flask backend"
    exit 1
fi 