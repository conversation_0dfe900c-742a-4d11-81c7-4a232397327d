#!/usr/bin/env python3
"""
API Backend for TCF Classification React Interface

This Flask application provides a REST API backend for the React-based
TCF Classification System with drag-and-drop functionality.

Features:
- RESTful API endpoints for classification management
- Task movement and search capabilities
- Topic/subtopic creation and renaming
- Checkpoint management system
- Demo classification testing
- Serves React frontend interface
"""

from flask import Flask, request, jsonify, send_from_directory
import json
import os
from pathlib import Path
from datetime import datetime
import uuid
import sys

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from human_loop.classifier import HumanInTheLoopClassifier

app = Flask(__name__, static_folder='build', static_url_path='')
app.secret_key = 'tcf_classification_secret_key_2024'

# Global classifier instance
classifier = HumanInTheLoopClassifier()

# CORS support for React development
@app.after_request
def after_request(response):
    """Add CORS headers to all responses."""
    response.headers.add('Access-Control-Allow-Origin', 'http://localhost:3000')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS')
    return response

# Buffer state storage (in production, this would be in a database)
task_buffers = {}  # Format: {tache_number: [task_objects]}

# =============== FRONTEND ROUTES ===============

@app.route('/')
def serve_react_app():
    """Serve the React frontend application."""
    # For development, return a simple interface
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>TCF Classification System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
            .status { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .endpoint { background: #f8f9fa; border-left: 4px solid #007bff; padding: 15px; margin: 10px 0; }
            .method { font-weight: bold; color: #007bff; }
            .url { font-family: monospace; background: #e9ecef; padding: 2px 6px; border-radius: 3px; }
            .note { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .success { color: #28a745; font-weight: bold; }
            .links { display: flex; gap: 15px; flex-wrap: wrap; }
            .links a { background: #007bff; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; font-weight: bold; }
            .links a:hover { background: #0056b3; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎯 TCF Classification System</h1>
            
            <div class="status">
                <h3>✅ System Status: Online</h3>
                <p>The TCF Classification API backend is running successfully!</p>
            </div>
            
            <div class="note">
                <h3>📋 Human-in-the-Loop Classification System</h3>
                <p>This system allows you to:</p>
                <ul>
                    <li><strong>Load automatic classifications</strong> generated by rule-based systems</li>
                    <li><strong>Review and modify</strong> task categorizations using drag-and-drop interface</li>
                    <li><strong>Create new topics</strong> and rename existing ones</li>
                    <li><strong>Save checkpoints</strong> of approved classifications</li>
                    <li><strong>Search tasks</strong> by content and move them between categories</li>
                </ul>
            </div>
            
            <h3>🚀 Quick Actions</h3>
            <div class="links">
                <a href="/api/health">API Health Check</a>
                <a href="http://localhost:3000" target="_blank">React Interface (if running)</a>
            </div>
            
            <h3>📡 Available API Endpoints</h3>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/load_classification</div>
                <p>Load automatic classification for a specific tâche (1, 2, or 3)</p>
            </div>
            
            <div class="endpoint">
                <div class="method">GET</div>
                <div class="url">/list_checkpoints</div>
                <p>List all saved classification checkpoints</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/move_task</div>
                <p>Move a task between subtopics</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/create_topic</div>
                <p>Create new main topic or subtopic</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/rename_topic</div>
                <p>Rename existing topics or subtopics</p>
            </div>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/save_checkpoint</div>
                <p>Save current classification as a checkpoint</p>
            </div>
            
            <div class="note">
                <h4>🔄 Current Classification Status:</h4>
                <p><span class="success">✅ Tâche 1:</span> Rule-based classification ready (5 main topics, 22 subtopics)</p>
                <p><span class="success">✅ Tâche 2:</span> Placeholder classification available</p>
                <p><span class="success">✅ Tâche 3:</span> Placeholder classification available</p>
            </div>
            
            <h3>🛠️ Development Setup</h3>
            <p>To run the full React interface:</p>
            <ol>
                <li>Navigate to <code>src/classify/web/</code></li>
                <li>Run <code>npm install</code> (if not done already)</li>
                <li>Run <code>npm start</code></li>
                <li>Access the React interface at <a href="http://localhost:3000">http://localhost:3000</a></li>
            </ol>
        </div>
    </body>
    </html>
    '''

@app.route('/build/<path:filename>')
def serve_static_files(filename):
    """Serve static files from the React build directory."""
    return send_from_directory(app.static_folder, filename)

# =============== API ROUTES ===============

@app.route('/api/health')
def health_check():
    """API health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'service': 'TCF Classification API',
        'version': '2.0.0',
        'frontend': 'React interface available at http://localhost:3000',
        'endpoints': [
            '/load_classification',
            '/load_checkpoint', 
            '/save_checkpoint',
            '/move_task',
            '/move_to_buffer',
            '/move_from_buffer',
            '/buffer',
            '/search_tasks',
            '/create_topic',
            '/delete_topic',
            '/rename_topic',
            '/refresh_counts',
            '/cleanup_empty_topics',
            '/demo_classification'
        ]
    })

@app.route('/load_classification', methods=['POST'])
def load_classification():
    """Load automatic classification for a specific tâche."""
    data = request.get_json()
    tache_number = data.get('tache_number')
    
    if not tache_number or tache_number not in [1, 2, 3]:
        return jsonify({'success': False, 'error': 'Invalid tâche number'})
    
    success = classifier.load_automatic_classification(tache_number)
    
    if success:
        # Ensure all task counts are properly updated after loading
        update_all_task_counts()
        return jsonify({
            'success': True,
            'classification': classifier.current_classification,
            'tache_number': tache_number
        })
    else:
        return jsonify({'success': False, 'error': 'Failed to load classification'})

@app.route('/load_checkpoint', methods=['POST'])
def load_checkpoint():
    """Load an existing checkpoint."""
    data = request.get_json()
    checkpoint_path = data.get('checkpoint_path')
    
    if not checkpoint_path:
        return jsonify({'success': False, 'error': 'No checkpoint path provided'})
    
    checkpoint_file = Path(checkpoint_path)
    success = classifier.load_checkpoint(checkpoint_file)
    
    if success:
        # Convert unique_tasks format to task_entries format if needed
        convert_unique_tasks_format(classifier.current_classification)
        
        # Ensure all task counts are properly updated
        update_all_task_counts()
        
        return jsonify({
            'success': True,
            'classification': classifier.current_classification,
            'tache_number': classifier.tache_number
        })
    else:
        return jsonify({'success': False, 'error': 'Failed to load checkpoint'})

@app.route('/list_checkpoints', methods=['GET'])
def list_checkpoints():
    """List all available checkpoints."""
    tache_number = request.args.get('tache_number', type=int)
    checkpoints = classifier.list_checkpoints(tache_number)
    
    # Convert Path objects to strings for JSON serialization
    checkpoint_list = []
    for checkpoint in checkpoints:
        checkpoint_data = checkpoint.copy()
        checkpoint_data['path'] = str(checkpoint['path'])
        checkpoint_list.append(checkpoint_data)
    
    return jsonify({'checkpoints': checkpoint_list})

@app.route('/rename_topic', methods=['POST'])
def rename_topic():
    """Rename a main topic or subtopic."""
    data = request.get_json()
    topic_type = data.get('topic_type')  # 'main' or 'sub'
    old_name = data.get('old_name')
    new_name = data.get('new_name')
    
    success = classifier.rename_topic(topic_type, old_name, new_name)
    
    return jsonify({
        'success': success,
        'classification': classifier.current_classification if success else None
    })

@app.route('/create_topic', methods=['POST'])
def create_topic():
    """Create a new main topic or subtopic."""
    data = request.get_json()
    topic_type = data.get('topic_type')  # 'main' or 'sub'
    topic_name = data.get('topic_name')
    main_topic_name = data.get('main_topic_name')  # For subtopics
    
    if topic_type == 'main':
        success = classifier.create_new_main_topic(topic_name)
    elif topic_type == 'sub':
        success = classifier.create_new_subtopic(main_topic_name, topic_name)
    else:
        success = False
    
    if success:
        # Ensure all task counts are updated after creating new topics
        update_all_task_counts()
        
        # Verify newly created topics have proper structure
        if topic_type == 'main' and topic_name in classifier.current_classification['main_topics']:
            main_topic = classifier.current_classification['main_topics'][topic_name]
            if 'total_tasks' not in main_topic:
                main_topic['total_tasks'] = 0
            if 'subtopics' not in main_topic:
                main_topic['subtopics'] = {}
        elif topic_type == 'sub' and main_topic_name in classifier.current_classification['main_topics']:
            subtopics = classifier.current_classification['main_topics'][main_topic_name]['subtopics']
            if topic_name in subtopics:
                subtopic = subtopics[topic_name]
                if 'task_count' not in subtopic:
                    subtopic['task_count'] = 0
                if 'task_entries' not in subtopic:
                    subtopic['task_entries'] = []
                if 'unique_task_count' not in subtopic:
                    subtopic['unique_task_count'] = 0
    
    return jsonify({
        'success': success,
        'classification': classifier.current_classification if success else None
    })

@app.route('/delete_topic', methods=['POST'])
def delete_topic():
    """Delete a main topic or subtopic."""
    data = request.get_json()
    topic_type = data.get('topic_type')  # 'main' or 'sub'
    topic_name = data.get('topic_name')
    main_topic_name = data.get('main_topic_name')  # For subtopics
    
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    try:
        if topic_type == 'main':
            # Check if main topic exists
            main_topics = classifier.current_classification['main_topics']
            if topic_name not in main_topics:
                return jsonify({'success': False, 'error': f'Main topic "{topic_name}" not found'})
            
            # Check if main topic has any tasks
            main_topic = main_topics[topic_name]
            total_tasks = main_topic.get('total_tasks', 0)
            if total_tasks > 0:
                return jsonify({
                    'success': False, 
                    'error': f'Cannot delete main topic "{topic_name}" - it contains {total_tasks} tasks. Please move or delete all tasks first.'
                })
            
            # Delete the main topic (only if empty)
            del main_topics[topic_name]
            
            # Log modification
            classifier.modifications_log.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'delete_main_topic',
                'topic_name': topic_name,
                'deleted_tasks_count': 0
            })
            
        elif topic_type == 'sub':
            # Check if main topic and subtopic exist
            main_topics = classifier.current_classification['main_topics']
            if main_topic_name not in main_topics:
                return jsonify({'success': False, 'error': f'Main topic "{main_topic_name}" not found'})
            
            subtopics = main_topics[main_topic_name]['subtopics']
            if topic_name not in subtopics:
                return jsonify({'success': False, 'error': f'Subtopic "{topic_name}" not found'})
            
            # Check if subtopic has any tasks
            subtopic = subtopics[topic_name]
            task_count = subtopic.get('task_count', 0)
            if task_count > 0:
                return jsonify({
                    'success': False, 
                    'error': f'Cannot delete subtopic "{topic_name}" - it contains {task_count} tasks. Please move or delete all tasks first.'
                })
            
            # Delete the subtopic (only if empty)
            del subtopics[topic_name]
            
            # Log modification
            classifier.modifications_log.append({
                'timestamp': datetime.now().isoformat(),
                'action': 'delete_subtopic',
                'main_topic_name': main_topic_name,
                'subtopic_name': topic_name,
                'deleted_tasks_count': 0
            })
            
        else:
            return jsonify({'success': False, 'error': 'Invalid topic type'})
        
        # Update all task counts after deletion
        update_all_task_counts()
        
        return jsonify({
            'success': True,
            'classification': classifier.current_classification,
            'message': f'{"Main topic" if topic_type == "main" else "Subtopic"} "{topic_name}" deleted successfully'
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': f'Failed to delete topic: {str(e)}'})

@app.route('/move_task', methods=['POST'])
def move_task():
    """Move a task from one subtopic to another."""
    data = request.get_json()
    task_id = data.get('task_id')
    target_main_topic = data.get('target_main_topic')
    target_subtopic = data.get('target_subtopic')
    
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    # Find and remove task from source location
    moved_entry = None
    source_main_topic = None
    source_subtopic = None
    
    for main_topic_name, main_topic_info in classifier.current_classification['main_topics'].items():
        for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
            # Handle task_entries format (original tasks)
            if 'task_entries' in subtopic_info:
                for i, entry in enumerate(subtopic_info['task_entries']):
                    if task_id in entry['task_ids']:
                        moved_entry = subtopic_info['task_entries'].pop(i)
                        source_main_topic = main_topic_name
                        source_subtopic = subtopic_name
                        break

            # Handle tasks format (new merged tasks) - can coexist with task_entries
            if not moved_entry and 'tasks' in subtopic_info:
                for i, task in enumerate(subtopic_info['tasks']):
                    if task['id'] == task_id:
                        # Convert tasks format to task_entries format for consistency
                        moved_task = subtopic_info['tasks'].pop(i)
                        moved_entry = {
                            'task_ids': [moved_task['id']],
                            'representative_id': moved_task['id'],
                            'task_content': moved_task['task_content'],
                            'month_years': [moved_task.get('month_year', '')],
                            'combination_numbers': [moved_task.get('combination_number', '')],
                            'is_duplicate_group': False,
                            'duplicate_count': 1
                        }
                        source_main_topic = main_topic_name
                        source_subtopic = subtopic_name
                        break

            if moved_entry:
                break
        if moved_entry:
            break
    
    if not moved_entry:
        return jsonify({'success': False, 'error': 'Task not found'})
    
    try:
        main_topics = classifier.current_classification['main_topics']
        if target_main_topic not in main_topics:
            return jsonify({'success': False, 'error': f'Main topic "{target_main_topic}" not found'})
        
        subtopics = main_topics[target_main_topic]['subtopics']
        if target_subtopic not in subtopics:
            return jsonify({'success': False, 'error': f'Subtopic "{target_subtopic}" not found'})
        
        # Add task to target subtopic
        # Handle both old format (tasks) and new format (task_entries)
        if 'task_entries' in subtopics[target_subtopic]:
            # Target uses new format, add buffer task directly
            subtopics[target_subtopic]['task_entries'].append(moved_entry)
        elif 'tasks' in subtopics[target_subtopic]:
            # Target uses old format, convert buffer task back to old format
            if 'representative_id' in moved_entry:
                # Convert from new format to old format
                old_format_task = {
                    'id': moved_entry['representative_id'],
                    'task_content': moved_entry.get('task_content', ''),
                    'month_year': moved_entry.get('month_years', [None])[0],
                    'combination_number': moved_entry.get('combination_numbers', [None])[0]
                }
                subtopics[target_subtopic]['tasks'].append(old_format_task)
            else:
                # Task is already in old format
                subtopics[target_subtopic]['tasks'].append(moved_entry)
        else:
            # Create new format if neither exists
            subtopics[target_subtopic]['task_entries'] = [moved_entry]
        
        # Update all task counts after successful move
        update_all_task_counts()
        
        # Log modification
        classifier.modifications_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'move_task',
            'task_id': task_id,
            'source_main_topic': source_main_topic,
            'source_subtopic': source_subtopic,
            'target_main_topic': target_main_topic,
            'target_subtopic': target_subtopic,
            'task_content': moved_entry.get('task_content', '')[:50] + '...'
        })
        
        return jsonify({
            'success': True,
            'classification': classifier.current_classification
        })
        
    except Exception as e:
        # If something goes wrong, put entry back in source location
        if source_main_topic and source_subtopic:
            source_subtopics = classifier.current_classification['main_topics'][source_main_topic]['subtopics']
            if 'task_entries' not in source_subtopics[source_subtopic]:
                source_subtopics[source_subtopic]['task_entries'] = []
            source_subtopics[source_subtopic]['task_entries'].append(moved_entry)
            update_all_task_counts()
        
        return jsonify({'success': False, 'error': f'Failed to move task: {str(e)}'})

def update_all_task_counts():
    """Update all task counts in the classification to ensure consistency."""
    if not classifier.current_classification:
        return

    # For merged checkpoints, preserve the original total_tasks from the file
    # Don't recalculate it as it may lead to double counting
    original_total = classifier.current_classification.get('total_tasks', 0)

    total_tasks = 0

    # Update counts for each main topic and subtopic
    for main_topic_name, main_topic_info in classifier.current_classification['main_topics'].items():
        main_topic_task_count = 0

        # Update subtopic counts
        for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
            actual_task_count = 0

            # Count tasks from task_entries format
            if 'task_entries' in subtopic_info:
                actual_task_count += sum(entry.get('duplicate_count', 1) for entry in subtopic_info['task_entries'])

            # Count tasks from tasks format (for merged checkpoints)
            if 'tasks' in subtopic_info:
                actual_task_count += len(subtopic_info['tasks'])

            subtopic_info['task_count'] = actual_task_count
            main_topic_task_count += actual_task_count

        # Update main topic count
        main_topic_info['total_tasks'] = main_topic_task_count
        total_tasks += main_topic_task_count

    # For merged checkpoints, preserve the original total_tasks to avoid double counting
    # Only update if the original total seems wrong (e.g., 0 or missing)
    if original_total > 0 and abs(original_total - total_tasks) <= 10:
        # Keep the original total if it's close to our calculated total
        classifier.current_classification['total_tasks'] = original_total
    else:
        # Use calculated total if original seems wrong
        classifier.current_classification['total_tasks'] = total_tasks

@app.route('/search_tasks', methods=['POST'])
def search_tasks():
    """Search for tasks containing specific text."""
    data = request.get_json()
    query = data.get('query', '')
    limit = data.get('limit', 20)
    
    results = classifier.search_tasks(query, limit)
    
    return jsonify({'results': results})

@app.route('/save_checkpoint', methods=['POST'])
def save_checkpoint():
    """Save current classification as a checkpoint."""
    data = request.get_json()
    checkpoint_name = data.get('checkpoint_name')
    description = data.get('description', '')
    
    success = classifier.save_checkpoint(checkpoint_name, description)
    
    return jsonify({'success': success})

@app.route('/get_task_details', methods=['POST'])
def get_task_details():
    """Get detailed information about a specific task."""
    data = request.get_json()
    task_id = data.get('task_id')
    
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    # Find task in classification
    for main_topic_name, main_topic_info in classifier.current_classification['main_topics'].items():
        for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
            # Handle task_entries format
            if 'task_entries' in subtopic_info:
                for entry in subtopic_info['task_entries']:
                    if task_id in entry['task_ids']:
                        return jsonify({
                            'success': True,
                            'task_entry': entry,
                            'main_topic': main_topic_name,
                            'subtopic': subtopic_name
                        })

            # Handle tasks format (can coexist with task_entries in merged checkpoints)
            if 'tasks' in subtopic_info:
                for task in subtopic_info['tasks']:
                    if task['id'] == task_id:
                        return jsonify({
                            'success': True,
                            'task': task,
                            'main_topic': main_topic_name,
                            'subtopic': subtopic_name
                        })
    
    return jsonify({'success': False, 'error': 'Task not found'})

@app.route('/get_current_classification', methods=['GET'])
def get_current_classification():
    """Get the current classification state."""
    if classifier.current_classification:
        return jsonify({
            'success': True,
            'classification': classifier.current_classification,
            'tache_number': classifier.tache_number,
            'modifications_count': len(classifier.modifications_log)
        })
    else:
        return jsonify({'success': False, 'error': 'No classification loaded'})

@app.route('/demo_classification', methods=['POST'])
def demo_classification():
    """Demo classification endpoint - currently disabled."""
    return jsonify({
        'success': False,
        'error': 'Demo classification is currently disabled. Use the merge_new_tasks.py script instead.'
    })

# =============== BUFFER MANAGEMENT ENDPOINTS ===============

@app.route('/buffer', methods=['GET'])
def get_buffer():
    """Get current buffer tasks for the active tâche."""
    if not classifier.tache_number:
        return jsonify({'success': False, 'error': 'No tâche loaded'})
    
    buffer_tasks = task_buffers.get(classifier.tache_number, [])
    return jsonify({
        'success': True,
        'tasks': buffer_tasks,
        'count': len(buffer_tasks)
    })

@app.route('/buffer', methods=['POST'])
def save_buffer():
    """Save buffer tasks for the active tâche."""
    if not classifier.tache_number:
        return jsonify({'success': False, 'error': 'No tâche loaded'})
    
    data = request.get_json()
    tasks = data.get('tasks', [])
    
    task_buffers[classifier.tache_number] = tasks
    
    return jsonify({
        'success': True,
        'saved_count': len(tasks)
    })

@app.route('/move_to_buffer', methods=['POST'])
def move_to_buffer():
    """Move a task from classification to buffer."""
    data = request.get_json()
    task_id = data.get('task_id')
    
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    # Find and remove task from classification
    moved_entry = None
    source_main_topic = None
    source_subtopic = None
    
    for main_topic_name, main_topic_info in classifier.current_classification['main_topics'].items():
        for subtopic_name, subtopic_info in main_topic_info['subtopics'].items():
            # Handle task_entries format (original tasks)
            if 'task_entries' in subtopic_info:
                for i, entry in enumerate(subtopic_info['task_entries']):
                    if task_id in entry['task_ids']:
                        moved_entry = subtopic_info['task_entries'].pop(i)
                        source_main_topic = main_topic_name
                        source_subtopic = subtopic_name
                        break

            # Handle tasks format (new merged tasks) - can coexist with task_entries
            if not moved_entry and 'tasks' in subtopic_info:
                for i, task in enumerate(subtopic_info['tasks']):
                    if task['id'] == task_id:
                        # Convert tasks format to task_entries format for buffer
                        moved_task = subtopic_info['tasks'].pop(i)
                        moved_entry = {
                            'task_ids': [moved_task['id']],
                            'representative_id': moved_task['id'],
                            'task_content': moved_task['task_content'],
                            'month_years': [moved_task.get('month_year', '')],
                            'combination_numbers': [moved_task.get('combination_number', '')],
                            'is_duplicate_group': False,
                            'duplicate_count': 1
                        }
                        source_main_topic = main_topic_name
                        source_subtopic = subtopic_name
                        break

            if moved_entry:
                break
        if moved_entry:
            break
    
    if not moved_entry:
        return jsonify({'success': False, 'error': 'Task not found'})
    
    # Add to buffer
    if classifier.tache_number not in task_buffers:
        task_buffers[classifier.tache_number] = []
    
    task_buffers[classifier.tache_number].append(moved_entry)
    
    # Update all task counts after removal
    update_all_task_counts()
    
    # Log modification
    classifier.modifications_log.append({
        'timestamp': datetime.now().isoformat(),
        'action': 'move_to_buffer',
        'task_id': task_id,
        'source_main_topic': source_main_topic,
        'source_subtopic': source_subtopic,
        'task_content': moved_entry.get('task_content', '')[:50] + '...'
    })
    
    return jsonify({
        'success': True,
        'classification': classifier.current_classification,
        'buffer_tasks': task_buffers[classifier.tache_number]
    })

@app.route('/move_from_buffer', methods=['POST'])
def move_from_buffer():
    """Move a task from buffer to classification."""
    data = request.get_json()
    task_id = data.get('task_id')
    target_main_topic = data.get('target_main_topic')
    target_subtopic = data.get('target_subtopic')
    
    print(f"🎯 move_from_buffer called with task_id: {task_id}, target: {target_main_topic} > {target_subtopic}")
    
    if not classifier.current_classification:
        print("❌ No classification loaded")
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    if not classifier.tache_number or classifier.tache_number not in task_buffers:
        print("❌ No buffer tasks available")
        return jsonify({'success': False, 'error': 'No buffer tasks available'})
    
    # Find and remove task from buffer
    buffer_tasks = task_buffers[classifier.tache_number]
    moved_task = None
    for i, task in enumerate(buffer_tasks):
        # Buffer tasks use 'representative_id' or 'task_ids', not 'id'
        task_identifier = task.get('representative_id') or (task.get('task_ids', [None])[0] if task.get('task_ids') else None)
        if task_identifier == task_id or (task.get('task_ids') and task_id in task.get('task_ids', [])):
            moved_task = buffer_tasks.pop(i)
            print(f"✅ Found task in buffer: {task_identifier}")
            break
    
    if not moved_task:
        print(f"❌ Task {task_id} not found in buffer")
        print(f"📋 Available buffer tasks: {[t.get('representative_id') or t.get('id') for t in buffer_tasks]}")
        return jsonify({'success': False, 'error': 'Task not found in buffer'})
    
    # Add to target location
    try:
        main_topics = classifier.current_classification['main_topics']
        if target_main_topic not in main_topics:
            print(f"❌ Main topic '{target_main_topic}' not found")
            print(f"📋 Available main topics: {list(main_topics.keys())}")
            return jsonify({'success': False, 'error': f'Main topic "{target_main_topic}" not found'})
        
        subtopics = main_topics[target_main_topic]['subtopics']
        if target_subtopic not in subtopics:
            print(f"❌ Subtopic '{target_subtopic}' not found in '{target_main_topic}'")
            print(f"📋 Available subtopics in {target_main_topic}: {list(subtopics.keys())}")
            return jsonify({'success': False, 'error': f'Subtopic "{target_subtopic}" not found'})
        
        print(f"✅ Target subtopic found: {target_main_topic} > {target_subtopic}")
        print(f"📊 Subtopic structure: {subtopics[target_subtopic].keys()}")
        
        # Add task to target subtopic
        # Handle both old format (tasks) and new format (task_entries)
        if 'task_entries' in subtopics[target_subtopic]:
            # Target uses new format, add buffer task directly
            subtopics[target_subtopic]['task_entries'].append(moved_task)
            print(f"✅ Added task to task_entries (new format)")
        elif 'tasks' in subtopics[target_subtopic]:
            # Target uses old format, convert buffer task back to old format
            if 'representative_id' in moved_task:
                # Convert from new format to old format
                old_format_task = {
                    'id': moved_task['representative_id'],
                    'task_content': moved_task.get('task_content', ''),
                    'month_year': moved_task.get('month_years', [None])[0],
                    'combination_number': moved_task.get('combination_numbers', [None])[0]
                }
                subtopics[target_subtopic]['tasks'].append(old_format_task)
                print(f"✅ Added task to tasks (old format)")
            else:
                # Task is already in old format
                subtopics[target_subtopic]['tasks'].append(moved_task)
                print(f"✅ Added task to tasks (old format, direct)")
        else:
            # Create new format if neither exists
            subtopics[target_subtopic]['task_entries'] = [moved_task]
            print(f"✅ Created new task_entries and added task")
        
        # Update all task counts after addition
        update_all_task_counts()
        
        # Log modification
        classifier.modifications_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': 'move_from_buffer',
            'task_id': task_id,
            'target_main_topic': target_main_topic,
            'target_subtopic': target_subtopic,
            'task_content': moved_task.get('task_content', '')[:50] + '...'
        })
        
        return jsonify({
            'success': True,
            'classification': classifier.current_classification,
            'buffer_tasks': task_buffers[classifier.tache_number]
        })
        
    except Exception as e:
        # If something goes wrong, put task back in buffer
        buffer_tasks.append(moved_task)
        return jsonify({'success': False, 'error': f'Failed to move task: {str(e)}'})

@app.route('/refresh_counts', methods=['POST'])
def refresh_counts():
    """Manually refresh all task counts in the classification."""
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    update_all_task_counts()
    
    return jsonify({
        'success': True,
        'classification': classifier.current_classification,
        'message': 'All task counts have been refreshed'
    })

@app.route('/cleanup_empty_topics', methods=['POST'])
def cleanup_empty_topics():
    """Delete empty subtopics and main topics from the classification."""
    if not classifier.current_classification:
        return jsonify({'success': False, 'error': 'No classification loaded'})
    
    if 'main_topics' not in classifier.current_classification:
        return jsonify({'success': False, 'error': 'Invalid classification structure'})
    
    deleted_subtopics = []
    deleted_main_topics = []
    
    # Get list of main topics to avoid modification during iteration
    main_topics = list(classifier.current_classification['main_topics'].items())
    
    for main_topic_name, main_topic_info in main_topics:
        if 'subtopics' not in main_topic_info:
            continue
            
        # Get list of subtopics to avoid modification during iteration
        subtopics = list(main_topic_info['subtopics'].items())
        
        for subtopic_name, subtopic_info in subtopics:
            # Check if subtopic is empty
            is_empty = False
            
            if 'task_entries' in subtopic_info:
                # New format: check if task_entries is empty
                is_empty = len(subtopic_info['task_entries']) == 0
            elif 'tasks' in subtopic_info:
                # Old format: check if tasks is empty
                is_empty = len(subtopic_info['tasks']) == 0
            else:
                # No tasks or task_entries field, consider empty
                is_empty = True
            
            if is_empty:
                # Delete empty subtopic
                del main_topic_info['subtopics'][subtopic_name]
                deleted_subtopics.append(f"{main_topic_name} > {subtopic_name}")
                print(f"🗑️ Deleted empty subtopic: {main_topic_name} > {subtopic_name}")
        
        # Check if main topic is now empty (no subtopics left)
        if len(main_topic_info['subtopics']) == 0:
            # Delete empty main topic
            del classifier.current_classification['main_topics'][main_topic_name]
            deleted_main_topics.append(main_topic_name)
            print(f"🗑️ Deleted empty main topic: {main_topic_name}")
    
    # Update all task counts after cleanup
    update_all_task_counts()
    
    # Log the cleanup operation
    classifier.modifications_log.append({
        'timestamp': datetime.now().isoformat(),
        'action': 'cleanup_empty_topics',
        'deleted_subtopics': deleted_subtopics,
        'deleted_main_topics': deleted_main_topics,
        'total_deleted': len(deleted_subtopics) + len(deleted_main_topics)
    })
    
    cleanup_summary = {
        'deleted_subtopics': deleted_subtopics,
        'deleted_main_topics': deleted_main_topics,
        'total_deleted': len(deleted_subtopics) + len(deleted_main_topics)
    }
    
    return jsonify({
        'success': True,
        'classification': classifier.current_classification,
        'cleanup_summary': cleanup_summary,
        'message': f"Cleanup completed: {len(deleted_subtopics)} subtopics and {len(deleted_main_topics)} main topics deleted"
    })

# =============== END BUFFER ENDPOINTS ===============

def convert_unique_tasks_format(classification):
    """Convert unique_tasks format to task_entries format for web interface compatibility."""
    if not classification:
        return
    
    main_topics = classification.get('main_topics', {})
    for topic_name, topic_data in main_topics.items():
        subtopics = topic_data.get('subtopics', {})
        for subtopic_name, subtopic_data in subtopics.items():
            # Check if this subtopic uses unique_tasks format
            if 'unique_tasks' in subtopic_data and 'task_entries' not in subtopic_data:
                unique_tasks = subtopic_data.get('unique_tasks', [])
                
                # Convert unique_tasks to task_entries format
                task_entries = []
                for unique_task in unique_tasks:
                    task_entry = {
                        'task_ids': unique_task.get('all_instance_ids', [unique_task.get('representative_id')]),
                        'representative_id': unique_task.get('representative_id'),
                        'task_content': unique_task.get('task_content', ''),
                        'month_years': [unique_task.get('month_year')] if unique_task.get('month_year') else [],
                        'combination_numbers': [unique_task.get('combination_number')] if unique_task.get('combination_number') else [],
                        'is_duplicate_group': unique_task.get('duplicate_count', 1) > 1,
                        'duplicate_count': unique_task.get('duplicate_count', 1),
                        'main_topic_score': unique_task.get('main_topic_score', 0)
                    }
                    task_entries.append(task_entry)
                
                # Replace unique_tasks with task_entries
                subtopic_data['task_entries'] = task_entries
                
                # Update counts to use the expected field names
                subtopic_data['task_count'] = subtopic_data.get('original_task_count', len(task_entries))
                subtopic_data['unique_task_count'] = subtopic_data.get('unique_task_count', len(task_entries))
                
                # Remove the old format fields
                if 'unique_tasks' in subtopic_data:
                    del subtopic_data['unique_tasks']
                if 'all_task_instances' in subtopic_data:
                    del subtopic_data['all_task_instances']
        
        # Update main topic counts
        if 'total_unique_tasks' in topic_data:
            topic_data['unique_tasks'] = topic_data['total_unique_tasks']
            del topic_data['total_unique_tasks']
        if 'total_original_tasks' in topic_data:
            topic_data['total_tasks'] = topic_data['total_original_tasks']
            del topic_data['total_original_tasks']

if __name__ == '__main__':
    print("🚀 Starting TCF Classification API Backend...")
    print("🌐 API Health Check: http://localhost:5001/api/health")
    print("🎯 React Interface: http://localhost:3000")
    print("📡 Ready for React development")
    
    app.run(debug=True, host='0.0.0.0', port=5001) 