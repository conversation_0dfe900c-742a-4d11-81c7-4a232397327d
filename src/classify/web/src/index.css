/* Global Styles */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  background: white;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  text-align: center;
}

.header h1 {
  color: #667eea;
  font-size: 2.5em;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 1.1em;
}

/* Controls */
.controls {
  background: white;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 20px rgba(0,0,0,0.08);
}

.control-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.btn {
  background-color: #e2e8f0;
  border: 1px solid #d1d9e6;
  border-radius: 6px;
  color: #2d3748;
  cursor: pointer;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  text-decoration: none;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:hover {
  background-color: #cbd5e0;
  border-color: #a0aec0;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.btn-primary {
  background-color: #667eea;
  border-color: #667eea;
  color: white;
}

.btn.btn-primary:hover {
  background-color: #5a67d8;
  border-color: #5a67d8;
}

.btn.btn-danger {
  background-color: #e53e3e;
  border-color: #e53e3e;
  color: white;
}

.btn.btn-danger:hover {
  background-color: #c53030;
  border-color: #c53030;
}

.btn.btn-success {
  background-color: #38a169;
  border-color: #38a169;
  color: white;
}

.btn.btn-success:hover {
  background-color: #2f855a;
  border-color: #2f855a;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: center;
}

input, select {
  padding: 10px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
}

input:focus, select:focus {
  outline: none;
  border-color: #667eea;
}

.search-box {
  width: 300px;
}

/* Status */
.status {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.status.loaded {
  border-color: #48bb78;
  background: #f0fff4;
}

/* Classification */
.classification-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.main-topic {
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  overflow: hidden;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.main-topic-header {
  background: #667eea;
  color: white;
  padding: 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.main-topic-header:hover {
  background: #5a67d8;
}

.main-topic-title {
  font-weight: 600;
  font-size: 1.1em;
}

.task-count {
  background: rgba(255,255,255,0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9em;
}

.subtopics {
  max-height: 600px;
  overflow-y: auto;
}

.subtopic {
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.subtopic-header {
  background: #edf2f7;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
  position: relative;
  min-height: 50px;
}

.subtopic-header:hover {
  background: #e2e8f0;
}

/* Enhanced drag target styling */
.subtopic-header.drag-target-active {
  background: linear-gradient(135deg, #2196f3 0%, #64b5f6 100%) !important;
  color: white !important;
  border: 3px solid #1976d2 !important;
  box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4) !important;
  z-index: 10 !important;
  position: relative !important;
}

.subtopic-header.drag-target-active .task-count {
  background: rgba(255, 255, 255, 0.3) !important;
  color: white !important;
}

.subtopic-header.drag-target-active .drop-zone-indicator {
  color: rgba(255, 255, 255, 0.8) !important;
}

.drop-target-indicator {
  color: #fff !important;
  font-size: 0.9em !important;
  margin-left: 12px !important;
  font-weight: bold !important;
  animation: bounce 0.6s infinite alternate !important;
  background: rgba(255, 255, 255, 0.2) !important;
  padding: 2px 8px !important;
  border-radius: 12px !important;
}

@keyframes bounce {
  from { transform: translateY(0px); }
  to { transform: translateY(-3px); }
}

@keyframes pulse {
  0% { opacity: 0.6; transform: scale(0.98); }
  50% { opacity: 1; transform: scale(1.02); }
  100% { opacity: 0.6; transform: scale(0.98); }
}

.subtopic.drag-over {
  background-color: #f7fafc;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(66, 153, 225, 0.2);
}

.subtopic.drag-over .subtopic-header {
  background-color: #e6f3ff;
  color: #2d3748;
}

.drop-zone-indicator {
  color: #a0aec0;
  font-size: 0.75em;
  margin-left: 8px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.subtopic:hover .drop-zone-indicator {
  opacity: 1;
  color: #4299e1;
}

.tasks {
  padding: 10px;
  min-height: 60px;
  background: white;
  transition: all 0.2s ease;
}

.tasks.drag-over {
  background-color: #f0f8ff;
  border: 2px dashed #4299e1;
  border-radius: 6px;
  padding: 8px;
  margin: 4px 0;
}

.task {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  cursor: move;
  transition: all 0.3s ease;
  position: relative;
}

.task:hover {
  border-color: #667eea;
  box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.task.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.task-id {
  font-weight: bold;
  color: #667eea;
  font-size: 0.9em;
}

.task-content {
  margin-top: 8px;
  color: #4a5568;
  line-height: 1.4;
}

.task-content.truncated {
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expand-btn {
  color: #667eea;
  cursor: pointer;
  font-size: 0.8em;
  margin-top: 5px;
  text-decoration: underline;
}

.collapsed {
  display: none;
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 15px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 15px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #a0aec0;
  padding: 0;
  margin: 0;
}

.close-btn:hover {
  color: #667eea;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4a5568;
}

.form-group input, 
.form-group textarea, 
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* Alerts */
.alert {
  padding: 15px;
  border-radius: 8px;
  margin: 15px 0;
  position: relative;
}

.alert-success {
  background: #f0fff4;
  color: #22543d;
  border: 1px solid #c6f6d5;
}

.alert-error {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #feb2b2;
}

.alert-close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* Toggle Icons */
.toggle-icon {
  transition: transform 0.3s ease;
  margin-right: 8px;
  font-size: 0.9em;
}

.toggle-icon.rotated {
  transform: rotate(90deg);
}

/* Demo Section */
.demo-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-top: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.demo-result {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

/* Checkpoint List */
.checkpoint-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 10px;
}

.checkpoint-item {
  padding: 10px;
  border-bottom: 1px solid #e2e8f0;
  cursor: pointer;
  border-radius: 6px;
  transition: background 0.2s ease;
}

.checkpoint-item:hover {
  background: #f7fafc;
}

.checkpoint-item:last-child {
  border-bottom: none;
}

/* Responsive */
@media (max-width: 768px) {
  .classification-container {
    grid-template-columns: 1fr;
  }
  
  .control-row {
    flex-direction: column;
  }
  
  .input-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    width: 100%;
  }
}

/* React Beautiful DND overrides */
.task[data-rbd-drag-handle-context-id] {
  cursor: grab;
}

.task[data-rbd-drag-handle-dragging] {
  cursor: grabbing;
}

/* Task Buffer Styles */
.task-buffer {
  width: 320px;
  max-height: calc(100vh - 40px);
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
  border: 3px solid #667eea;
  /* Note: position, left, top, z-index are set inline */
}

.task-buffer.minimized {
  height: auto;
  max-height: 60px;
}

.task-buffer.dragging {
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
  transform: rotate(1deg);
}

.buffer-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  user-select: none;
  transition: background 0.2s ease;
  animation: bufferPulse 3s ease-in-out infinite;
}

@keyframes bufferPulse {
  0%, 100% { box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4); }
  50% { box-shadow: 0 0 0 8px rgba(102, 126, 234, 0.1); }
}

.buffer-header:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.buffer-header:active {
  cursor: grabbing;
}

.buffer-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  flex: 1;
}

.buffer-icon {
  font-size: 18px;
}

.buffer-count {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.drag-indicator {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-left: auto;
  margin-right: 8px;
  letter-spacing: -2px;
  transform: rotate(90deg);
}

.buffer-toggle {
  font-size: 16px;
  transition: transform 0.3s ease;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
}

.buffer-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
}

.buffer-content {
  padding: 15px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  /* Prevent scrolling conflicts with react-beautiful-dnd */
  position: relative;
  overflow-anchor: none;
}

.buffer-controls {
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
}

.buffer-clear-btn {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #feb2b2;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.buffer-clear-btn:hover:not(:disabled) {
  background: #feb2b2;
}

.buffer-clear-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.buffer-drop-zone {
  min-height: 120px;
  border: 2px dashed #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
  background: #f8fafc;
  position: relative;
  /* Prevent scrolling conflicts */
  overflow: visible !important;
  contain: none;
}

.buffer-drop-zone::before {
  content: "🎯 DROP TASKS HERE";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #667eea;
  font-weight: bold;
  font-size: 14px;
  opacity: 0.3;
  pointer-events: none;
}

.buffer-drop-zone.dragging-over {
  border-color: #667eea;
  background: #f0f4ff;
  box-shadow: inset 0 0 0 2px rgba(102, 126, 234, 0.1);
  background: linear-gradient(45deg, #e3f2fd, #f0f4ff);
  border: 3px solid #2196f3;
  transform: scale(1.02);
}

.buffer-drop-zone.dragging-over::before {
  content: "🎯 RELEASE TO ADD!";
  color: #2196f3;
  opacity: 1;
  animation: bounce 0.5s infinite alternate;
}

.buffer-empty {
  text-align: center;
  color: #a0aec0;
  padding: 20px 0;
}

.buffer-empty p {
  margin: 0 0 8px 0;
  font-weight: 500;
}

.buffer-empty small {
  font-size: 11px;
  color: #cbd5e0;
}

.buffer-tasks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.buffer-info {
  margin-top: 15px;
  text-align: center;
  color: #718096;
}

.buffer-info small {
  font-size: 11px;
}

/* Buffer specific task styling */
.task-buffer .task {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  padding: 10px;
  margin: 0;
  font-size: 13px;
}

.task-buffer .task:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.task-buffer .task-id {
  font-size: 10px;
  color: #a0aec0;
  margin-bottom: 4px;
}

.task-buffer .task-content {
  line-height: 1.4;
}

/* Responsive adjustments for buffer */
@media (max-width: 768px) {
  .task-buffer {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}

@media (max-width: 480px) {
  .task-buffer {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
  }
  
  .buffer-content {
    padding: 10px;
  }
}

/* Collapsed drop zone styling */
.collapsed-drop-zone {
  background: transparent !important;
  padding: 0;
  transition: all 0.2s ease;
}

.collapsed-drop-zone.drag-over {
  background: #f0f8ff !important;
  border: 2px dashed #4299e1 !important;
  border-radius: 6px !important;
  margin: 8px 0 !important;
  padding: 8px !important;
}

/* Collapsed drop indicator styling */
.collapsed-drop-indicator {
  border-radius: 8px !important;
  margin: 8px 16px !important;
  transition: all 0.3s ease !important;
}

/* Duplicate group styling */
.task.duplicate-group {
  background: linear-gradient(135deg, #fff5f5, #fef5e7);
  border-left: 4px solid #f6ad55;
  box-shadow: 0 2px 4px rgba(246, 173, 85, 0.1);
}

.task.duplicate-group:hover {
  background: linear-gradient(135deg, #fed7d7, #feebc8);
  box-shadow: 0 4px 8px rgba(246, 173, 85, 0.2);
}

.duplicate-group-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.duplicate-indicator {
  font-size: 10px;
  color: #c05621;
  font-weight: 600;
  background: rgba(246, 173, 85, 0.2);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid rgba(246, 173, 85, 0.3);
}

.all-ids {
  font-size: 9px;
  color: #744210;
  background: rgba(246, 173, 85, 0.1);
  padding: 4px 6px;
  border-radius: 4px;
  max-width: 100%;
  word-break: break-all;
  line-height: 1.2;
  margin-top: 4px;
} 