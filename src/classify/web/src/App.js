import React, { useState, useCallback, useEffect, useRef } from 'react';
import { DragDropContext } from 'react-beautiful-dnd';
import axios from 'axios';

import Header from './components/Header';
import Controls from './components/Controls';
import Status from './components/Status';
import ClassificationContainer from './components/ClassificationContainer';
import DemoSection from './components/DemoSection';
import Alert from './components/Alert';
import SaveCheckpointModal from './components/SaveCheckpointModal';
import LoadCheckpointModal from './components/LoadCheckpointModal';
import CreateTopicModal from './components/CreateTopicModal';
import RenameTopicModal from './components/RenameTopicModal';
import DeleteTopicModal from './components/DeleteTopicModal';
import TaskBuffer from './components/TaskBuffer';

function App() {
  const [currentClassification, setCurrentClassification] = useState(null);
  const [currentTache, setCurrentTache] = useState(null);
  const [selectedTache, setSelectedTache] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [alert, setAlert] = useState(null);
  const [loading, setLoading] = useState(false);
  
  // Buffer states
  const [bufferTasks, setBufferTasks] = useState([]);
  const [isBufferMinimized, setIsBufferMinimized] = useState(false);
  
  // Modal states
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [showCheckpointModal, setShowCheckpointModal] = useState(false);
  const [showCreateTopicModal, setShowCreateTopicModal] = useState(false);
  const [showRenameModal, setShowRenameModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [renameData, setRenameData] = useState(null);

  // Drag state to prevent updates during drag operations
  const [isDragging, setIsDragging] = useState(false);
  const pendingUpdateRef = useRef(null);

  // Show alert helper
  const showAlert = useCallback((message, type = 'success') => {
    setAlert({ message, type });
    setTimeout(() => setAlert(null), 5000);
  }, []);

  // Load buffer from server
  const loadBuffer = useCallback(async () => {
    try {
      const response = await axios.get('/buffer');
      if (response.data.success) {
        setBufferTasks(response.data.tasks || []);
      }
    } catch (error) {
      console.error('Failed to load buffer:', error);
    }
  }, []);

  // Save buffer to server
  const saveBuffer = useCallback(async (tasks) => {
    try {
      await axios.post('/buffer', { tasks });
    } catch (error) {
      console.error('Failed to save buffer:', error);
    }
  }, []);

  // Clear buffer
  const clearBuffer = useCallback(async () => {
    setBufferTasks([]);
    await saveBuffer([]);
    showAlert('Buffer cleared', 'success');
  }, [saveBuffer, showAlert]);

  // Toggle buffer minimize
  const toggleBufferMinimize = useCallback(() => {
    setIsBufferMinimized(!isBufferMinimized);
  }, [isBufferMinimized]);

  // Load buffer on app start
  useEffect(() => {
    loadBuffer();
  }, [loadBuffer]);

  // Disable window scrolling during drag to prevent react-beautiful-dnd errors
  useEffect(() => {
    if (isDragging) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isDragging]);

  // Load automatic classification
  const loadAutomaticClassification = useCallback(async () => {
    if (!selectedTache) {
      showAlert('Please select a Tâche number', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post('/load_classification', {
        tache_number: parseInt(selectedTache)
      });

      if (response.data.success) {
        setCurrentClassification(response.data.classification);
        setCurrentTache(response.data.tache_number);
        showAlert('Classification loaded successfully!', 'success');
        // Load buffer for this tache
        await loadBuffer();
      } else {
        showAlert('Error: ' + response.data.error, 'error');
      }
    } catch (error) {
      showAlert('Network error: ' + error.message, 'error');
    } finally {
      setLoading(false);
    }
  }, [selectedTache, showAlert, loadBuffer]);

  // Handle drag start
  const onDragStart = useCallback((start) => {
    setIsDragging(true);
  }, []);

  // Handle drag end - modified to handle buffer operations and prevent state conflicts
  const onDragEnd = useCallback(async (result) => {
    // Always reset dragging state first
    setIsDragging(false);
    
    const { destination, source, draggableId } = result;

    // Check if dropped outside a valid destination
    if (!destination) {
      return;
    }

    // Check if dropped in the same position
    if (
      destination.droppableId === source.droppableId &&
      destination.index === source.index
    ) {
      return;
    }

    try {
      // Handle buffer operations
      if (destination.droppableId === 'task-buffer') {
        // Moving task TO buffer
        const response = await axios.post('/move_to_buffer', {
          task_id: draggableId
        });

        if (response.data.success) {
          // Update state immediately but batch the updates
          setCurrentClassification(response.data.classification);
          setBufferTasks(response.data.buffer_tasks);
          showAlert('Task moved to buffer', 'success');
        } else {
          showAlert('Error moving task to buffer: ' + response.data.error, 'error');
        }
        return;
      } 
      
      if (source.droppableId === 'task-buffer') {
        // Moving task FROM buffer to topic/subtopic
        const [targetMainTopic, targetSubtopic] = destination.droppableId.split(':::');
        
        if (!targetMainTopic || !targetSubtopic) {
          showAlert('Invalid drop location', 'error');
          return;
        }

        const response = await axios.post('/move_from_buffer', {
          task_id: draggableId,
          target_main_topic: targetMainTopic,
          target_subtopic: targetSubtopic
        });

        if (response.data.success) {
          // Update state immediately but batch the updates
          setCurrentClassification(response.data.classification);
          setBufferTasks(response.data.buffer_tasks);
          showAlert(`Task moved from buffer to ${targetMainTopic} > ${targetSubtopic}`, 'success');
        } else {
          showAlert('Error moving task from buffer: ' + response.data.error, 'error');
        }
        return;
      }

      // Regular topic-to-topic movement
      const [targetMainTopic, targetSubtopic] = destination.droppableId.split(':::');
      
      if (!targetMainTopic || !targetSubtopic) {
        showAlert('Invalid drop location', 'error');
        return;
      }

      const response = await axios.post('/move_task', {
        task_id: draggableId,
        target_main_topic: targetMainTopic,
        target_subtopic: targetSubtopic
      });

      if (response.data.success) {
        // Update state immediately but batch the updates
        setCurrentClassification(response.data.classification);
        showAlert(`Task moved to ${targetMainTopic} > ${targetSubtopic}`, 'success');
      } else {
        showAlert('Error moving task: ' + response.data.error, 'error');
      }
    } catch (error) {
      showAlert('Network error while moving task', 'error');
    }
  }, [showAlert]);

  // Search tasks
  const searchTasks = useCallback(async () => {
    if (!searchQuery.trim()) {
      showAlert('Please enter a search query', 'error');
      return;
    }

    try {
      const response = await axios.post('/search_tasks', {
        query: searchQuery,
        limit: 50
      });

      const results = response.data.results;
      if (results.length === 0) {
        showAlert('No tasks found matching your search', 'error');
        return;
      }

      // For now, just show alert with count
      // In the future, we could highlight matching tasks
      showAlert(`Found ${results.length} matching tasks`, 'success');
    } catch (error) {
      showAlert('Error searching tasks', 'error');
    }
  }, [searchQuery, showAlert]);

  // Refresh classification with proper state management
  const refreshClassification = useCallback(async () => {
    if (!currentClassification) {
      showAlert('No classification to refresh', 'error');
      return;
    }

    try {
      // Call the refresh endpoint to ensure counts are accurate
      const response = await axios.post('/refresh_counts');
      if (response.data.success) {
        setCurrentClassification(response.data.classification);
        showAlert('Classification refreshed', 'success');
      } else {
        showAlert('Error refreshing classification', 'error');
      }
    } catch (error) {
      // Fallback to local refresh
      setCurrentClassification({...currentClassification});
      showAlert('Classification refreshed (local)', 'success');
    }
  }, [currentClassification, showAlert]);

  // Cleanup empty topics and subtopics
  const cleanupEmptyTopics = useCallback(async () => {
    if (!currentClassification) {
      showAlert('No classification to cleanup', 'error');
      return;
    }

    try {
      const response = await axios.post('/cleanup_empty_topics');
      if (response.data.success) {
        setCurrentClassification(response.data.classification);
        const summary = response.data.cleanup_summary;
        
        if (summary.total_deleted === 0) {
          showAlert('No empty topics found to clean up', 'success');
        } else {
          const message = `Cleanup completed: ${summary.deleted_subtopics.length} subtopics and ${summary.deleted_main_topics.length} main topics removed`;
          showAlert(message, 'success');
        }
      } else {
        showAlert('Error during cleanup: ' + response.data.error, 'error');
      }
    } catch (error) {
      showAlert('Network error during cleanup: ' + error.message, 'error');
    }
  }, [currentClassification, showAlert]);

  // Handle rename topic
  const handleRename = useCallback((topicType, currentName, mainTopicName) => {
    setRenameData({ topicType, currentName, mainTopicName });
    setShowRenameModal(true);
  }, []);

  return (
    <div className="app-wrapper" style={{ 
      height: '100vh', 
      overflow: 'auto',
      position: 'relative'
    }}>
      <div className="container">
        <Header />
        
        <Controls
          selectedTache={selectedTache}
          setSelectedTache={setSelectedTache}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          onLoadClassification={loadAutomaticClassification}
          onShowCheckpointModal={() => setShowCheckpointModal(true)}
          onShowSaveModal={() => setShowSaveModal(true)}
          onShowCreateTopicModal={() => setShowCreateTopicModal(true)}
          onShowDeleteTopicModal={() => setShowDeleteModal(true)}
          onSearch={searchTasks}
          onRefresh={refreshClassification}
          onCleanupEmpty={cleanupEmptyTopics}
          loading={loading}
        />

        <Status 
          classification={currentClassification}
          tache={currentTache}
        />

        {alert && (
          <Alert
            message={alert.message}
            type={alert.type}
            onClose={() => setAlert(null)}
          />
        )}

        <DragDropContext 
          key={currentClassification ? JSON.stringify(Object.keys(currentClassification.main_topics || {})) : 'no-classification'}
          onDragStart={onDragStart}
          onDragEnd={onDragEnd}
          onBeforeCapture={(before) => {
          }}
          onBeforeDragStart={(initial) => {
          }}
        >
          <ClassificationContainer
            classification={currentClassification}
            onRename={handleRename}
          />
          
          {/* Task Buffer - rendered as portal to avoid DragDropContext conflicts */}
          <TaskBuffer
            bufferTasks={bufferTasks}
            isMinimized={isBufferMinimized}
            onToggleMinimize={toggleBufferMinimize}
            onClearBuffer={clearBuffer}
          />
        </DragDropContext>

        <DemoSection currentTache={currentTache} showAlert={showAlert} />

        {/* Modals */}
        {showSaveModal && (
          <SaveCheckpointModal
            onClose={() => setShowSaveModal(false)}
            showAlert={showAlert}
            hasClassification={!!currentClassification}
          />
        )}

        {showCheckpointModal && (
          <LoadCheckpointModal
            onClose={() => setShowCheckpointModal(false)}
            onLoad={(classification, tache) => {
              setCurrentClassification(classification);
              setCurrentTache(tache);
              setSelectedTache(tache.toString());
            }}
            showAlert={showAlert}
          />
        )}

        {showCreateTopicModal && (
          <CreateTopicModal
            onClose={() => setShowCreateTopicModal(false)}
            classification={currentClassification}
            onUpdate={(newClassification) => {
              setCurrentClassification(newClassification);
              // Force a small delay to ensure DOM updates complete before DnD re-initializes
              setTimeout(() => {
              }, 100);
            }}
            showAlert={showAlert}
          />
        )}

        {showRenameModal && renameData && (
          <RenameTopicModal
            onClose={() => {
              setShowRenameModal(false);
              setRenameData(null);
            }}
            topicType={renameData.topicType}
            currentName={renameData.currentName}
            mainTopicName={renameData.mainTopicName}
            onUpdate={setCurrentClassification}
            showAlert={showAlert}
          />
        )}

        {showDeleteModal && (
          <DeleteTopicModal
            onClose={() => setShowDeleteModal(false)}
            classification={currentClassification}
            onUpdate={setCurrentClassification}
            showAlert={showAlert}
          />
        )}
      </div>
    </div>
  );
}

export default App; 