import React, { useState } from 'react';
import Subtopic from './Subtopic';

const MainTopic = ({ name, data, totalTasks, onRename }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  const percentage = totalTasks > 0 ? ((data.total_tasks / totalTasks) * 100).toFixed(1) : 0;

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleRename = (e) => {
    e.stopPropagation();
    onRename('main', name);
  };

  return (
    <div className="main-topic">
      <div className="main-topic-header" onClick={handleToggle}>
        <div>
          <span className={`toggle-icon ${isExpanded ? 'rotated' : ''}`}>
            {isExpanded ? '▼' : '▶'}
          </span>
          <span className="main-topic-title">{name}</span>
          <span style={{ marginLeft: '10px', fontSize: '0.9em', opacity: 0.8 }}>
            ({percentage}%)
          </span>
        </div>
        <div>
          <span className="task-count">{data.total_tasks} tasks</span>
          <button 
            className="btn" 
            style={{ marginLeft: '10px', padding: '5px 10px', fontSize: '0.8em' }}
            onClick={handleRename}
            title="Rename main topic"
          >
            ✏️
          </button>
        </div>
      </div>
      
      {isExpanded && (
        <div className="subtopics">
          {Object.entries(data.subtopics || {}).map(([subtopicName, subtopicData]) => (
            <Subtopic
              key={`subtopic-${name}-${subtopicName}`}
              name={subtopicName}
              data={subtopicData}
              mainTopicName={name}
              onRename={onRename}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default MainTopic; 