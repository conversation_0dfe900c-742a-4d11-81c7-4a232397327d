import React, { useState } from 'react';
import axios from 'axios';

const RenameTopicModal = ({ onClose, topicType, currentName, onUpdate, showAlert }) => {
  const [newName, setNewName] = useState('');
  const [renaming, setRenaming] = useState(false);

  const handleRename = async () => {
    if (!newName.trim()) {
      showAlert('Please enter a new name', 'error');
      return;
    }

    setRenaming(true);
    try {
      const response = await axios.post('/rename_topic', {
        topic_type: topicType,
        old_name: currentName,
        new_name: newName
      });

      if (response.data.success) {
        onUpdate(response.data.classification);
        showAlert(`${topicType === 'main' ? 'Main topic' : 'Subtopic'} renamed successfully!`, 'success');
        onClose();
      } else {
        showAlert('Error renaming topic', 'error');
      }
    } catch (error) {
      showAlert('Network error while renaming topic', 'error');
    } finally {
      setRenaming(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>✏️ Rename Topic</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>
        
        <div className="form-group">
          <label htmlFor="currentName">Current Name:</label>
          <input
            type="text"
            id="currentName"
            value={currentName}
            readOnly
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="newName">New Name:</label>
          <input
            type="text"
            id="newName"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            placeholder="Enter new name..."
          />
        </div>
        
        <div className="form-actions">
          <button className="btn" onClick={onClose}>Cancel</button>
          <button 
            className="btn btn-success" 
            onClick={handleRename}
            disabled={renaming}
          >
            {renaming ? 'Renaming...' : '✏️ Rename'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RenameTopicModal; 