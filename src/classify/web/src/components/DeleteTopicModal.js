import React, { useState } from 'react';
import axios from 'axios';

const DeleteTopicModal = ({ onClose, classification, onUpdate, showAlert }) => {
  const [topicType, setTopicType] = useState('main');
  const [selectedMainTopic, setSelectedMainTopic] = useState('');
  const [selectedSubtopic, setSelectedSubtopic] = useState('');
  const [deleting, setDeleting] = useState(false);

  // Initialize with first main topic
  React.useEffect(() => {
    if (classification && Object.keys(classification.main_topics || {}).length > 0) {
      const firstMainTopic = Object.keys(classification.main_topics)[0];
      setSelectedMainTopic(firstMainTopic);
      
      // Set first subtopic if available
      const subtopics = classification.main_topics[firstMainTopic]?.subtopics || {};
      if (Object.keys(subtopics).length > 0) {
        setSelectedSubtopic(Object.keys(subtopics)[0]);
      }
    }
  }, [classification]);

  // Update subtopic list when main topic changes
  React.useEffect(() => {
    if (topicType === 'sub' && selectedMainTopic && classification) {
      const subtopics = classification.main_topics[selectedMainTopic]?.subtopics || {};
      if (Object.keys(subtopics).length > 0) {
        setSelectedSubtopic(Object.keys(subtopics)[0]);
      } else {
        setSelectedSubtopic('');
      }
    }
  }, [selectedMainTopic, topicType, classification]);

  const getTopicToDelete = () => {
    return topicType === 'main' ? selectedMainTopic : selectedSubtopic;
  };

  const getDisplayName = () => {
    const topicName = getTopicToDelete();
    return topicType === 'main' ? topicName : `${selectedMainTopic} > ${topicName}`;
  };

  const getTaskCount = () => {
    if (topicType === 'main' && selectedMainTopic && classification) {
      return classification.main_topics[selectedMainTopic]?.total_tasks || 0;
    } else if (topicType === 'sub' && selectedMainTopic && selectedSubtopic && classification) {
      return classification.main_topics[selectedMainTopic]?.subtopics?.[selectedSubtopic]?.task_count || 0;
    }
    return 0;
  };

  const getAvailableSubtopics = () => {
    if (!selectedMainTopic || !classification) return [];
    return Object.keys(classification.main_topics[selectedMainTopic]?.subtopics || {});
  };

  const canDelete = () => {
    const topicToDelete = getTopicToDelete();
    const taskCount = getTaskCount();
    return topicToDelete && taskCount === 0;
  };

  const handleDelete = async () => {
    const topicToDelete = getTopicToDelete();
    
    if (!topicToDelete) {
      showAlert('Please select a topic to delete', 'error');
      return;
    }

    const taskCount = getTaskCount();
    if (taskCount > 0) {
      showAlert(`Cannot delete - this ${topicType === 'main' ? 'topic' : 'subtopic'} contains ${taskCount} tasks. Please move or delete all tasks first.`, 'error');
      return;
    }
    
    setDeleting(true);
    
    try {
      const response = await axios.post('/delete_topic', {
        topic_type: topicType,
        topic_name: topicToDelete,
        main_topic_name: topicType === 'sub' ? selectedMainTopic : undefined
      });

      if (response.data.success) {
        onUpdate(response.data.classification);
        showAlert(response.data.message, 'success');
        onClose();
      } else {
        showAlert('Error deleting topic: ' + response.data.error, 'error');
      }
    } catch (error) {
      showAlert('Network error while deleting topic', 'error');
    } finally {
      setDeleting(false);
    }
  };

  const availableSubtopics = getAvailableSubtopics();
  const topicToDelete = getTopicToDelete();
  const taskCount = getTaskCount();
  const canDeleteTopic = canDelete();

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>🗑️ Delete Topic</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>
        
        <div className="form-group">
          <label htmlFor="topicType">Topic Type:</label>
          <select
            id="topicType"
            value={topicType}
            onChange={(e) => setTopicType(e.target.value)}
          >
            <option value="main">Main Topic</option>
            <option value="sub">Subtopic</option>
          </select>
        </div>
        
        <div className="form-group">
          <label htmlFor="selectedMainTopic">Main Topic:</label>
          <select
            id="selectedMainTopic"
            value={selectedMainTopic}
            onChange={(e) => setSelectedMainTopic(e.target.value)}
          >
            {Object.keys(classification?.main_topics || {}).map(mainTopic => (
              <option key={mainTopic} value={mainTopic}>
                {mainTopic}
              </option>
            ))}
          </select>
        </div>

        {topicType === 'sub' && (
          <div className="form-group">
            <label htmlFor="selectedSubtopic">Subtopic:</label>
            <select
              id="selectedSubtopic"
              value={selectedSubtopic}
              onChange={(e) => setSelectedSubtopic(e.target.value)}
              disabled={availableSubtopics.length === 0}
            >
              {availableSubtopics.length > 0 ? (
                availableSubtopics.map(subtopic => (
                  <option key={subtopic} value={subtopic}>
                    {subtopic}
                  </option>
                ))
              ) : (
                <option value="">No subtopics available</option>
              )}
            </select>
          </div>
        )}
        
        {topicToDelete && (
          <>
            <div className="form-group">
              <label>Topic to Delete:</label>
              <div style={{ 
                padding: '12px', 
                background: taskCount > 0 ? '#fed7d7' : '#f0fff4', 
                border: `2px solid ${taskCount > 0 ? '#feb2b2' : '#9ae6b4'}`, 
                borderRadius: '8px',
                fontWeight: 'bold',
                color: taskCount > 0 ? '#742a2a' : '#276749'
              }}>
                {getDisplayName()}
              </div>
            </div>
            
            <div className="form-group">
              <label>Task Count:</label>
              <div style={{ 
                padding: '8px 12px', 
                background: taskCount > 0 ? '#fff5f5' : '#f0fff4',
                border: `1px solid ${taskCount > 0 ? '#fed7d7' : '#c6f6d5'}`,
                borderRadius: '4px',
                fontSize: '14px',
                color: taskCount > 0 ? '#742a2a' : '#276749'
              }}>
                {taskCount > 0 ? (
                  <>⚠️ Contains {taskCount} tasks - cannot delete</>
                ) : (
                  <>✅ Empty - can be deleted</>
                )}
              </div>
            </div>

            {taskCount > 0 && (
              <div style={{
                padding: '12px',
                background: '#fff8e1',
                border: '1px solid #ffcc02',
                borderRadius: '8px',
                fontSize: '14px',
                color: '#8a5100'
              }}>
                💡 <strong>To delete this {topicType === 'main' ? 'topic' : 'subtopic'}:</strong><br/>
                Please move all {taskCount} tasks to other {topicType === 'main' ? 'topics' : 'subtopics'} first, or delete them individually.
              </div>
            )}
          </>
        )}
        
        <div className="form-actions">
          <button className="btn" onClick={onClose} disabled={deleting}>
            Cancel
          </button>
          <button 
            className="btn btn-danger" 
            onClick={handleDelete}
            disabled={deleting || !canDeleteTopic}
            title={!canDeleteTopic ? 'Cannot delete - topic contains tasks' : 'Delete empty topic'}
          >
            {deleting ? 'Deleting...' : '🗑️ Delete'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeleteTopicModal; 