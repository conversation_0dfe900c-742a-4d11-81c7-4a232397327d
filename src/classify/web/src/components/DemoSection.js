import React, { useState } from 'react';
import axios from 'axios';

const DemoSection = ({ currentTache, showAlert }) => {
  const [taskContent, setTaskContent] = useState('');
  const [result, setResult] = useState(null);
  const [testing, setTesting] = useState(false);

  const handleTest = async () => {
    if (!taskContent.trim()) {
      showAlert('Please enter task content to test', 'error');
      return;
    }

    setTesting(true);
    try {
      const response = await axios.post('/demo_classification', {
        task_content: taskContent,
        tache_number: currentTache || 1
      });

      if (response.data.success) {
        setResult(response.data.classification);
      } else {
        setResult({ error: response.data.error });
      }
    } catch (error) {
      setResult({ error: 'Network error during classification' });
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="demo-section">
      <h3>🧪 Test Classification</h3>
      <p>Test how new tasks would be classified using your current checkpoint:</p>
      
      <div className="form-group">
        <label htmlFor="demoTaskContent">Task Content:</label>
        <textarea
          id="demoTaskContent"
          rows="3"
          value={taskContent}
          onChange={(e) => setTaskContent(e.target.value)}
          placeholder="Enter a task description to test classification..."
        />
      </div>
      
      <button 
        className="btn" 
        onClick={handleTest}
        disabled={testing}
      >
        {testing ? 'Classifying...' : '🎯 Classify Task'}
      </button>
      
      {result && (
        <div className="demo-result">
          {result.error ? (
            <p style={{ color: 'red' }}>Error: {result.error}</p>
          ) : (
            <>
              <h4>🎯 Classification Result:</h4>
              <p><strong>Main Topic:</strong> {result.main_topic}</p>
              <p><strong>Subtopic:</strong> {result.subtopic}</p>
              <p><strong>Similarity:</strong> {(result.similarity * 100).toFixed(1)}%</p>
              <p><strong>Confidence:</strong> {result.confidence}</p>
              {result.reference_task_id && (
                <p><strong>Reference Task:</strong> {result.reference_task_id}</p>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default DemoSection; 