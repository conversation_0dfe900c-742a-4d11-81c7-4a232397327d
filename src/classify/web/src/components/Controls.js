import React from 'react';

const Controls = ({
  selectedTache,
  setSelectedTache,
  searchQuery,
  setSearchQuery,
  onLoadClassification,
  onShowCheckpointModal,
  onShowSaveModal,
  onShowCreateTopicModal,
  onShowDeleteTopicModal,
  onSearch,
  onRefresh,
  onCleanupEmpty,
  loading
}) => {
  const handleSearchKeyPress = (e) => {
    if (e.key === 'Enter') {
      onSearch();
    }
  };

  return (
    <div className="controls">
      <div className="control-row">
        <div className="input-group">
          <label htmlFor="tacheSelect">Tâche:</label>
          <select 
            id="tacheSelect"
            value={selectedTache}
            onChange={(e) => setSelectedTache(e.target.value)}
          >
            <option value="">Select Tâche</option>
            <option value="1">Tâche 1 (Email)</option>
            <option value="2">Tâche 2 (Article)</option>
            <option value="3">Tâche 3 (Argumentative)</option>
          </select>
        </div>
        <button 
          className="btn" 
          onClick={onLoadClassification}
          disabled={loading}
        >
          📂 {loading ? 'Loading...' : 'Load Auto Classification'}
        </button>
        <button className="btn" onClick={onShowCheckpointModal}>
          📋 Load Checkpoint
        </button>
        <button className="btn btn-success" onClick={onShowSaveModal}>
          💾 Save Checkpoint
        </button>
      </div>
      
      <div className="control-row">
        <div className="input-group">
          <input
            type="text"
            className="search-box"
            placeholder="Search tasks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={handleSearchKeyPress}
          />
          <button className="btn" onClick={onSearch}>
            🔍 Search
          </button>
        </div>
        <button className="btn" onClick={onShowCreateTopicModal}>
          ➕ Create Topic
        </button>
        <button className="btn btn-danger" onClick={onShowDeleteTopicModal}>
          🗑️ Delete Topic
        </button>
        <button className="btn" onClick={onRefresh}>
          🔄 Refresh
        </button>
        <button 
          className="btn" 
          onClick={onCleanupEmpty}
          title="Remove empty topics and subtopics"
        >
          🧹 Cleanup Empty
        </button>
      </div>
    </div>
  );
};

export default Controls; 