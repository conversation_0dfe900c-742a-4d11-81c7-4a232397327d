import React, { useState, useEffect } from 'react';
import axios from 'axios';

const LoadCheckpointModal = ({ onClose, onLoad, showAlert }) => {
  const [checkpoints, setCheckpoints] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCheckpointList();
  }, []);

  const loadCheckpointList = async () => {
    try {
      const response = await axios.get('/list_checkpoints');
      setCheckpoints(response.data.checkpoints || []);
    } catch (error) {
      showAlert('Error loading checkpoints', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadCheckpoint = async (checkpointPath) => {
    try {
      const response = await axios.post('/load_checkpoint', {
        checkpoint_path: checkpointPath
      });

      if (response.data.success) {
        onLoad(response.data.classification, response.data.tache_number);
        showAlert('Checkpoint loaded successfully!', 'success');
        onClose();
      } else {
        showAlert('Error loading checkpoint: ' + response.data.error, 'error');
      }
    } catch (error) {
      showAlert('Network error while loading checkpoint', 'error');
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>📋 Load Checkpoint</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>
        
        <div className="checkpoint-list">
          {loading ? (
            <p>Loading checkpoints...</p>
          ) : checkpoints.length === 0 ? (
            <p>No checkpoints found</p>
          ) : (
            checkpoints.map((checkpoint, index) => (
              <div
                key={index}
                className="checkpoint-item"
                onClick={() => handleLoadCheckpoint(checkpoint.path)}
              >
                <strong>{checkpoint.name}</strong> (Tâche {checkpoint.tache})
                <br />
                <small>{checkpoint.description}</small>
                <br />
                <small>
                  Created: {checkpoint.created.substring(0, 19)} | {checkpoint.modifications} modifications
                </small>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default LoadCheckpointModal; 