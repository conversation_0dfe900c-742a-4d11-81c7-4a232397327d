import React, { useState } from 'react';
import { Droppable } from 'react-beautiful-dnd';
import Task from './Task';

const Subtopic = ({ name, data, mainTopicName, onRename }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleRename = (e) => {
    e.stopPropagation();
    onRename('sub', name, mainTopicName);
  };

  const droppableId = `${mainTopicName}:::${name}`;
  
  console.log(`🎯 Subtopic "${name}" has droppableId: "${droppableId}"`);

  // Handle both old format (tasks) and new format (task_entries)
  // For merged checkpoints, we need to show BOTH formats
  const hasTaskEntries = data.task_entries && Array.isArray(data.task_entries);
  const hasTasks = data.tasks && Array.isArray(data.tasks);

  // Combine both formats for merged checkpoints
  const tasksToRender = [];
  if (hasTaskEntries) {
    tasksToRender.push(...data.task_entries.map(task => ({ ...task, format: 'task_entries' })));
  }
  if (hasTasks) {
    tasksToRender.push(...data.tasks.map(task => ({ ...task, format: 'tasks' })));
  }
  
  // Calculate task counts
  const uniqueTaskCount = data.unique_task_count || (hasTaskEntries ? data.task_entries.length : data.task_count || 0);
  const totalTaskCount = data.task_count || tasksToRender.length;
  const hasDeduplication = hasTaskEntries && uniqueTaskCount !== totalTaskCount;

  return (
    <div className="subtopic">
      <Droppable 
        droppableId={droppableId}
        key={droppableId}
      >
        {(provided, snapshot) => {
          if (snapshot.isDraggingOver) {
            console.log(`🎯 Task being dragged over subtopic "${name}" (${droppableId})`);
          }
          
          return (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              <div 
                className={`subtopic-header ${snapshot.isDraggingOver ? 'drag-target-active' : ''}`}
                onClick={handleToggle}
              >
                <div>
                  <span className={`toggle-icon ${isExpanded ? 'rotated' : ''}`}>
                    {isExpanded ? '▼' : '▶'}
                  </span>
                  <span>{name}</span>
                  {snapshot.isDraggingOver && (
                    <span className="drop-target-indicator">
                      📥 Drop here
                    </span>
                  )}
                  {!snapshot.isDraggingOver && (
                    <small className="drop-zone-indicator">Drop tasks here</small>
                  )}
                </div>
                <div>
                  <span className="task-count">
                    {hasDeduplication ? (
                      <span>
                        {uniqueTaskCount} unique ({totalTaskCount} total)
                      </span>
                    ) : (
                      <span>{totalTaskCount} tasks</span>
                    )}
                  </span>
                  <button 
                    className="btn" 
                    style={{ marginLeft: '10px', padding: '3px 8px', fontSize: '0.7em' }}
                    onClick={handleRename}
                    title="Rename subtopic"
                  >
                    ✏️
                  </button>
                </div>
              </div>
              
              {isExpanded && (
                <div className="tasks">
                  {tasksToRender && tasksToRender.length > 0 ? (
                    tasksToRender.map((task, index) => (
                      <Task
                        key={task.format === 'task_entries' ? task.representative_id : task.id}
                        task={task}
                        index={index}
                      />
                    ))
                  ) : (
                    <p style={{ color: '#a0aec0', textAlign: 'center', padding: '20px' }}>
                      No tasks in this subtopic
                    </p>
                  )}
                </div>
              )}
              
              {/* Hidden drop zone for collapsed subtopics */}
              {!isExpanded && snapshot.isDraggingOver && (
                <div 
                  className="collapsed-drop-indicator"
                  style={{
                    background: '#e3f2fd',
                    border: '2px dashed #2196f3',
                    borderRadius: '8px',
                    padding: '12px',
                    margin: '8px 16px',
                    textAlign: 'center',
                    color: '#2196f3',
                    fontWeight: 'bold',
                    fontSize: '0.9em',
                    animation: 'pulse 1.5s infinite'
                  }}
                >
                  ✨ Release to add task to "{name}" ✨
                </div>
              )}
              
              {provided.placeholder}
            </div>
          );
        }}
      </Droppable>
    </div>
  );
};

export default Subtopic; 