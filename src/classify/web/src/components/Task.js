import React, { useState } from 'react';
import { Draggable } from 'react-beautiful-dnd';

const Task = ({ task, index }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Handle both old format (task.task_content) and new format (task_entry)
  const isTaskEntry = task.task_ids && Array.isArray(task.task_ids);
  const taskContent = isTaskEntry ? task.task_content : task.task_content;
  const taskId = isTaskEntry ? task.representative_id : task.id;
  const allTaskIds = isTaskEntry ? task.task_ids : [task.id];
  const isDuplicateGroup = isTaskEntry && task.is_duplicate_group;
  const duplicateCount = isTaskEntry ? task.duplicate_count : 1;
  
  const shouldTruncate = taskContent.length > 100;
  const shortContent = shouldTruncate ? 
    taskContent.substring(0, 100) + '...' : 
    taskContent;

  const handleToggleContent = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Draggable 
      draggableId={taskId} 
      index={index}
      key={taskId}
    >
      {(provided, snapshot) => {
        if (snapshot.isDragging) {
          console.log(`🎯 Task "${taskId}" is being dragged`);
        }
        
        return (
          <div
            ref={provided.innerRef}
            {...provided.draggableProps}
            {...provided.dragHandleProps}
            className={`task ${snapshot.isDragging ? 'dragging' : ''} ${isDuplicateGroup ? 'duplicate-group' : ''}`}
          >
            <div className="task-id">
              {isDuplicateGroup ? (
                <div className="duplicate-group-info">
                  <div className="main-id">[{taskId}]</div>
                  <div className="duplicate-indicator">
                    🔗 Group of {duplicateCount}
                  </div>
                  {isExpanded && allTaskIds.length > 1 && (
                    <div className="all-ids">
                      All IDs: {allTaskIds.join(', ')}
                    </div>
                  )}
                </div>
              ) : (
                <div>[{taskId}]</div>
              )}
            </div>
            <div className={`task-content ${isExpanded ? 'expanded' : 'truncated'}`}>
              {isExpanded || !shouldTruncate ? taskContent : shortContent}
            </div>
            {shouldTruncate && (
              <div className="expand-btn" onClick={handleToggleContent}>
                {isExpanded ? 'Show less...' : 'Show more...'}
              </div>
            )}
          </div>
        );
      }}
    </Draggable>
  );
};

export default Task; 