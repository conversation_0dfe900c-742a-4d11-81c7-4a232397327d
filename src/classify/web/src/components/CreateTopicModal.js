import React, { useState } from 'react';
import axios from 'axios';

const CreateTopicModal = ({ onClose, classification, onUpdate, showAlert }) => {
  const [topicType, setTopicType] = useState('main');
  const [topicName, setTopicName] = useState('');
  const [parentMainTopic, setParentMainTopic] = useState('');
  const [creating, setCreating] = useState(false);

  React.useEffect(() => {
    if (classification && Object.keys(classification.main_topics || {}).length > 0) {
      setParentMainTopic(Object.keys(classification.main_topics)[0]);
    }
  }, [classification]);

  const handleCreate = async () => {
    if (!topicName.trim()) {
      showAlert('Please enter a topic name', 'error');
      return;
    }

    if (!classification) {
      showAlert('No classification loaded', 'error');
      return;
    }

    const requestData = {
      topic_type: topicType,
      topic_name: topicName
    };

    if (topicType === 'sub') {
      requestData.main_topic_name = parentMainTopic;
    }

    setCreating(true);
    try {
      const response = await axios.post('/create_topic', requestData);

      if (response.data.success) {
        onUpdate(response.data.classification);
        showAlert(`${topicType === 'main' ? 'Main topic' : 'Subtopic'} created successfully!`, 'success');
        onClose();
      } else {
        showAlert('Error creating topic', 'error');
      }
    } catch (error) {
      showAlert('Network error while creating topic', 'error');
    } finally {
      setCreating(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>➕ Create New Topic</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>
        
        <div className="form-group">
          <label htmlFor="topicType">Topic Type:</label>
          <select
            id="topicType"
            value={topicType}
            onChange={(e) => setTopicType(e.target.value)}
          >
            <option value="main">Main Topic</option>
            <option value="sub">Subtopic</option>
          </select>
        </div>
        
        {topicType === 'sub' && (
          <div className="form-group">
            <label htmlFor="parentMainTopic">Parent Main Topic:</label>
            <select
              id="parentMainTopic"
              value={parentMainTopic}
              onChange={(e) => setParentMainTopic(e.target.value)}
            >
              {Object.keys(classification?.main_topics || {}).map(mainTopic => (
                <option key={mainTopic} value={mainTopic}>
                  {mainTopic}
                </option>
              ))}
            </select>
          </div>
        )}
        
        <div className="form-group">
          <label htmlFor="newTopicName">Topic Name:</label>
          <input
            type="text"
            id="newTopicName"
            value={topicName}
            onChange={(e) => setTopicName(e.target.value)}
            placeholder="Enter topic name..."
          />
        </div>
        
        <div className="form-actions">
          <button className="btn" onClick={onClose}>Cancel</button>
          <button 
            className="btn btn-success" 
            onClick={handleCreate}
            disabled={creating}
          >
            {creating ? 'Creating...' : '➕ Create'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateTopicModal; 