import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Droppable } from 'react-beautiful-dnd';
import Task from './Task';

const TaskBuffer = ({ bufferTasks = [], isMinimized, onToggleMinimize, onClearBuffer }) => {
  const taskCount = bufferTasks.length;
  const bufferRef = useRef(null);
  const dragRef = useRef({
    isDragging: false,
    startX: 0,
    startY: 0,
    initialX: 0,
    initialY: 0
  });

  // Position state - start at top-right but allow dragging
  const [position, setPosition] = useState(() => {
    // Load saved position from localStorage or use default
    const saved = localStorage.getItem('bufferPosition');
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (e) {
        // If parsing fails, use default
      }
    }
    return { x: window.innerWidth - 350, y: 20 }; // Default top-right
  });

  const [isDragging, setIsDragging] = useState(false);

  // Save position to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('bufferPosition', JSON.stringify(position));
  }, [position]);

  // Handle window resize to keep buffer on screen
  useEffect(() => {
    const handleResize = () => {
      const buffer = bufferRef.current;
      if (!buffer) return;

      const rect = buffer.getBoundingClientRect();
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      let newX = position.x;
      let newY = position.y;

      // Keep buffer within screen bounds
      if (newX + rect.width > windowWidth) {
        newX = windowWidth - rect.width - 20;
      }
      if (newX < 20) {
        newX = 20;
      }
      if (newY + rect.height > windowHeight) {
        newY = windowHeight - rect.height - 20;
      }
      if (newY < 20) {
        newY = 20;
      }

      if (newX !== position.x || newY !== position.y) {
        setPosition({ x: newX, y: newY });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [position]);

  const handleMouseDown = (e) => {
    // Only start dragging if clicking on the header area, not the toggle button
    if (e.target.closest('.buffer-toggle')) {
      return;
    }

    e.preventDefault();
    setIsDragging(true);
    
    const rect = bufferRef.current.getBoundingClientRect();
    dragRef.current = {
      isDragging: true,
      startX: e.clientX,
      startY: e.clientY,
      initialX: rect.left,
      initialY: rect.top
    };

    // Add global mouse event listeners
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleMouseMove = (e) => {
    if (!dragRef.current.isDragging) return;

    e.preventDefault();
    
    const deltaX = e.clientX - dragRef.current.startX;
    const deltaY = e.clientY - dragRef.current.startY;
    
    let newX = dragRef.current.initialX + deltaX;
    let newY = dragRef.current.initialY + deltaY;

    // Keep buffer within screen bounds
    const buffer = bufferRef.current;
    if (buffer) {
      const rect = buffer.getBoundingClientRect();
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      if (newX < 0) newX = 0;
      if (newY < 0) newY = 0;
      if (newX + rect.width > windowWidth) newX = windowWidth - rect.width;
      if (newY + rect.height > windowHeight) newY = windowHeight - rect.height;
    }

    setPosition({ x: newX, y: newY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    dragRef.current.isDragging = false;
    
    // Remove global mouse event listeners
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  const handleToggleClick = (e) => {
    e.stopPropagation(); // Prevent drag from starting
    onToggleMinimize();
  };

  // Render buffer as a portal to avoid react-beautiful-dnd conflicts
  const bufferElement = (
    <div 
      ref={bufferRef}
      className={`task-buffer ${isMinimized ? 'minimized' : ''} ${isDragging ? 'dragging' : ''}`}
      style={{
        position: 'absolute',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 1000
      }}
    >
      {/* Header */}
      <div 
        className="buffer-header"
        onMouseDown={handleMouseDown}
        style={{ cursor: isDragging ? 'grabbing' : 'move' }}
      >
        <div className="buffer-title">
          <span className="buffer-icon">📝</span>
          <span>Task Buffer</span>
          <span className="buffer-count">({taskCount})</span>
          <span className="drag-indicator">⋮⋮</span>
        </div>
        <div className="buffer-toggle" onClick={handleToggleClick}>
          {isMinimized ? '▲' : '▼'}
        </div>
      </div>

      {/* Content */}
      {!isMinimized && (
        <div className="buffer-content">
          {/* Controls */}
          <div className="buffer-controls">
            <button 
              className="buffer-clear-btn"
              onClick={onClearBuffer}
              disabled={taskCount === 0}
              title="Clear all tasks from buffer"
            >
              🗑️ Clear All
            </button>
          </div>

          {/* Drop Zone */}
          <Droppable 
            droppableId="task-buffer"
            isDropDisabled={false}
            ignoreContainerClipping={true}
          >
            {(provided, snapshot) => {
              // Log drag interactions
              if (snapshot.isDraggingOver) {
                console.log('🎯 TaskBuffer: Task being dragged over buffer');
              }
              
              return (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className={`buffer-drop-zone ${snapshot.isDraggingOver ? 'dragging-over' : ''}`}
                  style={{
                    overflow: 'visible',
                    position: 'relative'
                  }}
                >
                  {taskCount === 0 ? (
                    <div className="buffer-empty">
                      <p>💭 Drag tasks here to review later</p>
                      <small>Tasks will be removed from their current location</small>
                    </div>
                  ) : (
                    <div className="buffer-tasks">
                      {bufferTasks.map((task, index) => (
                        <Task
                          key={task.representative_id || task.id}
                          task={task}
                          index={index}
                        />
                      ))}
                    </div>
                  )}
                  {provided.placeholder}
                </div>
              );
            }}
          </Droppable>

          {/* Info */}
          <div className="buffer-info">
            <small>💡 Drag buffered tasks to topics when ready</small>
          </div>
        </div>
      )}
    </div>
  );

  // Don't use portal, render directly to avoid conflicts
  return bufferElement;
};

export default TaskBuffer; 