import React, { useState } from 'react';
import axios from 'axios';

const SaveCheckpointModal = ({ onClose, showAlert, hasClassification }) => {
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!name.trim()) {
      showAlert('Please enter a checkpoint name', 'error');
      return;
    }

    if (!hasClassification) {
      showAlert('No classification loaded to save', 'error');
      return;
    }

    setSaving(true);
    try {
      const response = await axios.post('/save_checkpoint', {
        checkpoint_name: name,
        description: description
      });

      if (response.data.success) {
        showAlert('Checkpoint saved successfully!', 'success');
        onClose();
      } else {
        showAlert('Error saving checkpoint', 'error');
      }
    } catch (error) {
      showAlert('Network error while saving checkpoint', 'error');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>💾 Save Checkpoint</h2>
          <button className="close-btn" onClick={onClose}>&times;</button>
        </div>
        
        <div className="form-group">
          <label htmlFor="checkpointName">Checkpoint Name:</label>
          <input
            type="text"
            id="checkpointName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g., final_review_v1"
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="checkpointDescription">Description:</label>
          <textarea
            id="checkpointDescription"
            rows="3"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Describe the changes made..."
          />
        </div>
        
        <div className="form-actions">
          <button className="btn" onClick={onClose}>Cancel</button>
          <button 
            className="btn btn-success" 
            onClick={handleSave}
            disabled={saving}
          >
            {saving ? 'Saving...' : '💾 Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SaveCheckpointModal; 