import React from 'react';
import MainTopic from './MainTopic';

const ClassificationContainer = ({ classification, onRename }) => {
  if (!classification) {
    return (
      <div className="classification-container">
        <p>No classification loaded</p>
      </div>
    );
  }

  return (
    <div className="classification-container">
      {Object.entries(classification.main_topics || {}).map(([mainTopicName, mainTopicInfo]) => (
        <MainTopic
          key={`maintopic-${mainTopicName}`}
          name={mainTopicName}
          data={mainTopicInfo}
          totalTasks={classification.total_tasks}
          onRename={onRename}
        />
      ))}
    </div>
  );
};

export default ClassificationContainer; 