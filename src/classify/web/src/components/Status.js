import React from 'react';

const Status = ({ classification, tache }) => {
  const getStatusMessage = () => {
    if (!classification) {
      return 'No classification loaded. Please load an automatic classification or checkpoint.';
    }
    
    const totalTasks = classification.total_tasks || 0;
    const mainTopicsCount = Object.keys(classification.main_topics || {}).length;
    
    return `Loaded Tâche ${tache} classification: ${totalTasks} tasks, ${mainTopicsCount} main topics`;
  };

  return (
    <div className={`status ${classification ? 'loaded' : ''}`}>
      <h3>📊 Status</h3>
      <p>{getStatusMessage()}</p>
    </div>
  );
};

export default Status; 