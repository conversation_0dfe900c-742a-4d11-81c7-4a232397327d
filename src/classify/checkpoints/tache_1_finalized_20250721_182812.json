{"metadata": {"checkpoint_name": "finalized", "description": "", "tache_number": 1, "created_timestamp": "2025-07-21T18:28:12.484603", "total_modifications": 424, "modifications_log": [{"action": "move_task", "task_id": "juillet-2024_c10_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:14:51.237255"}, {"action": "move_task", "task_id": "juillet-2024_c31_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:15:05.868785"}, {"action": "move_task", "task_id": "fevrier-2025_c25_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:15:45.911523"}, {"action": "move_task", "task_id": "novembre-2024_c4_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:16:05.542144"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_university", "timestamp": "2025-06-01T01:17:03.412027"}, {"action": "move_task", "task_id": "aout-2024_c1_t1", "from": "description_places > description_general_place", "to": "description_places > description_university", "timestamp": "2025-06-01T01:19:09.732772"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_restaurant", "timestamp": "2025-06-01T01:19:39.091178"}, {"action": "move_task", "task_id": "decembre-2024_c14_t1", "from": "description_places > description_general_place", "to": "description_places > description_restaurant", "timestamp": "2025-06-01T01:19:50.470555"}, {"action": "move_task", "task_id": "avril-2025_c15_t1", "from": "description_places > description_general_place", "to": "description_places > description_restaurant", "timestamp": "2025-06-01T01:19:58.477242"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_hotel", "timestamp": "2025-06-01T01:23:25.198657"}, {"action": "move_task", "task_id": "avril-2025_c20_t1", "from": "description_places > description_general_place", "to": "description_places > description_hotel", "timestamp": "2025-06-01T01:23:56.231553"}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_general_place", "new_name": "description_countryside", "timestamp": "2025-06-01T01:24:39.351421"}, {"action": "move_task", "task_id": "octobre-2024_c10_t1", "from": "description_places > description_tourist_place", "to": "description_places > description_city", "timestamp": "2025-06-01T01:25:41.640160"}, {"action": "move_task", "task_id": "juillet-2024_c24_t1", "from": "description_places > description_office", "to": "description_places > description_hotel", "timestamp": "2025-06-01T01:26:51.692091"}, {"timestamp": "2025-06-01T04:07:21.730153", "action": "move_to_buffer", "task_id": "octobre-2024_c5_t1", "source_main_topic": "description_places", "source_subtopic": "description_countryside", "task_content": "écrivez un message à votre ami pour lui décrire le..."}, {"timestamp": "2025-06-01T04:07:45.177418", "action": "move_to_buffer", "task_id": "septembre-2024_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_countryside", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-01T04:08:11.365157", "action": "move_to_buffer", "task_id": "novembre-2024_c20_t1", "source_main_topic": "description_places", "source_subtopic": "description_office", "task_content": "Votre ami souhaite commencer à faire du sport. Réd..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_gym", "timestamp": "2025-06-01T04:08:29.029923"}, {"timestamp": "2025-06-01T04:08:38.395617", "action": "move_from_buffer", "task_id": "novembre-2024_c20_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Votre ami souhaite commencer à faire du sport. Réd..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_tourist_place", "new_name": "description_park", "timestamp": "2025-06-01T04:09:59.565130"}, {"timestamp": "2025-06-01T04:11:06.867540", "action": "move_to_buffer", "task_id": "septembre-2024_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:11:11.684642", "action": "move_to_buffer", "task_id": "septembre-2024_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Votre ami veut se mettre au sport. Vous lui envoye..."}, {"timestamp": "2025-06-01T04:11:33.054703", "action": "move_to_buffer", "task_id": "novembre-2024_c14_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:11:53.125678", "action": "move_from_buffer", "task_id": "septembre-2024_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:12:05.573177", "action": "move_from_buffer", "task_id": "novembre-2024_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:12:23.312665", "action": "move_from_buffer", "task_id": "septembre-2024_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Votre ami veut se mettre au sport. Vous lui envoye..."}, {"timestamp": "2025-06-01T04:12:40.013489", "action": "move_from_buffer", "task_id": "octobre-2024_c5_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "écrivez un message à votre ami pour lui décrire le..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_venue", "new_name": "description_birthday_party", "timestamp": "2025-06-01T04:13:46.261413"}, {"timestamp": "2025-06-01T04:14:45.605202", "action": "move_to_buffer", "task_id": "juillet-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:14:57.733399", "action": "move_to_buffer", "task_id": "juillet-2024_c14_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:15:05.416025", "action": "move_to_buffer", "task_id": "juillet-2024_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:15:11.060099", "action": "move_to_buffer", "task_id": "juillet-2024_c20_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "<PERSON><PERSON>, Tu as commencé ton nouveau travail ! C’est ..."}, {"timestamp": "2025-06-01T04:15:21.572774", "action": "move_to_buffer", "task_id": "juillet-2024_c21_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-01T04:15:28.893483", "action": "move_to_buffer", "task_id": "juillet-2024_c22_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_wedding_place", "timestamp": "2025-06-01T04:15:54.928809"}, {"timestamp": "2025-06-01T04:16:09.272966", "action": "move_from_buffer", "task_id": "juillet-2024_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_shopping_place", "timestamp": "2025-06-01T04:16:23.548089"}, {"timestamp": "2025-06-01T04:16:34.510073", "action": "move_from_buffer", "task_id": "juillet-2024_c19_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:16:55.467309", "action": "move_from_buffer", "task_id": "juillet-2024_c22_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-01T04:16:59.388641", "action": "move_from_buffer", "task_id": "juillet-2024_c21_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-01T04:17:28.288066", "action": "move_from_buffer", "task_id": "juillet-2024_c20_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON><PERSON>, Tu as commencé ton nouveau travail ! C’est ..."}, {"timestamp": "2025-06-01T04:17:35.687206", "action": "move_from_buffer", "task_id": "septembre-2024_c10_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-01T04:18:05.914433", "action": "move_to_buffer", "task_id": "juillet-2024_c37_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:13.807425", "action": "move_to_buffer", "task_id": "decembre-2024_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:18.680330", "action": "move_to_buffer", "task_id": "decembre-2024_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:22.445585", "action": "move_to_buffer", "task_id": "janvier-2025_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:18:37.519705", "action": "move_to_buffer", "task_id": "janvier-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:18:41.526780", "action": "move_to_buffer", "task_id": "janvier-2025_c5_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:18:45.694592", "action": "move_to_buffer", "task_id": "janvier-2025_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-01T04:18:49.784460", "action": "move_to_buffer", "task_id": "janvier-2025_c11_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:06.228947", "action": "move_from_buffer", "task_id": "decembre-2024_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:14.039584", "action": "move_from_buffer", "task_id": "decembre-2024_c19_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:19.369378", "action": "move_from_buffer", "task_id": "juillet-2024_c37_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:22.711594", "action": "move_from_buffer", "task_id": "janvier-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:19:29.713770", "action": "move_from_buffer", "task_id": "janvier-2025_c5_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:37.665925", "action": "move_from_buffer", "task_id": "janvier-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:50.540563", "action": "move_from_buffer", "task_id": "janvier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_countryside", "new_name": "description_outdoor_activity", "timestamp": "2025-06-01T04:20:11.154282"}, {"timestamp": "2025-06-01T04:20:22.604959", "action": "move_from_buffer", "task_id": "janvier-2025_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:20:31.731440", "action": "move_to_buffer", "task_id": "janvier-2025_c12_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "L’été est arrivé ! Je vous propose de faire un piq..."}, {"timestamp": "2025-06-01T04:20:39.181278", "action": "move_to_buffer", "task_id": "janvier-2025_c13_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Salut je vais venir en vacances dans ton pays tu p..."}, {"timestamp": "2025-06-01T04:20:41.557516", "action": "move_to_buffer", "task_id": "janvier-2025_c16_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:20:47.932444", "action": "move_to_buffer", "task_id": "aout-2024_c3_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:20:53.612184", "action": "move_to_buffer", "task_id": "fevrier-2025_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Léa t’a envoyé un message pour te proposer un piqu..."}, {"timestamp": "2025-06-01T04:21:03.212723", "action": "move_to_buffer", "task_id": "fevrier-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-01T04:21:07.258036", "action": "move_to_buffer", "task_id": "avril-2025_c3_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Votre ami(e) désire explorer la région dans laquel..."}, {"timestamp": "2025-06-01T04:21:25.190452", "action": "move_from_buffer", "task_id": "janvier-2025_c13_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Salut je vais venir en vacances dans ton pays tu p..."}, {"timestamp": "2025-06-01T04:21:34.268209", "action": "move_from_buffer", "task_id": "aout-2024_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:21:36.802691", "action": "move_from_buffer", "task_id": "janvier-2025_c16_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:21:45.147548", "action": "move_from_buffer", "task_id": "avril-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) désire explorer la région dans laquel..."}, {"timestamp": "2025-06-01T04:22:00.524493", "action": "move_from_buffer", "task_id": "fevrier-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Léa t’a envoyé un message pour te proposer un piqu..."}, {"timestamp": "2025-06-01T04:22:04.003638", "action": "move_from_buffer", "task_id": "janvier-2025_c12_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "L’été est arrivé ! Je vous propose de faire un piq..."}, {"timestamp": "2025-06-01T04:22:52.864955", "action": "move_to_buffer", "task_id": "novembre-2024_c1_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Écrire un message à votre amie pour lui proposer u..."}, {"timestamp": "2025-06-01T04:22:56.822772", "action": "move_to_buffer", "task_id": "novembre-2024_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:22:59.405795", "action": "move_to_buffer", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:02.643008", "action": "move_to_buffer", "task_id": "novembre-2024_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:23:05.685274", "action": "move_to_buffer", "task_id": "mars-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Rédigez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-01T04:23:08.084934", "action": "move_to_buffer", "task_id": "Mai-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-01T04:23:16.954611", "action": "move_to_buffer", "task_id": "octobre-2024_c1_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-01T04:23:20.022306", "action": "move_to_buffer", "task_id": "octobre-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:29.209839", "action": "move_to_buffer", "task_id": "octobre-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:54.793719", "action": "move_from_buffer", "task_id": "juillet-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:57.745448", "action": "move_from_buffer", "task_id": "fevrier-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-01T04:24:01.527170", "action": "move_from_buffer", "task_id": "novembre-2024_c1_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Écrire un message à votre amie pour lui proposer u..."}, {"timestamp": "2025-06-01T04:24:04.890343", "action": "move_from_buffer", "task_id": "novembre-2024_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:13.597219", "action": "move_from_buffer", "task_id": "mars-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_birthday_party", "task_content": "Rédigez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-01T04:24:21.400493", "action": "move_from_buffer", "task_id": "novembre-2024_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:24:35.178792", "action": "move_from_buffer", "task_id": "Mai-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-01T04:24:46.537477", "action": "move_from_buffer", "task_id": "octobre-2024_c1_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_help", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-01T04:24:50.537261", "action": "move_from_buffer", "task_id": "octobre-2024_c17_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:53.452643", "action": "move_from_buffer", "task_id": "octobre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:58.863318", "action": "move_from_buffer", "task_id": "novembre-2024_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-02T19:25:37.442524", "action": "move_to_buffer", "task_id": "juillet-2024_c6_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"timestamp": "2025-06-02T19:26:06.827260", "action": "move_task", "task_id": "juillet-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_housing", "new_name": "looking_for_roommate", "timestamp": "2025-06-02T19:26:27.540842"}, {"timestamp": "2025-06-02T19:26:43.051177", "action": "move_to_buffer", "task_id": "janvier-2025_c14_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_roommate", "task_content": "Vous allez déménager à Nice, en France. Vous écriv..."}, {"timestamp": "2025-06-02T19:27:04.747533", "action": "move_to_buffer", "task_id": "avril-2025_c2_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_roommate", "task_content": "Vous avez trouvé une annonce en ligne pour louer u..."}, {"timestamp": "2025-06-02T19:28:35.545794", "action": "move_to_buffer", "task_id": "juillet-2024_c11_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "task_content": "Votre ami <PERSON> vient de s’installer dans votre vi..."}, {"timestamp": "2025-06-02T19:28:53.151793", "action": "move_task", "task_id": "juillet-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_birthday_party", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T19:29:02.971231", "action": "move_task", "task_id": "juillet-2024_c23_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_sports_help", "timestamp": "2025-06-02T19:30:15.372298"}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_sports_help", "new_name": "looking_for_sport_help", "timestamp": "2025-06-02T19:30:28.531233"}, {"timestamp": "2025-06-02T19:31:07.437459", "action": "move_to_buffer", "task_id": "decembre-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:10.606577", "action": "move_to_buffer", "task_id": "janvier-2025_c7_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:18.198983", "action": "move_to_buffer", "task_id": "fevrier-2025_c14_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Rédigez un message à publier dans le journal de vo..."}, {"timestamp": "2025-06-02T19:31:22.907576", "action": "move_to_buffer", "task_id": "novembre-2024_c9_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:47.837745", "action": "move_from_buffer", "task_id": "janvier-2025_c7_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:54.960819", "action": "move_from_buffer", "task_id": "fevrier-2025_c14_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Rédigez un message à publier dans le journal de vo..."}, {"timestamp": "2025-06-02T19:31:59.374112", "action": "move_from_buffer", "task_id": "novembre-2024_c9_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_apartment", "timestamp": "2025-06-02T19:32:50.846191"}, {"timestamp": "2025-06-02T19:32:57.938950", "action": "move_from_buffer", "task_id": "avril-2025_c2_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_apartment", "task_content": "Vous avez trouvé une annonce en ligne pour louer u..."}, {"timestamp": "2025-06-02T19:33:05.263803", "action": "move_from_buffer", "task_id": "janvier-2025_c14_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_apartment", "task_content": "Vous allez déménager à Nice, en France. Vous écriv..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_transportation", "timestamp": "2025-06-02T19:34:02.112454"}, {"timestamp": "2025-06-02T19:34:09.924247", "action": "move_from_buffer", "task_id": "juillet-2024_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de s’installer dans votre vi..."}, {"timestamp": "2025-06-02T19:34:22.683814", "action": "move_from_buffer", "task_id": "decembre-2024_c15_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_gift", "timestamp": "2025-06-02T19:34:50.563855"}, {"timestamp": "2025-06-02T19:34:54.508303", "action": "move_from_buffer", "task_id": "juillet-2024_c6_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_birthday_party", "new_name": "description_party", "timestamp": "2025-06-02T20:07:00.360553"}, {"timestamp": "2025-06-02T20:07:27.639244", "action": "move_task", "task_id": "juillet-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T20:07:34.734633", "action": "move_task", "task_id": "juillet-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre ami va fêter son anniversaire. Écrivez un me..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_housekeeping", "timestamp": "2025-06-02T20:07:53.818444"}, {"timestamp": "2025-06-02T20:07:58.121786", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_help", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T20:16:18.290943", "action": "move_task", "task_id": "decembre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Écrivez un message pour inviter vos amis à une fêt..."}, {"timestamp": "2025-06-02T20:16:30.635908", "action": "move_task", "task_id": "fevrier-2025_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Tu loues un appartement qui est trop grand pour to..."}, {"timestamp": "2025-06-02T20:16:40.756828", "action": "move_task", "task_id": "fevrier-2025_c19_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T20:16:50.389570", "action": "move_task", "task_id": "fevrier-2025_c20_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_sport", "timestamp": "2025-06-02T20:18:37.190102"}, {"timestamp": "2025-06-02T20:18:49.743534", "action": "move_task", "task_id": "fevrier-2025_c22_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T20:18:59.468678", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_services", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T20:19:04.323917", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_services", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T20:19:23.406768", "action": "move_task", "task_id": "fevrier-2025_c23_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous êtes locataire d’un appartement qui vous semb..."}, {"timestamp": "2025-06-02T20:20:07.218573", "action": "move_task", "task_id": "avril-2025_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T20:20:23.711673", "action": "move_task", "task_id": "avril-2025_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Vous avez découvert une plateforme en ligne mettan..."}, {"timestamp": "2025-06-02T21:04:42.814294", "action": "move_to_buffer", "task_id": "fevrier-2025_c27_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:04:55.643358", "action": "move_to_buffer", "task_id": "avril-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:05:00.552715", "action": "move_task", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:05:07.944448", "action": "move_to_buffer", "task_id": "novembre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie...."}, {"timestamp": "2025-06-02T21:05:10.190932", "action": "move_to_buffer", "task_id": "novembre-2024_c15_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "“ <PERSON><PERSON>, j’ai appris que tu vas à une salle de spo..."}, {"timestamp": "2025-06-02T21:05:11.435424", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:05:12.786193", "action": "move_to_buffer", "task_id": "Mai-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Votre anniversaire approche. Vos amis vous posent ..."}, {"timestamp": "2025-06-02T21:05:14.146442", "action": "move_to_buffer", "task_id": "octobre-2024_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à vos amis pour leur dire que v..."}, {"timestamp": "2025-06-02T21:05:15.298233", "action": "move_to_buffer", "task_id": "octobre-2024_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:05:16.414312", "action": "move_to_buffer", "task_id": "octobre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:05:17.597338", "action": "move_to_buffer", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T21:05:19.127279", "action": "move_to_buffer", "task_id": "octobre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami qui a accepté de s’..."}, {"timestamp": "2025-06-02T21:05:20.302799", "action": "move_to_buffer", "task_id": "fevrier-2025_c25_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:05:21.684052", "action": "move_to_buffer", "task_id": "novembre-2024_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous voulez communiquer avec quelqu’un en français..."}, {"timestamp": "2025-06-02T21:05:22.878006", "action": "move_to_buffer", "task_id": "septembre-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-02T21:05:23.989019", "action": "move_to_buffer", "task_id": "fevrier-2025_c20_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"timestamp": "2025-06-02T21:05:25.257410", "action": "move_to_buffer", "task_id": "fevrier-2025_c22_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:05:26.523111", "action": "move_to_buffer", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:05:47.529225", "action": "move_from_buffer", "task_id": "Mai-2025_c1_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre anniversaire approche. Vos amis vous posent ..."}, {"timestamp": "2025-06-02T21:06:17.937528", "action": "move_from_buffer", "task_id": "juillet-2024_c31_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:06:23.510672", "action": "move_from_buffer", "task_id": "fevrier-2025_c22_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:06:31.566013", "action": "move_from_buffer", "task_id": "fevrier-2025_c20_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_apartment", "timestamp": "2025-06-02T21:07:16.972570"}, {"timestamp": "2025-06-02T21:07:22.183372", "action": "move_from_buffer", "task_id": "septembre-2024_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_apartment", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-02T21:07:27.009317", "action": "move_from_buffer", "task_id": "novembre-2024_c4_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Vous voulez communiquer avec quelqu’un en français..."}, {"timestamp": "2025-06-02T21:07:32.983734", "action": "move_from_buffer", "task_id": "octobre-2024_c13_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "écrivez un message à votre ami qui a accepté de s’..."}, {"timestamp": "2025-06-02T21:08:03.816702", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:08:14.779669", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "description_places", "source_subtopic": "description_hotel", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:08:20.164176", "action": "move_from_buffer", "task_id": "octobre-2024_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "écrivez un message à vos amis pour leur dire que v..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_outdoor_activity", "new_name": "description_trip", "timestamp": "2025-06-02T21:09:34.829598"}, {"timestamp": "2025-06-02T21:09:56.785124", "action": "move_to_buffer", "task_id": "juillet-2024_c22_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:07.073608", "action": "move_to_buffer", "task_id": "juillet-2024_c21_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-02T21:10:12.983956", "action": "move_to_buffer", "task_id": "janvier-2025_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:25.481503", "action": "move_from_buffer", "task_id": "juillet-2024_c22_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:28.411768", "action": "move_from_buffer", "task_id": "janvier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:54.837843", "action": "move_from_buffer", "task_id": "juillet-2024_c21_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-02T21:11:09.089459", "action": "move_from_buffer", "task_id": "novembre-2024_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "“ <PERSON><PERSON>, j’ai appris que tu vas à une salle de spo..."}, {"timestamp": "2025-06-02T21:11:27.904112", "action": "move_from_buffer", "task_id": "avril-2025_c18_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_trip", "new_name": "description_camping", "timestamp": "2025-06-02T21:12:01.745093"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "descripiton_trip", "timestamp": "2025-06-02T21:12:13.443757"}, {"timestamp": "2025-06-02T21:12:35.651488", "action": "move_to_buffer", "task_id": "juillet-2024_c33_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:12:37.336234", "action": "move_to_buffer", "task_id": "novembre-2024_c18_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:12:41.937538", "action": "move_to_buffer", "task_id": "juillet-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:43.424128", "action": "move_to_buffer", "task_id": "fevrier-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-02T21:12:48.753742", "action": "move_to_buffer", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:51.108455", "action": "move_to_buffer", "task_id": "Mai-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-02T21:12:52.366136", "action": "move_to_buffer", "task_id": "octobre-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:53.793436", "action": "move_to_buffer", "task_id": "octobre-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:57.226060", "action": "move_to_buffer", "task_id": "fevrier-2025_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T21:12:59.426355", "action": "move_to_buffer", "task_id": "avril-2025_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T21:13:10.787354", "action": "move_from_buffer", "task_id": "novembre-2024_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_apartment", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:16.057666", "action": "move_task", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_apartment", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:23.475876", "action": "move_from_buffer", "task_id": "octobre-2024_c17_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:29.566307", "action": "move_from_buffer", "task_id": "Mai-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-02T21:13:31.184873", "action": "move_from_buffer", "task_id": "octobre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:33.823474", "action": "move_from_buffer", "task_id": "fevrier-2025_c19_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T21:13:35.902549", "action": "move_from_buffer", "task_id": "fevrier-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-02T21:13:38.605596", "action": "move_from_buffer", "task_id": "novembre-2024_c18_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:13:40.541434", "action": "move_from_buffer", "task_id": "juillet-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:42.323477", "action": "move_from_buffer", "task_id": "juillet-2024_c33_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:13:44.956394", "action": "move_from_buffer", "task_id": "avril-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T21:13:54.511027", "action": "move_from_buffer", "task_id": "novembre-2024_c12_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie...."}, {"timestamp": "2025-06-02T21:14:29.571767", "action": "move_from_buffer", "task_id": "fevrier-2025_c27_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:14:30.815671", "action": "move_from_buffer", "task_id": "octobre-2024_c12_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:14:32.546463", "action": "move_from_buffer", "task_id": "juillet-2024_c10_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T21:14:33.657413", "action": "move_from_buffer", "task_id": "octobre-2024_c8_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:14:34.654759", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:14:35.789983", "action": "move_from_buffer", "task_id": "fevrier-2025_c25_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:14:58.089663", "action": "move_task", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"action": "rename_main_topic", "old_name": "looking_for_service", "new_name": "looking_for_organizing_help", "timestamp": "2025-06-02T21:15:32.682691"}, {"action": "rename_main_topic", "old_name": "looking_for_organizing_help", "new_name": "looking_for_service", "timestamp": "2025-06-02T21:15:50.605101"}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_orgianizing_help", "timestamp": "2025-06-02T21:16:18.372561"}, {"timestamp": "2025-06-02T21:16:26.329735", "action": "move_task", "task_id": "octobre-2024_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:17:09.454981", "action": "move_task", "task_id": "juillet-2024_c12_t1", "source_main_topic": "description_places", "source_subtopic": "description_party", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:17:16.824954", "action": "move_task", "task_id": "juillet-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_party", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_orgianizing_help", "new_name": "looking_for_orgianizing_party", "timestamp": "2025-06-02T21:17:33.824550"}, {"timestamp": "2025-06-02T21:17:46.845784", "action": "move_task", "task_id": "juillet-2024_c30_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous allez déménager. Des amis ont accepté de vous..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_help", "new_name": "looking_for_renovation_help", "timestamp": "2025-06-02T21:17:56.737051"}, {"timestamp": "2025-06-02T21:18:09.291551", "action": "move_task", "task_id": "decembre-2024_c13_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:18:12.109449", "action": "move_task", "task_id": "decembre-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:18:14.344005", "action": "move_task", "task_id": "janvier-2025_c18_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous voulez organiser une fête. Écrivez un message..."}, {"timestamp": "2025-06-02T21:18:18.990991", "action": "move_task", "task_id": "fevrier-2025_c24_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’informer de vot..."}, {"timestamp": "2025-06-02T21:18:21.264932", "action": "move_task", "task_id": "fevrier-2025_c28_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez célébrer votre anniversaire dans un..."}, {"timestamp": "2025-06-02T21:18:22.926610", "action": "move_task", "task_id": "fevrier-2025_c32_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:18:26.717226", "action": "move_task", "task_id": "novembre-2024_c5_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"timestamp": "2025-06-02T21:18:30.184425", "action": "move_task", "task_id": "novembre-2024_c13_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:18:31.477460", "action": "move_task", "task_id": "novembre-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez commandé un objet sur Internet et après ..."}, {"timestamp": "2025-06-02T21:18:33.746730", "action": "move_task", "task_id": "novembre-2024_c19_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Ecrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:18:36.574499", "action": "move_task", "task_id": "mars-2025_c1_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui souhait..."}, {"timestamp": "2025-06-02T21:18:37.867555", "action": "move_task", "task_id": "mars-2025_c5_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:18:45.667255", "action": "move_task", "task_id": "octobre-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T21:18:47.950344", "action": "move_task", "task_id": "octobre-2024_c18_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"timestamp": "2025-06-02T21:18:51.125286", "action": "move_task", "task_id": "octobre-2024_c1_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-02T21:18:53.496807", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_relocation_help", "timestamp": "2025-06-02T21:19:04.625308"}, {"timestamp": "2025-06-02T21:19:18.174490", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:24.929145", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_gift", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:37.655191", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:45.245668", "action": "move_task", "task_id": "octobre-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-02T21:19:52.433126", "action": "move_task", "task_id": "octobre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_orgianizing_party", "new_name": "looking_for_organizing_party", "timestamp": "2025-06-02T21:20:12.502294"}, {"timestamp": "2025-06-02T21:20:20.348801", "action": "move_task", "task_id": "octobre-2024_c15_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_organizing_party", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T21:20:28.185222", "action": "move_task", "task_id": "novembre-2024_c19_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Ecrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:20:36.384849", "action": "move_task", "task_id": "novembre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:20:43.118567", "action": "move_task", "task_id": "novembre-2024_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"timestamp": "2025-06-02T21:20:49.330576", "action": "move_task", "task_id": "fevrier-2025_c28_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez célébrer votre anniversaire dans un..."}, {"timestamp": "2025-06-02T21:21:01.311233", "action": "move_task", "task_id": "fevrier-2025_c24_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Rédigez un message à un ami pour l’informer de vot..."}, {"timestamp": "2025-06-02T21:21:11.523099", "action": "move_task", "task_id": "janvier-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_organizing_party", "task_content": "Vous voulez organiser une fête. Écrivez un message..."}, {"timestamp": "2025-06-02T21:21:32.494550", "action": "move_task", "task_id": "juillet-2024_c30_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Vous allez déménager. Des amis ont accepté de vous..."}, {"timestamp": "2025-06-02T21:22:37.234212", "action": "move_task", "task_id": "decembre-2024_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"action": "create_subtopic", "main_topic": "sharing_information", "subtopic_name": "sharing_customer_feedback", "timestamp": "2025-06-02T21:23:18.180798"}, {"timestamp": "2025-06-02T21:23:26.525260", "action": "move_task", "task_id": "mars-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:23:33.279037", "action": "move_task", "task_id": "mars-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui souhait..."}, {"timestamp": "2025-06-02T21:23:40.258902", "action": "move_task", "task_id": "novembre-2024_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Vous avez commandé un objet sur Internet et après ..."}, {"timestamp": "2025-06-02T21:23:41.827357", "action": "move_task", "task_id": "fevrier-2025_c32_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:23:50.450943", "action": "move_task", "task_id": "decembre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Écrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:24:53.831105", "action": "move_task", "task_id": "decembre-2024_c5_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_entertainment", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T21:25:00.394963", "action": "move_task", "task_id": "decembre-2024_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Écrivez un courriel à vos amis pour les inviter à ..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_entertainment", "new_name": "recommendation_sport", "timestamp": "2025-06-02T21:25:21.378354"}, {"timestamp": "2025-06-02T21:25:24.589393", "action": "move_task", "task_id": "juillet-2024_c2_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_general", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:25:40.025922", "action": "move_task", "task_id": "juillet-2024_c26_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:09.621158", "action": "move_to_buffer", "task_id": "juillet-2024_c27_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "task_content": "Vous voulez passer un week-end avec vos amis et fa..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_general", "new_name": "recommendation_career", "timestamp": "2025-06-02T21:26:22.733294"}, {"timestamp": "2025-06-02T21:26:30.549185", "action": "move_to_buffer", "task_id": "janvier-2025_c8_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:33.011923", "action": "move_to_buffer", "task_id": "fevrier-2025_c13_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) souhaite découvrir votre région. Rédi..."}, {"timestamp": "2025-06-02T21:26:36.588724", "action": "move_to_buffer", "task_id": "avril-2025_c19_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Rédigez un message destiné à un ami pour lui propo..."}, {"timestamp": "2025-06-02T21:26:39.219325", "action": "move_to_buffer", "task_id": "novembre-2024_c10_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:41.079104", "action": "move_to_buffer", "task_id": "juillet-2024_c2_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:26:46.838338", "action": "move_from_buffer", "task_id": "juillet-2024_c2_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:26:49.465506", "action": "move_from_buffer", "task_id": "avril-2025_c19_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Rédigez un message destiné à un ami pour lui propo..."}, {"timestamp": "2025-06-02T21:27:11.809460", "action": "move_from_buffer", "task_id": "juillet-2024_c27_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Vous voulez passer un week-end avec vos amis et fa..."}, {"timestamp": "2025-06-02T21:27:22.503184", "action": "move_from_buffer", "task_id": "janvier-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:27:24.296096", "action": "move_from_buffer", "task_id": "fevrier-2025_c13_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) souhaite découvrir votre région. Rédi..."}, {"timestamp": "2025-06-02T21:27:25.724445", "action": "move_from_buffer", "task_id": "novembre-2024_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_other_service", "new_name": "looking_for_sale_buy", "timestamp": "2025-06-02T21:28:31.954646"}, {"timestamp": "2025-06-02T21:29:10.346944", "action": "move_task", "task_id": "decembre-2024_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:29:23.620130", "action": "move_to_buffer", "task_id": "decembre-2024_c10_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:29:25.186762", "action": "move_to_buffer", "task_id": "aout-2024_c4_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:29:26.764740", "action": "move_to_buffer", "task_id": "fevrier-2025_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire. T..."}, {"timestamp": "2025-06-02T21:29:28.155270", "action": "move_to_buffer", "task_id": "septembre-2024_c5_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:29:37.413726", "action": "move_task", "task_id": "fevrier-2025_c29_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_services", "target_main_topic": "recommendation", "target_subtopic": "recommendation_marketplace", "task_content": "Les enseignants de votre quartier projettent d’org..."}, {"timestamp": "2025-06-02T21:29:44.303687", "action": "move_task", "task_id": "fevrier-2025_c29_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "target_main_topic": "recommendation", "target_subtopic": "recommendation_career", "task_content": "Les enseignants de votre quartier projettent d’org..."}, {"timestamp": "2025-06-02T21:29:49.836593", "action": "move_to_buffer", "task_id": "juillet-2024_c26_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_sport", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:29:59.224532", "action": "move_to_buffer", "task_id": "decembre-2024_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_sport", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:30:11.759847", "action": "move_to_buffer", "task_id": "janvier-2025_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:16.155894", "action": "move_to_buffer", "task_id": "janvier-2025_c15_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:18.954743", "action": "move_to_buffer", "task_id": "fevrier-2025_c11_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:30:24.522956", "action": "move_to_buffer", "task_id": "avril-2025_c1_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "<PERSON><PERSON><PERSON>, Je viens bientôt en vacances dans ta régi..."}, {"timestamp": "2025-06-02T21:30:27.872451", "action": "move_to_buffer", "task_id": "novembre-2024_c7_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:29.134814", "action": "move_to_buffer", "task_id": "mars-2025_c12_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:30:49.426167", "action": "move_from_buffer", "task_id": "novembre-2024_c7_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:56.380027", "action": "move_from_buffer", "task_id": "mars-2025_c12_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:31:07.296350", "action": "move_from_buffer", "task_id": "avril-2025_c1_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "<PERSON><PERSON><PERSON>, Je viens bientôt en vacances dans ta régi..."}, {"timestamp": "2025-06-02T21:31:14.471523", "action": "move_from_buffer", "task_id": "fevrier-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:31:18.241059", "action": "move_from_buffer", "task_id": "janvier-2025_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:31:22.715693", "action": "move_from_buffer", "task_id": "janvier-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:31:27.950916", "action": "move_from_buffer", "task_id": "decembre-2024_c3_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:31:35.834571", "action": "move_from_buffer", "task_id": "juillet-2024_c26_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:31:37.722871", "action": "move_from_buffer", "task_id": "septembre-2024_c5_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:31:46.342995", "action": "move_from_buffer", "task_id": "fevrier-2025_c3_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire. T..."}, {"timestamp": "2025-06-02T21:31:58.297247", "action": "move_from_buffer", "task_id": "decembre-2024_c10_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:31:59.741583", "action": "move_from_buffer", "task_id": "aout-2024_c4_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:32:06.263578", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:32:07.265409", "action": "move_to_buffer", "task_id": "octobre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:32:08.292174", "action": "move_to_buffer", "task_id": "fevrier-2025_c25_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:32:09.146520", "action": "move_to_buffer", "task_id": "fevrier-2025_c27_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:32:10.179625", "action": "move_to_buffer", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_services", "new_name": "recommendation_talk", "timestamp": "2025-06-02T21:33:11.204757"}, {"timestamp": "2025-06-02T21:33:36.547322", "action": "move_from_buffer", "task_id": "juillet-2024_c31_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_travel", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:33:42.816628", "action": "move_task", "task_id": "juillet-2024_c31_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "target_main_topic": "recommendation", "target_subtopic": "recommendation_talk", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_talk", "new_name": "recommendation_library_event", "timestamp": "2025-06-02T21:34:12.148255"}, {"timestamp": "2025-06-02T21:34:20.551085", "action": "move_from_buffer", "task_id": "fevrier-2025_c27_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:34:33.080928", "action": "move_from_buffer", "task_id": "fevrier-2025_c25_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_library_event", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:34:59.423657", "action": "move_from_buffer", "task_id": "octobre-2024_c12_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:35:39.893412", "action": "move_to_buffer", "task_id": "fevrier-2025_c33_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:35:48.591906", "action": "move_to_buffer", "task_id": "mars-2025_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:35:49.964216", "action": "move_to_buffer", "task_id": "mars-2025_c14_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T21:36:07.987854", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:36:09.549786", "action": "move_from_buffer", "task_id": "fevrier-2025_c33_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:36:11.487089", "action": "move_from_buffer", "task_id": "mars-2025_c4_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:36:13.871179", "action": "move_from_buffer", "task_id": "mars-2025_c14_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:02:34.398490", "action": "delete_subtopic", "main_topic_name": "recommendation", "subtopic_name": "recommendation_marketplace", "deleted_tasks_count": 0, "deleted_tasks": []}, {"timestamp": "2025-06-02T22:02:54.205954", "action": "delete_subtopic", "main_topic_name": "recommendation", "subtopic_name": "recommendation_travel", "deleted_tasks_count": 0, "deleted_tasks": []}, {"timestamp": "2025-06-02T22:04:03.183639", "action": "move_to_buffer", "task_id": "mars-2025_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_general_info", "task_content": "<PERSON><PERSON><PERSON><PERSON> une réponse à un courriel d’un ami demanda..."}, {"timestamp": "2025-06-02T22:04:18.221623", "action": "move_to_buffer", "task_id": "fevrier-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:04:20.761253", "action": "move_to_buffer", "task_id": "fevrier-2025_c15_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:04:26.462186", "action": "move_to_buffer", "task_id": "fevrier-2025_c31_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:04:33.551053", "action": "move_to_buffer", "task_id": "avril-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous pratiquez un sport dans un club et vous venez..."}, {"timestamp": "2025-06-02T22:04:39.396064", "action": "move_to_buffer", "task_id": "mars-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:04:40.709460", "action": "move_to_buffer", "task_id": "octobre-2024_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:04:43.986712", "action": "move_to_buffer", "task_id": "octobre-2024_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "écrivez un message à votre ami pour lui raconter v..."}, {"timestamp": "2025-06-02T22:04:47.135587", "action": "move_to_buffer", "task_id": "septembre-2024_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:04:48.404056", "action": "move_to_buffer", "task_id": "septembre-2024_c9_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Ecrivez un message à vos amis pour les inviter à v..."}, {"timestamp": "2025-06-02T22:04:59.461962", "action": "move_to_buffer", "task_id": "avril-2025_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_personal_news", "task_content": "Rédigez un courriel à vos amis pour les convier à ..."}, {"timestamp": "2025-06-02T22:05:01.893592", "action": "move_to_buffer", "task_id": "Mai-2025_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_personal_news", "task_content": "Vous habitez dans une nouvelle ville depuis un moi..."}, {"action": "rename_subtopic", "main_topic": "sharing_information", "old_name": "sharing_event_experience", "new_name": "sharing_job_interview", "timestamp": "2025-06-02T22:05:32.345430"}, {"action": "rename_subtopic", "main_topic": "sharing_information", "old_name": "sharing_information_general", "new_name": "sharing_sport_experience", "timestamp": "2025-06-02T22:06:56.652483"}, {"timestamp": "2025-06-02T22:07:04.113818", "action": "move_from_buffer", "task_id": "octobre-2024_c3_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:07:05.677357", "action": "move_from_buffer", "task_id": "mars-2025_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:07:08.262574", "action": "move_from_buffer", "task_id": "septembre-2024_c7_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:07:09.621649", "action": "move_from_buffer", "task_id": "avril-2025_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous pratiquez un sport dans un club et vous venez..."}, {"timestamp": "2025-06-02T22:08:00.305460", "action": "move_from_buffer", "task_id": "septembre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Ecrivez un message à vos amis pour les inviter à v..."}, {"timestamp": "2025-06-02T22:08:17.313020", "action": "move_from_buffer", "task_id": "fevrier-2025_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_camping", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:08:26.902867", "action": "move_from_buffer", "task_id": "fevrier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_camping", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:08:36.765831", "action": "move_from_buffer", "task_id": "mars-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON><PERSON><PERSON><PERSON> une réponse à un courriel d’un ami demanda..."}, {"timestamp": "2025-06-02T22:08:50.151584", "action": "move_from_buffer", "task_id": "avril-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Rédigez un courriel à vos amis pour les convier à ..."}, {"timestamp": "2025-06-02T22:08:57.267138", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T22:08:59.299145", "action": "move_to_buffer", "task_id": "fevrier-2025_c33_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:00.315063", "action": "move_to_buffer", "task_id": "mars-2025_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:01.349190", "action": "move_to_buffer", "task_id": "mars-2025_c14_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:09:14.675253", "action": "move_from_buffer", "task_id": "fevrier-2025_c33_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:16.310356", "action": "move_from_buffer", "task_id": "mars-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:29.875813", "action": "move_from_buffer", "task_id": "mars-2025_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:09:48.480161", "action": "move_from_buffer", "task_id": "Mai-2025_c2_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_general_info", "task_content": "Vous habitez dans une nouvelle ville depuis un moi..."}, {"timestamp": "2025-06-02T22:10:01.726805", "action": "move_from_buffer", "task_id": "octobre-2024_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "écrivez un message à votre ami pour lui raconter v..."}, {"timestamp": "2025-06-02T22:10:22.281323", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T22:10:25.107448", "action": "move_from_buffer", "task_id": "fevrier-2025_c31_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"action": "restore_missing_task", "task_id": "juillet-2024_c10_t1", "original_location": "manual_review > manual_review_general", "restored_to": "manual_review > manual_review_general", "timestamp": "2025-06-02T22:18:42.263684", "reason": "Task was missing from current checkpoint"}, {"action": "restore_missing_task", "task_id": "decembre-2024_c18_t1", "original_location": "manual_review > manual_review_general", "restored_to": "manual_review > manual_review_general", "timestamp": "2025-06-02T22:18:42.263694", "reason": "Task was missing from current checkpoint"}, {"timestamp": "2025-06-02T22:28:47.289895", "action": "move_task", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T22:28:52.320998", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T22:29:41.218148", "action": "move_task", "task_id": "juillet-2024_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:29:54.733805", "action": "move_task", "task_id": "juillet-2024_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "« Salut ! Je sais que tu fais du sport depuis le m..."}, {"timestamp": "2025-06-02T22:29:59.762934", "action": "move_task", "task_id": "juillet-2024_c13_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_film_event", "timestamp": "2025-06-02T22:30:30.567827"}, {"timestamp": "2025-06-02T22:30:37.805162", "action": "move_task", "task_id": "juillet-2024_c35_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:30:43.511623", "action": "move_task", "task_id": "aout-2024_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:30:45.128362", "action": "move_task", "task_id": "janvier-2025_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:30:51.806934", "action": "move_task", "task_id": "fevrier-2025_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:30:54.044587", "action": "move_task", "task_id": "fevrier-2025_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Tu aimerais assister à un festival de cinéma organ..."}, {"timestamp": "2025-06-02T22:30:56.606560", "action": "move_task", "task_id": "fevrier-2025_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:30:59.530211", "action": "move_task", "task_id": "fevrier-2025_c9_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon ..."}, {"timestamp": "2025-06-02T22:31:01.602727", "action": "move_task", "task_id": "fevrier-2025_c12_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-02T22:31:04.995858", "action": "move_task", "task_id": "fevrier-2025_c16_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:31:08.124716", "action": "move_task", "task_id": "fevrier-2025_c30_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:10.705591", "action": "move_task", "task_id": "avril-2025_c5_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:13.047203", "action": "move_task", "task_id": "avril-2025_c10_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T22:31:15.337463", "action": "move_task", "task_id": "avril-2025_c16_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami vous demande des informations sur votre ..."}, {"timestamp": "2025-06-02T22:31:25.142798", "action": "move_task", "task_id": "avril-2025_c17_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un email pour inviter un ami à passer une ..."}, {"timestamp": "2025-06-02T22:31:27.674249", "action": "move_task", "task_id": "mars-2025_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message à un ami intéressé par des cour..."}, {"timestamp": "2025-06-02T22:31:30.685627", "action": "move_task", "task_id": "mars-2025_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:44.215931", "action": "move_task", "task_id": "mars-2025_c13_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "description_places", "target_subtopic": "description_university", "task_content": "Vous avez reçu un message d’un ami Alex qui vous d..."}, {"timestamp": "2025-06-02T22:31:50.601828", "action": "move_task", "task_id": "octobre-2024_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "vous avez mis en ligne une annonce pour la locatio..."}, {"timestamp": "2025-06-02T22:31:52.949862", "action": "move_task", "task_id": "septembre-2024_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_concert", "timestamp": "2025-06-02T22:32:14.772151"}, {"timestamp": "2025-06-02T22:32:26.956100", "action": "move_task", "task_id": "septembre-2024_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_concert", "task_content": "Vous souhaitez aller au concert de votre artiste p..."}, {"timestamp": "2025-06-02T22:32:47.324483", "action": "move_task", "task_id": "fevrier-2025_c5_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:32:50.514940", "action": "move_task", "task_id": "fevrier-2025_c17_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:32:52.802472", "action": "move_task", "task_id": "fevrier-2025_c18_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:32:55.000754", "action": "move_task", "task_id": "fevrier-2025_c21_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:32:56.498800", "action": "move_task", "task_id": "mars-2025_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez..."}, {"timestamp": "2025-06-02T22:33:17.178546", "action": "move_task", "task_id": "juillet-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:33:36.112101", "action": "move_task", "task_id": "aout-2024_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:33:52.662595", "action": "move_task", "task_id": "fevrier-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:33:56.471334", "action": "move_task", "task_id": "fevrier-2025_c7_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:34:07.824046", "action": "move_task", "task_id": "janvier-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:34:21.293904", "action": "move_task", "task_id": "fevrier-2025_c9_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon ..."}, {"timestamp": "2025-06-02T22:34:32.448835", "action": "move_task", "task_id": "fevrier-2025_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:34:39.710017", "action": "move_task", "task_id": "avril-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:34:46.006680", "action": "move_task", "task_id": "avril-2025_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Envoyez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T22:34:55.224918", "action": "move_task", "task_id": "avril-2025_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Votre ami vous demande des informations sur votre ..."}, {"timestamp": "2025-06-02T22:35:52.041193", "action": "move_task", "task_id": "fevrier-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:35:57.082271", "action": "move_task", "task_id": "fevrier-2025_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:36:33.471167", "action": "move_task", "task_id": "mars-2025_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez..."}, {"timestamp": "2025-06-02T22:36:42.590721", "action": "move_task", "task_id": "septembre-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:36:44.471536", "action": "move_task", "task_id": "octobre-2024_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "vous avez mis en ligne une annonce pour la locatio..."}, {"timestamp": "2025-06-02T22:36:55.222861", "action": "move_task", "task_id": "mars-2025_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Rédiger un message à un ami intéressé par des cour..."}, {"timestamp": "2025-06-02T22:37:12.403987", "action": "move_task", "task_id": "juillet-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:37:29.242014", "action": "move_task", "task_id": "fevrier-2025_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-02T22:37:45.853246", "action": "move_task", "task_id": "fevrier-2025_c30_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:37:48.766592", "action": "move_task", "task_id": "mars-2025_c7_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:37:53.452811", "action": "move_task", "task_id": "fevrier-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:37:54.939649", "action": "move_task", "task_id": "fevrier-2025_c21_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:38:16.445956", "action": "move_task", "task_id": "juillet-2024_c36_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "L’anniversaire de votre ami approche à grands pas ..."}, {"timestamp": "2025-06-02T22:38:18.360789", "action": "move_task", "task_id": "avril-2025_c14_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous venez d’emménager dans une nouvelle ville. Ré..."}, {"timestamp": "2025-06-02T22:38:19.966312", "action": "move_task", "task_id": "novembre-2024_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez récemment déména<PERSON> dans une nouvelle vil..."}, {"timestamp": "2025-06-02T22:38:42.338524", "action": "move_task", "task_id": "juillet-2024_c36_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "L’anniversaire de votre ami approche à grands pas ..."}, {"timestamp": "2025-06-02T22:38:49.862988", "action": "move_task", "task_id": "avril-2025_c14_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Vous venez d’emménager dans une nouvelle ville. Ré..."}, {"timestamp": "2025-06-02T22:38:51.337409", "action": "move_task", "task_id": "novembre-2024_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Vous avez récemment déména<PERSON> dans une nouvelle vil..."}, {"timestamp": "2025-06-02T22:39:01.829857", "action": "move_task", "task_id": "avril-2025_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Rédigez un email pour inviter un ami à passer une ..."}, {"timestamp": "2025-06-03T02:47:58.775453", "action": "delete_subtopic", "main_topic_name": "sharing_information", "subtopic_name": "sharing_work_news", "deleted_tasks_count": 0}, {"timestamp": "2025-06-03T02:48:08.495307", "action": "delete_subtopic", "main_topic_name": "sharing_information", "subtopic_name": "sharing_study_info", "deleted_tasks_count": 0}, {"timestamp": "2025-06-03T02:48:15.364957", "action": "delete_subtopic", "main_topic_name": "sharing_information", "subtopic_name": "sharing_travel_experience", "deleted_tasks_count": 0}, {"timestamp": "2025-06-03T02:48:23.834972", "action": "delete_subtopic", "main_topic_name": "sharing_information", "subtopic_name": "sharing_personal_news", "deleted_tasks_count": 0}, {"action": "merge_classifications", "added_duplicates": 2, "added_new_to_manual_review": 1, "timestamp": "2025-07-02T16:00:12.801246"}, {"timestamp": "2025-07-02T16:02:25.344592", "action": "move_task", "task_id": "juin-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez..."}, {"timestamp": "2025-07-15T12:10:35.974840", "action": "move_task", "task_id": "juillet-2025_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) souhaite visiter votre région.Rédigez..."}, {"action": "update_counts", "old_total_tasks": 203, "old_unique_tasks": 139, "new_total_tasks": 206, "new_unique_tasks": 138, "timestamp": "2025-07-18T19:39:36.312516"}, {"timestamp": "2025-07-18T20:02:07.165116", "action": "cleanup_empty_topics", "deleted_subtopics": ["manual_review > manual_review_general"], "deleted_main_topics": ["manual_review"], "total_deleted": 2}, {"timestamp": "2025-07-21T18:27:42.830377", "action": "move_to_buffer", "task_id": "juillet-2024_c25_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-07-21T18:27:50.343071", "action": "move_from_buffer", "task_id": "juillet-2024_c25_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}]}, "classification": {"task_number": 1, "method": "predefined_classification_with_deduplication_with_new_tasks_merged", "total_tasks": 206, "unique_tasks": 138, "n_main_topics": 5, "main_topics": {"recommendation": {"topic_id": 0, "keywords": ["recommand", "conseil", "<PERSON>gg<PERSON><PERSON>", "propose", "avis", "choix", "meilleur", "excellent", "bon", "bonne", "qualité", "fortement"], "total_tasks": 16, "unique_tasks": 15, "subtopics": {"recommendation_sport": {"subtopic_id": 2, "task_count": 8, "unique_task_count": 8, "task_ids": ["decembre-2024_c5_t1"], "keywords": ["sortir", "activité", "spectacle", "concert", "cinéma", "divertissement"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c2_t1"], "representative_id": "juillet-2024_c2_t1", "task_content": "Vous souhaitez faire du sport et vous voulez que votre ami vous accompagne. Écrivez lui un message pour lui proposer de pratiquer ensemble.", "month_years": ["juillet-2024"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez faire du sport et vous voulez que votre ami vous accompagne. écrivez lui un message pour lui proposer de pratiquer ensemble"}, {"task_ids": ["avril-2025_c19_t1"], "representative_id": "avril-2025_c19_t1", "task_content": "Rédigez un message destiné à un ami pour lui proposer de faire du sport ensemble. (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message destiné à un ami pour lui proposer de faire du sport ensemble"}, {"task_ids": ["juillet-2024_c27_t1"], "representative_id": "juillet-2024_c27_t1", "task_content": "Vous voulez passer un week-end avec vos amis et faire du sport. Vous leur écrivez une lettre pour proposer ça en citant (date, lieu, programme.).", "month_years": ["juillet-2024"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez passer un week-end avec vos amis et faire du sport. vous leur écrivez une lettre pour proposer ça en citant (date, lieu, programme.)"}, {"task_ids": ["fevrier-2025_c27_t1"], "representative_id": "fevrier-2025_c27_t1", "task_content": "Écrivez un message à votre ami pour encourager à intégrer le sport dans son quotidien. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour encourager à intégrer le sport dans son quotidien"}, {"task_ids": ["octobre-2024_c12_t1"], "representative_id": "octobre-2024_c12_t1", "task_content": "écrivez un message à votre ami pour l’inviter à pratiquer un sport avec vous", "month_years": ["octobre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour l’inviter à pratiquer un sport avec vous"}, {"task_ids": ["juillet-2024_c10_t1"], "representative_id": "juillet-2024_c10_t1", "task_content": "Un évènement sportif aura lieu dans votre ville bientôt. Faites parvenir un message à vos amis pour les inviter (date, lieu, inscription, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un évènement sportif aura lieu dans votre ville bientôt. faites parvenir un message à vos amis pour les inviter (date, lieu, inscription, etc.)"}, {"task_ids": ["decembre-2024_c18_t1"], "representative_id": "decembre-2024_c18_t1", "task_content": "Écrivez un message à votre ami pour le convaincre à pratiquer une activité physique. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour le convaincre à pratiquer une activité physique"}, {"task_ids": ["juillet-2024_c3_t1"], "representative_id": "juillet-2024_c3_t1", "task_content": "« Salut ! Je sais que tu fais du sport depuis le mois dernier. Ça m’intéresse beaucoup et j’aimerais bien venir avec toi. Tu peux m’en dire plus ? A bientôt ! Camille » Vous répondez à votre ami Camille. Dans votre message, vous décrivez votre activité sportive et vous donnez des informations utiles (lieu, durée, prix, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« salut ! je sais que tu fais du sport depuis le mois dernier. ça m’intéresse beaucoup et j’aimerais bien venir avec toi. tu peux m’en dire plus ? a bientôt ! camille » vous répondez à votre ami camille. dans votre message, vous décrivez votre activité sportive et vous donnez des informations utiles (lieu, durée, prix, etc.)"}], "name_en": "Recommended Sport", "name_fr": "sport recommandé", "name_zh": "推荐运动", "original_name": "recommendation_sport"}, "recommendation_career": {"subtopic_id": 0, "task_count": 2, "unique_task_count": 2, "task_ids": ["juillet-2024_c2_t1", "juillet-2024_c26_t1", "juillet-2024_c27_t1", "decembre-2024_c16_t1", "janvier-2025_c8_t1", "fevrier-2025_c13_t1", "avril-2025_c19_t1", "novembre-2024_c10_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["decembre-2024_c16_t1"], "representative_id": "decembre-2024_c16_t1", "task_content": "Les professeurs de votre quartier veulent organiser une rencontre avec leurs élèves pour les orienter dans le choix de leurs futures professions, vous leur écrivez un mail pour leur donner les avantages de votre profession. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "les professeurs de votre quartier veulent organiser une rencontre avec leurs élèves pour les orienter dans le choix de leurs futures professions, vous leur écrivez un mail pour leur donner les avantages de votre profession"}, {"task_ids": ["fevrier-2025_c29_t1"], "representative_id": "fevrier-2025_c29_t1", "task_content": "Les enseignants de votre quartier projettent d’organiser une rencontre pour guider leurs élèves dans le choix de leur futur métier. Rédigez un courriel pour leur exposer les atouts de votre profession . (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "les enseignants de votre quartier projettent d’organiser une rencontre pour guider leurs élèves dans le choix de leur futur métier. rédigez un courriel pour leur exposer les atouts de votre profession"}], "name_en": "Career Recommendation", "name_fr": "orientation professionnelle", "name_zh": "推荐职业", "original_name": "recommendation_career"}, "recommendation_library_event": {"subtopic_id": 4, "task_count": 2, "unique_task_count": 2, "task_ids": ["fevrier-2025_c29_t1"], "keywords": ["service", "professionnel", "coiffeur", "médecin", "garage"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c31_t1"], "representative_id": "juillet-2024_c31_t1", "task_content": "La bibliothèque de votre ville organise une rencontre avec un écrivain. Écrivez un message à votre ami(e) pour l’inviter à cet événement.", "month_years": ["juillet-2024"], "combination_numbers": ["31"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "la bibliothèque de votre ville organise une rencontre avec un écrivain. écrivez un message à votre ami(e) pour l’inviter à cet événement"}, {"task_ids": ["fevrier-2025_c25_t1"], "representative_id": "fevrier-2025_c25_t1", "task_content": "Rédigez un message à un ami pour l’inviter à une rencontre avec un écrivain organisée par la bibliothèque de votre ville. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["25"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un ami pour l’inviter à une rencontre avec un écrivain organisée par la bibliothèque de votre ville"}], "name_en": "Recommended Library Event", "name_fr": "événement de la bibliothèque de recommandations", "name_zh": "推荐图书馆活动", "original_name": "recommendation_library_event"}, "recommendation_film_event": {"subtopic_id": 3, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c35_t1", "juillet-2024_c1_t1"], "representative_id": "juillet-2024_c35_t1", "task_content": "Vous souhaitez assister à un festival de cinéma dans votre ville. Vous écrivez un message à votre ami(e) pour lui proposer de venir avec vous. Vous lui donnez toutes les informations nécessaires sur févènement (films, dates et horaires, tarifs, etc.).", "month_years": ["juillet-2024", "juillet-2024"], "combination_numbers": ["35", "1"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous souhaitez assister à un festival de cinéma dans votre ville. vous écrivez un message à votre ami(e) pour lui proposer de venir avec vous. vous lui donnez toutes les informations nécessaires sur févènement (films, dates et horaires, tarifs, etc.)"}, {"task_ids": ["fevrier-2025_c4_t1"], "representative_id": "fevrier-2025_c4_t1", "task_content": "Tu aimerais assister à un festival de cinéma organisé dans ta ville. Tu écris un message à ton ami(e) pour lui proposer de t’accompagner. Tu lui donnes toutes les informations nécessaires sur l’événement, notamment les films projetés, les dates et horaires, ainsi que les tarifs d’entrée. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu aimerais assister à un festival de cinéma organisé dans ta ville. tu écris un message à ton ami(e) pour lui proposer de t’accompagner. tu lui donnes toutes les informations nécessaires sur l’événement, notamment les films projetés, les dates et horaires, ainsi que les tarifs d’entrée"}], "name_en": "Film Event Recommendation", "name_fr": "événement de film recommandé", "name_zh": "推荐电影活动", "original_name": "recommendation_film_event"}, "recommendation_concert": {"subtopic_id": 4, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c6_t1"], "representative_id": "septembre-2024_c6_t1", "task_content": "Vous souhaitez aller au concert de votre artiste préféré(e). Vous proposez à votre ami(e) de vous accompagner. Vous lui écrivez un courriel avec les informations utiles (date, prix, lieu, artiste, durée).", "month_years": ["septembre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez aller au concert de votre artiste préféré(e). vous proposez à votre ami(e) de vous accompagner. vous lui écrivez un courriel avec les informations utiles (date, prix, lieu, artiste, durée)"}], "name_en": "Recommended Concert", "name_fr": "concert de recommandation", "name_zh": "推荐音乐会", "original_name": "recommendation_concert"}}, "name_en": "Suggestion", "name_fr": "recommandation", "name_zh": "推荐", "original_name": "recommendation"}, "sharing_information": {"topic_id": 1, "keywords": ["nouvelles", "racont", "partag", "expérience", "inform", "nouvelle", "savoir", "voulais", "commencé"], "total_tasks": 15, "unique_tasks": 10, "subtopics": {"sharing_general_info": {"subtopic_id": 1, "task_count": 3, "unique_task_count": 2, "task_ids": ["juillet-2024_c8_t1", "mars-2025_c3_t1", "octobre-2024_c19_t1"], "keywords": ["équipements", "nouveaux", "piscine", "fitness", "quartier"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c8_t1", "octobre-2024_c19_t1"], "representative_id": "juillet-2024_c8_t1", "task_content": "Vous avez déménagé dans une nouvelle ville il y a un mois. Rédigez un courriel à votre ami Léo pour lui donner de vos nouvelles. Dans votre message, décrivez votre appartement, le quartier, la ville, ainsi que les activités que vous avez découvertes.", "month_years": ["juillet-2024", "octobre-2024"], "combination_numbers": ["8", "19"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez déménagé dans une nouvelle ville il y a un mois. rédigez un courriel à votre ami léo pour lui donner de vos nouvelles. dans votre message, décrivez votre appartement, le quartier, la ville, ainsi que les activités que vous avez découvertes"}, {"task_ids": ["Mai-2025_c2_t1"], "representative_id": "Mai-2025_c2_t1", "task_content": "Vous habitez dans une nouvelle ville depuis un mois. Écrivez un message à votre ami Léo pour lui raconter comment se passe votre nouvelle vie : votre appartement, votre quartier, la ville en général, et ce que vous aimez y faire . (60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous habitez dans une nouvelle ville depuis un mois. écrivez un message à votre ami léo pour lui raconter comment se passe votre nouvelle vie : votre appartement, votre quartier, la ville en général, et ce que vous aimez y faire"}], "name_en": "Sharing General Information", "name_fr": "partage d'informations générales", "name_zh": "共享一般信息", "original_name": "sharing_general_info"}, "sharing_customer_feedback": {"subtopic_id": 7, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["mars-2025_c5_t1", "fevrier-2025_c32_t1"], "representative_id": "mars-2025_c5_t1", "task_content": "Rédiger un e-mail au service client pour signaler la réception d’un objet endommagé après une commande en ligne, en décrivant le problème et en précisant la solution souhaitée . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025", "fevrier-2025"], "combination_numbers": ["5", "32"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédiger un e-mail au service client pour signaler la réception d’un objet endommagé après une commande en ligne, en décrivant le problème et en précisant la solution souhaitée"}, {"task_ids": ["novembre-2024_c17_t1"], "representative_id": "novembre-2024_c17_t1", "task_content": "Vous avez commandé un objet sur Internet et après réception du colis, vous constatez que l’objet est cassé. Rédigez un e-mail au service clientèle pour signaler le problème, décrivez le dommage de l’objet et précisez ce que vous attendez comme solution. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez commandé un objet sur internet et après réception du colis, vous constatez que l’objet est cassé. rédigez un e-mail au service clientèle pour signaler le problème, décrivez le dommage de l’objet et précisez ce que vous attendez comme solution"}], "name_en": "Customer Feedback Sharing", "name_fr": "partage des retours clients", "name_zh": "分享客户反馈", "original_name": "sharing_customer_feedback"}, "sharing_job_interview": {"subtopic_id": 5, "task_count": 3, "unique_task_count": 2, "task_ids": ["fevrier-2025_c33_t1", "mars-2025_c4_t1", "mars-2025_c14_t1"], "keywords": ["événement", "spectacle", "concert", "visite", "sortie", "louvre", "tour", "eiffel"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c6_t1"], "representative_id": "octobre-2024_c6_t1", "task_content": "écrivez un message à votre ami pour lui raconter votre entretien d’embauche", "month_years": ["octobre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour lui raconter votre entretien d’embauche"}, {"task_ids": ["fevrier-2025_c18_t1", "fevrier-2025_c17_t1"], "representative_id": "fevrier-2025_c18_t1", "task_content": "Envoyez un message à votre ami(e) pour lui partager les détails de votre entretien d’embauche, comme l’entreprise, le salaire proposé, le type de poste, etc. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["18", "17"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "envoyez un message à votre ami(e) pour lui partager les détails de votre entretien d’embauche, comme l’entreprise, le salaire proposé, le type de poste, etc"}], "name_en": "Sharing Job Interview", "name_fr": "partage d'entretien d'embauche", "name_zh": "分享工作面试", "original_name": "sharing_job_interview"}, "sharing_sport_experience": {"subtopic_id": 4, "task_count": 6, "unique_task_count": 4, "task_ids": ["fevrier-2025_c6_t1", "fevrier-2025_c15_t1", "fevrier-2025_c31_t1", "avril-2025_c6_t1", "mars-2025_c6_t1", "octobre-2024_c3_t1", "octobre-2024_c6_t1", "septembre-2024_c7_t1", "septembre-2024_c9_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c3_t1", "septembre-2024_c7_t1"], "representative_id": "octobre-2024_c3_t1", "task_content": "Vous faites du sport dans un club. Vous venez de remporter une compétition Vous écrivez un courriel à vos amis pour leur raconter cet évènement sportif et annoncer votre réussite sportive.", "month_years": ["octobre-2024", "septembre-2024"], "combination_numbers": ["3", "7"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous faites du sport dans un club. vous venez de remporter une compétition vous écrivez un courriel à vos amis pour leur raconter cet évènement sportif et annoncer votre réussite sportive"}, {"task_ids": ["mars-2025_c6_t1", "fevrier-2025_c31_t1"], "representative_id": "mars-2025_c6_t1", "task_content": "France Télévisions réalise un reportage sur le sport amateur et invite les passionnés à partager leur expérience en tant que sportifs sur francetélévision.fr. (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025", "fevrier-2025"], "combination_numbers": ["6", "31"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "france télévisions réalise un reportage sur le sport amateur et invite les passionnés à partager leur expérience en tant que sportifs sur francetélévision.fr"}, {"task_ids": ["avril-2025_c6_t1"], "representative_id": "avril-2025_c6_t1", "task_content": "Vous pratiquez un sport dans un club et vous venez de gagner une compétition. Écrivez un courriel à vos amis pour leur raconter ce qui s’est passé et partager votre joie . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous pratiquez un sport dans un club et vous venez de gagner une compétition. écrivez un courriel à vos amis pour leur raconter ce qui s’est passé et partager votre joie"}, {"task_ids": ["novembre-2024_c16_t1"], "representative_id": "novembre-2024_c16_t1", "task_content": "France Télévision prépare un reportage sur le sport amateur. Et vous, quel sportif êtes-vous ? Envoyez-nous vos témoignages sur francetélévision.fr. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "france télévision prépare un reportage sur le sport amateur. et vous, quel sportif êtes-vous ? envoyez-nous vos témoignages sur francetélévision.fr"}], "name_en": "Sharing Sport Experience", "name_fr": "partage d'expérience sportive", "name_zh": "分享运动经历", "original_name": "sharing_sport_experience"}}, "name_en": "Sharing Information", "name_fr": "partage d'informations", "name_zh": "共享信息", "original_name": "sharing_information"}, "description_places": {"topic_id": 3, "keywords": ["ville", "endroit", "lieu", "quartier", "bureau", "local", "adresse", "situé", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nouveau"], "total_tasks": 120, "unique_tasks": 77, "subtopics": {"description_city": {"subtopic_id": 1, "task_count": 26, "unique_task_count": 19, "task_ids": ["juillet-2024_c7_t1", "juillet-2024_c32_t1", "decembre-2024_c6_t1", "decembre-2024_c7_t1", "decembre-2024_c8_t1", "decembre-2024_c11_t1", "avril-2025_c4_t1", "Mai-2025_c3_t1", "octobre-2024_c2_t1", "septembre-2024_c3_t1", "octobre-2024_c10_t1", "juin-2025_c3_t1", "juillet-2025_c4_t1"], "keywords": ["ville", "centre", "municipal", "urbain", "métropole"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c7_t1"], "representative_id": "juillet-2024_c7_t1", "task_content": "Vous venez de vous installer dans une nouvelle ville. Vous écrivez un message à un(e) ami(e) pour décrire votre nouvel environnement (quartier, voisins, magasins, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez de vous installer dans une nouvelle ville. vous écrivez un message à un(e) ami(e) pour décrire votre nouvel environnement (quartier, voisins, magasins, etc.)"}, {"task_ids": ["juillet-2024_c32_t1"], "representative_id": "juillet-2024_c32_t1", "task_content": "Rédigez un message pour inviter votre ami à passer ses vacances dans votre ville.", "month_years": ["juillet-2024"], "combination_numbers": ["32"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message pour inviter votre ami à passer ses vacances dans votre ville"}, {"task_ids": ["decembre-2024_c6_t1", "octobre-2024_c2_t1"], "representative_id": "decembre-2024_c6_t1", "task_content": "Vous voulez organiser une visite culturelle dans votre ville. Vous envoyez un message pour inviter vos amis. Vous leur donner toutes les informations nécessaires (activités, date, lieu, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024", "octobre-2024"], "combination_numbers": ["6", "2"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous voulez organiser une visite culturelle dans votre ville. vous envoyez un message pour inviter vos amis. vous leur donner toutes les informations nécessaires (activités, date, lieu, etc.)"}, {"task_ids": ["decembre-2024_c7_t1"], "representative_id": "decembre-2024_c7_t1", "task_content": "écrivez un email pour répondre à votre ami(e) qui va passer le Week-end dans votre ville. Il faut décrire les moyens de transports. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un email pour répondre à votre ami(e) qui va passer le week-end dans votre ville. il faut décrire les moyens de transports"}, {"task_ids": ["decembre-2024_c8_t1", "decembre-2024_c11_t1", "septembre-2024_c3_t1"], "representative_id": "decembre-2024_c8_t1", "task_content": "le journal ” Bienvenue” compte publier un article qui parle des habitants de notre ville. Écrivez nous un message qui sera diffusé dans ce numéro. Vous êtes récemment installé dans cette ville, et il vous est demandé de vous présenter et décrire ensuite tous vos lieux préférés au sein de cette ville. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024", "decembre-2024", "septembre-2024"], "combination_numbers": ["8", "11", "3"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "le journal ” bienvenue” compte publier un article qui parle des habitants de notre ville. écrivez nous un message qui sera diffusé dans ce numéro. vous êtes récemment installé dans cette ville, et il vous est demandé de vous présenter et décrire ensuite tous vos lieux préférés au sein de cette ville"}, {"task_ids": ["avril-2025_c4_t1"], "representative_id": "avril-2025_c4_t1", "task_content": "Invitez votre ami(e) à venir passer les vacances chez vous. Dans votre message, décrivez les sites touristiques et les endroits intéressants à découvrir dans votre ville . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "invitez votre ami(e) à venir passer les vacances chez vous. dans votre message, décrivez les sites touristiques et les endroits intéressants à découvrir dans votre ville"}, {"task_ids": ["Mai-2025_c3_t1"], "representative_id": "Mai-2025_c3_t1", "task_content": "Le journal « Bienvenue » souhaite publier un article sur les nouveaux habitants de la ville. Rédigez un message dans lequel vous vous présentez et décrivez vos endroits favoris dans la ville . (60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "le journal « bienvenue » souhaite publier un article sur les nouveaux habitants de la ville. rédigez un message dans lequel vous vous présentez et décrivez vos endroits favoris dans la ville"}, {"task_ids": ["octobre-2024_c10_t1"], "representative_id": "octobre-2024_c10_t1", "task_content": "« Salut, Je suis vraiment intéressé à l’idée de voyager et de découvrir un autre pays. Peux-tu me parler un peu de ton pays et de sa culture ? Marc. » Écrivez un message à votre ami Marc, qui veut voyager et découvrir un autre pays, pour lui parler de votre pays et de sa culture (lieux, sites touristiques, monuments, etc). »", "month_years": ["octobre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« salut, je suis vraiment intéressé à l’idée de voyager et de découvrir un autre pays. peux-tu me parler un peu de ton pays et de sa culture ? marc. » écrivez un message à votre ami marc, qui veut voyager et découvrir un autre pays, pour lui parler de votre pays et de sa culture (lieux, sites touristiques, monuments, etc). »"}, {"task_ids": ["octobre-2024_c5_t1"], "representative_id": "octobre-2024_c5_t1", "task_content": "écrivez un message à votre ami pour lui décrire le programme de vos prochaines vacances", "month_years": ["octobre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour lui décrire le programme de vos prochaines vacances"}, {"task_ids": ["janvier-2025_c13_t1"], "representative_id": "janvier-2025_c13_t1", "task_content": "Salut je vais venir en vacances dans ton pays tu peux me conseiller un endroit à visiter ? Merci! Vous répondez à votre ami dans votre message ,vous décrivez un lieu intéressant à visiter ( ville,monuments site naturel etc) . (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "salut je vais venir en vacances dans ton pays tu peux me conseiller un endroit à visiter ? merci! vous répondez à votre ami dans votre message ,vous décrivez un lieu intéressant à visiter ( ville,monuments site naturel etc)"}, {"task_ids": ["avril-2025_c3_t1"], "representative_id": "avril-2025_c3_t1", "task_content": "Votre ami(e) désire explorer la région dans laquelle vous vivez. Rédigez-lui un message en lui suggérant des lieux intéressants à découvrir . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami(e) désire explorer la région dans laquelle vous vivez. rédigez-lui un message en lui suggérant des lieux intéressants à découvrir"}, {"task_ids": ["janvier-2025_c8_t1", "novembre-2024_c10_t1", "juillet-2024_c26_t1"], "representative_id": "janvier-2025_c8_t1", "task_content": "Votre ami(e) veut découvrir la région dans laquelle vous habitez. Écrivez lui un message pour lui proposer des sites à visiter . (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025", "novembre-2024", "juillet-2024"], "combination_numbers": ["8", "10", "26"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "votre ami(e) veut découvrir la région dans laquelle vous habitez. écrivez lui un message pour lui proposer des sites à visiter"}, {"task_ids": ["fevrier-2025_c13_t1"], "representative_id": "fevrier-2025_c13_t1", "task_content": "Votre ami(e) souhaite découvrir votre région. Rédigez un message pour lui recommander des sites intéressants à visiter . (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami(e) souhaite découvrir votre région. rédigez un message pour lui recommander des sites intéressants à visiter"}, {"task_ids": ["avril-2025_c1_t1"], "representative_id": "avril-2025_c1_t1", "task_content": "<PERSON><PERSON><PERSON>, Je viens bientôt en vacances dans ta région. Est-ce que tu peux me conseiller une visite à faire pendant mon voyage ? Merc<PERSON> beaucoup. <PERSON><PERSON> Vous répondez à votre ami <PERSON>. Vous décrivez un lieu à visiter dans votre région . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "bonjour, je viens bientôt en vacances dans ta région. est-ce que tu peux me conseiller une visite à faire pendant mon voyage ? merci beaucoup. mathieu vous répondez à votre ami mathieu. vous décrivez un lieu à visiter dans votre région"}, {"task_ids": ["janvier-2025_c1_t1", "juillet-2024_c13_t1"], "representative_id": "janvier-2025_c1_t1", "task_content": "Je suis votre amie Anna et je compte passer un weekend dans ta ville. Donnez-moi des informations sur les moyens de transport pour explorer la ville. Répondez à Anna dans un message. (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025", "juillet-2024"], "combination_numbers": ["1", "13"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "je suis votre amie anna et je compte passer un weekend dans ta ville. donnez-moi des informations sur les moyens de transport pour explorer la ville. répondez à anna dans un message"}, {"task_ids": ["avril-2025_c14_t1"], "representative_id": "avril-2025_c14_t1", "task_content": "Vous venez d’emménager dans une nouvelle ville. Rédigez un message pour inviter une amie à venir passer les vacances chez vous . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez d’emménager dans une nouvelle ville. rédigez un message pour inviter une amie à venir passer les vacances chez vous"}, {"task_ids": ["novembre-2024_c11_t1"], "representative_id": "novembre-2024_c11_t1", "task_content": "Vous avez récemment déménagé dans une nouvelle ville. Invitez votre amie à venir passer les vacances chez vous. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment déménagé dans une nouvelle ville. invitez votre amie à venir passer les vacances chez vous"}, {"task_ids": ["avril-2025_c17_t1", "juin-2024_c3_t1"], "representative_id": "avril-2025_c17_t1", "task_content": "Rédigez un email pour inviter un ami à passer une journée avec vous, en incluant les informations essentielles telles que le lieu, la date, l’heure, et les activités prévues pour la journée . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025", "juin-2024"], "combination_numbers": ["17", "3"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un email pour inviter un ami à passer une journée avec vous, en incluant les informations essentielles telles que le lieu, la date, l’heure, et les activités prévues pour la journée"}, {"task_ids": ["juillet-2025_c2_t1"], "representative_id": "juillet-2025_c2_t1", "task_content": "Votre ami(e) souhaite visiter votre région.Rédigez-lui un message pour lui recommander quelques lieux intéressants à découvrir.\n(60 mots minimum/120 mots maximum)", "month_years": ["juillet-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1}], "tasks": [{"id": "juin-2025_c3_t1", "task_content": "Rédigez un email pour inviter un ami à passer une journée avec vous, en incluant les informations essentielles telles que le lieu, la date, l’heure, et les activités prévues pour la journée.\n(60 mots minimum/120 mots maximum)", "month_year": "juin-2025", "combination_number": "3"}, {"id": "juillet-2025_c4_t1", "task_content": "Invitez votre ami(e) à venir passer les vacances chez vous.Dans votre message, décrivez les sites touristiques et les endroits intéressants à découvrir dans votre ville.\n(60 mots minimum/120 mots maximum)", "month_year": "juillet-2025", "combination_number": "4"}], "name_en": "City Description", "name_fr": "ville de description", "name_zh": "描述城市", "original_name": "description_city"}, "description_office": {"subtopic_id": 2, "task_count": 16, "unique_task_count": 9, "task_ids": ["fevrier-2025_c26_t1", "avril-2025_c9_t1", "novembre-2024_c20_t1", "juin-2025_c1_t1"], "keywords": ["bureau", "entreprise", "local", "travail", "professionnel", "affaires"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c26_t1"], "representative_id": "fevrier-2025_c26_t1", "task_content": "Envoyez un courriel à votre ami Lucas pour lui présenter les nouveaux locaux de votre entreprise en précisant leur localisation, l’organisation des espaces et les équipements mis à disposition. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["26"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "envoyez un courriel à votre ami lucas pour lui présenter les nouveaux locaux de votre entreprise en précisant leur localisation, l’organisation des espaces et les équipements mis à disposition"}, {"task_ids": ["avril-2025_c9_t1"], "representative_id": "avril-2025_c9_t1", "task_content": "Répondez au courriel de votre ami Lucas en lui fournissant des détails sur les nouveaux locaux de votre entreprise : emplacement, aménagement des pièces, équipements disponibles, etc . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "répondez au courriel de votre ami lucas en lui fournissant des détails sur les nouveaux locaux de votre entreprise : emplacement, aménagement des pièces, équipements disponibles, etc"}, {"task_ids": ["septembre-2024_c2_t1", "novembre-2024_c14_t1", "decembre-2024_c4_t1", "decembre-2024_c19_t1", "juillet-2024_c37_t1", "aout-2024_c3_t1"], "representative_id": "septembre-2024_c2_t1", "task_content": "Répondez au courriel de votre ami Lucas pour lui donner des informations sur les nouveaux locaux de votre entreprise (lieu, disposition des pièces, équipements, etc.)", "month_years": ["septembre-2024", "novembre-2024", "decembre-2024", "decembre-2024", "juillet-2024", "aout-2024"], "combination_numbers": ["2", "14", "4", "19", "37", "3"], "is_duplicate_group": true, "duplicate_count": 6, "clean_content": "répondez au courriel de votre ami lucas pour lui donner des informations sur les nouveaux locaux de votre entreprise (lieu, disposition des pièces, équipements, etc.)"}, {"task_ids": ["juillet-2024_c20_t1"], "representative_id": "juillet-2024_c20_t1", "task_content": "<PERSON><PERSON>, <PERSON> as commencé ton nouveau travail ! C’est comment ? Tu es content(e) ? <PERSON> Vous répondez à votre ami Ali. Dans votre message, vous décrivez votre nouveau travail (lieu, collègues, etc.) et vous donnez vos impressions.", "month_years": ["juillet-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "salut, tu as commencé ton nouveau travail ! c’est comment ? tu es content(e) ? ali vous répondez à votre ami ali. dans votre message, vous décrivez votre nouveau travail (lieu, collègues, etc.) et vous donnez vos impressions"}, {"task_ids": ["janvier-2025_c4_t1", "janvier-2025_c16_t1"], "representative_id": "janvier-2025_c4_t1", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez à votre amie Francophone pour lui annoncer la bonne nouvelle. Vous décrivez votre poste, vos collègues et votre lieu de travail. (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025", "janvier-2025"], "combination_numbers": ["4", "16"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez trouvé un nouveau travail. vous écrivez à votre amie francophone pour lui annoncer la bonne nouvelle. vous décrivez votre poste, vos collègues et votre lieu de travail"}, {"task_ids": ["mars-2025_c3_t1"], "representative_id": "mars-2025_c3_t1", "task_content": "Rédiger une réponse à un courriel d’un ami demandant des informations sur les nouveaux locaux de votre entreprise, en décrivant leur emplacement, l’aménagement des espaces et les équipements disponibles . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger une réponse à un courriel d’un ami demandant des informations sur les nouveaux locaux de votre entreprise, en décrivant leur emplacement, l’aménagement des espaces et les équipements disponibles"}, {"task_ids": ["mars-2025_c11_t1"], "representative_id": "mars-2025_c11_t1", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone. Décrivez votre travail, vos collègues et votre environnement professionnel . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone. décrivez votre travail, vos collègues et votre environnement professionnel"}, {"task_ids": ["fevrier-2025_c5_t1", "fevrier-2025_c21_t1"], "representative_id": "fevrier-2025_c5_t1", "task_content": "Ali t’a envoyé un message pour savoir comment se passe ton nouveau travail et si tu en es satisfait(e). Tu lui réponds en décrivant ton emploi (lieu, ambiance, collègues, etc.) et en partageant tes impressions. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["5", "21"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "ali t’a envoyé un message pour savoir comment se passe ton nouveau travail et si tu en es satisfait(e). tu lui réponds en décrivant ton emploi (lieu, ambiance, collègues, etc.) et en partageant tes impressions"}, {"task_ids": ["juin-2024_c1_t1"], "representative_id": "juin-2024_c1_t1", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone.Décrivez votre travail, vos collègues et votre environnement professionnel.\n(60 mots minimum/120 mots maximum)", "month_years": ["juin-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone.décrivez votre travail, vos collègues et votre environnement professionnel"}], "tasks": [{"id": "juin-2025_c1_t1", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone.Décrivez votre travail, vos collègues et votre environnement professionnel.\n(60 mots minimum/120 mots maximum)", "month_year": "juin-2025", "combination_number": "1"}], "name_en": "Office Description", "name_fr": "bureau de description", "name_zh": "描述办公室", "original_name": "description_office"}, "description_university": {"subtopic_id": 5, "task_count": 3, "unique_task_count": 2, "task_ids": ["aout-2024_c1_t1", "juin-2025_c2_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["aout-2024_c1_t1"], "representative_id": "aout-2024_c1_t1", "task_content": "Vous êtes nouveaux dans une université. Décrivez à vos amis comment cela se passe avec ( les prof, autres étudiants, les activités et autres ).", "month_years": ["aout-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes nouveaux dans une université. décrivez à vos amis comment cela se passe avec ( les prof, autres étudiants, les activités et autres )"}, {"task_ids": ["mars-2025_c13_t1", "juin-2024_c2_t1"], "representative_id": "mars-2025_c13_t1", "task_content": "Vous avez reçu un message d’un ami Alex qui vous demande des nouvelles de votre nouvelle université. Vous lui répondre en décrivant brièvement votre environnement universitaire . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025", "juin-2024"], "combination_numbers": ["13", "2"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez reçu un message d’un ami alex qui vous demande des nouvelles de votre nouvelle université. vous lui répondre en décrivant brièvement votre environnement universitaire"}], "tasks": [{"id": "juin-2025_c2_t1", "task_content": "Vous avez reçu un message d’un ami Alex qui vous demande des nouvelles de votre nouvelle université.Vous lui répondre en décrivant brièvement votre environnement universitaire.\n(60 mots minimum/120 mots maximum)", "month_year": "juin-2025", "combination_number": "2"}], "name_en": "University Description", "name_fr": "université de description", "name_zh": "大学描述", "original_name": "description_university"}, "description_restaurant": {"subtopic_id": 6, "task_count": 7, "unique_task_count": 5, "task_ids": ["decembre-2024_c14_t1", "avril-2025_c15_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["decembre-2024_c14_t1"], "representative_id": "decembre-2024_c14_t1", "task_content": "Un nouveau restaurant vient d’ouvrir près de chez vous. Vous écrivez à un(e) ami(e) pour lui proposer d’y aller avec vous. Vous décrivez le restaurant (cuisine, prix, décoration, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un nouveau restaurant vient d’ouvrir près de chez vous. vous écrivez à un(e) ami(e) pour lui proposer d’y aller avec vous. vous décrivez le restaurant (cuisine, prix, décoration, etc.)"}, {"task_ids": ["avril-2025_c15_t1"], "representative_id": "avril-2025_c15_t1", "task_content": "Vous avez découvert un nouveau restaurant. Écrivez un message à votre ami pour décrire l’ambiance, la décoration, les plats et le service . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez découvert un nouveau restaurant. écrivez un message à votre ami pour décrire l’ambiance, la décoration, les plats et le service"}, {"task_ids": ["juillet-2024_c15_t1", "novembre-2024_c13_t1", "decembre-2024_c17_t1"], "representative_id": "juillet-2024_c15_t1", "task_content": "Vous souhaitez fêter votre anniversaire dans un restaurant. Vous invitez vos amis. Vous leur écrivez un courriel pour leur donner toutes les informations nécessaires (lieu, date, menu, prix…) et vous leur demandez une réponse.", "month_years": ["juillet-2024", "novembre-2024", "decembre-2024"], "combination_numbers": ["15", "13", "17"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous souhaitez fêter votre anniversaire dans un restaurant. vous invitez vos amis. vous leur écrivez un courriel pour leur donner toutes les informations nécessaires (lieu, date, menu, prix…) et vous leur demandez une réponse"}, {"task_ids": ["fevrier-2025_c28_t1"], "representative_id": "fevrier-2025_c28_t1", "task_content": "Vous souhaitez célébrer votre anniversaire dans un restaurant et avez invité vos amis. Vous leur écrivez un courriel pour leur fournir toutes les informations nécessaires telles que le lieu, la date, le menu, les prix, et vous leur demandez de confirmer leur présence . (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["28"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez célébrer votre anniversaire dans un restaurant et avez invité vos amis. vous leur écrivez un courriel pour leur fournir toutes les informations nécessaires telles que le lieu, la date, le menu, les prix, et vous leur demandez de confirmer leur présence"}, {"task_ids": ["avril-2025_c10_t1"], "representative_id": "avril-2025_c10_t1", "task_content": "Envoyez un courriel à vos amis pour les inviter à fêter votre anniversaire dans un restaurant. Incluez toutes les informations nécessaires comme le lieu, la date, le menu, les prix, et demandez-leur de confirmer leur présence . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "envoyez un courriel à vos amis pour les inviter à fêter votre anniversaire dans un restaurant. incluez toutes les informations nécessaires comme le lieu, la date, le menu, les prix, et demandez-leur de confirmer leur présence"}], "name_en": "Restaurant Description", "name_fr": "Description du restaurant", "name_zh": "描述餐厅", "original_name": "description_restaurant"}, "description_hotel": {"subtopic_id": 7, "task_count": 6, "unique_task_count": 5, "task_ids": ["avril-2025_c20_t1", "juillet-2024_c24_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c20_t1"], "representative_id": "avril-2025_c20_t1", "task_content": "Vous partez en vacances avec des amis et avez trouvé un hôtel. Écrivez-leur pour décrire l’hôtel : son emplacement, son tarif, ses équipements, et suggérez-leur de réserver cet hôtel . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en vacances avec des amis et avez trouvé un hôtel. écrivez-leur pour décrire l’hôtel : son emplacement, son tarif, ses équipements, et suggérez-leur de réserver cet hôtel"}, {"task_ids": ["juillet-2024_c24_t1"], "representative_id": "juillet-2024_c24_t1", "task_content": "Vous partez en vacances avec vos amis, vous avez trouvé un hôtel. Vous écrivez un message à vos amis pour décrire cet hôtel (localisation, prix, équipements, etc.) et vous leur proposez de réserver cet hôtel.", "month_years": ["juillet-2024"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en vacances avec vos amis, vous avez trouvé un hôtel. vous écrivez un message à vos amis pour décrire cet hôtel (localisation, prix, équipements, etc.) et vous leur proposez de réserver cet hôtel"}, {"task_ids": ["juillet-2024_c23_t1", "novembre-2024_c5_t1"], "representative_id": "juillet-2024_c23_t1", "task_content": "“Bon<PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le Canada. Je vais arriver le 3 mars. Est-ce que tu pourras m’aider à trouver un hôtel pour la première semaine ? Merci d’avance pour ton aide.<PERSON> <PERSON>. Vous avez trouvé un hôtel pour Matthias. Vous lui écrivez un courriel. Dans ce message vous décrivez l’hôtel et vous lui donnez toutes les informations utiles (situation, tarif…).", "month_years": ["juillet-2024", "novembre-2024"], "combination_numbers": ["23", "5"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "“bonjour, ça y est, j’ai obtenu mon visa pour le canada. je vais arriver le 3 mars. est-ce que tu pourras m’aider à trouver un hôtel pour la première semaine ? merci d’avance pour ton aide.” matthias. vous avez trouvé un hôtel pour matthias. vous lui écrivez un courriel. dans ce message vous décrivez l’hôtel et vous lui donnez toutes les informations utiles (situation, tarif…)"}, {"task_ids": ["octobre-2024_c11_t1"], "representative_id": "octobre-2024_c11_t1", "task_content": "écrivez un message à vos amis pour leur dire que vous avez trouvé un hôtel pour aller en vacances", "month_years": ["octobre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à vos amis pour leur dire que vous avez trouvé un hôtel pour aller en vacances"}, {"task_ids": ["fevrier-2025_c9_t1"], "representative_id": "fevrier-2025_c9_t1", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon visa pour le Canada. Mon arrivée est prévue pour le 3 mars. Pourrais-tu m’aider à trouver un hôtel pour ma première semaine sur place ? Merci beaucoup pour ton aide ! » <PERSON>. Vous avez réservé un hôtel pour <PERSON> et lui envoyez un courriel contenant une description détaillée de l’établissement ainsi que toutes les informations essentielles, telles que son emplacement, le prix et les services proposés. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« bonjour, bonne nouvelle ! j’ai enfin obtenu mon visa pour le canada. mon arrivée est prévue pour le 3 mars. pourrais-tu m’aider à trouver un hôtel pour ma première semaine sur place ? merci beaucoup pour ton aide ! » matthias. vous avez réservé un hôtel pour matthias et lui envoyez un courriel contenant une description détaillée de l’établissement ainsi que toutes les informations essentielles, telles que son emplacement, le prix et les services proposés"}], "name_en": "Hotel Description", "name_fr": "hôtel description", "name_zh": "描述酒店", "original_name": "description_hotel"}, "description_gym": {"subtopic_id": 8, "task_count": 7, "unique_task_count": 5, "task_ids": ["juillet-2025_c3_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c20_t1"], "representative_id": "novembre-2024_c20_t1", "task_content": "Votre ami souhaite commencer à faire du sport. Rédigez un message pour lui recommander une salle de sport située dans votre quartier (localisation, tarifs, types d’activités, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami souhaite commencer à faire du sport. rédigez un message pour lui recommander une salle de sport située dans votre quartier (localisation, tarifs, types d’activités, etc.)"}, {"task_ids": ["septembre-2024_c8_t1"], "representative_id": "septembre-2024_c8_t1", "task_content": "Votre ami veut se mettre au sport. Vous lui envoyez un message pour lui conseiller une salle de sport situé dans votre quartier ( localisation, prix, type d’activités, etc…)", "month_years": ["septembre-2024"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami veut se mettre au sport. vous lui envoyez un message pour lui conseiller une salle de sport situé dans votre quartier ( localisation, prix, type d’activités, etc…)"}, {"task_ids": ["novembre-2024_c15_t1"], "representative_id": "novembre-2024_c15_t1", "task_content": "“ Sal<PERSON>, j’ai appris que tu vas à une salle de sport et qu’elle est magnifique. Peux-tu m’en dire plus “. Écrivez un message pour répondre à votre ami concernant ce sujet. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "“ salut, j’ai appris que tu vas à une salle de sport et qu’elle est magnifique. peux-tu m’en dire plus “. écrivez un message pour répondre à votre ami concernant ce sujet"}, {"task_ids": ["mars-2025_c1_t1"], "representative_id": "mars-2025_c1_t1", "task_content": "Rédiger un message en réponse à un ami qui souhaite commencer le sport et cherche une salle adaptée à ses besoins. (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un message en réponse à un ami qui souhaite commencer le sport et cherche une salle adaptée à ses besoins"}, {"task_ids": ["avril-2025_c5_t1", "fevrier-2025_c30_t1", "mars-2025_c7_t1"], "representative_id": "avril-2025_c5_t1", "task_content": "Rédiger un message en réponse à un ami qui demande des informations sur une salle de sport réputée pour sa qualité. (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025", "fevrier-2025", "mars-2025"], "combination_numbers": ["5", "30", "7"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "rédiger un message en réponse à un ami qui demande des informations sur une salle de sport réputée pour sa qualité"}], "tasks": [{"id": "juillet-2025_c3_t1", "task_content": "Rédiger un message en réponse à un ami qui demande des informations sur une salle de sport réputée pour sa qualité.\n(60 mots minimum/120 mots maximum)", "month_year": "juillet-2025", "combination_number": "3"}], "name_en": "Gym Description", "name_fr": "gymnastique descriptive", "name_zh": "描述体育馆", "original_name": "description_gym"}, "description_park": {"subtopic_id": 4, "task_count": 3, "unique_task_count": 1, "task_ids": ["juillet-2024_c34_t1", "novembre-2024_c3_t1", "octobre-2024_c7_t1"], "keywords": ["château", "monument", "musée", "touristique", "historique"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c34_t1", "novembre-2024_c3_t1", "octobre-2024_c7_t1"], "representative_id": "juillet-2024_c34_t1", "task_content": "Pa<PERSON> de loisi<PERSON> : “J’ai hâte de passer la journée avec toi demain. S’il te plaît, dis-moi, quelle activité nous pourrons faire ?” Répondez à votre ami pour lui décrire la sortie (horaires, transport, billet, activités).", "month_years": ["juillet-2024", "novembre-2024", "octobre-2024"], "combination_numbers": ["34", "3", "7"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "parc de loisirs : “j’ai hâte de passer la journée avec toi demain. s’il te plaît, dis-moi, quelle activité nous pourrons faire ?” répondez à votre ami pour lui décrire la sortie (horaires, transport, billet, activités)"}], "name_en": "Park Description", "name_fr": "parc descriptif", "name_zh": "描述公园", "original_name": "description_park"}, "description_wedding_place": {"subtopic_id": 9, "task_count": 4, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c14_t1", "janvier-2025_c2_t1", "novembre-2024_c8_t1"], "representative_id": "juillet-2024_c14_t1", "task_content": "Vous avez invité votre ami Cédric à votre mariage au Château de Chombony et il vous a répondu qu’il ne connait pas ce château. Décrivez à votre ami (lieu, localisation, transports, etc.).", "month_years": ["juillet-2024", "janvier-2025", "novembre-2024"], "combination_numbers": ["14", "2", "8"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous avez invité votre ami cédric à votre mariage au château de chombony et il vous a répondu qu’il ne connait pas ce château. décrivez à votre ami (lieu, localisation, transports, etc.)"}, {"task_ids": ["fevrier-2025_c12_t1"], "representative_id": "fevrier-2025_c12_t1", "task_content": "Vous avez invité votre ami Cédric à votre mariage au Château de Chombony, mais il vous a répondu qu’il ne connaît pas ce château. Décrivez-lui le lieu en précisant son emplacement, les transports à prendre, et d’autres informations utiles. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez invité votre ami cédric à votre mariage au château de chombony, mais il vous a répondu qu’il ne connaît pas ce château. décrivez-lui le lieu en précisant son emplacement, les transports à prendre, et d’autres informations utiles"}], "name_en": "Wedding Place Description", "name_fr": "Description du lieu de mariage", "name_zh": "婚礼场地描述", "original_name": "description_wedding_place"}, "description_shopping_place": {"subtopic_id": 10, "task_count": 7, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c19_t1", "janvier-2025_c5_t1", "janvier-2025_c11_t1", "novembre-2024_c2_t1"], "representative_id": "juillet-2024_c19_t1", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherche un endroit sympathique pour faire mes courses. Est-ce que tu connais un marché intéressant ? Merci d’avance et à bientôt ! <PERSON> Vous répondez à votre ami <PERSON>. Dans votre message, vous décrivez un marché de votre quartier que vous aimez bien (lieu, horaires, produits, etc.)", "month_years": ["juillet-2024", "janvier-2025", "janvier-2025", "novembre-2024"], "combination_numbers": ["19", "5", "11", "2"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "je vais bientôt vivre dans ton quartier. je cherche un endroit sympathique pour faire mes courses. est-ce que tu connais un marché intéressant ? merci d’avance et à bientôt ! bernard vous répondez à votre ami bernard. dans votre message, vous décrivez un marché de votre quartier que vous aimez bien (lieu, horaires, produits, etc.)"}, {"task_ids": ["fevrier-2025_c1_t1", "fevrier-2025_c7_t1", "fevrier-2025_c16_t1"], "representative_id": "fevrier-2025_c1_t1", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans ton quartier et qu’il cherche un bon marché pour faire ses courses. Tu lui réponds en lui décrivant un marché que tu apprécies particulièrement, en précisant son emplacement, ses horaires et les produits qu’on peut y trouver. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025", "fevrier-2025"], "combination_numbers": ["1", "7", "16"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "be<PERSON>rd t’informe qu’il va bientôt habiter dans ton quartier et qu’il cherche un bon marché pour faire ses courses. tu lui réponds en lui décrivant un marché que tu apprécies particulièrement, en précisant son emplacement, ses horaires et les produits qu’on peut y trouver"}], "name_en": "Shopping Place Description", "name_fr": "lieu d'achat description", "name_zh": "购物场所描述", "original_name": "description_shopping_place"}, "description_transportation": {"subtopic_id": 11, "task_count": 7, "unique_task_count": 4, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c11_t1"], "representative_id": "juillet-2024_c11_t1", "task_content": "Votre ami Mehdi vient de s’installer dans votre ville et il a besoin d’aide concernant les transports. Répondez-lui en lui donnant les informations (types de transport, abonnement, prix…)", "month_years": ["juillet-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami mehdi vient de s’installer dans votre ville et il a besoin d’aide concernant les transports. répondez-lui en lui donnant les informations (types de transport, abonnement, prix…)"}, {"task_ids": ["novembre-2024_c7_t1", "janvier-2025_c15_t1", "janvier-2025_c3_t1"], "representative_id": "novembre-2024_c7_t1", "task_content": "Votre ami <PERSON> vient de d’emménager dans votre ville et cherche des renseignements sur les moyens de transports. Écrivez un message en lui donnant les informations nécessaires (types de transport, abonnement, tarif, etc. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "janvier-2025", "janvier-2025"], "combination_numbers": ["7", "15", "3"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "votre ami mehdi vient de d’emménager dans votre ville et cherche des renseignements sur les moyens de transports. écrivez un message en lui donnant les informations nécessaires (types de transport, abonnement, tarif, etc"}, {"task_ids": ["mars-2025_c12_t1", "fevrier-2025_c11_t1"], "representative_id": "mars-2025_c12_t1", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville et cherche des informations sur les moyens de transport. Écrivez-lui un message pour lui fournir toutes les informations utiles, telles que les options de transport disponibles, les tarifs, les abonnements, etc . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025", "fevrier-2025"], "combination_numbers": ["12", "11"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "votre ami mehdi vient d’arriver dans votre ville et cherche des informations sur les moyens de transport. écrivez-lui un message pour lui fournir toutes les informations utiles, telles que les options de transport disponibles, les tarifs, les abonnements, etc"}, {"task_ids": ["mars-2025_c14_t1"], "representative_id": "mars-2025_c14_t1", "task_content": "Votre amie Anna prévoit de passer un week-end dans votre ville et souhaite en savoir plus sur les moyens de transport disponibles pour visiter les environs. Rédigez-lui un message en détaillant les différentes options (bus, métro, vélo, etc.) . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre amie anna prévoit de passer un week-end dans votre ville et souhaite en savoir plus sur les moyens de transport disponibles pour visiter les environs. rédigez-lui un message en détaillant les différentes options (bus, métro, vélo, etc.)"}], "name_en": "Transportation Description", "name_fr": "transport de description", "name_zh": "描述交通", "original_name": "description_transportation"}, "description_party": {"subtopic_id": 0, "task_count": 11, "unique_task_count": 7, "task_ids": ["juillet-2024_c4_t1", "juillet-2024_c9_t1", "juillet-2024_c14_t1", "juillet-2024_c19_t1", "juillet-2024_c20_t1", "juillet-2024_c21_t1", "juillet-2024_c22_t1", "juillet-2024_c37_t1", "decembre-2024_c1_t1", "decembre-2024_c4_t1", "decembre-2024_c19_t1", "janvier-2025_c2_t1", "janvier-2025_c4_t1", "janvier-2025_c5_t1", "janvier-2025_c6_t1", "janvier-2025_c11_t1", "janvier-2025_c12_t1", "janvier-2025_c13_t1", "janvier-2025_c16_t1", "janvier-2025_c19_t1", "aout-2024_c3_t1", "aout-2024_c6_t1", "fevrier-2025_c8_t1", "fevrier-2025_c10_t1", "avril-2025_c3_t1", "avril-2025_c7_t1", "avril-2025_c12_t1", "novembre-2024_c1_t1", "novembre-2024_c2_t1", "novembre-2024_c6_t1", "novembre-2024_c8_t1", "novembre-2024_c14_t1", "Mai-2025_c4_t1", "mars-2025_c10_t1", "octobre-2024_c1_t1", "octobre-2024_c9_t1", "octobre-2024_c16_t1", "octobre-2024_c17_t1", "septembre-2024_c2_t1", "septembre-2024_c8_t1"], "keywords": ["lieu", "endroit", "salle", "espace", "bâtiment"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c4_t1", "decembre-2024_c1_t1", "janvier-2025_c19_t1", "aout-2024_c6_t1", "octobre-2024_c16_t1"], "representative_id": "juillet-2024_c4_t1", "task_content": "Écrivez un courriel à vos amis pour les inviter à un anniversaire surpris de votre meilleur ami. (Lieu, date, horaire, etc.).", "month_years": ["juillet-2024", "decembre-2024", "janvier-2025", "aout-2024", "octobre-2024"], "combination_numbers": ["4", "1", "19", "6", "16"], "is_duplicate_group": true, "duplicate_count": 5, "clean_content": "écrivez un courriel à vos amis pour les inviter à un anniversaire surpris de votre meilleur ami. (lieu, date, horaire, etc.)"}, {"task_ids": ["avril-2025_c7_t1"], "representative_id": "avril-2025_c7_t1", "task_content": "Vous organisez bientôt votre fête d’anniversaire. Écrivez un message à vos amis pour les inviter et leur expliquer ce que vous avez prévu (lieu, activités, ambiance, etc.) . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous organisez bientôt votre fête d’anniversaire. écrivez un message à vos amis pour les inviter et leur expliquer ce que vous avez prévu (lieu, activités, ambiance, etc.)"}, {"task_ids": ["avril-2025_c12_t1"], "representative_id": "avril-2025_c12_t1", "task_content": "Vous souhaitez organiser une fête et souhaitez inviter vos amis tout en leur demandant un coup de main pour la préparation (lieu, date, thème, etc.). Rédigez un message en ce sens . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez organiser une fête et souhaitez inviter vos amis tout en leur demandant un coup de main pour la préparation (lieu, date, thème, etc.). rédigez un message en ce sens"}, {"task_ids": ["mars-2025_c10_t1"], "representative_id": "mars-2025_c10_t1", "task_content": "Rédigez un courriel à vos amis pour les inviter à une fête d’anniversaire surprise en précisant les détails de l’événement (lieu, date, heure, et organisation) . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel à vos amis pour les inviter à une fête d’anniversaire surprise en précisant les détails de l’événement (lieu, date, heure, et organisation)"}, {"task_ids": ["decembre-2024_c12_t1"], "representative_id": "decembre-2024_c12_t1", "task_content": "Écrivez un message pour inviter vos amis à une fête de fin d’année. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message pour inviter vos amis à une fête de fin d’année"}, {"task_ids": ["septembre-2024_c9_t1"], "representative_id": "septembre-2024_c9_t1", "task_content": "Ecrivez un message à vos amis pour les inviter à votre anniversaire et leur raconter comment va se dérouler votre fête.", "month_years": ["septembre-2024"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ecrivez un message à vos amis pour les inviter à votre anniversaire et leur raconter comment va se dérouler votre fête"}, {"task_ids": ["avril-2025_c11_t1"], "representative_id": "avril-2025_c11_t1", "task_content": "Rédigez un courriel à vos amis pour les convier à une fête d’anniversaire surprise organisée en l’honneur de votre meilleur ami. Mentionnez les détails essentiels : lieu, date, heure et toute autre information importante . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel à vos amis pour les convier à une fête d’anniversaire surprise organisée en l’honneur de votre meilleur ami. mentionnez les détails essentiels : lieu, date, heure et toute autre information importante"}], "name_en": "Party Description", "name_fr": "catégorie de description", "name_zh": "描述派对", "original_name": "description_party"}, "description_apartment": {"subtopic_id": 12, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c10_t1"], "representative_id": "septembre-2024_c10_t1", "task_content": "Vous partez en voyage et vous laissez votre appartement à un ami qui veut venir rester chez-vous pendant vos vacances. Vous lui envoyez un message pour décrire votre appartement (immeuble, logement, accès…).", "month_years": ["septembre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en voyage et vous laissez votre appartement à un ami qui veut venir rester chez-vous pendant vos vacances. vous lui envoyez un message pour décrire votre appartement (immeuble, logement, accès…)"}], "name_en": "Apartment Description", "name_fr": "description de l'appartement", "name_zh": "描述公寓", "original_name": "description_apartment"}, "description_camping": {"subtopic_id": 3, "task_count": 8, "unique_task_count": 6, "task_ids": ["juillet-2024_c33_t1", "novembre-2024_c18_t1", "octobre-2024_c5_t1", "septembre-2024_c10_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c8_t1"], "representative_id": "fevrier-2025_c8_t1", "task_content": "Léa t’a envoyé un message pour te proposer un pique-nique samedi prochain et te demander si tu connais un endroit agréable pour les enfants et les adultes. Tu réponds en acceptant son invitation et en lui suggérant un lieu adapté pour le pique-nique, en décrivant les caractéristiques de l’endroit et en expliquant les activités possibles sur place. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "léa t’a envoyé un message pour te proposer un pique-nique samedi prochain et te demander si tu connais un endroit agréable pour les enfants et les adultes. tu réponds en acceptant son invitation et en lui suggérant un lieu adapté pour le pique-nique, en décrivant les caractéristiques de l’endroit et en expliquant les activités possibles sur place"}, {"task_ids": ["janvier-2025_c12_t1"], "representative_id": "janvier-2025_c12_t1", "task_content": "L’été est arrivé ! Je vous propose de faire un pique-nique samedi prochain. Connaissez-vous un endroit sympa pour les enfants et les adultes où nous pouvons tous nous retrouver ? Bisous, à bientôt. Léa” Vous acceptez l’invitation de Léa et vous lui proposez un endroit pour organiser le pique-nique. Vous décrivez le lieu et expliquez quelles sont les activités possibles . (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "l’été est arrivé ! je vous propose de faire un pique-nique samedi prochain. connaissez-vous un endroit sympa pour les enfants et les adultes où nous pouvons tous nous retrouver ? bisous, à bientôt. léa” vous acceptez l’invitation de léa et vous lui proposez un endroit pour organiser le pique-nique. vous décrivez le lieu et expliquez quelles sont les activités possibles"}, {"task_ids": ["novembre-2024_c1_t1"], "representative_id": "novembre-2024_c1_t1", "task_content": "Écrire un message à votre amie pour lui proposer un lieu de camping (lieu, date, activités…) (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrire un message à votre amie pour lui proposer un lieu de camping (lieu, date, activités…)"}, {"task_ids": ["juillet-2024_c22_t1", "janvier-2025_c6_t1"], "representative_id": "juillet-2024_c22_t1", "task_content": "Je cherche un endroit pour déjeuner en plein air ce week-end. Qu’est-ce que tu me proposes ? <PERSON> <PERSON><PERSON><PERSON><PERSON>, <PERSON> répondez à votre amie Barbara en décrivant le lieu (parc, jardin, terrasse, etc.).", "month_years": ["juillet-2024", "janvier-2025"], "combination_numbers": ["22", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "je cherche un endroit pour déjeuner en plein air ce week-end. qu’est-ce que tu me proposes ? à bient<PERSON><PERSON>, barbara vous répondez à votre amie barbara en décrivant le lieu (parc, jardin, terrasse, etc.)"}, {"task_ids": ["juillet-2024_c21_t1"], "representative_id": "juillet-2024_c21_t1", "task_content": "Votre ami vous propose de faire du camping. Écrivez un message à votre ami pour lui suggérer un endroit où camper en précisant le lieu, la date et les activités prévues.", "month_years": ["juillet-2024"], "combination_numbers": ["21"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami vous propose de faire du camping. écrivez un message à votre ami pour lui suggérer un endroit où camper en précisant le lieu, la date et les activités prévues"}, {"task_ids": ["fevrier-2025_c15_t1", "fevrier-2025_c6_t1"], "representative_id": "fevrier-2025_c15_t1", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle pourrait déjeuner en plein air ce week-end. Tu lui réponds en décrivant un lieu agréable (parc, jardin, terrasse, etc.) et ses caractéristiques. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["15", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "barbara t’a envoyé un message pour savoir où elle pourrait déjeuner en plein air ce week-end. tu lui réponds en décrivant un lieu agréable (parc, jardin, terrasse, etc.) et ses caractéristiques"}], "name_en": "Camping Description", "name_fr": "Description du camping", "name_zh": "露营描述", "original_name": "description_camping"}, "descripiton_trip": {"subtopic_id": 13, "task_count": 14, "unique_task_count": 9, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c6_t1", "octobre-2024_c17_t1", "octobre-2024_c9_t1", "juillet-2024_c9_t1"], "representative_id": "novembre-2024_c6_t1", "task_content": "Vous voulez partir en week-end avec vos amis le mois prochain. Vous leur écrivez un message pour décrire votre projet (lieu, transport, activités, etc.) (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "octobre-2024", "octobre-2024", "juillet-2024"], "combination_numbers": ["6", "17", "9", "9"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous voulez partir en week-end avec vos amis le mois prochain. vous leur écrivez un message pour décrire votre projet (lieu, transport, activités, etc.)"}, {"task_ids": ["Mai-2025_c4_t1"], "representative_id": "Mai-2025_c4_t1", "task_content": "Vous envisagez de partir en week-end avec vos amis le mois prochain. Rédigez un message pour leur présenter votre projet, incluant le lieu, les moyens de transport et les activités prévues . (60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous envisagez de partir en week-end avec vos amis le mois prochain. rédigez un message pour leur présenter votre projet, incluant le lieu, les moyens de transport et les activités prévues"}, {"task_ids": ["fevrier-2025_c19_t1"], "representative_id": "fevrier-2025_c19_t1", "task_content": "Rédigez un message à votre ami(e) dans lequel vous détaillez le programme de vos prochaines vacances, en précisant la destination, les dates et les activités prévues. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à votre ami(e) dans lequel vous détaillez le programme de vos prochaines vacances, en précisant la destination, les dates et les activités prévues"}, {"task_ids": ["fevrier-2025_c10_t1"], "representative_id": "fevrier-2025_c10_t1", "task_content": "Vous avez l’intention de partir en week-end avec vos amis le mois prochain. Vous leur envoyez un message pour leur présenter votre projet, en détaillant le lieu, les moyens de transport et les activités prévues . (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez l’intention de partir en week-end avec vos amis le mois prochain. vous leur envoyez un message pour leur présenter votre projet, en détaillant le lieu, les moyens de transport et les activités prévues"}, {"task_ids": ["novembre-2024_c18_t1", "juillet-2024_c33_t1"], "representative_id": "novembre-2024_c18_t1", "task_content": "Vous avez passé un week-end à la campagne. Écrivez un message à votre ami(e) pour lui décrire ce qui s’est passé. (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024"], "combination_numbers": ["18", "33"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez passé un week-end à la campagne. écrivez un message à votre ami(e) pour lui décrire ce qui s’est passé"}, {"task_ids": ["avril-2025_c8_t1"], "representative_id": "avril-2025_c8_t1", "task_content": "Vous avez décidé d’offrir un voyage à votre ami pour son anniversaire. Écrivez un message pour lui expliquer ce que vous avez préparé : la destination, les dates et les autres détails du voyage . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez décidé d’offrir un voyage à votre ami pour son anniversaire. écrivez un message pour lui expliquer ce que vous avez préparé : la destination, les dates et les autres détails du voyage"}, {"task_ids": ["novembre-2024_c12_t1"], "representative_id": "novembre-2024_c12_t1", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie. Vous voulez lui offrir un voyage et écrivez à votre amie Flavie. – Expliquez votre projet. – Décrivez le programme du voyage (activités, destination, logement, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "c’est bientôt l’anniversaire de votre amie flavie. vous voulez lui offrir un voyage et écrivez à votre amie flavie. – expliquez votre projet. – décrivez le programme du voyage (activités, destination, logement, etc.)"}, {"task_ids": ["fevrier-2025_c33_t1", "mars-2025_c4_t1"], "representative_id": "fevrier-2025_c33_t1", "task_content": "Écrire un message à un(e) ami(e) pour raconter son week-end à la campagne en détaillant les événements qui se sont déroulés. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["33", "4"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "écrire un message à un(e) ami(e) pour raconter son week-end à la campagne en détaillant les événements qui se sont déroulés"}, {"task_ids": ["juillet-2024_c36_t1"], "representative_id": "juillet-2024_c36_t1", "task_content": "L’anniversaire de votre ami approche à grands pas et vous avez décidé de lui offrir un voyage comme cadeau. Rédigez un message pour l’informer des préparations que vous avez faites (destination, dates, préparatifs, etc).", "month_years": ["juillet-2024"], "combination_numbers": ["36"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "l’anniversaire de votre ami approche à grands pas et vous avez décidé de lui offrir un voyage comme cadeau. rédigez un message pour l’informer des préparations que vous avez faites (destination, dates, préparatifs, etc)"}], "name_en": "Description of the trip", "name_fr": "voyage descriptif", "name_zh": "描述之旅", "original_name": "descripiton_trip"}}, "name_en": "Description of Places", "name_fr": "lieux de description", "name_zh": "描述地点", "original_name": "description_places"}, "looking_for_service": {"topic_id": 2, "keywords": ["aide", "demande", "service", "assistance", "cherche", "besoin", "problème", "j'ai besoin", "pouvez-vous"], "total_tasks": 52, "unique_tasks": 37, "subtopics": {"looking_for_language_help": {"subtopic_id": 0, "task_count": 11, "unique_task_count": 7, "task_ids": ["juillet-2024_c5_t1", "juillet-2024_c25_t1", "decembre-2024_c9_t1", "decembre-2024_c15_t1", "janvier-2025_c7_t1", "janvier-2025_c17_t1", "fevrier-2025_c14_t1", "novembre-2024_c9_t1", "octobre-2024_c14_t1", "septembre-2024_c4_t1"], "keywords": ["français", "langue", "pratiquer", "apprendre", "conversation", "partenaires", "linguistiques"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c5_t1", "decembre-2024_c9_t1", "octobre-2024_c14_t1", "septembre-2024_c4_t1"], "representative_id": "juillet-2024_c5_t1", "task_content": "Vous avez lu une annonce sur un site internet qui propose l’aide aux personnes qui souhaitent apprendre le français et les aider à trouver des personnes avec lesquelles elles peuvent pratiquer le français et qui leur permettront d’améliorer leur niveau. Envoyez un email pour répondre à cette annonce tout en vous présentant et en expliquant pourquoi vouloir pratiquer cette langue.", "month_years": ["juillet-2024", "decembre-2024", "octobre-2024", "septembre-2024"], "combination_numbers": ["5", "9", "14", "4"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous avez lu une annonce sur un site internet qui propose l’aide aux personnes qui souhaitent apprendre le français et les aider à trouver des personnes avec lesquelles elles peuvent pratiquer le français et qui leur permettront d’améliorer leur niveau. envoyez un email pour répondre à cette annonce tout en vous présentant et en expliquant pourquoi vouloir pratiquer cette langue"}, {"task_ids": ["janvier-2025_c17_t1"], "representative_id": "janvier-2025_c17_t1", "task_content": "Vous avez lu une annonce sur un site internet qui propose d’aider ceux qui souhaitent apprendre le français en les mettant en contact avec des partenaires linguistiques. Envoyez un courriel en vous présentant et en expliquant pourquoi vous souhaitez pratiquer le français . (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez lu une annonce sur un site internet qui propose d’aider ceux qui souhaitent apprendre le français en les mettant en contact avec des partenaires linguistiques. envoyez un courriel en vous présentant et en expliquant pourquoi vous souhaitez pratiquer le français"}, {"task_ids": ["avril-2025_c13_t1"], "representative_id": "avril-2025_c13_t1", "task_content": "Vous avez découvert une plateforme en ligne mettant en relation des personnes souhaitant apprendre le français avec des partenaires linguistiques. Rédigez un courriel pour vous présenter et expliquer votre motivation à pratiquer cette langue . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez découvert une plateforme en ligne mettant en relation des personnes souhaitant apprendre le français avec des partenaires linguistiques. rédigez un courriel pour vous présenter et expliquer votre motivation à pratiquer cette langue"}, {"task_ids": ["novembre-2024_c4_t1"], "representative_id": "novembre-2024_c4_t1", "task_content": "Vous voulez communiquer avec quelqu’un en français en toute convivialité. Vous écrivez un courriel à cette adresse(…) pour qu’on vous propose quelqu’un (centre d’in<PERSON><PERSON><PERSON><PERSON>, présentez vous…) (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez communiquer avec quelqu’un en français en toute convivialité. vous écrivez un courriel à cette adresse(…) pour qu’on vous propose quelqu’un (centre d’in<PERSON>r<PERSON><PERSON>, présentez vous…)"}, {"task_ids": ["novembre-2024_c19_t1", "decembre-2024_c13_t1"], "representative_id": "novembre-2024_c19_t1", "task_content": "Ecrivez un message à votre ami(e) qui souhaite suivre des cours de langue dans votre école. Donnez les détails spécifiques pour aider votre ami(e) à faire son choix. (lieu, tarifs, types de cours disponible, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "decembre-2024"], "combination_numbers": ["19", "13"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "ecrivez un message à votre ami(e) qui souhaite suivre des cours de langue dans votre école. donnez les détails spécifiques pour aider votre ami(e) à faire son choix. (lieu, tarifs, types de cours disponible, etc.)"}, {"task_ids": ["avril-2025_c16_t1"], "representative_id": "avril-2025_c16_t1", "task_content": "Votre ami vous demande des informations sur votre école de langue. Vous devez lui répondre, en détaillant votre expérience dans cette école . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami vous demande des informations sur votre école de langue. vous devez lui répondre, en détaillant votre expérience dans cette école"}, {"task_ids": ["mars-2025_c2_t1"], "representative_id": "mars-2025_c2_t1", "task_content": "Rédiger un message à un ami intéressé par des cours de langue dans votre école, en lui fournissant des informations utiles sur les programmes proposés, les tarifs et le lieu . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un message à un ami intéressé par des cours de langue dans votre école, en lui fournissant des informations utiles sur les programmes proposés, les tarifs et le lieu"}], "name_en": "Looking for language help", "name_fr": "à la recherche d'aide en langue", "name_zh": "寻找语言帮助", "original_name": "looking_for_language_help"}, "looking_for_roommate": {"subtopic_id": 2, "task_count": 8, "unique_task_count": 7, "task_ids": ["juillet-2024_c16_t1", "juillet-2024_c28_t1", "janvier-2025_c14_t1", "avril-2025_c2_t1"], "keywords": ["appartement", "logement", "chambre", "colocataire", "louer", "centre-ville", "immobilières"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c16_t1"], "representative_id": "juillet-2024_c16_t1", "task_content": "Vous êtes locataire d’un appartement trop grand pour vous. Écrivez une annonce dans un journal pour chercher un colocataire (superficie de l’appartement, caractère du colocataire, prix, etc…).", "month_years": ["juillet-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement trop grand pour vous. écrivez une annonce dans un journal pour chercher un colocataire (superficie de l’appartement, caractère du colocataire, prix, etc…)"}, {"task_ids": ["juillet-2024_c28_t1"], "representative_id": "juillet-2024_c28_t1", "task_content": "Vous êtes locataire d’un appartement trop grand pour vous. Écrivez une annonce dans un journal pour chercher un colocataire. Il faut mentionner : la superficie, le caractère du colocataire, le prix, etc.", "month_years": ["juillet-2024"], "combination_numbers": ["28"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement trop grand pour vous. écrivez une annonce dans un journal pour chercher un colocataire. il faut mentionner : la superficie, le caractère du colocataire, le prix, etc"}, {"task_ids": ["fevrier-2025_c2_t1"], "representative_id": "fevrier-2025_c2_t1", "task_content": "Tu loues un appartement qui est trop grand pour toi. Tu rédiges une annonce dans un journal pour trouver un colocataire en mentionnant la superficie du logement, le type de colocataire recherché et le prix du loyer. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu loues un appartement qui est trop grand pour toi. tu rédiges une annonce dans un journal pour trouver un colocataire en mentionnant la superficie du logement, le type de colocataire recherché et le prix du loyer"}, {"task_ids": ["fevrier-2025_c23_t1"], "representative_id": "fevrier-2025_c23_t1", "task_content": "Vous êtes locataire d’un appartement qui vous semble trop grand. Rédigez une annonce dans un journal pour trouver un colocataire, en indiquant la superficie, le profil recherché et le montant du loyer, entre autres détails. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["23"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement qui vous semble trop grand. rédigez une annonce dans un journal pour trouver un colocataire, en indiquant la superficie, le profil recherché et le montant du loyer, entre autres détails"}, {"task_ids": ["fevrier-2025_c20_t1"], "representative_id": "fevrier-2025_c20_t1", "task_content": "Vous avez mis en ligne une offre de location pour votre appartement montréalais. Rédigez un email à l’attention d’un candidat locataire potentiel en y intégrant les détails essentiels : spécificités du logement, prix de la location, avantages du secteur géographique, etc . (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez mis en ligne une offre de location pour votre appartement montréalais. rédigez un email à l’attention d’un candidat locataire potentiel en y intégrant les détails essentiels : spécificités du logement, prix de la location, avantages du secteur géographique, etc"}, {"task_ids": ["aout-2024_c2_t1", "septembre-2024_c1_t1"], "representative_id": "aout-2024_c2_t1", "task_content": "Vous avez publié une annonce pour la location de votre appartement. Rédigez un courriel à une personne intéressée en lui fournissant des informations sur l’appartement ainsi que sur le quartier.", "month_years": ["aout-2024", "septembre-2024"], "combination_numbers": ["2", "1"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez publié une annonce pour la location de votre appartement. rédigez un courriel à une personne intéressée en lui fournissant des informations sur l’appartement ainsi que sur le quartier"}, {"task_ids": ["octobre-2024_c4_t1"], "representative_id": "octobre-2024_c4_t1", "task_content": "vous avez mis en ligne une annonce pour la location de votre appartement. Écrivez un courriel à une personne intéressée pour lui donner les informations sur l’appartement et le quartier", "month_years": ["octobre-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez mis en ligne une annonce pour la location de votre appartement. écrivez un courriel à une personne intéressée pour lui donner les informations sur l’appartement et le quartier"}], "name_en": "Looking for Roommate", "name_fr": "cherche colocataire", "name_zh": "找室友", "original_name": "looking_for_roommate"}, "looking_for_sport_help": {"subtopic_id": 4, "task_count": 5, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["janvier-2025_c7_t1", "novembre-2024_c9_t1", "decembre-2024_c15_t1"], "representative_id": "janvier-2025_c7_t1", "task_content": "Écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport. (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025", "novembre-2024", "decembre-2024"], "combination_numbers": ["7", "9", "15"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport"}, {"task_ids": ["fevrier-2025_c14_t1"], "representative_id": "fevrier-2025_c14_t1", "task_content": "Rédigez un message à publier dans le journal de votre université afin de rechercher un partenaire avec qui pratiquer une activité sportive. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à publier dans le journal de votre université afin de rechercher un partenaire avec qui pratiquer une activité sportive"}, {"task_ids": ["juillet-2024_c25_t1"], "representative_id": "juillet-2024_c25_t1", "task_content": "Écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport.", "month_years": ["juillet-2024"], "combination_numbers": ["25"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport"}], "name_en": "Looking for sport help", "name_fr": "à la recherche d'aide en sport", "name_zh": "寻找体育帮助", "original_name": "looking_for_sport_help"}, "looking_for_apartment": {"subtopic_id": 5, "task_count": 2, "unique_task_count": 2, "task_ids": ["juillet-2025_c1_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c2_t1"], "representative_id": "avril-2025_c2_t1", "task_content": "Vous avez trouvé une annonce en ligne pour louer un appartement. Écrivez un courriel pour demander des informations sur l’appartement (logement et quartier) . (60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez trouvé une annonce en ligne pour louer un appartement. écrivez un courriel pour demander des informations sur l’appartement (logement et quartier)"}, {"task_ids": ["janvier-2025_c14_t1"], "representative_id": "janvier-2025_c14_t1", "task_content": "Vous allez déménager à Nice, en France. Vous écrivez un message sur le site d’une agence immobilière. Vous donnez les informations nécessaires pour votre recherche d’appartement (date, personnes, loyer…). (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous allez déménager à nice, en france. vous écrivez un message sur le site d’une agence immobilière. vous donnez les informations nécessaires pour votre recherche d’appartement (date, personnes, loyer…)"}], "tasks": [{"id": "juillet-2025_c1_t1", "task_content": "Vous avez trouvé une annonce en ligne pour louer un appartement.Écrivez un courriel pour demander des informations sur l’appartement (logement et quartier).\n(60 mots minimum/120 mots maximum)", "month_year": "juillet-2025", "combination_number": "1"}], "name_en": "Looking for apartment", "name_fr": "Recherche d'appartement", "name_zh": "寻找公寓", "original_name": "looking_for_apartment"}, "looking_for_gift": {"subtopic_id": 6, "task_count": 6, "unique_task_count": 5, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c6_t1", "octobre-2024_c18_t1"], "representative_id": "juillet-2024_c6_t1", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous demandent ce que vous souhaitez comme cadeaux. Vous voulez des vêtements, vous leur écrivez un message pour leur décrire les vêtements que vous aimeriez recevoir.", "month_years": ["juillet-2024", "octobre-2024"], "combination_numbers": ["6", "18"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous allez fêter votre anniversaire. vos amis vous demandent ce que vous souhaitez comme cadeaux. vous voulez des vêtements, vous leur écrivez un message pour leur décrire les vêtements que vous aimeriez recevoir"}, {"task_ids": ["juillet-2024_c18_t1"], "representative_id": "juillet-2024_c18_t1", "task_content": "Votre ami va fêter son anniversaire. Écrivez un message à vos amis pour lui acheter un cadeau commun.", "month_years": ["juillet-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami va fêter son anniversaire. écrivez un message à vos amis pour lui acheter un cadeau commun"}, {"task_ids": ["Mai-2025_c1_t1"], "representative_id": "Mai-2025_c1_t1", "task_content": "Votre anniversaire approche. Vos amis vous posent des questions sur les cadeaux que vous voulez. Vous répondez par un message en expliquant que vous aimeriez recevoir des vêtements, et vous donnez les détails . (60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre anniversaire approche. vos amis vous posent des questions sur les cadeaux que vous voulez. vous répondez par un message en expliquant que vous aimeriez recevoir des vêtements, et vous donnez les détails"}, {"task_ids": ["decembre-2024_c5_t1"], "representative_id": "decembre-2024_c5_t1", "task_content": "Écrivez un courriel à vos amis pour les inviter à un anniversaire surpris pour votre meilleure ami(e), et proposez de lui offrir un cadeau commun (lieu cadeau, activités, etc.). (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un courriel à vos amis pour les inviter à un anniversaire surpris pour votre meilleure ami(e), et proposez de lui offrir un cadeau commun (lieu cadeau, activités, etc.)"}, {"task_ids": ["fevrier-2025_c3_t1"], "representative_id": "fevrier-2025_c3_t1", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire. Tu écris un message à tes amis pour leur proposer d’acheter un cadeau commun. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ton ami(e) va bientôt célébrer son anniversaire. tu écris un message à tes amis pour leur proposer d’acheter un cadeau commun"}], "name_en": "Looking for gift", "name_fr": "à la recherche de cadeaux", "name_zh": "寻找礼物", "original_name": "looking_for_gift"}, "looking_for_housekeeping": {"subtopic_id": 7, "task_count": 4, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c22_t1", "avril-2025_c18_t1"], "representative_id": "fevrier-2025_c22_t1", "task_content": "Rédigez un message à votre ami Cédric pour lui fournir des instructions concernant l’entretien de votre maison et de votre jardin pendant votre absence. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "avril-2025"], "combination_numbers": ["22", "18"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un message à votre ami cédric pour lui fournir des instructions concernant l’entretien de votre maison et de votre jardin pendant votre absence"}, {"task_ids": ["octobre-2024_c13_t1"], "representative_id": "octobre-2024_c13_t1", "task_content": "écrivez un message à votre ami qui a accepté de s’occuper de votre maison et de votre jardin pendant votre absence pour lui dire ce qu’il doit faire.", "month_years": ["octobre-2024"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami qui a accepté de s’occuper de votre maison et de votre jardin pendant votre absence pour lui dire ce qu’il doit faire"}, {"task_ids": ["juillet-2024_c29_t1"], "representative_id": "juillet-2024_c29_t1", "task_content": "Votre ami Cédric a accepté de garder votre maison et jardin pendant vos vacances. Écrivez un message pour lui dire ce qu’il doit faire.", "month_years": ["juillet-2024"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami cédric a accepté de garder votre maison et jardin pendant vos vacances. écrivez un message pour lui dire ce qu’il doit faire"}], "name_en": "Looking for housekeeping", "name_fr": "Recherche de ménage", "name_zh": "寻找家政", "original_name": "looking_for_housekeeping"}, "looking_for_renovation_help": {"subtopic_id": 1, "task_count": 3, "unique_task_count": 2, "task_ids": ["juillet-2024_c6_t1", "juillet-2024_c11_t1", "juillet-2024_c15_t1", "juillet-2024_c17_t1", "juillet-2024_c23_t1", "juillet-2024_c30_t1", "decembre-2024_c2_t1", "decembre-2024_c13_t1", "decembre-2024_c17_t1", "janvier-2025_c18_t1", "aout-2024_c5_t1", "fevrier-2025_c24_t1", "fevrier-2025_c28_t1", "fevrier-2025_c32_t1", "novembre-2024_c5_t1", "novembre-2024_c13_t1", "novembre-2024_c17_t1", "novembre-2024_c19_t1", "mars-2025_c1_t1", "mars-2025_c5_t1", "mars-2025_c9_t1", "octobre-2024_c15_t1", "octobre-2024_c18_t1"], "keywords": ["aide", "assistance", "problème", "difficile", "besoin", "pouvez-vous"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["decembre-2024_c2_t1", "aout-2024_c5_t1"], "representative_id": "decembre-2024_c2_t1", "task_content": "Vous voulez changer la décoration de votre appartement (meubles, peinture, objets, etc.). Vous écrivez un message à un(e) ami(e). Vous lui décrivez votre projet et vous lui demandez de vous aider. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024", "aout-2024"], "combination_numbers": ["2", "5"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous voulez changer la décoration de votre appartement (meubles, peinture, objets, etc.). vous écrivez un message à un(e) ami(e). vous lui décrivez votre projet et vous lui demandez de vous aider"}, {"task_ids": ["mars-2025_c9_t1"], "representative_id": "mars-2025_c9_t1", "task_content": "Rédigez un message à un(e) ami(e) pour lui parler de votre projet de rénovation de votre appartement. Décrivez les changements que vous souhaitez faire et demandez-lui son aide ou ses conseils . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un(e) ami(e) pour lui parler de votre projet de rénovation de votre appartement. décrivez les changements que vous souhaitez faire et demandez-lui son aide ou ses conseils"}], "name_en": "Looking for renovation help", "name_fr": "À la recherche d'aide pour la rénovation", "name_zh": "寻找装修帮助", "original_name": "looking_for_renovation_help"}, "looking_for_relocation_help": {"subtopic_id": 9, "task_count": 3, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c1_t1"], "representative_id": "octobre-2024_c1_t1", "task_content": "Ecrivez un message à votre ami(e) pour lui faire part de votre programme de déménagement vers votre nouveau logement, en demandant son aide ( date, lieu, programme)", "month_years": ["octobre-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ecrivez un message à votre ami(e) pour lui faire part de votre programme de déménagement vers votre nouveau logement, en demandant son aide ( date, lieu, programme)"}, {"task_ids": ["fevrier-2025_c24_t1"], "representative_id": "fevrier-2025_c24_t1", "task_content": "Rédigez un message à un ami pour l’informer de votre déménagement et lui demander son aide, en précisant la date, le lieu et le déroulement du programme. (60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un ami pour l’informer de votre déménagement et lui demander son aide, en précisant la date, le lieu et le déroulement du programme"}, {"task_ids": ["juillet-2024_c30_t1"], "representative_id": "juillet-2024_c30_t1", "task_content": "Vous allez déménager. Des amis ont accepté de vous aider. Vous leur écrivez un message collectif pour leur expliquer comment le déménagement va se passer (lieux, horaires, durée, trajet, tâches à faire, etc.)", "month_years": ["juillet-2024"], "combination_numbers": ["30"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous allez déménager. des amis ont accepté de vous aider. vous leur écrivez un message collectif pour leur expliquer comment le déménagement va se passer (lieux, horaires, durée, trajet, tâches à faire, etc.)"}], "name_en": "Looking for relocation help", "name_fr": "recherche d'aide à la réinstallation", "name_zh": "寻找搬迁帮助", "original_name": "looking_for_relocation_help"}, "looking_for_organizing_party": {"subtopic_id": 8, "task_count": 5, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c8_t1", "juillet-2024_c12_t1"], "representative_id": "octobre-2024_c8_t1", "task_content": "Invitez vos amis à célébrer votre anniversaire tout en sollicitant leur soutien pour organiser la fête.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["8", "12"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "invitez vos amis à célébrer votre anniversaire tout en sollicitant leur soutien pour organiser la fête"}, {"task_ids": ["juillet-2024_c17_t1", "octobre-2024_c15_t1"], "representative_id": "juillet-2024_c17_t1", "task_content": "Vous organisez un événement. Écrivez une lettre à vos amis pour les inviter à cet événement, et pour leur demander de vous aider dans l’organisation (en précisant la date, le lieu, l’horaire…).", "month_years": ["juillet-2024", "octobre-2024"], "combination_numbers": ["17", "15"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous organisez un événement. écrivez une lettre à vos amis pour les inviter à cet événement, et pour leur demander de vous aider dans l’organisation (en précisant la date, le lieu, l’horaire…)"}, {"task_ids": ["janvier-2025_c18_t1"], "representative_id": "janvier-2025_c18_t1", "task_content": "Vous voulez organiser une fête. Écrivez un message à vos amis pour les inviter et de vous aider à l’organiser (lieu, date, thème, etc.) . (60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez organiser une fête. écrivez un message à vos amis pour les inviter et de vous aider à l’organiser (lieu, date, thème, etc.)"}], "name_en": "Label: Searching for a Party Organizer", "name_fr": "Recherche d'organisation de fête", "name_zh": "寻找组织派对", "original_name": "looking_for_organizing_party"}, "looking_for_sale_buy": {"subtopic_id": 3, "task_count": 5, "unique_task_count": 2, "task_ids": ["mars-2025_c8_t1"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["mars-2025_c8_t1"], "representative_id": "mars-2025_c8_t1", "task_content": "Rédigez un courriel en réponse à une annonce de recherche de vélo. Présentez votre vélo, décrivez son état, proposez un prix et fixez un rendez-vous pour un essai . (60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel en réponse à une annonce de recherche de vélo. présentez votre vélo, décrivez son état, proposez un prix et fixez un rendez-vous pour un essai"}, {"task_ids": ["decembre-2024_c3_t1", "septembre-2024_c5_t1", "decembre-2024_c10_t1", "aout-2024_c4_t1"], "representative_id": "decembre-2024_c3_t1", "task_content": "« Je cherche un vélo en bon état et bon marché. Contactez-moi par courriel : <EMAIL> » Vous avez un vélo à vendre. Vous écrivez un courriel pour décrire votre vélo et proposer un prix. Vous lui Donnez un RDV pour essayer le vélo. (60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024", "septembre-2024", "decembre-2024", "aout-2024"], "combination_numbers": ["3", "5", "10", "4"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "« je cherche un vélo en bon état et bon marché. contactez-moi par courriel : <EMAIL> » vous avez un vélo à vendre. vous écrivez un courriel pour décrire votre vélo et proposer un prix. vous lui donnez un rdv pour essayer le vélo"}], "name_en": "Looking for sale or buy", "name_fr": "recherche de vente achat", "name_zh": "寻找销售购买", "original_name": "looking_for_sale_buy"}}, "name_en": "Looking for service", "name_fr": "À la recherche de service", "name_zh": "寻找服务", "original_name": "looking_for_service"}}}}