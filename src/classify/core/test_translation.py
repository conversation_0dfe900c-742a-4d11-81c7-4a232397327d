#!/usr/bin/env python3
"""
Test script for the translation functionality in merge_new_tasks.py
"""

import sys
from pathlib import Path

# Add the current directory to path for imports
sys.path.append(str(Path(__file__).parent))

# Import the translation class from merge_new_tasks
try:
    from merge_new_tasks import TopicTranslator
    print("✅ Successfully imported TopicTranslator from merge_new_tasks")
except ImportError as e:
    print(f"❌ Failed to import TopicTranslator: {e}")
    # Fallback: define a simple version for testing
    class TopicTranslator:
        def __init__(self):
            print("⚠️  Using fallback TopicTranslator")

        def translate_text(self, text, target_lang):
            return text

        def add_translations_to_topic(self, topic_data, topic_name):
            return topic_data

    print("⚠️  Using fallback TopicTranslator")

def test_translation():
    """Test the translation functionality."""
    print("\n🧪 TESTING TOPIC TRANSLATION")
    print("=" * 50)
    
    # Initialize translator
    translator = TopicTranslator()
    
    # Test predefined translations
    test_topics = [
        'recommendation',
        'description_places', 
        'description_person',
        'narration',
        'opinion',
        'explanation',
        'argumentation'
    ]
    
    print("\n📝 Testing predefined translations:")
    for topic in test_topics:
        fr_translation = translator.translate_text(topic, 'fr')
        zh_translation = translator.translate_text(topic, 'zh')
        print(f"  {topic:20} → FR: {fr_translation:20} | ZH: {zh_translation}")
    
    # Test topic structure translation
    print("\n🏗️  Testing topic structure translation:")
    sample_topic_data = {
        'topic_id': 1,
        'keywords': ['test', 'sample'],
        'total_tasks': 10,
        'subtopics': {
            'recommendation_general': {
                'subtopic_id': 1,
                'task_count': 5,
                'keywords': ['recommend', 'suggest']
            },
            'recommendation_specific': {
                'subtopic_id': 2,
                'task_count': 5,
                'keywords': ['specific', 'detailed']
            }
        }
    }
    
    # Apply translations
    translated_data = translator.add_translations_to_topic(sample_topic_data, 'recommendation')
    
    print(f"Topic translations:")
    print(f"  EN: {translated_data.get('name_en', 'N/A')}")
    print(f"  FR: {translated_data.get('name_fr', 'N/A')}")
    print(f"  ZH: {translated_data.get('name_zh', 'N/A')}")
    
    print(f"\nSubtopic translations:")
    for subtopic_name, subtopic_data in translated_data['subtopics'].items():
        print(f"  {subtopic_name}:")
        print(f"    EN: {subtopic_data.get('name_en', 'N/A')}")
        print(f"    FR: {subtopic_data.get('name_fr', 'N/A')}")
        print(f"    ZH: {subtopic_data.get('name_zh', 'N/A')}")
    
    print("\n✅ Translation test completed!")

if __name__ == '__main__':
    test_translation()
