#!/usr/bin/env python3
"""
Run All Classifications

This script runs all TCF writing task classifications with deduplication.
It provides a clean, consistent approach to generate all classification files.
"""

import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

from tache_1_classification import run_tache_1_classification
from tache_2_classification import run_tache_2_classification  
from tache_3_classification import run_tache_3_classification
from data_loader import print_data_summary

def run_all_classifications():
    """Run all classification systems with deduplication."""
    
    print("🚀 TCF WRITING TASK CLASSIFICATION SUITE")
    print("="*80)
    print("Running all classification systems with deduplication...")
    print("="*80)
    
    # Print data summary first
    print("\n📊 DATA OVERVIEW:")
    print_data_summary()
    
    print(f"\n{'='*80}")
    print("RUNNING CLASSIFICATIONS...")
    print(f"{'='*80}")
    
    results = {}
    
    try:
        # Run Tâche 1 classification
        print(f"\n🎯 Starting Tâche 1...")
        results[1] = run_tache_1_classification()
        print(f"✅ Tâche 1 completed: {results[1]['unique_tasks']} unique tasks")
    except Exception as e:
        print(f"❌ Tâche 1 failed: {e}")
        results[1] = None
    
    try:
        # Run Tâche 2 classification
        print(f"\n🎯 Starting Tâche 2...")
        results[2] = run_tache_2_classification()
        print(f"✅ Tâche 2 completed: {results[2]['unique_tasks']} unique tasks")
    except Exception as e:
        print(f"❌ Tâche 2 failed: {e}")
        results[2] = None
    
    try:
        # Run Tâche 3 classification
        print(f"\n🎯 Starting Tâche 3...")
        results[3] = run_tache_3_classification()
        print(f"✅ Tâche 3 completed: {results[3]['unique_tasks']} unique tasks")
    except Exception as e:
        print(f"❌ Tâche 3 failed: {e}")
        results[3] = None
    
    # Print summary
    print(f"\n{'='*80}")
    print("CLASSIFICATION SUITE COMPLETED")
    print(f"{'='*80}")
    
    for tache_num, result in results.items():
        if result:
            total = result['total_tasks']
            unique = result['unique_tasks']
            duplicates = result.get('duplicate_count', total - unique)
            method = result.get('method', 'unknown')
            
            print(f"📋 Tâche {tache_num}:")
            print(f"   Total tasks: {total}")
            print(f"   Unique tasks: {unique}")
            print(f"   Duplicates removed: {duplicates}")
            print(f"   Deduplication rate: {duplicates/total*100:.1f}%")
            print(f"   Method: {method}")
        else:
            print(f"❌ Tâche {tache_num}: Failed to complete")
    
    # Calculate overall statistics
    successful_results = [r for r in results.values() if r is not None]
    if successful_results:
        total_tasks = sum(r['total_tasks'] for r in successful_results)
        total_unique = sum(r['unique_tasks'] for r in successful_results)
        total_duplicates = total_tasks - total_unique
        
        print(f"\n🎯 OVERALL STATISTICS:")
        print(f"   Total tasks processed: {total_tasks}")
        print(f"   Unique tasks identified: {total_unique}")
        print(f"   Total duplicates removed: {total_duplicates}")
        print(f"   Overall deduplication rate: {total_duplicates/total_tasks*100:.1f}%")
        print(f"   Successful classifications: {len(successful_results)}/3")
    
    return results

if __name__ == '__main__':
    results = run_all_classifications() 