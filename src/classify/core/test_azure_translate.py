#!/usr/bin/env python3
"""
Test Azure Translator using backend .env credentials
"""

import requests
from pathlib import Path

def load_azure_credentials():
    """Load Azure credentials from backend .env file."""
    backend_env_path = Path(__file__).parent.parent.parent.parent / 'web_react' / 'backend' / '.env'
    
    azure_key = None
    azure_region = None
    azure_endpoint = None
    
    if backend_env_path.exists():
        print(f"📁 Loading credentials from: {backend_env_path}")
        with open(backend_env_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line.startswith('AZURE_TRANSLATOR_KEY='):
                    azure_key = line.split('=', 1)[1]
                elif line.startswith('AZURE_TRANSLATOR_REGION='):
                    azure_region = line.split('=', 1)[1]
                elif line.startswith('AZURE_TRANSLATOR_ENDPOINT='):
                    azure_endpoint = line.split('=', 1)[1]
    else:
        print(f"❌ Backend .env file not found at: {backend_env_path}")
        return None, None, None
    
    azure_endpoint = azure_endpoint or 'https://api.cognitive.microsofttranslator.com'
    
    return azure_key, azure_region, azure_endpoint

def translate_with_azure(text, target_lang, azure_key, azure_region, azure_endpoint):
    """Translate text using Azure Translator."""
    # Map language codes
    lang_map = {'zh': 'zh-Hans'}  # Chinese simplified
    target_lang = lang_map.get(target_lang, target_lang)
    
    url = f"{azure_endpoint}/translate"
    params = {
        'api-version': '3.0',
        'from': 'en',
        'to': target_lang
    }
    headers = {
        'Ocp-Apim-Subscription-Key': azure_key,
        'Ocp-Apim-Subscription-Region': azure_region,
        'Content-type': 'application/json'
    }
    body = [{'text': text}]
    
    response = requests.post(url, params=params, headers=headers, json=body)
    response.raise_for_status()
    
    result = response.json()
    return result[0]['translations'][0]['text']

def test_azure_translation():
    """Test Azure Translator functionality."""
    print("🧪 TESTING AZURE TRANSLATOR")
    print("=" * 50)
    
    # Load credentials
    azure_key, azure_region, azure_endpoint = load_azure_credentials()
    
    if not azure_key or not azure_region:
        print("❌ Azure credentials not found in backend .env file")
        return False
    
    print(f"✅ Azure credentials loaded:")
    print(f"   Key: {azure_key[:10]}...")
    print(f"   Region: {azure_region}")
    print(f"   Endpoint: {azure_endpoint}")
    
    # Test translations
    test_topics = [
        'recommendation',
        'description places', 
        'description person',
        'narration',
        'opinion',
        'explanation',
        'argumentation',
        'travel blog',
        'food review'
    ]
    
    print("\n📝 Testing translations:")
    success_count = 0
    
    for topic in test_topics:
        try:
            # Clean up text
            clean_text = topic.replace('_', ' ').replace('-', ' ').strip()
            
            # Translate to French
            fr_translation = translate_with_azure(clean_text, 'fr', azure_key, azure_region, azure_endpoint)
            
            # Translate to Chinese
            zh_translation = translate_with_azure(clean_text, 'zh', azure_key, azure_region, azure_endpoint)
            
            print(f"  {topic:20} → FR: {fr_translation:25} | ZH: {zh_translation}")
            success_count += 1
            
        except Exception as e:
            print(f"  {topic:20} → ERROR: {e}")
    
    print(f"\n✅ Azure Translator test completed! ({success_count}/{len(test_topics)} successful)")
    return success_count == len(test_topics)

if __name__ == '__main__':
    test_azure_translation()
