#!/usr/bin/env python3
"""
Test Web Interface Visibility

This script tests if the new merged tasks are properly visible in the web interface
by simulating the same data processing that the web interface does.

Usage:
    python3 test_web_visibility.py --tache 1
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any

def load_merged_checkpoint(tache_number: int) -> Dict[str, Any]:
    """Load the latest merged checkpoint."""
    checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
    
    # Find merged checkpoint
    merged_link = checkpoints_dir / f"tache_{tache_number}_merged_latest.json"
    if merged_link.exists() and merged_link.is_symlink():
        merged_checkpoint_path = merged_link.resolve()
    else:
        # Try to find any merged checkpoint
        pattern = f"tache_{tache_number}_merged_*.json"
        merged_checkpoints = [p for p in checkpoints_dir.glob(pattern) if not p.is_symlink()]
        
        if not merged_checkpoints:
            raise FileNotFoundError(f"No merged checkpoint found for Tâche {tache_number}")
        
        merged_checkpoint_path = max(merged_checkpoints, key=lambda p: p.stat().st_mtime)
    
    with open(merged_checkpoint_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def simulate_web_interface_processing(checkpoint: Dict[str, Any]) -> Dict[str, Any]:
    """Simulate how the web interface processes the checkpoint data."""
    classification = checkpoint.get('classification', {})
    
    # Simulate the Subtopic.js processing
    results = {
        'total_subtopics': 0,
        'subtopics_with_both_formats': 0,
        'total_task_entries': 0,
        'total_tasks': 0,
        'combined_tasks_shown': 0,
        'manual_review_tasks': [],
        'sample_subtopics': []
    }
    
    for main_topic_name, main_topic_data in classification.get('main_topics', {}).items():
        for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).items():
            results['total_subtopics'] += 1
            
            # Simulate the React component logic
            has_task_entries = 'task_entries' in subtopic_data and isinstance(subtopic_data['task_entries'], list)
            has_tasks = 'tasks' in subtopic_data and isinstance(subtopic_data['tasks'], list)
            
            if has_task_entries and has_tasks:
                results['subtopics_with_both_formats'] += 1
            
            # Count tasks in each format
            task_entries_count = len(subtopic_data.get('task_entries', []))
            tasks_count = len(subtopic_data.get('tasks', []))
            
            results['total_task_entries'] += task_entries_count
            results['total_tasks'] += tasks_count
            
            # Simulate the combined tasks rendering
            tasks_to_render = []
            if has_task_entries:
                tasks_to_render.extend([{**task, 'format': 'task_entries'} for task in subtopic_data['task_entries']])
            if has_tasks:
                tasks_to_render.extend([{**task, 'format': 'tasks'} for task in subtopic_data['tasks']])
            
            results['combined_tasks_shown'] += len(tasks_to_render)
            
            # Check for manual review tasks
            if 'manual_review' in main_topic_name.lower():
                for task in tasks_to_render:
                    task_content = task.get('task_content', '')[:100] + "..." if len(task.get('task_content', '')) > 100 else task.get('task_content', '')
                    results['manual_review_tasks'].append({
                        'id': task.get('representative_id') if task['format'] == 'task_entries' else task.get('id'),
                        'content': task_content,
                        'format': task['format'],
                        'subtopic': f"{main_topic_name} > {subtopic_name}"
                    })
            
            # Sample some subtopics for detailed view
            if len(results['sample_subtopics']) < 5 and (has_task_entries or has_tasks):
                results['sample_subtopics'].append({
                    'name': f"{main_topic_name} > {subtopic_name}",
                    'has_task_entries': has_task_entries,
                    'has_tasks': has_tasks,
                    'task_entries_count': task_entries_count,
                    'tasks_count': tasks_count,
                    'combined_count': len(tasks_to_render),
                    'sample_tasks': tasks_to_render[:3]  # First 3 tasks as sample
                })
    
    return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Test web interface visibility of merged tasks')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], required=True,
                       help='Tâche number to test (1, 2, or 3)')
    
    args = parser.parse_args()
    
    print(f"🧪 TESTING WEB INTERFACE VISIBILITY FOR TÂCHE {args.tache}")
    print("=" * 70)
    
    try:
        # Load merged checkpoint
        checkpoint = load_merged_checkpoint(args.tache)
        print(f"✅ Loaded merged checkpoint: {checkpoint['metadata']['checkpoint_name']}")
        print(f"📅 Created: {checkpoint['metadata']['created_timestamp']}")
        
        # Simulate web interface processing
        results = simulate_web_interface_processing(checkpoint)
        
        print(f"\n📊 WEB INTERFACE SIMULATION RESULTS:")
        print(f"  Total subtopics: {results['total_subtopics']}")
        print(f"  Subtopics with both formats: {results['subtopics_with_both_formats']}")
        print(f"  Task entries (original): {results['total_task_entries']}")
        print(f"  Tasks (new merged): {results['total_tasks']}")
        print(f"  Combined tasks shown in UI: {results['combined_tasks_shown']}")
        
        if results['manual_review_tasks']:
            print(f"\n🔍 MANUAL REVIEW TASKS ({len(results['manual_review_tasks'])} found):")
            for task in results['manual_review_tasks']:
                print(f"  • {task['id']} ({task['format']}): {task['content']}")
                print(f"    Location: {task['subtopic']}")
        else:
            print(f"\n✅ No tasks found in manual review sections")
        
        print(f"\n📝 SAMPLE SUBTOPICS:")
        for subtopic in results['sample_subtopics']:
            print(f"  📂 {subtopic['name']}:")
            print(f"    Task entries: {subtopic['task_entries_count']}, Tasks: {subtopic['tasks_count']}")
            print(f"    Combined shown: {subtopic['combined_count']}")
            if subtopic['sample_tasks']:
                print(f"    Sample tasks:")
                for task in subtopic['sample_tasks']:
                    task_id = task.get('representative_id') if task['format'] == 'task_entries' else task.get('id')
                    content = task.get('task_content', '')[:50] + "..." if len(task.get('task_content', '')) > 50 else task.get('task_content', '')
                    print(f"      - {task_id} ({task['format']}): {content}")
        
        print(f"\n🎯 CONCLUSION:")
        if results['subtopics_with_both_formats'] > 0:
            print(f"✅ Found {results['subtopics_with_both_formats']} subtopics with both formats")
            print(f"✅ Web interface should show all {results['combined_tasks_shown']} tasks")
            print(f"✅ New merged tasks are properly integrated and visible")
        else:
            print(f"⚠️  No subtopics found with both formats")
            print(f"ℹ️  This might be normal if no new tasks were merged")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == '__main__':
    main()
