#!/usr/bin/env python3
"""
Update Checkpoint Counts

Simple script to update the total_tasks and unique_tasks counts in checkpoint files
to match the authoritative rule-based classification files.

Usage:
    python3 update_checkpoint_counts.py --tache 1
    python3 update_checkpoint_counts.py --all
"""

import json
import sys
import argparse
from pathlib import Path
from datetime import datetime

class CheckpointUpdater:
    """Updates checkpoint counts to match rule-based classification files."""
    
    def __init__(self):
        self.workspace_root = Path(__file__).parent.parent.parent.parent
        self.rule_based_dir = self.workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
        
        # Authoritative counts from rule-based classification files
        self.authoritative_counts = {
            1: {'total_tasks': 206, 'unique_tasks': 138},
            2: {'total_tasks': 206, 'unique_tasks': 101}, 
            3: {'total_tasks': 206, 'unique_tasks': 132}
        }
    
    def get_checkpoint_path(self, tache_number: int) -> Path:
        """Get the path to the checkpoint file for a tâche."""
        # Look for final_review checkpoint
        pattern = f"tache_{tache_number}_final_review_*.json"
        checkpoints = [p for p in self.checkpoints_dir.glob(pattern)]
        
        if checkpoints:
            return max(checkpoints, key=lambda p: p.stat().st_mtime)
        
        # If no final_review checkpoint found, try any checkpoint
        pattern = f"tache_{tache_number}_*.json"
        checkpoints = [p for p in self.checkpoints_dir.glob(pattern)
                      if not p.is_symlink()]
        
        if checkpoints:
            return max(checkpoints, key=lambda p: p.stat().st_mtime)
        
        raise FileNotFoundError(f"No checkpoints found for Tâche {tache_number}")
    
    def update_tache_counts(self, tache_number: int) -> dict:
        """Update counts for a specific tâche."""
        print(f"\n🔄 UPDATING COUNTS FOR TÂCHE {tache_number}")
        print("=" * 50)
        
        # Get checkpoint file
        checkpoint_path = self.get_checkpoint_path(tache_number)
        print(f"📂 Loading checkpoint: {checkpoint_path.name}")
        
        # Load checkpoint
        with open(checkpoint_path, 'r', encoding='utf-8') as f:
            checkpoint = json.load(f)
        
        # Get current and target counts
        current_total = checkpoint['classification'].get('total_tasks', 0)
        current_unique = checkpoint['classification'].get('unique_tasks', 0)
        
        target_counts = self.authoritative_counts[tache_number]
        target_total = target_counts['total_tasks']
        target_unique = target_counts['unique_tasks']
        
        print(f"📊 Current counts: {current_total} total, {current_unique} unique")
        print(f"📊 Target counts:  {target_total} total, {target_unique} unique")
        
        if current_total == target_total and current_unique == target_unique:
            print("✅ Counts already correct!")
            return {
                'success': True,
                'updated': False,
                'message': 'Counts already correct'
            }
        
        # Update counts
        checkpoint['classification']['total_tasks'] = target_total
        checkpoint['classification']['unique_tasks'] = target_unique
        
        # Update metadata
        if 'modifications_log' not in checkpoint['metadata']:
            checkpoint['metadata']['modifications_log'] = []
        
        checkpoint['metadata']['modifications_log'].append({
            'action': 'update_counts',
            'old_total_tasks': current_total,
            'old_unique_tasks': current_unique,
            'new_total_tasks': target_total,
            'new_unique_tasks': target_unique,
            'timestamp': datetime.now().isoformat()
        })
        
        # Save back to original file
        with open(checkpoint_path, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Updated counts: {current_total} → {target_total} total, {current_unique} → {target_unique} unique")
        print(f"💾 Saved to: {checkpoint_path.name}")
        
        return {
            'success': True,
            'updated': True,
            'old_total': current_total,
            'old_unique': current_unique,
            'new_total': target_total,
            'new_unique': target_unique,
            'file': str(checkpoint_path)
        }

def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Update checkpoint counts to match rule-based classification')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], 
                       help='Tâche number to update (1, 2, or 3)')
    parser.add_argument('--all', action='store_true', 
                       help='Update all tâches (1, 2, and 3)')
    
    args = parser.parse_args()
    
    if not args.tache and not args.all:
        parser.print_help()
        return
    
    updater = CheckpointUpdater()
    
    # Determine which tâches to process
    taches_to_process = []
    if args.all:
        taches_to_process = [1, 2, 3]
    else:
        taches_to_process = [args.tache]
    
    print("🚀 CHECKPOINT COUNT UPDATER")
    print("=" * 60)
    print(f"Processing tâches: {taches_to_process}")
    
    overall_results = {}
    
    for tache_num in taches_to_process:
        try:
            result = updater.update_tache_counts(tache_num)
            overall_results[tache_num] = result
        except Exception as e:
            print(f"❌ Error processing Tâche {tache_num}: {e}")
            overall_results[tache_num] = {'success': False, 'error': str(e)}
    
    # Print overall summary
    print(f"\n{'='*60}")
    print("OVERALL UPDATE SUMMARY")
    print(f"{'='*60}")
    
    for tache_num, result in overall_results.items():
        if result['success']:
            if result.get('updated', False):
                print(f"✅ Tâche {tache_num}: Updated {result['old_total']}→{result['new_total']} total, {result['old_unique']}→{result['new_unique']} unique")
            else:
                print(f"✅ Tâche {tache_num}: Already correct")
        else:
            print(f"❌ Tâche {tache_num}: Failed - {result.get('error', 'Unknown error')}")

if __name__ == '__main__':
    main()
