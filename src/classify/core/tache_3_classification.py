#!/usr/bin/env python3
"""
Smart Tâche 3 Classification System

An intelligent classification system for TCF Tâche 3 tasks that uses semantic similarity
and clustering to automatically discover natural topic groupings from argumentative essays.

Features:
- Semantic embeddings for better topic understanding
- Automatic topic discovery through clustering
- Hierarchical classification (main topics → subtopics)
- Confidence scoring for classification quality
- Deduplication using the existing pipeline

This system automatically discovers themes from argumentative essays without requiring
predefined keyword lists, making it more flexible and accurate.
"""

import json
import numpy as np
from pathlib import Path
import sys
from collections import defaultdict, Counter
import re
from typing import List, Dict, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Machine learning imports
from sentence_transformers import SentenceTransformer
from sklearn.cluster import KMeans, AgglomerativeClustering
from sklearn.metrics import silhouette_score, calinski_harabasz_score
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
import pandas as pd

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_loader import load_tasks_for_tache
from deduplication import TaskDeduplicator, deduplicate_classification_tasks

class SmartTache3Classifier:
    """
    Smart classifier for TCF Tâche 3 using semantic similarity and clustering.
    
    This system automatically discovers natural topic groupings from argumentative essays
    without requiring predefined keyword lists, making it more flexible and accurate.
    """
    
    def __init__(self, similarity_threshold: float = 0.3, n_main_topics: int = 6):
        self.task_number = 3
        self.tasks = []
        self.similarity_threshold = similarity_threshold
        self.n_main_topics = n_main_topics
        
        # Initialize semantic model (multilingual for French text)
        print("🔄 Loading semantic model...")
        self.sentence_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        
        # Will be populated during classification
        self.embeddings = None
        self.discovered_topics = {}
        self.classification_confidence = {}
    
    def load_tasks(self):
        """Load tasks for Tâche 3."""
        self.tasks = load_tasks_for_tache(3)
        print(f"Loaded {len(self.tasks)} tasks for Tâche 3")
    
    def preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for better semantic understanding."""
        # Convert to lowercase
        text = text.lower()
        
        # Remove document structure patterns
        text = re.sub(r'document \d+\s*:', '', text)
        text = re.sub(r'pour ou contre \?', '', text)
        text = re.sub(r'qu\'en pensez-vous \?', '', text)
        text = re.sub(r'êtes-vous d\'accord \?', '', text)
        text = re.sub(r'selon vous.*?\?', '', text)
        text = re.sub(r'que pensez-vous.*?\?', '', text)
        
        # Remove quotes and common discourse markers
        text = re.sub(r'[«»""„"‚'']', '', text)
        text = re.sub(r'\s+', ' ', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text
    
    def extract_key_phrases(self, texts: List[str], max_features: int = 20) -> List[str]:
        """Extract key phrases from a collection of texts using TF-IDF with French stopwords."""
        try:
            # Define comprehensive French stopwords
            french_stopwords = {
                'le', 'de', 'et', 'à', 'un', 'il', 'être', 'et', 'en', 'avoir', 'que', 'pour', 
                'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus', 
                'par', 'grand', 'ou', 'si', 'les', 'du', 'la', 'des', 'au', 'elle', 'aux', 
                'celui', 'celle', 'ceux', 'celles', 'leur', 'leurs', 'nos', 'vos', 'ses', 
                'ces', 'mes', 'tes', 'sa', 'ta', 'ma', 'mon', 'ton', 'son', 'notre', 'votre',
                'aussi', 'comme', 'donc', 'mais', 'très', 'bien', 'où', 'sans', 'entre',
                'pendant', 'avant', 'après', 'sous', 'contre', 'parmi', 'chez', 'selon',
                'malgré', 'durant', 'vers', 'dès', 'depuis', 'jusqu', 'jusque', 'cependant',
                'néanmoins', 'toutefois', 'pourtant', 'ainsi', 'alors', 'car', 'donc',
                'effet', 'fait', 'exemple', 'cas', 'fois', 'temps', 'moment', 'année',
                'jour', 'mois', 'semaine', 'heure', 'minute', 'autre', 'autres', 'même',
                'mêmes', 'tel', 'tels', 'telle', 'telles', 'certain', 'certains', 'certaine',
                'certaines', 'plusieurs', 'quelque', 'quelques', 'chaque', 'tous', 'toutes',
                'aucun', 'aucune', 'nul', 'nulle', 'personne', 'rien', 'jamais', 'toujours',
                'souvent', 'parfois', 'quelquefois', 'encore', 'déjà', 'maintenant', 'puis',
                'ensuite', 'enfin', 'bientôt', 'tard', 'tôt', 'hier', 'aujourd', 'demain',
                'ici', 'là', 'ailleurs', 'partout', 'nulle', 'part', 'dehors', 'dedans',
                'dessus', 'dessous', 'devant', 'derrière', 'près', 'loin', 'haut', 'bas',
                'droite', 'gauche', 'côté', 'milieu', 'centre', 'bout', 'fin', 'début',
                'oui', 'non', 'peut', 'peuvent', 'pourrait', 'pourraient', 'doit', 'doivent',
                'devrait', 'devraient', 'peut-être', 'sûrement', 'certainement', 'probablement'
            }
            
            # Use TF-IDF to extract important terms
            vectorizer = TfidfVectorizer(
                max_features=max_features * 3,  # Get more candidates
                stop_words=list(french_stopwords),
                ngram_range=(1, 3),  # Include 1-3 word phrases
                min_df=2,  # Must appear in at least 2 documents
                max_df=0.7,  # Don't include terms that appear in >70% of documents
                token_pattern=r'\b[a-zA-ZÀ-ÿ]{3,}\b'  # Only meaningful words (3+ chars)
            )
            
            tfidf_matrix = vectorizer.fit_transform(texts)
            feature_names = vectorizer.get_feature_names_out()
            
            # Get average TF-IDF scores
            mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)
            
            # Sort by importance
            feature_scores = list(zip(feature_names, mean_scores))
            feature_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Filter out remaining generic terms and keep meaningful ones
            meaningful_phrases = []
            for phrase, score in feature_scores:
                # Skip if phrase is too generic or too short
                if (len(phrase) >= 3 and 
                    phrase not in french_stopwords and
                    not phrase.isdigit() and
                    score > 0.01):  # Minimum significance threshold
                    meaningful_phrases.append(phrase)
                
                if len(meaningful_phrases) >= max_features:
                    break
            
            return meaningful_phrases[:max_features]
            
        except Exception as e:
            print(f"Warning: Could not extract key phrases: {e}")
            return []
    
    def find_optimal_clusters(self, embeddings: np.ndarray, max_clusters: int = 10) -> int:
        """Find optimal number of clusters using silhouette score with improved validation."""
        if len(embeddings) < 4:
            return 2
        
        max_clusters = min(max_clusters, len(embeddings) - 1)
        best_score = -1
        best_k = 2
        scores = []
        
        for k in range(2, max_clusters + 1):
            try:
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(embeddings)
                
                # Check cluster distribution - avoid very imbalanced clusters
                cluster_counts = Counter(cluster_labels)
                min_cluster_size = min(cluster_counts.values())
                max_cluster_size = max(cluster_counts.values())
                
                # Skip if clusters are too imbalanced or too small
                if min_cluster_size < 3 or max_cluster_size / min_cluster_size > 10:
                    continue
                
                score = silhouette_score(embeddings, cluster_labels)
                scores.append((k, score))
                
                if score > best_score:
                    best_score = score
                    best_k = k
                    
            except Exception as e:
                print(f"Warning: Error in clustering with k={k}: {e}")
                continue
        
        # If no good clustering found, use a moderate number
        if best_score < 0.1:  # Very low silhouette score threshold
            return min(5, max_clusters // 2)
        
        return best_k
    
    def discover_topics(self) -> Dict[str, Any]:
        """Discover natural topic groupings using semantic clustering with validation."""
        print("🔍 Discovering topics using semantic clustering...")
        
        # Preprocess texts
        texts = [self.preprocess_text(task['task_content']) for task in self.tasks]
        
        # Generate embeddings
        print("📊 Generating semantic embeddings...")
        self.embeddings = self.sentence_model.encode(texts, show_progress_bar=True)
        
        # Try different clustering approaches for better results
        discovered_topics = self._try_clustering_approaches(texts)
        
        if not discovered_topics:
            print("⚠️  Falling back to simple clustering...")
            discovered_topics = self._simple_clustering_fallback(texts)
        
        # Add manual review category
        discovered_topics['manual_review'] = {
            'topic_id': len(discovered_topics),
            'keywords': ['unclassified'],
            'total_tasks': 0,
            'unique_tasks': 0,
            'subtopics': {
                'manual_review_general': {
                    'subtopic_id': 0,
                    'keywords': [],
                    'tasks': []
                }
            }
        }
        
        self.discovered_topics = discovered_topics
        return discovered_topics
    
    def _try_clustering_approaches(self, texts: List[str]) -> Dict[str, Any]:
        """Try different clustering approaches to find the most coherent topics."""
        best_topics = None
        best_coherence_score = 0
        
        # Try different numbers of clusters
        for n_clusters in [4, 5, 6, 7]:
            if n_clusters >= len(self.tasks) - 1:
                continue
                
            try:
                # Use KMeans clustering
                clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                cluster_labels = clusterer.fit_predict(self.embeddings)
                
                # Check cluster quality
                if len(set(cluster_labels)) < n_clusters:
                    continue
                
                # Generate topics for this clustering
                topics = self._generate_topics_from_clusters(cluster_labels, texts)
                
                # Calculate coherence score
                coherence_score = self._calculate_topic_coherence(topics, texts)
                
                if coherence_score > best_coherence_score:
                    best_coherence_score = coherence_score
                    best_topics = topics
                    print(f"🎯 Found {n_clusters} coherent topics (score: {coherence_score:.3f})")
                    
            except Exception as e:
                print(f"Warning: Clustering with {n_clusters} clusters failed: {e}")
                continue
        
        return best_topics if best_coherence_score > 0.3 else None
    
    def _generate_topics_from_clusters(self, cluster_labels: np.ndarray, texts: List[str]) -> Dict[str, Any]:
        """Generate topic structure from cluster labels."""
        discovered_topics = {}
        
        for topic_id in range(max(cluster_labels) + 1):
            # Get tasks in this cluster
            cluster_tasks = [self.tasks[i] for i, label in enumerate(cluster_labels) if label == topic_id]
            cluster_texts = [texts[i] for i, label in enumerate(cluster_labels) if label == topic_id]
            
            if len(cluster_tasks) < 2:  # Skip tiny clusters
                continue
            
            # Extract key phrases for this topic
            key_phrases = self.extract_key_phrases(cluster_texts)
            
            # Generate topic name based on key phrases
            topic_name = self.generate_topic_name(key_phrases, cluster_texts)
            
            # Ensure unique topic names
            original_name = topic_name
            counter = 1
            while topic_name in discovered_topics:
                topic_name = f"{original_name}_{counter}"
                counter += 1
            
            # Discover subtopics within this main topic
            subtopics = self.discover_subtopics(cluster_tasks, cluster_texts, topic_name)
            
            discovered_topics[topic_name] = {
                'topic_id': topic_id,
                'keywords': key_phrases,
                'total_tasks': len(cluster_tasks),
                'unique_tasks': len(cluster_tasks),  # Will be updated after deduplication
                'subtopics': subtopics,
                'sample_tasks': cluster_tasks[:3]  # Keep some samples for inspection
            }
        
        return discovered_topics
    
    def _calculate_topic_coherence(self, topics: Dict[str, Any], texts: List[str]) -> float:
        """Calculate a coherence score for the discovered topics."""
        if not topics:
            return 0.0
        
        total_coherence = 0
        topic_count = 0
        
        for topic_name, topic_data in topics.items():
            if not topic_data['keywords']:
                continue
                
            # Calculate internal coherence (how well keywords represent the topic)
            topic_texts = [task['task_content'].lower() for task in topic_data['sample_tasks']]
            topic_keywords = topic_data['keywords']
            
            # Count keyword coverage in topic texts
            keyword_matches = 0
            total_keywords = len(topic_keywords)
            
            for keyword in topic_keywords:
                matches_in_topic = sum(1 for text in topic_texts if keyword in text)
                keyword_matches += matches_in_topic / len(topic_texts) if topic_texts else 0
            
            # Normalize by number of keywords
            if total_keywords > 0:
                topic_coherence = keyword_matches / total_keywords
                total_coherence += topic_coherence
                topic_count += 1
        
        return total_coherence / topic_count if topic_count > 0 else 0.0
    
    def _simple_clustering_fallback(self, texts: List[str]) -> Dict[str, Any]:
        """Simple fallback clustering when advanced methods fail."""
        n_clusters = min(5, len(self.tasks) // 8)  # Conservative cluster count
        if n_clusters < 2:
            n_clusters = 2
            
        clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        cluster_labels = clusterer.fit_predict(self.embeddings)
        
        return self._generate_topics_from_clusters(cluster_labels, texts)
    
    def generate_topic_name(self, key_phrases: List[str], texts: List[str]) -> str:
        """Generate a meaningful topic name from key phrases and texts with improved logic."""
        if not key_phrases:
            return "topic_general"
        
        # More comprehensive topic patterns based on argumentative essay themes
        topic_patterns = {
            'travail_emploi': [
                'travail', 'emploi', 'travailleur', 'travailleuses', 'employé', 'employés', 
                'entreprise', 'bureau', 'salaire', 'carrière', 'professionnel', 'professionnelle',
                'collègues', 'patron', 'chef', 'équipe', 'réunion', 'horaires', 'congé',
                'formation', 'compétences', 'cv', 'entretien', 'recrutement', 'chômage'
            ],
            'education_enseignement': [
                'école', 'éducation', 'étudiant', 'étudiants', 'élève', 'élèves', 'enseignement',
                'professeur', 'professeurs', 'cours', 'classe', 'université', 'études',
                'apprentissage', 'formation', 'diplôme', 'diplômes', 'examen', 'examens',
                'devoirs', 'notes', 'scolaire', 'académique', 'lecture', 'livres'
            ],
            'famille_societe': [
                'famille', 'enfant', 'enfants', 'parents', 'père', 'mère', 'fils', 'fille',
                'mariage', 'couple', 'relation', 'relations', 'société', 'social', 'sociale',
                'communauté', 'tradition', 'valeurs', 'culture', 'génération', 'âge',
                'jeunes', 'adultes', 'personnes'
            ],
            'technologie_numerique': [
                'technologie', 'internet', 'ordinateur', 'téléphone', 'portable', 'numérique',
                'réseaux', 'sociaux', 'application', 'applications', 'jeux', 'vidéo',
                'télévision', 'média', 'médias', 'communication', 'information', 'données',
                'innovation', 'moderne'
            ],
            'environnement_transport': [
                'environnement', 'écologie', 'écologique', 'pollution', 'voiture', 'voitures',
                'transport', 'transports', 'vélo', 'vélos', 'bus', 'métro', 'train',
                'ville', 'villes', 'urbain', 'urbaine', 'circulation', 'trafic',
                'carbone', 'émissions', 'vert', 'verte', 'durable'
            ],
            'sante_alimentation': [
                'santé', 'médical', 'médicale', 'médecin', 'hôpital', 'soins', 'maladie',
                'alimentation', 'nourriture', 'restaurant', 'cuisine', 'repas', 'manger',
                'nutrition', 'sport', 'exercice', 'activité', 'physique', 'bien-être',
                'équilibré', 'sain', 'saine'
            ],
            'economie_consommation': [
                'argent', 'prix', 'coût', 'économie', 'économique', 'financier', 'budget',
                'dépenses', 'acheter', 'vendre', 'marché', 'produit', 'produits',
                'consommation', 'consommer', 'commercial', 'entreprise', 'business',
                'investissement', 'profit'
            ]
        }
        
        # Score each potential topic name with weighted scoring
        topic_scores = {}
        combined_text = ' '.join(texts).lower()
        key_phrases_text = ' '.join(key_phrases).lower()
        
        for topic_name, patterns in topic_patterns.items():
            score = 0
            
            # Score based on key phrases (higher weight)
            for pattern in patterns:
                for phrase in key_phrases:
                    if pattern in phrase.lower():
                        score += 3  # Higher weight for key phrases
                        
            # Score based on text content (lower weight)
            for pattern in patterns:
                if pattern in combined_text:
                    score += 1
                    
            # Bonus for multiple pattern matches
            pattern_matches = sum(1 for pattern in patterns if pattern in key_phrases_text)
            if pattern_matches > 1:
                score += pattern_matches * 2
                
            topic_scores[topic_name] = score
        
        # Get best match with minimum threshold
        if topic_scores and max(topic_scores.values()) >= 3:  # Minimum threshold
            best_topic = max(topic_scores, key=topic_scores.get)
            return best_topic
        
        # Fallback: create topic name from most meaningful key phrase
        if key_phrases:
            # Find the most specific phrase (longer is often more specific)
            best_phrase = max(key_phrases, key=len)
            if len(best_phrase) > 5:  # Only use if reasonably specific
                clean_phrase = re.sub(r'[^a-zA-ZÀ-ÿ\s]', '', best_phrase)
                clean_phrase = clean_phrase.replace(' ', '_')
                return f"topic_{clean_phrase}"
        
        return "topic_general"
    
    def discover_subtopics(self, tasks: List[Dict], texts: List[str], main_topic: str) -> Dict[str, Any]:
        """Discover subtopics within a main topic."""
        if len(tasks) < 4:
            # Too few tasks for subtopic clustering
            return {
                f"{main_topic}_general": {
                    'subtopic_id': 0,
                    'keywords': [],
                    'tasks': tasks
                }
            }
        
        # Generate embeddings for subtopic clustering
        subtopic_embeddings = self.sentence_model.encode(texts)
        
        # Find optimal number of subtopics (2-4 typically)
        max_subtopics = min(4, len(tasks) // 2)
        optimal_subtopics = self.find_optimal_clusters(subtopic_embeddings, max_subtopics)
        
        # Perform subtopic clustering
        subtopic_clusterer = KMeans(n_clusters=optimal_subtopics, random_state=42, n_init=10)
        subtopic_labels = subtopic_clusterer.fit_predict(subtopic_embeddings)
        
        subtopics = {}
        for subtopic_id in range(optimal_subtopics):
            # Get tasks in this subtopic
            subtopic_tasks = [tasks[i] for i, label in enumerate(subtopic_labels) if label == subtopic_id]
            subtopic_texts = [texts[i] for i, label in enumerate(subtopic_labels) if label == subtopic_id]
            
            if not subtopic_tasks:
                continue
            
            # Extract key phrases for subtopic
            subtopic_keywords = self.extract_key_phrases(subtopic_texts, max_features=10)
            
            # Generate subtopic name
            subtopic_name = f"{main_topic}_subtype_{subtopic_id + 1}"
            
            subtopics[subtopic_name] = {
                'subtopic_id': subtopic_id,
                'keywords': subtopic_keywords,
                'tasks': subtopic_tasks
            }
        
        return subtopics
    
    def classify_task(self, task: Dict[str, Any]) -> Tuple[str, str, float]:
        """Classify a single task using semantic similarity to discovered topics."""
        text = self.preprocess_text(task['task_content'])
        task_embedding = self.sentence_model.encode([text])
        
        best_topic = None
        best_subtopic = None
        best_confidence = 0.0
        
        # Compare with each discovered topic
        for topic_name, topic_data in self.discovered_topics.items():
            if topic_name == 'manual_review':
                continue
            
            # Calculate similarity to topic samples
            topic_tasks = topic_data.get('sample_tasks', [])
            if not topic_tasks:
                continue
            
            # Get embeddings for topic samples
            topic_texts = [self.preprocess_text(t['task_content']) for t in topic_tasks]
            topic_embeddings = self.sentence_model.encode(topic_texts)
            
            # Calculate average similarity
            similarities = np.dot(task_embedding, topic_embeddings.T).flatten()
            avg_similarity = np.mean(similarities)
            
            if avg_similarity > best_confidence:
                best_confidence = avg_similarity
                best_topic = topic_name
                
                # Find best subtopic
                best_subtopic = self.classify_subtopic(task, topic_name, task_embedding)
        
        # If confidence is too low, assign to manual review
        if best_confidence < 0.5:  # Threshold for semantic similarity
            return 'manual_review', 'manual_review_general', best_confidence
        
        return best_topic, best_subtopic, best_confidence
    
    def classify_subtopic(self, task: Dict[str, Any], main_topic: str, task_embedding: np.ndarray) -> str:
        """Classify subtopic within a main topic."""
        subtopics = self.discovered_topics[main_topic]['subtopics']
        
        if len(subtopics) == 1:
            return list(subtopics.keys())[0]
        
        best_subtopic = None
        best_similarity = 0.0
        
        for subtopic_name, subtopic_data in subtopics.items():
            subtopic_tasks = subtopic_data.get('tasks', [])
            if not subtopic_tasks:
                continue
            
            # Calculate similarity to subtopic samples
            subtopic_texts = [self.preprocess_text(t['task_content']) for t in subtopic_tasks[:3]]
            subtopic_embeddings = self.sentence_model.encode(subtopic_texts)
            
            similarities = np.dot(task_embedding, subtopic_embeddings.T).flatten()
            avg_similarity = np.mean(similarities)
            
            if avg_similarity > best_similarity:
                best_similarity = avg_similarity
                best_subtopic = subtopic_name
        
        return best_subtopic or list(subtopics.keys())[0]
    
    def classify(self) -> Dict[str, Any]:
        """
        Perform smart classification for Tâche 3 with deduplication.
        """
        print(f"\n=== SMART TÂCHE 3 CLASSIFICATION ===")
        print("Using semantic similarity and clustering")
        
        # Discover topics automatically
        self.discover_topics()
        
        # Initialize classification structure
        final_classification = {
            'task_number': 3,
            'method': 'semantic_clustering_with_deduplication',
            'total_tasks': len(self.tasks),
            'unique_tasks': 0,  # Will be set after deduplication
            'duplicate_count': 0,  # Will be set after deduplication
            'n_main_topics': len(self.discovered_topics),
            'main_topics': {}
        }
        
        # Initialize structure from discovered topics
        for topic_name, topic_data in self.discovered_topics.items():
            final_classification['main_topics'][topic_name] = {
                'topic_id': topic_data['topic_id'],
                'keywords': topic_data['keywords'],
                'total_tasks': 0,
                'unique_tasks': 0,
                'subtopics': {}
            }
            
            # Initialize subtopics
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                final_classification['main_topics'][topic_name]['subtopics'][subtopic_name] = {
                    'subtopic_id': subtopic_data['subtopic_id'],
                    'task_count': 0,
                    'unique_task_count': 0,
                    'tasks': [],  # Temporary - will be converted to task_entries
                    'keywords': subtopic_data['keywords'],
                    'template_similarity': 1.0
                }
        
        # Classify each task
        print("🔍 Classifying tasks...")
        confidence_scores = []
        
        for task in self.tasks:
            main_topic, subtopic, confidence = self.classify_task(task)
            confidence_scores.append(confidence)
            
            # Add task to the appropriate category
            task_entry = {
                'id': task['id'],
                'task_content': task['task_content'],
                'month_year': task['month_year'],
                'combination_number': task['combination_number']
            }
            
            final_classification['main_topics'][main_topic]['subtopics'][subtopic]['tasks'].append(task_entry)
        
        # Store classification confidence
        avg_confidence = np.mean(confidence_scores)
        final_classification['classification_confidence'] = {
            'average_confidence': float(avg_confidence),
            'min_confidence': float(np.min(confidence_scores)),
            'max_confidence': float(np.max(confidence_scores))
        }
        
        # Apply deduplication using the centralized module
        print("🔄 Applying deduplication...")
        final_classification = deduplicate_classification_tasks(final_classification, self.similarity_threshold)
        
        # Update counts after deduplication
        self.update_all_task_counts(final_classification)
        
        # Print classification summary
        print("\nClassification Summary:")
        print(f"  Total tasks: {final_classification['total_tasks']}")
        print(f"  Unique tasks: {final_classification['unique_tasks']}")
        print(f"  Duplicates found: {final_classification['duplicate_count']}")
        print(f"  Average confidence: {avg_confidence:.3f}")
        
        print(f"\nTopic Distribution:")
        for topic_name, topic_data in final_classification['main_topics'].items():
            if topic_data['total_tasks'] > 0:
                print(f"  {topic_name}: {topic_data['total_tasks']} tasks ({topic_data['unique_tasks']} unique)")
        
        return final_classification
    
    def update_all_task_counts(self, classification):
        """Update all task counts (both unique and total) in the classification."""
        total_tasks = 0
        total_unique = 0
        
        for topic_name, topic_data in classification['main_topics'].items():
            topic_total = 0
            topic_unique = 0
            
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                unique_count = len(subtopic_data.get('task_entries', []))
                total_count = sum(entry['duplicate_count'] for entry in subtopic_data.get('task_entries', []))
                
                subtopic_data['unique_task_count'] = unique_count
                subtopic_data['task_count'] = total_count
                
                topic_unique += unique_count
                topic_total += total_count
            
            topic_data['unique_tasks'] = topic_unique
            topic_data['total_tasks'] = topic_total
            total_unique += topic_unique
            total_tasks += topic_total
        
        classification['unique_tasks'] = total_unique
        classification['total_tasks'] = total_tasks
    
    def save_classification(self, classification):
        """Save classification results."""
        workspace_root = Path(__file__).parent.parent.parent.parent
        output_dir = workspace_root / 'data' / 'classified' / 'writing' / 'smart_classification'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_file = output_dir / f'tache_3_smart_classification.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(classification, f, ensure_ascii=False, indent=2)
        
        print(f"Smart Tâche 3 classification saved to {output_file}")
    
    def generate_report(self, classification):
        """Generate detailed report for smart classification."""
        total_tasks = classification['total_tasks']
        unique_tasks = classification['unique_tasks']
        duplicate_count = classification['duplicate_count']
        
        report = [
            f"SMART TÂCHE {classification['task_number']} CLASSIFICATION RESULTS",
            f"="*70,
            f"Total Tasks: {total_tasks}",
            f"Unique Tasks: {unique_tasks}",
            f"Duplicates: {duplicate_count}",
            f"Classification Method: {classification['method']}",
            f"Main Topics: {classification['n_main_topics']}",
            ""
        ]
        
        # Add confidence metrics
        if 'classification_confidence' in classification:
            conf = classification['classification_confidence']
            report.extend([
                "CLASSIFICATION CONFIDENCE:",
                "-" * 30,
                f"  Average confidence: {conf['average_confidence']:.3f}",
                f"  Min confidence: {conf['min_confidence']:.3f}",
                f"  Max confidence: {conf['max_confidence']:.3f}",
                ""
            ])
        
        report.extend([
            "DISCOVERED TOPICS:",
            "-" * 30
        ])
        
        # Add topic breakdown
        for topic_name, topic_data in classification['main_topics'].items():
            if topic_data['total_tasks'] > 0:
                percentage = (topic_data['total_tasks'] / total_tasks) * 100
                report.append(f"  {topic_name}: {topic_data['total_tasks']} tasks ({topic_data['unique_tasks']} unique) - {percentage:.1f}%")
                
                # Add keywords
                if topic_data['keywords']:
                    keywords = ', '.join(topic_data['keywords'][:5])
                    report.append(f"    Keywords: {keywords}")
                
                # Add subtopic breakdown
                for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                    if subtopic_data['task_count'] > 0:
                        report.append(f"    └─ {subtopic_name}: {subtopic_data['task_count']} tasks ({subtopic_data['unique_task_count']} unique)")
        
        report.append("")
        
        # Add deduplication statistics if available
        if 'deduplication_stats' in classification:
            stats = classification['deduplication_stats']
            report.extend([
                "DEDUPLICATION STATISTICS:",
                "-" * 30,
                f"  Total duplicate groups: {stats['duplicate_groups']}",
                f"  Largest group size: {stats['largest_group_size']}",
                f"  Average group size: {stats['average_group_size']:.1f}",
                f"  Similarity threshold: {stats['similarity_threshold']:.0%}",
                f"  Reduction rate: {duplicate_count/total_tasks*100:.1f}%"
            ])
        
        return '\n'.join(report)

def run_smart_tache_3_classification():
    """Run Smart Tâche 3 classification."""
    
    print("="*80)
    print("SMART TÂCHE 3 CLASSIFICATION")
    print("="*80)
    
    classifier = SmartTache3Classifier()
    classifier.load_tasks()
    
    # Run classification
    classification = classifier.classify()
    
    # Save results
    classifier.save_classification(classification)
    
    # Generate and print report
    report = classifier.generate_report(classification)
    print("\n" + report)
    
    return classification

# Maintain backward compatibility
class Tache3Classifier(SmartTache3Classifier):
    """Backward compatibility alias for the smart classifier."""
    pass

def run_tache_3_classification():
    """Backward compatibility wrapper."""
    return run_smart_tache_3_classification()

if __name__ == '__main__':
    result = run_smart_tache_3_classification()
    
    print(f"\n🎯 SMART CLASSIFICATION SUMMARY:")
    print(f"   Total Tasks: {result['total_tasks']}")
    print(f"   Unique Tasks: {result['unique_tasks']}")
    print(f"   Discovered Topics: {result['n_main_topics']} (automatically discovered)")
    print(f"   Method: {result['method']}")
    
    if 'classification_confidence' in result:
        conf = result['classification_confidence']
        print(f"   Average Confidence: {conf['average_confidence']:.3f}")
    
    manual_review_count = result['main_topics'].get('manual_review', {}).get('total_tasks', 0)
    auto_classified_percent = ((result['total_tasks'] - manual_review_count) / result['total_tasks'] * 100) if result['total_tasks'] > 0 else 0
    print(f"   Auto-classified: {auto_classified_percent:.1f}%")
    print(f"   Classification: Smart semantic clustering for argumentative essays")
    print("\n✅ Smart Tâche 3 classification completed!") 