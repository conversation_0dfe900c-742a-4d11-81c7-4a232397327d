#!/usr/bin/env python3
"""
Verify Merge Results

This script compares the original checkpoint with the merged checkpoint to clearly show
what new tasks were added and where they were placed.

Usage:
    python3 verify_merge.py --tache 1
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set

class MergeVerifier:
    """Verifies that tasks were properly merged into checkpoints."""
    
    def __init__(self):
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
    
    def load_checkpoint(self, checkpoint_path: Path) -> Dict[str, Any]:
        """Load a checkpoint file."""
        with open(checkpoint_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def extract_all_tasks(self, checkpoint: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Extract all tasks from a checkpoint with their locations."""
        tasks = {}
        
        classification = checkpoint.get('classification', {})
        main_topics = classification.get('main_topics', {})
        
        for main_topic_name, main_topic_data in main_topics.items():
            for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).items():
                location = f"{main_topic_name} > {subtopic_name}"
                
                # Handle both formats
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        if 'task_ids' in task_entry:
                            for task_id in task_entry['task_ids']:
                                tasks[task_id] = {
                                    'location': location,
                                    'content': task_entry.get('task_content', ''),
                                    'type': 'task_entry'
                                }
                        elif 'representative_id' in task_entry:
                            task_id = task_entry['representative_id']
                            tasks[task_id] = {
                                'location': location,
                                'content': task_entry.get('task_content', ''),
                                'type': 'task_entry'
                            }
                
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        task_id = task.get('id', '')
                        if task_id:
                            tasks[task_id] = {
                                'location': location,
                                'content': task.get('task_content', ''),
                                'type': 'task'
                            }
        
        return tasks
    
    def verify_merge(self, tache_number: int):
        """Verify the merge results for a specific tâche."""
        print(f"🔍 VERIFYING MERGE RESULTS FOR TÂCHE {tache_number}")
        print("=" * 70)
        
        # Find original checkpoint
        pattern = f"tache_{tache_number}_final_review_*.json"
        original_checkpoints = [p for p in self.checkpoints_dir.glob(pattern)]
        
        if not original_checkpoints:
            print(f"❌ No original checkpoint found for Tâche {tache_number}")
            return
        
        original_checkpoint_path = max(original_checkpoints, key=lambda p: p.stat().st_mtime)
        
        # Find merged checkpoint
        merged_link = self.checkpoints_dir / f"tache_{tache_number}_merged_latest.json"
        if merged_link.exists() and merged_link.is_symlink():
            merged_checkpoint_path = merged_link.resolve()
        else:
            # Try to find any merged checkpoint
            pattern = f"tache_{tache_number}_merged_*.json"
            merged_checkpoints = [p for p in self.checkpoints_dir.glob(pattern) if not p.is_symlink()]
            
            if not merged_checkpoints:
                print(f"❌ No merged checkpoint found for Tâche {tache_number}")
                return
            
            merged_checkpoint_path = max(merged_checkpoints, key=lambda p: p.stat().st_mtime)
        
        print(f"📂 Original checkpoint: {original_checkpoint_path.name}")
        print(f"📂 Merged checkpoint: {merged_checkpoint_path.name}")
        
        # Load checkpoints
        original_checkpoint = self.load_checkpoint(original_checkpoint_path)
        merged_checkpoint = self.load_checkpoint(merged_checkpoint_path)
        
        # Extract all tasks
        original_tasks = self.extract_all_tasks(original_checkpoint)
        merged_tasks = self.extract_all_tasks(merged_checkpoint)
        
        # Find new tasks
        new_task_ids = set(merged_tasks.keys()) - set(original_tasks.keys())
        
        print(f"\n📊 COMPARISON RESULTS:")
        print(f"  Original checkpoint tasks: {len(original_tasks)}")
        print(f"  Merged checkpoint tasks: {len(merged_tasks)}")
        print(f"  New tasks added: {len(new_task_ids)}")
        
        if not new_task_ids:
            print("\n✅ No new tasks were added in the merge.")
            return
        
        print(f"\n📝 NEW TASKS ADDED ({len(new_task_ids)} tasks):")
        print("-" * 70)
        
        # Group new tasks by location
        tasks_by_location = {}
        for task_id in new_task_ids:
            task_info = merged_tasks[task_id]
            location = task_info['location']
            if location not in tasks_by_location:
                tasks_by_location[location] = []
            tasks_by_location[location].append({
                'id': task_id,
                'content': task_info['content']
            })
        
        # Display new tasks by location
        for location, tasks in sorted(tasks_by_location.items()):
            print(f"\n  📂 {location} ({len(tasks)} new tasks):")
            for task in tasks:
                content_preview = task['content'][:80] + "..." if len(task['content']) > 80 else task['content']
                content_preview = content_preview.replace('\n', ' ')
                print(f"    • {task['id']}: {content_preview}")
        
        # Show merge metadata if available
        merge_info = merged_checkpoint.get('metadata', {}).get('merge_info', {})
        if merge_info:
            print(f"\n🔄 MERGE METADATA:")
            print(f"  Original checkpoint: {merge_info.get('original_checkpoint', 'Unknown')}")
            print(f"  Merge timestamp: {merge_info.get('merge_timestamp', 'Unknown')}")
            print(f"  Similarity threshold: {merge_info.get('similarity_threshold', 'Unknown')}")
            print(f"  Auto-merged count: {merge_info.get('auto_merged_count', 0)}")
            print(f"  Manual review count: {merge_info.get('manual_review_count', 0)}")
        
        print(f"\n✅ VERIFICATION COMPLETE")
        print(f"The merge was successful! {len(new_task_ids)} new tasks were properly added to the checkpoint.")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Verify merge results')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], required=True,
                       help='Tâche number to verify (1, 2, or 3)')
    
    args = parser.parse_args()
    
    verifier = MergeVerifier()
    verifier.verify_merge(args.tache)


if __name__ == '__main__':
    main()
