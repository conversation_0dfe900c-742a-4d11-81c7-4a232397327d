#!/usr/bin/env python3
"""
Show Merge Details

This script shows the details of what tasks were merged in the latest merge operation.
It compares the original checkpoint with the merged checkpoint to show exactly what was added.

Usage:
    python3 show_merge_details.py --tache 1
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List, Any, Set

class MergeDetailsViewer:
    """Shows details of merged tasks."""
    
    def __init__(self):
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
    
    def load_checkpoint(self, checkpoint_path: Path) -> Dict[str, Any]:
        """Load a checkpoint file."""
        with open(checkpoint_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def extract_task_ids(self, checkpoint: Dict[str, Any]) -> Set[str]:
        """Extract all task IDs from a checkpoint."""
        task_ids = set()
        
        classification = checkpoint.get('classification', {})
        main_topics = classification.get('main_topics', {})
        
        for main_topic_data in main_topics.values():
            for subtopic_data in main_topic_data.get('subtopics', {}).values():
                # Handle both formats
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        if 'task_ids' in task_entry:
                            task_ids.update(task_entry['task_ids'])
                        elif 'representative_id' in task_entry:
                            task_ids.add(task_entry['representative_id'])
                
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        task_ids.add(task['id'])
        
        return task_ids
    
    def get_tasks_by_location(self, checkpoint: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """Get all tasks organized by their location (main_topic > subtopic)."""
        tasks_by_location = {}
        
        classification = checkpoint.get('classification', {})
        main_topics = classification.get('main_topics', {})
        
        for main_topic_name, main_topic_data in main_topics.items():
            for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).values():
                location_key = f"{main_topic_name} > {subtopic_name}"
                tasks_by_location[location_key] = []
                
                # Handle both formats
                if 'task_entries' in subtopic_data:
                    for task_entry in subtopic_data['task_entries']:
                        tasks_by_location[location_key].append({
                            'id': task_entry.get('representative_id', ''),
                            'task_content': task_entry.get('task_content', ''),
                            'type': 'task_entry'
                        })
                
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        tasks_by_location[location_key].append({
                            'id': task.get('id', ''),
                            'task_content': task.get('task_content', ''),
                            'type': 'task'
                        })
        
        return tasks_by_location
    
    def show_merge_details(self, tache_number: int):
        """Show details of the latest merge for a tâche."""
        print(f"🔍 MERGE DETAILS FOR TÂCHE {tache_number}")
        print("=" * 60)
        
        # Find original checkpoint
        pattern = f"tache_{tache_number}_final_review_*.json"
        original_checkpoints = [p for p in self.checkpoints_dir.glob(pattern)]
        
        if not original_checkpoints:
            print(f"❌ No original checkpoint found for Tâche {tache_number}")
            return
        
        original_checkpoint_path = max(original_checkpoints, key=lambda p: p.stat().st_mtime)
        
        # Find merged checkpoint
        merged_link = self.checkpoints_dir / f"tache_{tache_number}_merged_latest.json"
        if merged_link.exists() and merged_link.is_symlink():
            merged_checkpoint_path = merged_link.resolve()
        else:
            # Try to find any merged checkpoint
            pattern = f"tache_{tache_number}_merged_*.json"
            merged_checkpoints = [p for p in self.checkpoints_dir.glob(pattern) if not p.is_symlink()]

            if not merged_checkpoints:
                print(f"❌ No merged checkpoint found for Tâche {tache_number}")
                return

            merged_checkpoint_path = max(merged_checkpoints, key=lambda p: p.stat().st_mtime)
        
        print(f"📂 Original checkpoint: {original_checkpoint_path.name}")
        print(f"📂 Merged checkpoint: {merged_checkpoint_path.name}")
        
        # Load checkpoints
        original_checkpoint = self.load_checkpoint(original_checkpoint_path)
        merged_checkpoint = self.load_checkpoint(merged_checkpoint_path)
        
        # Extract task IDs
        original_task_ids = self.extract_task_ids(original_checkpoint)
        merged_task_ids = self.extract_task_ids(merged_checkpoint)
        
        # Find new tasks
        new_task_ids = merged_task_ids - original_task_ids
        
        print(f"\n📊 SUMMARY:")
        print(f"  Original tasks: {len(original_task_ids)}")
        print(f"  Merged tasks: {len(merged_task_ids)}")
        print(f"  New tasks added: {len(new_task_ids)}")
        
        if not new_task_ids:
            print("\n✅ No new tasks were added in the latest merge.")
            return
        
        # Find where new tasks were placed
        print(f"\n📝 NEW TASKS ADDED:")
        merged_classification = merged_checkpoint.get('classification', {})
        
        for main_topic_name, main_topic_data in merged_classification.get('main_topics', {}).items():
            for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).items():
                new_tasks_in_subtopic = []
                
                # Check tasks format
                if 'tasks' in subtopic_data:
                    for task in subtopic_data['tasks']:
                        if task.get('id') in new_task_ids:
                            new_tasks_in_subtopic.append(task)
                
                if new_tasks_in_subtopic:
                    print(f"\n  📂 {main_topic_name} > {subtopic_name}:")
                    for task in new_tasks_in_subtopic:
                        task_content = task.get('task_content', '')[:80] + "..." if len(task.get('task_content', '')) > 80 else task.get('task_content', '')
                        print(f"    • {task.get('id')}: {task_content}")
        
        # Show merge metadata if available
        merge_info = merged_checkpoint.get('metadata', {}).get('merge_info', {})
        if merge_info:
            print(f"\n🔄 MERGE METADATA:")
            print(f"  Merge timestamp: {merge_info.get('merge_timestamp', 'Unknown')}")
            print(f"  Similarity threshold: {merge_info.get('similarity_threshold', 'Unknown')}")
            print(f"  Auto-merged: {merge_info.get('auto_merged_count', 0)}")
            print(f"  Manual review: {merge_info.get('manual_review_count', 0)}")


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description='Show details of merged tasks')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3], required=True,
                       help='Tâche number to show details for (1, 2, or 3)')
    
    args = parser.parse_args()
    
    viewer = MergeDetailsViewer()
    viewer.show_merge_details(args.tache)


if __name__ == '__main__':
    main()
