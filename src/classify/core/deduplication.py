#!/usr/bin/env python3
"""
Deduplication Module for TCF Writing Tasks

This module provides comprehensive deduplication functionality for all TCF taches.
It detects similar tasks, groups them, and creates a standardized format for
the classification system and web interface.

Features:
- Text similarity detection using multiple algorithms
- Configurable similarity thresholds
- Standardized task entry format
- Comprehensive duplicate metadata
"""

import json
from difflib import SequenceMatcher
from pathlib import Path
from typing import List, Dict, Any, Tuple
import re
from collections import defaultdict


class TaskDeduplicator:
    """
    Comprehensive deduplication system for TCF writing tasks.
    """
    
    def __init__(self, similarity_threshold: float = 0.95):
        """
        Initialize the deduplicator.
        
        Args:
            similarity_threshold: Minimum similarity ratio to consider tasks as duplicates (0.0-1.0)
        """
        self.similarity_threshold = similarity_threshold
        self.duplicate_stats = {
            'total_tasks': 0,
            'unique_tasks': 0,
            'duplicate_groups': 0,
            'largest_group_size': 0,
            'total_duplicates': 0
        }
    
    def clean_text_for_comparison(self, text: str) -> str:
        """
        Clean and normalize text for comparison.
        
        Args:
            text: Raw task content
            
        Returns:
            Cleaned text suitable for similarity comparison
        """
        if not text:
            return ""
        
        # Convert to lowercase
        cleaned = text.lower().strip()
        
        # Remove word count requirements like "(120 mots minimum/150 mots maximum)"
        cleaned = re.sub(r'\(\d+\s*mots?\s*minimum[^)]*\)', '', cleaned)
        cleaned = re.sub(r'\(\d+\s*à\s*\d+\s*mots?\)', '', cleaned)
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned)
        
        # Remove leading/trailing punctuation and spaces
        cleaned = cleaned.strip(' .,;:!?')
        
        return cleaned
    
    def calculate_similarity(self, text1: str, text2: str) -> float:
        """
        Calculate similarity between two texts using SequenceMatcher.
        
        Args:
            text1: First text to compare
            text2: Second text to compare
            
        Returns:
            Similarity ratio between 0.0 and 1.0
        """
        clean_text1 = self.clean_text_for_comparison(text1)
        clean_text2 = self.clean_text_for_comparison(text2)
        
        if not clean_text1 or not clean_text2:
            return 0.0
        
        return SequenceMatcher(None, clean_text1, clean_text2).ratio()
    
    def find_duplicate_groups(self, tasks: List[Dict[str, Any]]) -> List[List[int]]:
        """
        Find groups of duplicate tasks based on similarity threshold.
        
        Args:
            tasks: List of task dictionaries with 'task_content' field
            
        Returns:
            List of groups, where each group is a list of task indices
        """
        n_tasks = len(tasks)
        used_indices = set()
        groups = []
        
        for i in range(n_tasks):
            if i in used_indices:
                continue
            
            # Start a new group with current task
            current_group = [i]
            used_indices.add(i)
            
            # Find all similar tasks
            for j in range(i + 1, n_tasks):
                if j in used_indices:
                    continue
                
                similarity = self.calculate_similarity(
                    tasks[i]['task_content'], 
                    tasks[j]['task_content']
                )
                
                if similarity >= self.similarity_threshold:
                    current_group.append(j)
                    used_indices.add(j)
            
            groups.append(current_group)
        
        return groups
    
    def create_task_entry(self, tasks: List[Dict[str, Any]], group_indices: List[int]) -> Dict[str, Any]:
        """
        Create a standardized task entry from a group of duplicate tasks.
        
        Args:
            tasks: Original tasks list
            group_indices: Indices of tasks that form this group
            
        Returns:
            Standardized task entry dictionary
        """
        # Use first task as representative
        representative_task = tasks[group_indices[0]]
        
        # Collect all task IDs and metadata
        task_ids = []
        month_years = []
        combination_numbers = []
        
        for idx in group_indices:
            task = tasks[idx]
            task_ids.append(task['id'])
            month_years.append(task.get('month_year', ''))
            combination_numbers.append(task.get('combination_number', ''))
        
        # Create task entry
        task_entry = {
            'task_ids': task_ids,
            'representative_id': representative_task['id'],
            'task_content': representative_task['task_content'],
            'month_years': month_years,
            'combination_numbers': combination_numbers,
            'is_duplicate_group': len(group_indices) > 1,
            'duplicate_count': len(group_indices),
            'clean_content': self.clean_text_for_comparison(representative_task['task_content'])
        }
        
        return task_entry
    
    def deduplicate_tasks(self, tasks: List[Dict[str, Any]]) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Perform deduplication on a list of tasks.
        
        Args:
            tasks: List of task dictionaries
            
        Returns:
            Tuple of (deduplicated_task_entries, duplicate_statistics)
        """
        if not tasks:
            return [], self._empty_stats()
        
        # Find duplicate groups
        duplicate_groups = self.find_duplicate_groups(tasks)
        
        # Create task entries
        task_entries = []
        for group_indices in duplicate_groups:
            task_entry = self.create_task_entry(tasks, group_indices)
            task_entries.append(task_entry)
        
        # Calculate statistics
        stats = self._calculate_stats(tasks, task_entries, duplicate_groups)
        
        return task_entries, stats
    
    def _calculate_stats(self, original_tasks: List[Dict[str, Any]], 
                        task_entries: List[Dict[str, Any]], 
                        groups: List[List[int]]) -> Dict[str, Any]:
        """Calculate deduplication statistics."""
        duplicate_groups = [group for group in groups if len(group) > 1]
        total_duplicates = sum(len(group) for group in duplicate_groups) - len(duplicate_groups)
        
        return {
            'total_tasks': len(original_tasks),
            'unique_tasks': len(task_entries),
            'duplicate_groups': len(duplicate_groups),
            'largest_group_size': max((len(group) for group in groups), default=0),
            'total_duplicates': total_duplicates,
            'average_group_size': sum(len(group) for group in duplicate_groups) / len(duplicate_groups) if duplicate_groups else 0,
            'similarity_threshold': self.similarity_threshold
        }
    
    def _empty_stats(self) -> Dict[str, Any]:
        """Return empty statistics dictionary."""
        return {
            'total_tasks': 0,
            'unique_tasks': 0,
            'duplicate_groups': 0,
            'largest_group_size': 0,
            'total_duplicates': 0,
            'average_group_size': 0,
            'similarity_threshold': self.similarity_threshold
        }
    
    def print_deduplication_report(self, stats: Dict[str, Any], 
                                 task_entries: List[Dict[str, Any]], 
                                 show_examples: bool = True) -> None:
        """
        Print a comprehensive deduplication report.
        
        Args:
            stats: Deduplication statistics
            task_entries: Deduplicated task entries
            show_examples: Whether to show example duplicate groups
        """
        print(f"\n{'='*60}")
        print("DEDUPLICATION REPORT")
        print(f"{'='*60}")
        print(f"Original tasks: {stats['total_tasks']}")
        print(f"Unique tasks: {stats['unique_tasks']}")
        print(f"Duplicates removed: {stats['total_duplicates']}")
        print(f"Duplicate groups: {stats['duplicate_groups']}")
        print(f"Largest group size: {stats['largest_group_size']}")
        print(f"Average group size: {stats['average_group_size']:.1f}")
        print(f"Similarity threshold: {stats['similarity_threshold']:.0%}")
        print(f"Reduction: {stats['total_duplicates']/stats['total_tasks']*100:.1f}%")
        
        if show_examples and stats['duplicate_groups'] > 0:
            print(f"\n📋 EXAMPLE DUPLICATE GROUPS:")
            duplicate_examples = [entry for entry in task_entries if entry['is_duplicate_group']]
            
            for i, entry in enumerate(duplicate_examples[:3], 1):
                print(f"\nGroup {i} ({entry['duplicate_count']} tasks):")
                print(f"  IDs: {entry['task_ids']}")
                print(f"  Content: {entry['task_content'][:80]}...")
                if len(set(entry['month_years'])) > 1:
                    print(f"  Appears in: {sorted(set(entry['month_years']))}")


def deduplicate_classification_tasks(classification: Dict[str, Any], 
                                   similarity_threshold: float = 0.95) -> Dict[str, Any]:
    """
    Deduplicate tasks within an existing classification structure.
    
    This function processes a classification dictionary and applies deduplication
    to all tasks within each subtopic, maintaining the classification structure.
    
    Args:
        classification: Classification dictionary with main_topics/subtopics structure
        similarity_threshold: Similarity threshold for duplicate detection
        
    Returns:
        Updated classification with deduplicated task_entries
    """
    deduplicator = TaskDeduplicator(similarity_threshold)
    total_stats = deduplicator._empty_stats()
    
    # Process each subtopic
    for main_topic_name, main_topic_data in classification.get('main_topics', {}).items():
        for subtopic_name, subtopic_data in main_topic_data.get('subtopics', {}).items():
            # Get tasks from either 'tasks' or 'task_entries' format
            if 'tasks' in subtopic_data:
                original_tasks = subtopic_data['tasks']
            elif 'task_entries' in subtopic_data:
                # Convert task_entries back to tasks format for processing
                original_tasks = []
                for entry in subtopic_data['task_entries']:
                    for i, task_id in enumerate(entry['task_ids']):
                        task = {
                            'id': task_id,
                            'task_content': entry['task_content'],
                            'month_year': entry['month_years'][i] if i < len(entry['month_years']) else '',
                            'combination_number': entry['combination_numbers'][i] if i < len(entry['combination_numbers']) else ''
                        }
                        original_tasks.append(task)
            else:
                continue  # Skip if no tasks found
            
            if not original_tasks:
                continue
            
            # Deduplicate tasks
            task_entries, subtopic_stats = deduplicator.deduplicate_tasks(original_tasks)
            
            # Update subtopic with deduplicated tasks
            subtopic_data['task_entries'] = task_entries
            subtopic_data['task_count'] = subtopic_stats['total_tasks']
            subtopic_data['unique_task_count'] = subtopic_stats['unique_tasks']
            
            # Remove old format
            if 'tasks' in subtopic_data:
                del subtopic_data['tasks']
            
            # Accumulate stats
            total_stats['total_tasks'] += subtopic_stats['total_tasks']
            total_stats['unique_tasks'] += subtopic_stats['unique_tasks']
            total_stats['duplicate_groups'] += subtopic_stats['duplicate_groups']
            total_stats['total_duplicates'] += subtopic_stats['total_duplicates']
            total_stats['largest_group_size'] = max(total_stats['largest_group_size'], 
                                                  subtopic_stats['largest_group_size'])
    
    # Update classification metadata
    classification['total_tasks'] = total_stats['total_tasks']
    classification['unique_tasks'] = total_stats['unique_tasks'] 
    classification['duplicate_count'] = total_stats['total_duplicates']
    classification['deduplication_stats'] = total_stats
    
    return classification


def create_task_id(month_year: str, combination_number: str, task_number: int) -> str:
    """
    Create a standardized task ID.
    
    Args:
        month_year: Month and year (e.g., "juillet-2024")
        combination_number: Combination number
        task_number: Task number (1, 2, or 3)
        
    Returns:
        Standardized task ID (e.g., "juillet-2024_c1_t2")
    """
    return f"{month_year}_c{combination_number}_t{task_number}"


if __name__ == '__main__':
    # Example usage and testing
    print("🔧 TCF Task Deduplication Module")
    print("This module provides deduplication functionality for TCF writing tasks.")
    print("Import this module in your classification systems to apply deduplication.") 