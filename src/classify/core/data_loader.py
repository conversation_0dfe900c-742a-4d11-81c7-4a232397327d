#!/usr/bin/env python3
"""
Data Loader for TCF Writing Tasks

This module loads raw writing task data from the scraped JSON files and 
provides a standardized interface for all classification systems.

Features:
- Loads from raw scraped data
- Creates standardized task IDs
- Separates tasks by tâche number
- Provides consistent data format
"""

import json
from pathlib import Path
from typing import Dict, List, Any

def create_task_id(month_year: str, combination_number: str, task_number: int) -> str:
    """
    Create a standardized task ID.
    
    Args:
        month_year: Month and year (e.g., "juillet-2024")
        combination_number: Combination number
        task_number: Task number (1, 2, or 3)
        
    Returns:
        Standardized task ID (e.g., "juillet-2024_c1_t2")
    """
    return f"{month_year}_c{combination_number}_t{task_number}"

def load_raw_writing_data() -> List[Dict[str, Any]]:
    """
    Load raw writing data from the scraped JSON file.
    
    Returns:
        List of all tasks with standardized structure
    """
    # Find the raw data file
    workspace_root = Path(__file__).parent.parent.parent.parent
    raw_data_file = workspace_root / 'data' / 'scraped' / 'scraped_writing' / 'all_expression_ecrite.json'
    
    if not raw_data_file.exists():
        raise FileNotFoundError(f"Raw data file not found: {raw_data_file}")
    
    with open(raw_data_file, 'r', encoding='utf-8') as f:
        raw_data = json.load(f)
    
    all_tasks = []
    
    # Process each month's data
    for month_data in raw_data:
        month_year = month_data['month_year']
        
        # Process each combination
        for combination in month_data['combinations']:
            combination_number = combination['combination_number']
            
            # Process each task in the combination
            for task_data in combination['tasks']:
                task_number = task_data['task_number']
                task_id = create_task_id(month_year, combination_number, task_number)
                
                # Create standardized task structure
                task = {
                    'id': task_id,
                    'task_number': task_number,
                    'task_content': task_data['task_content'],
                    'month_year': month_year,
                    'combination_number': combination_number,
                    'correction_content': task_data.get('correction_content', '')
                }
                
                all_tasks.append(task)
    
    print(f"✅ Loaded {len(all_tasks)} tasks from raw data")
    return all_tasks

def load_all_writing_tasks() -> Dict[int, List[Dict[str, Any]]]:
    """
    Load all writing tasks organized by tâche number.
    
    Returns:
        Dictionary mapping tâche numbers (1, 2, 3) to lists of tasks
    """
    all_tasks = load_raw_writing_data()
    
    # Organize tasks by tâche number
    tasks_by_tache = {1: [], 2: [], 3: []}
    
    for task in all_tasks:
        tache_number = task['task_number']
        if tache_number in tasks_by_tache:
            tasks_by_tache[tache_number].append(task)
    
    # Print summary
    print(f"📊 Task distribution:")
    for tache_num, tasks in tasks_by_tache.items():
        print(f"  Tâche {tache_num}: {len(tasks)} tasks")
    
    return tasks_by_tache

def load_tasks_for_tache(tache_number: int) -> List[Dict[str, Any]]:
    """
    Load tasks for a specific tâche number.
    
    Args:
        tache_number: The tâche number (1, 2, or 3)
        
    Returns:
        List of tasks for the specified tâche
    """
    if tache_number not in [1, 2, 3]:
        raise ValueError(f"Invalid tâche number: {tache_number}. Must be 1, 2, or 3.")
    
    all_tasks = load_all_writing_tasks()
    return all_tasks[tache_number]

def get_data_statistics() -> Dict[str, Any]:
    """
    Get comprehensive statistics about the loaded data.
    
    Returns:
        Dictionary with data statistics
    """
    all_tasks = load_raw_writing_data()
    tasks_by_tache = {1: [], 2: [], 3: []}
    
    # Organize and count
    for task in all_tasks:
        tache_number = task['task_number']
        if tache_number in tasks_by_tache:
            tasks_by_tache[tache_number].append(task)
    
    # Count unique months and combinations
    unique_months = set(task['month_year'] for task in all_tasks)
    unique_combinations = set((task['month_year'], task['combination_number']) for task in all_tasks)
    
    stats = {
        'total_tasks': len(all_tasks),
        'tasks_by_tache': {tache: len(tasks) for tache, tasks in tasks_by_tache.items()},
        'unique_months': len(unique_months),
        'unique_combinations': len(unique_combinations),
        'months_covered': sorted(unique_months),
        'average_tasks_per_month': len(all_tasks) / len(unique_months) if unique_months else 0,
        'average_combinations_per_month': len(unique_combinations) / len(unique_months) if unique_months else 0
    }
    
    return stats

def print_data_summary():
    """Print a comprehensive summary of the loaded data."""
    stats = get_data_statistics()
    
    print(f"\n{'='*60}")
    print("TCF WRITING TASKS DATA SUMMARY")
    print(f"{'='*60}")
    print(f"Total tasks: {stats['total_tasks']}")
    print(f"Unique months: {stats['unique_months']}")
    print(f"Unique combinations: {stats['unique_combinations']}")
    print(f"Average tasks per month: {stats['average_tasks_per_month']:.1f}")
    
    print(f"\n📊 Distribution by Tâche:")
    for tache_num, count in stats['tasks_by_tache'].items():
        percentage = (count / stats['total_tasks']) * 100
        print(f"  Tâche {tache_num}: {count} tasks ({percentage:.1f}%)")
    
    print(f"\n📅 Months covered:")
    for month in stats['months_covered']:
        print(f"  • {month}")

if __name__ == '__main__':
    # Test the data loader
    print("🔧 Testing TCF Data Loader...")
    print_data_summary()
    
    # Test loading specific tâche
    print(f"\n🧪 Testing Tâche 2 loading...")
    tache_2_tasks = load_tasks_for_tache(2)
    print(f"Loaded {len(tache_2_tasks)} Tâche 2 tasks")
    if tache_2_tasks:
        print(f"First task: {tache_2_tasks[0]['id']} - {tache_2_tasks[0]['task_content'][:50]}...") 