#!/usr/bin/env python3
"""
Tâche 2 Classification System

A classification system for TCF Tâche 2 tasks following the same structure as Tâche 1.
Uses predefined main topics (description_places, sharing_information, recommendation, 
looking_for_service, manual_review) with keyword-based classification and deduplication.
"""

import json
import numpy as np
from pathlib import Path
import sys
from collections import defaultdict
import re

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_loader import load_tasks_for_tache
from deduplication import TaskDeduplicator, deduplicate_classification_tasks

class Tache2Classifier:
    """
    Classifier for TCF Tâche 2 following the same structure as Tâche 1.
    Uses predefined main topics with keyword-based classification and deduplication.
    
    Main topics: description_places, sharing_information, recommendation, 
    looking_for_service, manual_review
    """
    
    def __init__(self, similarity_threshold: float = 0.5):
        self.task_number = 2
        self.tasks = []
        self.similarity_threshold = similarity_threshold
        
        # Define classification structure based on Tâche 1 style with specified main topics
        self.classification_structure = {
            'description_places': {
                'topic_id': 0,
                'keywords': [
                    'ville', 'endroit', 'lieu', 'quartier', 'bureau', 'local', 'adresse', 'situé', 
                    'décrire', 'nouveau', 'emplacement', 'région', 'centre', 'zone'
                ],
                'subtopics': {
                    'description_office': {
                        'keywords': ['bureau', 'entreprise', 'local', 'travail', 'professionnel', 'affaires', 'siège'],
                        'patterns': ['bureau', 'entreprise', 'local', 'travail']
                    },
                    'description_neighborhood': {
                        'keywords': ['quartier', 'voisinage', 'rue', 'zone', 'secteur', 'paix', 'métro', 'résidentiel'],
                        'patterns': ['quartier', 'voisinage', 'rue', 'zone']
                    },
                    'description_city': {
                        'keywords': ['ville', 'centre', 'municipal', 'urbain', 'métropole', 'région', 'départemen'],
                        'patterns': ['ville', 'centre', 'municipal', 'urbain']
                    },
                    'description_venue': {
                        'keywords': ['lieu', 'endroit', 'salle', 'espace', 'bâtiment', 'établissement'],
                        'patterns': ['lieu', 'endroit', 'salle', 'espace']
                    },
                    'description_tourist_place': {
                        'keywords': ['château', 'monument', 'musée', 'touristique', 'historique', 'patrimoine'],
                        'patterns': ['château', 'monument', 'musée', 'touristique']
                    },
                    'description_general_place': {
                        'keywords': [],
                        'patterns': []
                    }
                }
            },
            'sharing_information': {
                'topic_id': 1,
                'keywords': [
                    'nouvelles', 'racont', 'partag', 'expérience', 'inform', 'nouvelle', 'savoir', 
                    'voulais', 'commencé', 'blog', 'article', 'témoignage', 'histoire'
                ],
                'subtopics': {
                    'sharing_personal_news': {
                        'keywords': ['famille', 'personnel', 'vie', 'nouveau', 'changement', 'mariage', 'naissance'],
                        'patterns': ['famille', 'personnel', 'vie', 'nouveau']
                    },
                    'sharing_work_news': {
                        'keywords': ['travail', 'emploi', 'bureau', 'collègue', 'entreprise', 'technologie', 'carrière'],
                        'patterns': ['travail', 'emploi', 'bureau', 'entreprise']
                    },
                    'sharing_travel_experience': {
                        'keywords': ['voyage', 'vacances', 'destination', 'découverte', 'paris', 'visité', 'magnifique', 'étranger'],
                        'patterns': ['voyage', 'vacances', 'destination', 'visité']
                    },
                    'sharing_cultural_experience': {
                        'keywords': ['fête', 'festival', 'traditionnel', 'culture', 'événement', 'spectacle', 'concert'],
                        'patterns': ['fête', 'festival', 'traditionnel', 'culture']
                    },
                    'sharing_education_info': {
                        'keywords': ['université', 'cours', 'études', 'formation', 'école', 'apprentissage', 'langue'],
                        'patterns': ['université', 'cours', 'études', 'école']
                    },
                    'sharing_blog_article': {
                        'keywords': ['blog', 'article', 'site web', 'écrivez un article', 'rédigez un article'],
                        'patterns': ['blog', 'article', 'site web']
                    },
                    'sharing_general_info': {
                        'keywords': [],
                        'patterns': []
                    }
                }
            },
            'recommendation': {
                'topic_id': 2,
                'keywords': [
                    'recommand', 'conseil', 'suggér', 'propose', 'avis', 'choix', 'meilleur', 
                    'excellent', 'bon', 'bonne', 'qualité', 'fortement', 'conseiller'
                ],
                'subtopics': {
                    'recommendation_restaurant': {
                        'keywords': ['restaurant', 'manger', 'cuisine', 'déjeuner', 'dîner', 'menu', 'repas', 'italien', 'français'],
                        'patterns': ['restaurant', 'manger', 'cuisine', 'repas']
                    },
                    'recommendation_travel': {
                        'keywords': ['voyage', 'destination', 'vacances', 'hôtel', 'partir', 'nice', 'paris', 'ville', 'confortable'],
                        'patterns': ['voyage', 'destination', 'vacances', 'hôtel']
                    },
                    'recommendation_entertainment': {
                        'keywords': ['sortir', 'activité', 'spectacle', 'concert', 'cinéma', 'divertissement', 'loisir'],
                        'patterns': ['sortir', 'activité', 'spectacle', 'concert']
                    },
                    'recommendation_services': {
                        'keywords': ['service', 'professionnel', 'coiffeur', 'médecin', 'garage', 'prestataire'],
                        'patterns': ['service', 'professionnel', 'coiffeur', 'médecin']
                    },
                    'recommendation_venues': {
                        'keywords': ['lieux', 'visiter', 'endroits', 'restaurants', 'historiques', 'local', 'lieu'],
                        'patterns': ['lieux à visiter', 'endroits', 'local', 'lieu']
                    },
                    'recommendation_general': {
                        'keywords': [],
                        'patterns': []
                    }
                }
            },
            'looking_for_service': {
                'topic_id': 3,
                'keywords': [
                    'aide', 'demande', 'service', 'assistance', 'cherche', 'besoin', 'problème', 
                    'j\'ai besoin', 'pouvez-vous', 'recherche', 'sollicite'
                ],
                'subtopics': {
                    'looking_for_help': {
                        'keywords': ['aide', 'assistance', 'problème', 'difficile', 'besoin', 'pouvez-vous', 'soutien'],
                        'patterns': ['aide', 'assistance', 'problème', 'besoin']
                    },
                    'looking_for_housing': {
                        'keywords': ['appartement', 'logement', 'chambre', 'colocataire', 'louer', 'centre-ville', 'immobilières'],
                        'patterns': ['appartement', 'logement', 'chambre', 'louer']
                    },
                    'looking_for_work': {
                        'keywords': ['travail', 'emploi', 'stage', 'job', 'candidature', 'embauche'],
                        'patterns': ['travail', 'emploi', 'stage', 'job']
                    },
                    'looking_for_language_help': {
                        'keywords': ['français', 'langue', 'pratiquer', 'apprendre', 'conversation', 'partenaires', 'linguistiques'],
                        'patterns': ['français', 'langue', 'pratiquer', 'apprendre']
                    },
                    'looking_for_transport': {
                        'keywords': ['transport', 'voiture', 'train', 'aéroport', 'déplacement', 'covoiturage'],
                        'patterns': ['transport', 'voiture', 'train', 'aéroport']
                    },
                    'looking_for_venue': {
                        'keywords': ['lieu', 'salle', 'organiser', 'fête', 'événement', 'local', 'espace'],
                        'patterns': ['lieu', 'salle', 'organiser', 'local']
                    },
                    'looking_for_other_service': {
                        'keywords': [],
                        'patterns': []
                    }
                }
            },
            'manual_review': {
                'topic_id': 4,
                'keywords': ['unclassified'],
                'subtopics': {
                    'manual_review_general': {
                        'keywords': [],
                        'patterns': []
                    }
                }
            }
        }
    
    def load_tasks(self):
        """Load tasks for Tâche 2."""
        self.tasks = load_tasks_for_tache(2)
        print(f"Loaded {len(self.tasks)} tasks for Tâche 2")
    
    def classify_task(self, task):
        """Classify a single task based on keywords and patterns, following Tâche 1 approach."""
        content = task['task_content'].lower()
        
        # Score each main topic
        topic_scores = {}
        classification_threshold = 0.08  # Minimum similarity required (same as Tâche 1)
        
        for topic_name, topic_info in self.classification_structure.items():
            if topic_name == 'manual_review':
                continue
                
            score = self.calculate_topic_similarity(content, topic_info['keywords'])
            topic_scores[topic_name] = score
        
        # Find best matching topic
        if max(topic_scores.values()) == 0 or max(topic_scores.values()) < classification_threshold:
            best_topic = 'manual_review'
            best_score = 0.0
        else:
            best_topic = max(topic_scores, key=topic_scores.get)
            best_score = topic_scores[best_topic]
        
        # Now classify into subtopic
        best_subtopic = self.classify_subtopic(task, best_topic)
        
        return best_topic, best_subtopic, best_score
    
    def calculate_topic_similarity(self, text, topic_keywords):
        """Calculate similarity between text and topic keywords, same as Tâche 1."""
        cleaned_text = self.clean_text(text)
        text_words = set(cleaned_text.split())
        
        # Count matches with partial word matching
        matches = 0
        for keyword in topic_keywords:
            if keyword in cleaned_text:
                matches += 1
            # Also check for partial matches (for words like "recommand" matching "recommander")
            elif any(keyword in word or word in keyword for word in text_words if len(word) > 3):
                matches += 0.5
        
        if not topic_keywords:
            return 0.0
        
        return matches / len(topic_keywords)
    
    def clean_text(self, text):
        """Clean text by removing task requirements and keeping semantic content, same as Tâche 1."""
        text = text.lower()
        
        # Remove common task instruction patterns
        patterns_to_remove = [
            r'\(\d+\s*mots?\s*minimum\s*/?\s*\d*\s*mots?\s*maximum\)',
            r'\d+\s*mots?\s*minimum',
            r'\d+\s*mots?\s*maximum',
            r'écrivez\s+un\s+message',
            r'rédigez\s+un\s+message',
            r'répondez\s+au\s+courriel',
            r'vous\s+écrivez\s+à',
            r'écrivez\s+à\s+votre',
            r'message\s+à\s+votre',
            r'écrivez\s+une?\s+lettre',
            r'rédigez\s+une?\s+réponse',
            r'écrivez\s+un\s+article',
            r'rédigez\s+un\s+article'
        ]
        
        for pattern in patterns_to_remove:
            text = re.sub(pattern, '', text)
        
        # Remove standalone numbers (word counts, etc.)
        text = re.sub(r'\b\d+\b', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def classify_subtopic(self, task, main_topic):
        """Classify task into subtopic within the main topic, following Tâche 1 approach."""
        content = task['task_content'].lower()
        
        if main_topic not in self.classification_structure:
            return 'manual_review_general'
        
        subtopics = self.classification_structure[main_topic]['subtopics']
        best_subtopic = f"{main_topic}_general" if f"{main_topic}_general" in subtopics else list(subtopics.keys())[0]
        best_score = 0.0
        
        for subtopic_name, subtopic_info in subtopics.items():
            if subtopic_info['keywords']:  # Skip empty fallback subtopics
                score = self.calculate_topic_similarity(content, subtopic_info['keywords'])
                if score > best_score:
                    best_score = score
                    best_subtopic = subtopic_name
        
        # If no good match found, use the general fallback
        if best_score < 0.05:
            fallback_subtopics = [name for name, info in subtopics.items() if not info['keywords']]
            if fallback_subtopics:
                best_subtopic = fallback_subtopics[0]
        
        return best_subtopic
    
    def classify(self):
        """
        Perform classification for Tâche 2 with integrated deduplication.
        """
        print(f"\n=== TÂCHE 2 CLASSIFICATION WITH DEDUPLICATION ===")
        
        # Initialize classification structure
        final_classification = {
            'task_number': 2,
            'method': 'predefined_classification_with_deduplication',
            'total_tasks': len(self.tasks),
            'unique_tasks': 0,  # Will be set after deduplication
            'duplicate_count': 0,  # Will be set after deduplication
            'n_main_topics': len(self.classification_structure),
            'main_topics': {}
        }
        
        # Initialize topics
        for topic_name, topic_info in self.classification_structure.items():
            final_classification['main_topics'][topic_name] = {
                'topic_id': topic_info['topic_id'],
                'keywords': topic_info['keywords'],
                'total_tasks': 0,
                'unique_tasks': 0,
                'subtopics': {}
            }
            
            # Initialize subtopics
            for subtopic_name, subtopic_info in topic_info['subtopics'].items():
                final_classification['main_topics'][topic_name]['subtopics'][subtopic_name] = {
                    'subtopic_id': len(final_classification['main_topics'][topic_name]['subtopics']),
                    'task_count': 0,
                    'unique_task_count': 0,
                    'tasks': [],  # Temporary - will be converted to task_entries
                    'keywords': subtopic_info['keywords'],
                    'template_similarity': 1.0
                }
        
        # Classify each task (before deduplication)
        for task in self.tasks:
            main_topic, subtopic, score = self.classify_task(task)
            
            # Add task to classification
            enriched_task = {
                'id': task['id'],
                'task_content': task['task_content'],
                'month_year': task['month_year'],
                'combination_number': task['combination_number'],
                'main_topic_score': score / len(self.classification_structure[main_topic]['keywords']) if score > 0 else 0
            }
            
            final_classification['main_topics'][main_topic]['subtopics'][subtopic]['tasks'].append(enriched_task)
        
        # Apply deduplication using the centralized module
        print("🔄 Applying deduplication...")
        final_classification = deduplicate_classification_tasks(final_classification, self.similarity_threshold)
        
        # Update counts after deduplication
        self.update_all_task_counts(final_classification)
        
        # Print summary
        print("\nClassification Summary:")
        print(f"  Total tasks: {final_classification['total_tasks']}")
        print(f"  Unique tasks: {final_classification['unique_tasks']}")
        print(f"  Duplicates found: {final_classification['duplicate_count']}")
        print("\nTopic breakdown:")
        for topic_name, topic_data in final_classification['main_topics'].items():
            if topic_data['total_tasks'] > 0:
                print(f"  {topic_name}: {topic_data['unique_tasks']} unique ({topic_data['total_tasks']} total)")
                for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                    if subtopic_data.get('unique_task_count', 0) > 0:
                        print(f"    └─ {subtopic_name}: {subtopic_data['unique_task_count']} unique ({subtopic_data['task_count']} total)")
        
        return final_classification
    
    def update_all_task_counts(self, classification):
        """Update all task counts (both unique and total) in the classification."""
        total_tasks = 0
        total_unique = 0
        
        for topic_name, topic_data in classification['main_topics'].items():
            topic_total = 0
            topic_unique = 0
            
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                unique_count = len(subtopic_data.get('task_entries', []))
                total_count = sum(entry['duplicate_count'] for entry in subtopic_data.get('task_entries', []))
                
                subtopic_data['unique_task_count'] = unique_count
                subtopic_data['task_count'] = total_count
                
                topic_unique += unique_count
                topic_total += total_count
            
            topic_data['unique_tasks'] = topic_unique
            topic_data['total_tasks'] = topic_total
            total_unique += topic_unique
            total_tasks += topic_total
        
        classification['unique_tasks'] = total_unique
        classification['total_tasks'] = total_tasks
    
    def save_classification(self, classification):
        """Save classification results."""
        workspace_root = Path(__file__).parent.parent.parent.parent
        output_dir = workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_file = output_dir / f'tache_2_classification.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(classification, f, ensure_ascii=False, indent=2)
        
        print(f"Tâche 2 classification saved to {output_file}")
    
    def generate_report(self, classification):
        """Generate report for classification with duplicate information."""
        total_tasks = classification['total_tasks']
        unique_tasks = classification['unique_tasks']
        duplicate_count = classification['duplicate_count']
        
        report_lines = [
            f"TÂCHE {classification['task_number']} CLASSIFICATION RESULTS (WITH DEDUPLICATION)",
            f"="*70,
            f"Total Tasks: {total_tasks}",
            f"Unique Tasks: {unique_tasks}",
            f"Duplicates: {duplicate_count}",
            f"Classification Method: {classification['method']}",
            f"Main Topics: {classification['n_main_topics']}",
            "",
            "TOPIC BREAKDOWN:"
        ]
        
        for topic_name, topic_data in classification['main_topics'].items():
            if topic_data['total_tasks'] > 0:
                report_lines.append(f"  📁 {topic_name}: {topic_data['unique_tasks']} unique ({topic_data['total_tasks']} total)")
                
                for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                    if subtopic_data.get('unique_task_count', 0) > 0:
                        report_lines.append(f"    └─ {subtopic_name}: {subtopic_data['unique_task_count']} unique ({subtopic_data['task_count']} total)")
        
        # Add deduplication statistics if available
        if 'deduplication_stats' in classification:
            stats = classification['deduplication_stats']
            report_lines.extend([
                "",
                "DEDUPLICATION STATISTICS:",
                f"  Total duplicate groups: {stats['duplicate_groups']}",
                f"  Largest group size: {stats['largest_group_size']}",
                f"  Average group size: {stats['average_group_size']:.1f}",
                f"  Similarity threshold: {stats['similarity_threshold']:.0%}"
            ])
        
        return '\n'.join(report_lines)

def run_tache_2_classification():
    """Run Tâche 2 classification with deduplication."""
    
    print("="*80)
    print("TÂCHE 2 CLASSIFICATION WITH DEDUPLICATION")
    print("="*80)
    
    classifier = Tache2Classifier()
    classifier.load_tasks()
    
    # Run classification
    classification = classifier.classify()
    
    # Save results
    classifier.save_classification(classification)
    
    # Generate and print report
    report = classifier.generate_report(classification)
    print("\n" + report)
    
    return classification

if __name__ == '__main__':
    result = run_tache_2_classification()
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total Tasks: {result['total_tasks']}")
    print(f"   Unique Tasks: {result['unique_tasks']}")
    print(f"   Main Topics: {result['n_main_topics']}")
    print(f"   Method: {result['method']}")
    print("\n✅ Tâche 2 classification completed!") 