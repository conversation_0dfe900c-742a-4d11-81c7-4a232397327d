#!/usr/bin/env python3
"""
Tâche 1 Classification System

A predefined classification system for TCF Tâche 1 with specific main topics and subtopics.
Now includes deduplication support.

Main Topics:
- recommendation: Tasks about giving recommendations or suggestions
- sharing_information: Tasks about sharing news, experiences, or information  
- looking_for_service: Tasks about requesting help or seeking services
- description_places: Tasks about describing locations, offices, places
- manual_review: Tasks that cannot be classified automatically
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Tuple
import re
import sys
from collections import Counter, defaultdict

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from data_loader import load_all_writing_tasks
from deduplication import TaskDeduplicator, deduplicate_classification_tasks

class Tache1Classifier:
    """
    Predefined classifier for TCF Tâche 1 with specific main topics and subtopics.
    Now includes deduplication support.
    """
    
    def __init__(self, similarity_threshold: float = 0.95):
        self.task_number = 1
        self.tasks = []
        self.task_texts = []
        self.similarity_threshold = similarity_threshold
        
        # Define the predefined main topics structure
        self.predefined_main_topics = {
            'recommendation': {
                'keywords': ['recommand', 'conseil', 'suggér', 'propose', 'avis', 'choix', 'meilleur', 'excellent', 'bon', 'bonne', 'qualité', 'fortement'],
                'description': 'Tasks about giving recommendations or suggestions',
                'subtopics': {
                    'recommendation_restaurant': ['restaurant', 'manger', 'cuisine', 'déjeuner', 'dîner', 'menu', 'repas', 'italien', 'français'],
                    'recommendation_travel': ['voyage', 'destination', 'vacances', 'hôtel', 'partir', 'nice', 'paris', 'ville', 'confortable'],
                    'recommendation_marketplace': ['acheter', 'produit', 'magasin', 'marché', 'commerce', 'vêtements', 'sélection', 'prix', 'compétitifs'],
                    'recommendation_entertainment': ['sortir', 'activité', 'spectacle', 'concert', 'cinéma', 'divertissement'],
                    'recommendation_services': ['service', 'professionnel', 'coiffeur', 'médecin', 'garage'],
                    'recommendation_general': []  # fallback
                }
            },
            'sharing_information': {
                'keywords': ['nouvelles', 'racont', 'partag', 'expérience', 'inform', 'nouvelle', 'savoir', 'voulais', 'commencé'],
                'description': 'Tasks about sharing news, experiences, or information',
                'subtopics': {
                    'sharing_personal_news': ['famille', 'personnel', 'vie', 'nouveau', 'changement'],
                    'sharing_work_news': ['travail', 'emploi', 'bureau', 'collègue', 'entreprise', 'technologie'],
                    'sharing_event_experience': ['événement', 'spectacle', 'concert', 'visite', 'sortie', 'louvre', 'tour', 'eiffel'],
                    'sharing_travel_experience': ['voyage', 'vacances', 'destination', 'découverte', 'paris', 'visité', 'magnifique'],
                    'sharing_study_info': ['université', 'cours', 'études', 'formation', 'école'],
                    'sharing_general_info': ['équipements', 'nouveaux', 'piscine', 'fitness', 'quartier']  # fallback with keywords
                }
            },
            'looking_for_service': {
                'keywords': ['aide', 'demande', 'service', 'assistance', 'cherche', 'besoin', 'problème', 'j\'ai besoin', 'pouvez-vous'],
                'description': 'Tasks about requesting help or seeking services',
                'subtopics': {
                    'looking_for_help': ['aide', 'assistance', 'problème', 'difficile', 'besoin', 'pouvez-vous'],
                    'looking_for_housing': ['appartement', 'logement', 'chambre', 'colocataire', 'louer', 'centre-ville', 'immobilières'],
                    'looking_for_work': ['travail', 'emploi', 'stage', 'job', 'candidature'],
                    'looking_for_language_help': ['français', 'langue', 'pratiquer', 'apprendre', 'conversation', 'partenaires', 'linguistiques'],
                    'looking_for_transport': ['transport', 'voiture', 'train', 'aéroport', 'déplacement'],
                    'looking_for_other_service': []  # fallback
                }
            },
            'description_places': {
                'keywords': ['ville', 'endroit', 'lieu', 'quartier', 'bureau', 'local', 'adresse', 'situé', 'décrire', 'nouveau'],
                'description': 'Tasks about describing locations, offices, places',
                'subtopics': {
                    'description_office': ['bureau', 'entreprise', 'local', 'travail', 'professionnel', 'affaires'],
                    'description_neighborhood': ['quartier', 'voisinage', 'rue', 'zone', 'secteur', 'paix', 'métro'],
                    'description_city': ['ville', 'centre', 'municipal', 'urbain', 'métropole'],
                    'description_venue': ['lieu', 'endroit', 'salle', 'espace', 'bâtiment'],
                    'description_tourist_place': ['château', 'monument', 'musée', 'touristique', 'historique'],
                    'description_general_place': []  # fallback
                }
            }
        }
        
        # Extended stop words including task requirements
        self.tcf_stop_words = [
            # Basic French stop words
            'le', 'de', 'et', 'à', 'un', 'il', 'être', 'en', 'avoir', 'que', 'pour',
            'dans', 'ce', 'son', 'une', 'sur', 'avec', 'ne', 'se', 'pas', 'tout', 'plus',
            'par', 'grand', 'si', 'au', 'du', 'la', 'les', 'des', 'vous', 'votre', 'vos',
            'nous', 'notre', 'nos', 'je', 'me', 'moi', 'tu', 'te', 'toi', 'elle', 'lui',
            'ils', 'elles', 'leur', 'leurs', 'cette', 'ces', 'celui', 'celle', 'ceux',
            'celles', 'qui', 'quoi', 'dont', 'où', 'ou', 'ni', 'mais', 'donc', 'or',
            'car', 'comme', 'quand', 'bien', 'très', 'trop', 'peu', 'beaucoup', 'assez',
            'moins', 'tant', 'aussi', 'encore', 'déjà', 'toujours', 'jamais', 'souvent',
            'parfois', 'quelquefois',
            
            # TCF-specific task requirement words to ignore
            'minimum', 'maximum', 'mots', 'mot', '60', '120', '150', '200', '250',
            'écrivez', 'rédigez', 'rédiger', 'écrire', 'message', 'email', 'courriel',
            'lettre', 'ami', 'amie', 'amis', 'répondez', 'réponse', 'répondre',
            'décrivez', 'décrire', 'raconter', 'racontez', 'expliquer', 'expliquez',
            'parler', 'parlez', 'dire', 'dites', 'préciser', 'précisez',
            'informations', 'information', 'détails', 'détail', 'détailler',
            'tâche', 'consigne', 'sujet', 'thème', 'question',
            'cédric', 'lucas', 'marie', 'jean', 'pierre', 'sophie', 'nabil', 'alex',
            'flavie', 'john', 'mike', 'denis', 'eduard', 'essame', 'isaak',
            # Common email patterns
            'salut', 'bonjour', 'bonsoir', 'cher', 'chère', 'cordialement',
            'amicalement', 'bientôt', 'bisous', 'gros', 'bisous'
        ]
    
    def load_tasks(self):
        """Load tasks for Tâche 1."""
        all_tasks = load_all_writing_tasks()
        self.tasks = all_tasks[1]
        self.task_texts = [task['task_content'] for task in self.tasks]
        print(f"Loaded {len(self.tasks)} tasks for Tâche 1")
    
    def clean_text(self, text):
        """Clean text by removing task requirements and keeping semantic content."""
        text = text.lower()
        
        # Remove common task instruction patterns
        patterns_to_remove = [
            r'\(\d+\s*mots?\s*minimum\s*/?\s*\d*\s*mots?\s*maximum\)',
            r'\d+\s*mots?\s*minimum',
            r'\d+\s*mots?\s*maximum',
            r'écrivez\s+un\s+message',
            r'rédigez\s+un\s+message',
            r'répondez\s+au\s+courriel',
            r'vous\s+écrivez\s+à',
            r'écrivez\s+à\s+votre',
            r'message\s+à\s+votre',
            r'écrivez\s+une?\s+lettre',
            r'rédigez\s+une?\s+réponse'
        ]
        
        for pattern in patterns_to_remove:
            text = re.sub(pattern, '', text)
        
        # Remove standalone numbers (word counts, etc.)
        text = re.sub(r'\b\d+\b', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def calculate_topic_similarity(self, text, topic_keywords):
        """Calculate similarity between text and topic keywords."""
        cleaned_text = self.clean_text(text)
        text_words = set(cleaned_text.split())
        
        # Count matches with partial word matching
        matches = 0
        for keyword in topic_keywords:
            if keyword in cleaned_text:
                matches += 1
            # Also check for partial matches (for words like "recommand" matching "recommander")
            elif any(keyword in word or word in keyword for word in text_words if len(word) > 3):
                matches += 0.5
        
        if not topic_keywords:
            return 0.0
        
        return matches / len(topic_keywords)
    
    def classify_to_main_topic(self, task_text):
        """Classify a task to one of the predefined main topics."""
        best_topic = 'manual_review'
        best_score = 0.0
        classification_threshold = 0.08  # Minimum similarity required
        
        for topic_name, topic_info in self.predefined_main_topics.items():
            score = self.calculate_topic_similarity(task_text, topic_info['keywords'])
            if score > best_score:
                best_score = score
                best_topic = topic_name
        
        # If score is too low, send to manual review
        if best_score < classification_threshold:
            best_topic = 'manual_review'
        
        return best_topic, best_score
    
    def classify_to_subtopic(self, task_text, main_topic):
        """Classify a task to a specific subtopic within the main topic."""
        if main_topic not in self.predefined_main_topics:
            return f"{main_topic}_general", 0.0
        
        subtopics = self.predefined_main_topics[main_topic]['subtopics']
        best_subtopic = f"{main_topic}_general"
        best_score = 0.0
        
        for subtopic_name, subtopic_keywords in subtopics.items():
            if subtopic_keywords:  # Skip empty fallback subtopics
                score = self.calculate_topic_similarity(task_text, subtopic_keywords)
                if score > best_score:
                    best_score = score
                    best_subtopic = subtopic_name
        
        # If no good match found, use the general fallback
        if best_score < 0.05:
            fallback_subtopics = [name for name, keywords in subtopics.items() if not keywords]
            if fallback_subtopics:
                best_subtopic = fallback_subtopics[0]
        
        return best_subtopic, best_score
    
    def classify(self):
        """
        Perform classification using predefined main topics and subtopics with deduplication.
        """
        print(f"\n=== TÂCHE 1 CLASSIFICATION WITH DEDUPLICATION ===")
        print(f"Main topics: {list(self.predefined_main_topics.keys())} + manual_review")
        
        # Initialize classification structure
        final_classification = {
            'task_number': 1,
            'method': 'predefined_classification_with_deduplication',
            'total_tasks': len(self.tasks),
            'unique_tasks': 0,  # Will be set after deduplication
            'duplicate_count': 0,  # Will be set after deduplication
            'n_main_topics': len(self.predefined_main_topics) + 1,  # +1 for manual_review
            'main_topics': {}
        }
        
        # Initialize all main topics
        all_main_topics = list(self.predefined_main_topics.keys()) + ['manual_review']
        for topic_name in all_main_topics:
            final_classification['main_topics'][topic_name] = {
                'topic_id': len(final_classification['main_topics']),
                'keywords': self.predefined_main_topics.get(topic_name, {}).get('keywords', ['unclassified']),
                'total_tasks': 0,
                'unique_tasks': 0,
                'subtopics': {}
            }
        
        # Classify each task (before deduplication)
        classification_stats = defaultdict(int)
        
        for i, task in enumerate(self.tasks):
            task_text = task['task_content']
            
            # Step 1: Classify to main topic
            main_topic, main_score = self.classify_to_main_topic(task_text)
            classification_stats[main_topic] += 1
            
            # Step 2: Classify to subtopic
            if main_topic == 'manual_review':
                subtopic = 'manual_review_general'
            else:
                subtopic, sub_score = self.classify_to_subtopic(task_text, main_topic)
            
            # Add task to classification (using temporary tasks format)
            if subtopic not in final_classification['main_topics'][main_topic]['subtopics']:
                final_classification['main_topics'][main_topic]['subtopics'][subtopic] = {
                    'subtopic_id': len(final_classification['main_topics'][main_topic]['subtopics']),
                    'task_count': 0,
                    'unique_task_count': 0,
                    'tasks': [],  # Temporary - will be converted to task_entries
                    'keywords': self.predefined_main_topics.get(main_topic, {}).get('subtopics', {}).get(subtopic, []),
                    'template_similarity': 1.0
                }
            
            # Add task to subtopic
            enriched_task = {
                'id': task['id'],
                'task_content': task['task_content'],
                'month_year': task['month_year'],
                'combination_number': task['combination_number'],
                'main_topic_score': main_score if main_topic != 'manual_review' else 0.0
            }
            
            final_classification['main_topics'][main_topic]['subtopics'][subtopic]['tasks'].append(enriched_task)
        
        # Apply deduplication using the centralized module
        print("🔄 Applying deduplication...")
        final_classification = deduplicate_classification_tasks(final_classification, self.similarity_threshold)
        
        # Update counts after deduplication
        self.update_all_task_counts(final_classification)
        
        # Print summary
        print("\nClassification Summary:")
        print(f"  Total tasks: {final_classification['total_tasks']}")
        print(f"  Unique tasks: {final_classification['unique_tasks']}")
        print(f"  Duplicates found: {final_classification['duplicate_count']}")
        print("\nTopic breakdown:")
        for topic_name, topic_data in final_classification['main_topics'].items():
            if topic_data['total_tasks'] > 0:
                print(f"  {topic_name}: {topic_data['unique_tasks']} unique ({topic_data['total_tasks']} total)")
                for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                    if subtopic_data.get('unique_task_count', 0) > 0:
                        print(f"    └─ {subtopic_name}: {subtopic_data['unique_task_count']} unique ({subtopic_data['task_count']} total)")
        
        return final_classification
    
    def update_all_task_counts(self, classification):
        """Update all task counts (both unique and total) in the classification."""
        total_tasks = 0
        total_unique = 0
        
        for topic_name, topic_data in classification['main_topics'].items():
            topic_total = 0
            topic_unique = 0
            
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                unique_count = len(subtopic_data.get('task_entries', []))
                total_count = sum(entry['duplicate_count'] for entry in subtopic_data.get('task_entries', []))
                
                subtopic_data['unique_task_count'] = unique_count
                subtopic_data['task_count'] = total_count
                
                topic_unique += unique_count
                topic_total += total_count
            
            topic_data['unique_tasks'] = topic_unique
            topic_data['total_tasks'] = topic_total
            total_unique += topic_unique
            total_tasks += topic_total
        
        classification['unique_tasks'] = total_unique
        classification['total_tasks'] = total_tasks
    
    def save_classification(self, classification):
        """Save classification results."""
        workspace_root = Path(__file__).parent.parent.parent.parent
        output_dir = workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_file = output_dir / f'tache_1_classification.json'
        
        # Convert numpy types to Python types for JSON serialization
        def convert_numpy_types(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy_types(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            else:
                return obj
        
        classification_clean = convert_numpy_types(classification)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(classification_clean, f, ensure_ascii=False, indent=2)
        
        print(f"Tâche 1 classification saved to {output_file}")
    
    def generate_report(self, classification):
        """Generate detailed report for classification with deduplication information."""
        total_tasks = classification['total_tasks']
        unique_tasks = classification['unique_tasks']
        duplicate_count = classification['duplicate_count']
        manual_review_count = classification['main_topics'].get('manual_review', {}).get('total_tasks', 0)
        auto_classified = total_tasks - manual_review_count
        auto_classification_rate = (auto_classified / total_tasks) * 100 if total_tasks > 0 else 0
        
        report = [
            f"TÂCHE {classification['task_number']} CLASSIFICATION RESULTS (WITH DEDUPLICATION)",
            f"="*70,
            f"Total Tasks: {total_tasks}",
            f"Unique Tasks: {unique_tasks}",
            f"Duplicates: {duplicate_count}",
            f"Auto-classified: {auto_classified} ({auto_classification_rate:.1f}%)",
            f"Manual Review Required: {manual_review_count} ({100-auto_classification_rate:.1f}%)",
            f"Main Topics: {classification['n_main_topics']}",
            f"Total Subtopics: {sum(len(topic['subtopics']) for topic in classification['main_topics'].values())}",
            "",
            "MAIN TOPICS BREAKDOWN:",
            "-" * 30
        ]
        
        # Sort main topics by total tasks (excluding manual_review)
        predefined_topics = [(name, info) for name, info in classification['main_topics'].items() 
                           if name != 'manual_review']
        sorted_topics = sorted(predefined_topics, key=lambda x: x[1]['total_tasks'], reverse=True)
        
        # Add manual_review at the end if it has tasks
        if manual_review_count > 0:
            sorted_topics.append(('manual_review', classification['main_topics']['manual_review']))
        
        for main_topic_name, main_topic_info in sorted_topics:
            total_tasks_topic = main_topic_info['total_tasks']
            unique_tasks_topic = main_topic_info.get('unique_tasks', total_tasks_topic)
            
            if total_tasks_topic == 0:
                continue
                
            subtopics_count = len(main_topic_info['subtopics'])
            percentage = (total_tasks_topic / total_tasks) * 100
            
            if unique_tasks_topic < total_tasks_topic:
                report.append(f"\n• {main_topic_name.upper()}: {unique_tasks_topic} unique ({total_tasks_topic} total, {percentage:.1f}%)")
            else:
                report.append(f"\n• {main_topic_name.upper()}: {total_tasks_topic} tasks ({percentage:.1f}%)")
            
            if main_topic_name != 'manual_review':
                keywords = ', '.join(main_topic_info['keywords'][:5])
                report.append(f"  Keywords: {keywords}")
            
            # List subtopics with task counts
            if subtopics_count > 0:
                report.append(f"  Subtopics ({subtopics_count}):")
                subtopic_items = sorted(main_topic_info['subtopics'].items(), 
                                      key=lambda x: x[1].get('task_count', 0), reverse=True)
                for subtopic_name, subtopic_info in subtopic_items:
                    task_count = subtopic_info.get('task_count', 0)
                    unique_count = subtopic_info.get('unique_task_count', task_count)
                    
                    if task_count > 0:
                        subtopic_percentage = (task_count / total_tasks_topic) * 100
                        if unique_count < task_count:
                            report.append(f"    - {subtopic_name}: {unique_count} unique ({task_count} total, {subtopic_percentage:.1f}%)")
                        else:
                            report.append(f"    - {subtopic_name}: {task_count} tasks ({subtopic_percentage:.1f}%)")
        
        # Add deduplication statistics if available
        if 'deduplication_stats' in classification:
            stats = classification['deduplication_stats']
            report.extend([
                "",
                "DEDUPLICATION STATISTICS:",
                "-" * 30,
                f"  Total duplicate groups: {stats['duplicate_groups']}",
                f"  Largest group size: {stats['largest_group_size']}",
                f"  Average group size: {stats['average_group_size']:.1f}",
                f"  Similarity threshold: {stats['similarity_threshold']:.0%}",
                f"  Reduction rate: {duplicate_count/total_tasks*100:.1f}%"
            ])
        
        report.extend([
            "",
            "CLASSIFICATION QUALITY:",
            "-" * 25,
            f"✓ {auto_classification_rate:.1f}% tasks automatically classified",
            f"⚠ {100-auto_classification_rate:.1f}% tasks require manual review"
        ])
        
        return '\n'.join(report)

def run_tache_1_classification():
    """Run Tâche 1 classification."""
    
    print("="*80)
    print("TÂCHE 1 CLASSIFICATION")
    print("="*80)
    
    classifier = Tache1Classifier()
    classifier.load_tasks()
    
    # Run classification
    classification = classifier.classify()
    
    # Save results
    classifier.save_classification(classification)
    
    # Generate and print report
    report = classifier.generate_report(classification)
    print("\n" + report)
    
    return classification

if __name__ == '__main__':
    result = run_tache_1_classification()
    
    auto_classified = result['total_tasks'] - result['main_topics'].get('manual_review', {}).get('total_tasks', 0)
    auto_rate = (auto_classified / result['total_tasks']) * 100 if result['total_tasks'] > 0 else 0
    
    print(f"\n🎯 SUMMARY:")
    print(f"   Total Tasks: {result['total_tasks']}")
    print(f"   Auto-classified: {auto_classified} ({auto_rate:.1f}%)")
    print(f"   Manual Review: {result['main_topics'].get('manual_review', {}).get('total_tasks', 0)}")
    print(f"   Main Topics: 4 predefined + 1 manual review")
    print(f"   Subtopics: {sum(len(topic['subtopics']) for topic in result['main_topics'].values())}")
    print("\n✅ Tâche 1 classification completed!") 