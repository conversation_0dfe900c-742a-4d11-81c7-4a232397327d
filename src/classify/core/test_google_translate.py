#!/usr/bin/env python3
"""
Simple test for Google Translate functionality
"""

def test_google_translate():
    """Test Google Translate directly."""
    print("🧪 TESTING GOOGLE TRANSLATE")
    print("=" * 50)
    
    try:
        from googletrans import Translator
        print("✅ Google Translate imported successfully")
        
        translator = Translator()
        print("✅ Translator initialized")
        
        # Test translations
        test_topics = [
            'recommendation',
            'description places', 
            'description person',
            'narration',
            'opinion',
            'explanation',
            'argumentation',
            'travel blog',
            'food review'
        ]
        
        print("\n📝 Testing translations:")
        for topic in test_topics:
            try:
                # Translate to French
                fr_result = translator.translate(topic, src='en', dest='fr')
                fr_translation = fr_result.text
                
                # Translate to Chinese
                zh_result = translator.translate(topic, src='en', dest='zh')
                zh_translation = zh_result.text
                
                print(f"  {topic:20} → FR: {fr_translation:25} | ZH: {zh_translation}")
                
            except Exception as e:
                print(f"  {topic:20} → ERROR: {e}")
        
        print("\n✅ Google Translate test completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import Google Translate: {e}")
        return False
    except Exception as e:
        print(f"❌ Translation test failed: {e}")
        return False

if __name__ == '__main__':
    test_google_translate()
