# TCF Classification System - Clean Structure

## Overview
This is the cleaned-up TCF Classification System that powers the web interface at `/web/app.py`. All legacy and unnecessary files have been removed, keeping only the essential components.

## Directory Structure
```
src/classify/
├── 📁 core/                          # Core classification system
│   ├── tache_1_classification.py     # Tâche 1 with predefined topics (88.9% accuracy)
│   ├── tache_2_classification.py     # Tâche 2 placeholder system
│   ├── tache_3_classification.py     # Tâche 3 placeholder system
│   ├── run_all_classifications.py    # Unified runner for all classifications
│   ├── data_loader.py               # Data loading utilities
│   ├── checkpoint_classifier.py     # Checkpoint-based classification
│   └── __init__.py                  # Module initialization
├── 📁 human_loop/                   # Human-in-the-loop system
│   ├── classifier.py               # Main human review system
│   └── __init__.py                  # Module initialization
├── 📁 checkpoints/                  # Human-approved checkpoints
├── 📁 web/                          # Web interface (React + Flask)
│   ├── app.py                       # Flask API backend
│   ├── package.json                 # React frontend dependencies
│   ├── src/                         # React components
│   ├── public/                      # Static files
│   └── start-dev.sh                 # Development server script
└── README.md                        # This file
```

## What Powers the Web Interface

### Flask API Backend (`web/app.py`)
The web interface depends on these core components:

1. **HumanInTheLoopClassifier** (`human_loop/classifier.py`)
   - Load automatic classifications
   - Review and modify classifications
   - Save/load checkpoints
   - Move tasks between topics
   - Search functionality

2. **CheckpointBasedClassifier** (`core/checkpoint_classifier.py`)
   - Demo classification functionality
   - Use human-approved checkpoints for new classifications

3. **Classification Files** (generated by `core/run_all_classifications.py`)
   - `tache_1_classification.json` - Predefined classification system
   - `tache_2_classification.json` - Placeholder system
   - `tache_3_classification.json` - Placeholder system

## Key Features

### ✅ Tâche 1 Classification System
- **88.9% automatic classification accuracy**
- **4 main topics + manual review:**
  - `recommendation` (10.6% of tasks)
  - `sharing_information` (24.1% of tasks)
  - `looking_for_service` (19.1% of tasks)
  - `description_places` (35.2% of tasks)
  - `manual_review` (11.1% of tasks)
- **22 total subtopics** including:
  - `description_office`
  - `recommendation_marketplace`
  - And many more specific categories

### 🔧 Human-in-the-Loop System
- Review automatic classifications
- Rename topics/subtopics
- Move tasks between categories
- Create new topics/subtopics
- Save approved classifications as checkpoints
- Search through tasks

### 🌐 Web Interface
- **React frontend** with drag-and-drop functionality
- **Flask API backend** with RESTful endpoints
- **Real-time task management**
- **Checkpoint system integration**

## Quick Start

### 1. Generate Classifications
```bash
python3 core/run_all_classifications.py
```

### 2. Start Web Interface
```bash
cd web
./start-dev.sh
```

### 3. Access Interface
- **React Frontend:** http://localhost:3000
- **Flask API:** http://localhost:5001

## API Endpoints

The Flask backend provides these key endpoints:
- `/api/health` - Health check
- `/load_classification` - Load automatic classification
- `/get_current_classification` - Get current state
- `/move_task` - Move tasks between topics
- `/save_checkpoint` - Save human-approved classifications
- `/demo_classification` - Test checkpoint-based classification

## Data Flow

1. **Raw Data** → `data/scraped/scraped_writing/`
2. **Auto Classification** → `core/run_all_classifications.py`
3. **Generated Files** → `data/classified/writing/`
4. **Human Review** → Web interface (`web/app.py`)
5. **Approved Checkpoints** → `checkpoints/`

## Clean Architecture

This system has been cleaned to remove:
- ❌ Legacy hierarchical classification files
- ❌ Unused automation scripts
- ❌ Development utilities and test files
- ❌ Duplicate and outdated code
- ❌ Cache files and temporary data

**Only essential files remain** that are directly used by the web interface. 