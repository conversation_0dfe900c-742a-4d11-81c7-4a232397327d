#!/usr/bin/env python3
"""
Human-in-the-Loop Classification System for TCF Writing Tasks

This system allows human experts to review, modify, and approve automatic 
classifications, then save them as checkpoints for future use.

Features:
- Review automatic classifications with deduplication support
- Handle both old 'tasks' format and new 'task_entries' format
- Rename topics and subtopics
- Create new topics/subtopics
- Move tasks between categories (including duplicate groups)
- Save approved classifications as checkpoints
- Use checkpoints to guide future classifications
"""

import json
import os
from pathlib import Path
from datetime import datetime
import shutil
from typing import Dict, List, Any, Optional
from collections import defaultdict

class HumanInTheLoopClassifier:
    """
    Human-in-the-loop system for reviewing and modifying automatic classifications.
    Now supports both legacy 'tasks' format and new 'task_entries' format with deduplication.
    """
    
    def __init__(self):
        self.workspace_root = Path(__file__).parent.parent.parent.parent
        # Try new location first, then fall back to old location
        self.data_dir_new = self.workspace_root / 'data' / 'classified' / 'writing' / 'rule_based_classification'
        self.data_dir_old = self.workspace_root / 'data' / 'classified' / 'writing'
        self.checkpoints_dir = Path(__file__).parent.parent / 'checkpoints'
        self.checkpoints_dir.mkdir(parents=True, exist_ok=True)
        
        self.current_classification = None
        self.tache_number = None
        self.modifications_log = []
    
    def load_automatic_classification(self, tache_number: int):
        """Load automatic classification results for review."""
        self.tache_number = tache_number
        classification_file_new = self.data_dir_new / f'tache_{tache_number}_classification.json'
        classification_file_old = self.data_dir_old / f'tache_{tache_number}_classification.json'
        
        # Try new location first
        classification_file = None
        if classification_file_new.exists():
            classification_file = classification_file_new
            print(f"📁 Loading from new location: {classification_file}")
        elif classification_file_old.exists():
            classification_file = classification_file_old
            print(f"📁 Loading from old location: {classification_file}")
        else:
            print(f"❌ No automatic classification found for Tâche {tache_number}")
            print(f"Checked locations:")
            print(f"  - New: {classification_file_new}")
            print(f"  - Old: {classification_file_old}")
            return False
        
        with open(classification_file, 'r', encoding='utf-8') as f:
            self.current_classification = json.load(f)
        
        # Print information about format and deduplication
        total_tasks = self.current_classification.get('total_tasks', 0)
        unique_tasks = self.current_classification.get('unique_tasks', total_tasks)
        method = self.current_classification.get('method', 'unknown')
        
        print(f"✅ Loaded automatic classification for Tâche {tache_number}")
        print(f"📊 {len(self.current_classification['main_topics'])} main topics, "
              f"{sum(len(topic['subtopics']) for topic in self.current_classification['main_topics'].values())} subtopics")
        
        if unique_tasks < total_tasks:
            duplicates = total_tasks - unique_tasks
            print(f"🔄 Deduplication applied: {unique_tasks} unique tasks from {total_tasks} total ({duplicates} duplicates)")
        
        print(f"🔧 Classification method: {method}")
        
        return True
    
    def display_current_classification(self):
        """Display the current classification structure with deduplication info."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return
        
        total_tasks = self.current_classification.get('total_tasks', 0)
        unique_tasks = self.current_classification.get('unique_tasks', total_tasks)
        
        print(f"\n📋 CURRENT CLASSIFICATION - TÂCHE {self.current_classification['task_number']}")
        print(f"Total: {total_tasks} tasks")
        if unique_tasks < total_tasks:
            print(f"Unique: {unique_tasks} tasks ({total_tasks - unique_tasks} duplicates)")
        print("=" * 60)
        
        # Sort main topics by task count
        sorted_main_topics = sorted(
            self.current_classification['main_topics'].items(),
            key=lambda x: x[1].get('total_tasks', 0),
            reverse=True
        )
        
        for i, (main_topic_name, main_topic_info) in enumerate(sorted_main_topics, 1):
            total_tasks = main_topic_info.get('total_tasks', 0)
            unique_tasks = main_topic_info.get('unique_tasks', total_tasks)
            subtopics_count = len(main_topic_info['subtopics'])
            percentage = (total_tasks / self.current_classification['total_tasks']) * 100 if self.current_classification['total_tasks'] > 0 else 0
            
            if unique_tasks < total_tasks:
                print(f"\n{i}. 📂 {main_topic_name} ({unique_tasks} unique, {total_tasks} total, {percentage:.1f}%)")
            else:
                print(f"\n{i}. 📂 {main_topic_name} ({total_tasks} tasks, {percentage:.1f}%)")
            
            # Display subtopics
            for j, (subtopic_name, subtopic_info) in enumerate(main_topic_info['subtopics'].items(), 1):
                task_count = subtopic_info.get('task_count', 0)
                unique_count = subtopic_info.get('unique_task_count', task_count)
                
                if unique_count < task_count:
                    print(f"   {i}.{j} 📄 {subtopic_name}: {unique_count} unique ({task_count} total)")
                else:
                    print(f"   {i}.{j} 📄 {subtopic_name}: {task_count} tasks")
    
    def _get_task_from_any_format(self, subtopic_data: Dict[str, Any], task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a task from either task_entries or tasks format.
        Returns the task data and format info.
        """
        # Try task_entries format first (new format)
        if 'task_entries' in subtopic_data:
            for entry in subtopic_data['task_entries']:
                if task_id in entry['task_ids']:
                    return {
                        'format': 'task_entries',
                        'entry': entry,
                        'task_data': {
                            'id': entry['representative_id'],
                            'task_content': entry['task_content'],
                            'month_year': entry['month_years'][0] if entry['month_years'] else '',
                            'combination_number': entry['combination_numbers'][0] if entry['combination_numbers'] else '',
                            'is_duplicate_group': entry['is_duplicate_group'],
                            'duplicate_count': entry['duplicate_count'],
                            'all_task_ids': entry['task_ids']
                        }
                    }
        
        # Try tasks format (legacy format)
        if 'tasks' in subtopic_data:
            for task in subtopic_data['tasks']:
                if task['id'] == task_id:
                    return {
                        'format': 'tasks',
                        'entry': task,
                        'task_data': task
                    }
        
        return None
    
    def _remove_task_from_any_format(self, subtopic_data: Dict[str, Any], task_id: str) -> Optional[Dict[str, Any]]:
        """
        Remove a task from either task_entries or tasks format.
        Returns the removed task data.
        """
        # Try task_entries format first
        if 'task_entries' in subtopic_data:
            for i, entry in enumerate(subtopic_data['task_entries']):
                if task_id in entry['task_ids']:
                    return subtopic_data['task_entries'].pop(i)
        
        # Try tasks format
        if 'tasks' in subtopic_data:
            for i, task in enumerate(subtopic_data['tasks']):
                if task['id'] == task_id:
                    removed_task = subtopic_data['tasks'].pop(i)
                    # Convert to task_entries format for consistency
                    return {
                        'task_ids': [removed_task['id']],
                        'representative_id': removed_task['id'],
                        'task_content': removed_task['task_content'],
                        'month_years': [removed_task.get('month_year', '')],
                        'combination_numbers': [removed_task.get('combination_number', '')],
                        'is_duplicate_group': False,
                        'duplicate_count': 1
                    }
        
        return None
    
    def _add_task_to_subtopic(self, subtopic_data: Dict[str, Any], task_entry: Dict[str, Any]):
        """
        Add a task entry to a subtopic, using task_entries format.
        """
        if 'task_entries' not in subtopic_data:
            subtopic_data['task_entries'] = []
        
        subtopic_data['task_entries'].append(task_entry)
    
    def rename_topic(self, topic_type: str, old_name: str, new_name: str):
        """Rename a main topic or subtopic."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return False
        
        if topic_type == "main":
            if old_name in self.current_classification['main_topics']:
                # Rename main topic
                topic_data = self.current_classification['main_topics'].pop(old_name)
                self.current_classification['main_topics'][new_name] = topic_data
                
                self.modifications_log.append({
                    'action': 'rename_main_topic',
                    'old_name': old_name,
                    'new_name': new_name,
                    'timestamp': datetime.now().isoformat()
                })
                
                print(f"✅ Renamed main topic '{old_name}' → '{new_name}'")
                return True
            else:
                print(f"❌ Main topic '{old_name}' not found")
                return False
        
        elif topic_type == "sub":
            # Find subtopic across all main topics
            for main_topic_name, main_topic_info in self.current_classification['main_topics'].items():
                if old_name in main_topic_info['subtopics']:
                    # Rename subtopic
                    subtopic_data = main_topic_info['subtopics'].pop(old_name)
                    main_topic_info['subtopics'][new_name] = subtopic_data
                    
                    self.modifications_log.append({
                        'action': 'rename_subtopic',
                        'main_topic': main_topic_name,
                        'old_name': old_name,
                        'new_name': new_name,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    print(f"✅ Renamed subtopic '{old_name}' → '{new_name}' in '{main_topic_name}'")
                    return True
            
            print(f"❌ Subtopic '{old_name}' not found")
            return False
    
    def create_new_main_topic(self, topic_name: str):
        """Create a new empty main topic."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return False
        
        if topic_name in self.current_classification['main_topics']:
            print(f"❌ Main topic '{topic_name}' already exists")
            return False
        
        # Create new main topic
        new_topic_id = len(self.current_classification['main_topics'])
        self.current_classification['main_topics'][topic_name] = {
            'topic_id': new_topic_id,
            'keywords': [],
            'total_tasks': 0,
            'unique_tasks': 0,
            'subtopics': {}
        }
        
        self.modifications_log.append({
            'action': 'create_main_topic',
            'topic_name': topic_name,
            'timestamp': datetime.now().isoformat()
        })
        
        print(f"✅ Created new main topic '{topic_name}'")
        return True
    
    def create_new_subtopic(self, main_topic_name: str, subtopic_name: str):
        """Create a new empty subtopic within a main topic."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return False
        
        if main_topic_name not in self.current_classification['main_topics']:
            print(f"❌ Main topic '{main_topic_name}' not found")
            return False
        
        main_topic = self.current_classification['main_topics'][main_topic_name]
        
        if subtopic_name in main_topic['subtopics']:
            print(f"❌ Subtopic '{subtopic_name}' already exists in '{main_topic_name}'")
            return False
        
        # Create new subtopic with support for both formats
        new_subtopic_id = len(main_topic['subtopics'])
        main_topic['subtopics'][subtopic_name] = {
            'subtopic_id': new_subtopic_id,
            'task_count': 0,
            'unique_task_count': 0,
            'task_entries': [],  # Use new format by default
            'keywords': [],
            'template_similarity': 1.0
        }
        
        self.modifications_log.append({
            'action': 'create_subtopic',
            'main_topic': main_topic_name,
            'subtopic_name': subtopic_name,
            'timestamp': datetime.now().isoformat()
        })
        
        print(f"✅ Created new subtopic '{subtopic_name}' in '{main_topic_name}'")
        return True
    
    def move_task(self, task_id: str, target_main_topic: str, target_subtopic: str):
        """Move a task from one subtopic to another, handling both task formats."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return False
        
        # Find current location of task
        current_main_topic = None
        current_subtopic = None
        task_entry = None
        
        for main_name, main_info in self.current_classification['main_topics'].items():
            for sub_name, sub_info in main_info['subtopics'].items():
                task_entry = self._remove_task_from_any_format(sub_info, task_id)
                if task_entry:
                    current_main_topic = main_name
                    current_subtopic = sub_name
                    break
            if task_entry:
                break
        
        if not task_entry:
            print(f"❌ Task '{task_id}' not found")
            return False
        
        # Check if target exists
        if target_main_topic not in self.current_classification['main_topics']:
            print(f"❌ Target main topic '{target_main_topic}' not found")
            return False
        
        if target_subtopic not in self.current_classification['main_topics'][target_main_topic]['subtopics']:
            print(f"❌ Target subtopic '{target_subtopic}' not found in '{target_main_topic}'")
            return False
        
        # Add to target location
        target_sub = self.current_classification['main_topics'][target_main_topic]['subtopics'][target_subtopic]
        self._add_task_to_subtopic(target_sub, task_entry)
        
        # Update counts
        self._update_subtopic_counts(self.current_classification['main_topics'][current_main_topic]['subtopics'][current_subtopic])
        self._update_subtopic_counts(target_sub)
        self._update_main_topic_counts(self.current_classification['main_topics'][current_main_topic])
        self._update_main_topic_counts(self.current_classification['main_topics'][target_main_topic])
        
        self.modifications_log.append({
            'action': 'move_task',
            'task_id': task_id,
            'from': f"{current_main_topic} > {current_subtopic}",
            'to': f"{target_main_topic} > {target_subtopic}",
            'is_duplicate_group': task_entry.get('is_duplicate_group', False),
            'duplicate_count': task_entry.get('duplicate_count', 1),
            'timestamp': datetime.now().isoformat()
        })
        
        if task_entry.get('is_duplicate_group', False):
            print(f"✅ Moved duplicate group '{task_id}' ({task_entry['duplicate_count']} tasks) from '{current_main_topic} > {current_subtopic}' to '{target_main_topic} > {target_subtopic}'")
        else:
            print(f"✅ Moved task '{task_id}' from '{current_main_topic} > {current_subtopic}' to '{target_main_topic} > {target_subtopic}'")
        
        return True
    
    def _update_subtopic_counts(self, subtopic_data: Dict[str, Any]):
        """Update task counts for a subtopic."""
        if 'task_entries' in subtopic_data:
            unique_count = len(subtopic_data['task_entries'])
            total_count = sum(entry.get('duplicate_count', 1) for entry in subtopic_data['task_entries'])
            subtopic_data['unique_task_count'] = unique_count
            subtopic_data['task_count'] = total_count
        elif 'tasks' in subtopic_data:
            count = len(subtopic_data['tasks'])
            subtopic_data['task_count'] = count
            subtopic_data['unique_task_count'] = count
        else:
            subtopic_data['task_count'] = 0
            subtopic_data['unique_task_count'] = 0
    
    def _update_main_topic_counts(self, main_topic_data: Dict[str, Any]):
        """Update task counts for a main topic."""
        total_tasks = 0
        unique_tasks = 0
        
        for subtopic_data in main_topic_data['subtopics'].values():
            total_tasks += subtopic_data.get('task_count', 0)
            unique_tasks += subtopic_data.get('unique_task_count', 0)
        
        main_topic_data['total_tasks'] = total_tasks
        main_topic_data['unique_tasks'] = unique_tasks
    
    def update_all_counts(self):
        """Update all task counts throughout the classification."""
        if not self.current_classification:
            return
        
        total_tasks = 0
        unique_tasks = 0
        
        for main_topic_data in self.current_classification['main_topics'].values():
            # Update subtopic counts
            for subtopic_data in main_topic_data['subtopics'].values():
                self._update_subtopic_counts(subtopic_data)
            
            # Update main topic counts
            self._update_main_topic_counts(main_topic_data)
            
            # Add to global totals
            total_tasks += main_topic_data.get('total_tasks', 0)
            unique_tasks += main_topic_data.get('unique_tasks', 0)
        
        # Update global counts
        self.current_classification['total_tasks'] = total_tasks
        self.current_classification['unique_tasks'] = unique_tasks
    
    def search_tasks(self, query: str, limit: int = 10):
        """Search for tasks containing specific text in both old and new formats."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return []
        
        results = []
        query_lower = query.lower()
        
        for main_name, main_info in self.current_classification['main_topics'].items():
            for sub_name, sub_info in main_info['subtopics'].items():
                # Handle task_entries format (new)
                if 'task_entries' in sub_info:
                    for entry in sub_info['task_entries']:
                        if query_lower in entry['task_content'].lower():
                            results.append({
                                'task_id': entry['representative_id'],
                                'main_topic': main_name,
                                'subtopic': sub_name,
                                'content': entry['task_content'][:100] + '...' if len(entry['task_content']) > 100 else entry['task_content'],
                                'is_duplicate_group': entry.get('is_duplicate_group', False),
                                'duplicate_count': entry.get('duplicate_count', 1),
                                'all_task_ids': entry.get('task_ids', [entry['representative_id']])
                            })

                            if len(results) >= limit:
                                break

                # Handle tasks format (for merged checkpoints - can coexist with task_entries)
                if 'tasks' in sub_info:
                    for task in sub_info['tasks']:
                        if query_lower in task['task_content'].lower():
                            results.append({
                                'task_id': task['id'],
                                'main_topic': main_name,
                                'subtopic': sub_name,
                                'content': task['task_content'][:100] + '...' if len(task['task_content']) > 100 else task['task_content'],
                                'is_duplicate_group': False,
                                'duplicate_count': 1,
                                'all_task_ids': [task['id']]
                            })

                            if len(results) >= limit:
                                break
                
                if len(results) >= limit:
                    break
            if len(results) >= limit:
                break
        
        print(f"\n🔍 Found {len(results)} tasks matching '{query}':")
        for i, result in enumerate(results, 1):
            task_info = f"[{result['task_id']}]"
            if result['is_duplicate_group']:
                task_info += f" (group of {result['duplicate_count']})"
            
            print(f"{i}. {task_info} {result['main_topic']} > {result['subtopic']}")
            print(f"   Content: {result['content']}")
            print()
        
        return results
    
    def save_checkpoint(self, checkpoint_name: str, description: str = ""):
        """Save current classification as a checkpoint."""
        if not self.current_classification:
            print("❌ No classification loaded")
            return False
        
        # Update all counts before saving to ensure data integrity
        self.update_all_counts()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_filename = f"tache_{self.tache_number}_{checkpoint_name}_{timestamp}.json"
        checkpoint_path = self.checkpoints_dir / checkpoint_filename
        
        # Create checkpoint data
        checkpoint_data = {
            'metadata': {
                'checkpoint_name': checkpoint_name,
                'description': description,
                'tache_number': self.tache_number,
                'created_timestamp': datetime.now().isoformat(),
                'total_modifications': len(self.modifications_log),
                'modifications_log': self.modifications_log
            },
            'classification': self.current_classification
        }
        
        # Save checkpoint
        with open(checkpoint_path, 'w', encoding='utf-8') as f:
            json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
        
        # Update latest checkpoint link
        latest_link = self.checkpoints_dir / f"tache_{self.tache_number}_latest.json"
        if latest_link.exists():
            latest_link.unlink()
        latest_link.symlink_to(checkpoint_filename)
        
        print(f"✅ Checkpoint saved: {checkpoint_path}")
        print(f"📝 Description: {description}")
        print(f"🔄 Total modifications: {len(self.modifications_log)}")
        
        return True
    
    def list_checkpoints(self, tache_number: Optional[int] = None):
        """List all available checkpoints."""
        pattern = f"tache_{tache_number or '*'}_*.json"
        checkpoints = list(self.checkpoints_dir.glob(pattern))
        
        if not checkpoints:
            print(f"📂 No checkpoints found for Tâche {tache_number or 'any'}")
            return []
        
        print(f"\n📂 Available Checkpoints for Tâche {tache_number or 'all'}:")
        print("=" * 60)
        
        checkpoint_info = []
        for checkpoint_path in sorted(checkpoints):
            if checkpoint_path.is_symlink():
                continue  # Skip symlinks (latest links)
            
            try:
                with open(checkpoint_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                metadata = data.get('metadata', {})
                
                # Handle different metadata formats
                checkpoint_name = metadata.get('checkpoint_name')
                if not checkpoint_name:
                    # Extract name from filename
                    filename = checkpoint_path.stem
                    parts = filename.split('_')
                    if len(parts) >= 3:
                        checkpoint_name = '_'.join(parts[2:-1])  # Remove 'tache', 'X' and timestamp
                    else:
                        checkpoint_name = filename
                
                # Get tache number from metadata or filename
                tache_num = metadata.get('tache_number') or metadata.get('task_number')
                if not tache_num:
                    # Extract from filename
                    filename = checkpoint_path.stem
                    if 'tache_1' in filename:
                        tache_num = 1
                    elif 'tache_2' in filename:
                        tache_num = 2
                    elif 'tache_3' in filename:
                        tache_num = 3
                    else:
                        tache_num = 0
                
                # Get creation timestamp
                created_timestamp = metadata.get('created_timestamp')
                if not created_timestamp:
                    created_timestamp = metadata.get('classification_date', 'Unknown')
                    if created_timestamp != 'Unknown' and len(created_timestamp) == 15:  # Format: 20250603_011300
                        # Convert to ISO format
                        date_part = created_timestamp[:8]  # 20250603
                        time_part = created_timestamp[9:]  # 011300
                        created_timestamp = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}T{time_part[:2]}:{time_part[2:4]}:{time_part[4:6]}"
                
                description = metadata.get('description', '')
                if not description:
                    # Generate description based on content
                    classification = data.get('classification', {})
                    method = classification.get('method', 'unknown')
                    total_tasks = classification.get('total_tasks', 0)
                    unique_tasks = classification.get('unique_tasks', total_tasks)
                    
                    if 'duplicate' in method:
                        description = f"Classification with duplicate detection - {unique_tasks} unique tasks from {total_tasks} total"
                    else:
                        description = f"Classification - {total_tasks} tasks"
                
                modifications = metadata.get('total_modifications', 0)
                if modifications == 0 and 'modifications_log' in metadata:
                    modifications = len(metadata['modifications_log'])
                
                checkpoint_info.append({
                    'path': checkpoint_path,
                    'name': checkpoint_name,
                    'description': description,
                    'created': created_timestamp,
                    'modifications': modifications,
                    'tache': tache_num
                })
            except Exception as e:
                print(f"⚠️  Error reading {checkpoint_path}: {e}")
        
        for i, info in enumerate(checkpoint_info, 1):
            created_date = info['created'][:10] if len(info['created']) >= 10 else info['created']
            print(f"{i}. {info['name']} (Tâche {info['tache']}) - {created_date}")
            print(f"   📝 {info['description']}")
            print(f"   🔄 {info['modifications']} modifications")
            print(f"   📁 {info['path'].name}")
            print()
        
        return checkpoint_info
    
    def load_checkpoint(self, checkpoint_path: Path):
        """Load a specific checkpoint."""
        try:
            with open(checkpoint_path, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            self.current_classification = checkpoint_data['classification']
            
            # Handle different metadata formats
            metadata = checkpoint_data.get('metadata', {})
            
            # Get tache number from either field
            self.tache_number = metadata.get('tache_number') or metadata.get('task_number')
            if not self.tache_number:
                # Extract from filename as fallback
                filename = checkpoint_path.stem
                if 'tache_1' in filename:
                    self.tache_number = 1
                elif 'tache_2' in filename:
                    self.tache_number = 2
                elif 'tache_3' in filename:
                    self.tache_number = 3
                else:
                    self.tache_number = 1  # Default fallback
            
            self.modifications_log = metadata.get('modifications_log', [])
            
            # Handle checkpoint name
            checkpoint_name = metadata.get('checkpoint_name', 'Unknown')
            if checkpoint_name == 'Unknown':
                # Extract from filename
                filename = checkpoint_path.stem
                parts = filename.split('_')
                if len(parts) >= 3:
                    checkpoint_name = '_'.join(parts[2:-1])  # Remove 'tache', 'X' and timestamp
                else:
                    checkpoint_name = filename
            
            # Handle creation timestamp
            created_timestamp = metadata.get('created_timestamp', 'Unknown')
            if created_timestamp == 'Unknown':
                created_timestamp = metadata.get('classification_date', 'Unknown')
            
            description = metadata.get('description', '')
            if not description:
                # Generate description based on content
                classification = checkpoint_data.get('classification', {})
                method = classification.get('method', 'unknown')
                total_tasks = classification.get('total_tasks', 0)
                unique_tasks = classification.get('unique_tasks', total_tasks)
                
                if 'duplicate' in method:
                    description = f"Classification with duplicate detection - {unique_tasks} unique tasks from {total_tasks} total"
                else:
                    description = f"Classification - {total_tasks} tasks"
            
            print(f"✅ Loaded checkpoint: {checkpoint_name}")
            print(f"📝 Description: {description}")
            print(f"📅 Created: {created_timestamp[:19] if len(created_timestamp) >= 19 else created_timestamp}")
            print(f"🔄 Previous modifications: {len(self.modifications_log)}")
            
            return True
        except Exception as e:
            print(f"❌ Error loading checkpoint: {e}")
            return False


def interactive_review_session():
    """Start an interactive review session."""
    classifier = HumanInTheLoopClassifier()
    
    print("🔄 HUMAN-IN-THE-LOOP CLASSIFICATION SYSTEM")
    print("=" * 50)
    
    while True:
        print("\n📋 MAIN MENU:")
        print("1. Load automatic classification")
        print("2. Load existing checkpoint")
        print("3. Display current classification")
        print("4. Rename topic/subtopic")
        print("5. Create new topic/subtopic")
        print("6. Move task")
        print("7. Search tasks")
        print("8. Save checkpoint")
        print("9. List checkpoints")
        print("0. Exit")
        
        choice = input("\n🎯 Choose an option (0-9): ").strip()
        
        if choice == "0":
            print("👋 Goodbye!")
            break
        
        elif choice == "1":
            tache_num = input("Enter Tâche number (1, 2, or 3): ").strip()
            try:
                classifier.load_automatic_classification(int(tache_num))
            except ValueError:
                print("❌ Invalid Tâche number")
        
        elif choice == "2":
            checkpoints = classifier.list_checkpoints()
            if checkpoints:
                try:
                    idx = int(input("Enter checkpoint number: ")) - 1
                    if 0 <= idx < len(checkpoints):
                        classifier.load_checkpoint(checkpoints[idx]['path'])
                    else:
                        print("❌ Invalid checkpoint number")
                except ValueError:
                    print("❌ Invalid number")
        
        elif choice == "3":
            classifier.display_current_classification()
        
        elif choice == "4":
            topic_type = input("Rename (main/sub): ").strip().lower()
            if topic_type in ["main", "sub"]:
                old_name = input("Current name: ").strip()
                new_name = input("New name: ").strip()
                classifier.rename_topic(topic_type, old_name, new_name)
            else:
                print("❌ Invalid type. Use 'main' or 'sub'")
        
        elif choice == "5":
            create_type = input("Create (main/sub): ").strip().lower()
            if create_type == "main":
                topic_name = input("New main topic name: ").strip()
                classifier.create_new_main_topic(topic_name)
            elif create_type == "sub":
                main_topic = input("Main topic name: ").strip()
                subtopic_name = input("New subtopic name: ").strip()
                classifier.create_new_subtopic(main_topic, subtopic_name)
            else:
                print("❌ Invalid type. Use 'main' or 'sub'")
        
        elif choice == "6":
            task_id = input("Task ID to move: ").strip()
            target_main = input("Target main topic: ").strip()
            target_sub = input("Target subtopic: ").strip()
            classifier.move_task(task_id, target_main, target_sub)
        
        elif choice == "7":
            query = input("Search query: ").strip()
            if query:
                classifier.search_tasks(query)
        
        elif choice == "8":
            checkpoint_name = input("Checkpoint name: ").strip()
            description = input("Description (optional): ").strip()
            classifier.save_checkpoint(checkpoint_name, description)
        
        elif choice == "9":
            tache_filter = input("Filter by Tâche (1,2,3 or press Enter for all): ").strip()
            try:
                tache_num = int(tache_filter) if tache_filter else None
                classifier.list_checkpoints(tache_num)
            except ValueError:
                classifier.list_checkpoints()
        
        else:
            print("❌ Invalid option")


if __name__ == '__main__':
    interactive_review_session() 