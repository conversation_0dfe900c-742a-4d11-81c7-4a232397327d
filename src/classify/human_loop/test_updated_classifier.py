#!/usr/bin/env python3
"""
Test script for the updated Human-in-the-Loop Classifier with deduplication support.

This script demonstrates all the key features of the updated classifier:
- Loading classifications with deduplication
- Searching through task_entries
- Moving tasks (including duplicate groups)
- Creating and loading checkpoints
- Displaying deduplication statistics
"""

from classifier import HumanInTheLoopClassifier
from pathlib import Path

def test_updated_classifier():
    """Test all functionality of the updated classifier."""
    
    print("🧪 TESTING UPDATED HUMAN-IN-THE-LOOP CLASSIFIER")
    print("=" * 60)
    
    # Initialize classifier
    classifier = HumanInTheLoopClassifier()
    
    # Test 1: Load clean classification with deduplication
    print("\n1️⃣  TESTING: Load classification with deduplication")
    success = classifier.load_automatic_classification(2)
    if not success:
        print("❌ Could not load Tâche 2 classification")
        return
    
    # Test 2: Display classification structure
    print("\n2️⃣  TESTING: Display classification structure")
    classifier.display_current_classification()
    
    # Test 3: Search functionality
    print("\n3️⃣  TESTING: Search functionality")
    print("Searching for 'voyage'...")
    results = classifier.search_tasks('voyage', 5)
    print(f"Found {len(results)} results")
    
    # Test 4: Create checkpoint
    print("\n4️⃣  TESTING: Save checkpoint")
    success = classifier.save_checkpoint('test_baseline', 'Test checkpoint for updated classifier')
    if success:
        print("✅ Checkpoint saved successfully")
    
    # Test 5: List checkpoints
    print("\n5️⃣  TESTING: List checkpoints")
    checkpoints = classifier.list_checkpoints(2)
    print(f"Found {len(checkpoints)} checkpoints for Tâche 2")
    
    # Test 6: Move a task (if we find one)
    print("\n6️⃣  TESTING: Move task functionality")
    if results:
        test_task_id = results[0]['task_id']
        print(f"Testing move of task: {test_task_id}")
        
        # Create a test subtopic first
        created = classifier.create_new_subtopic('manual_review', 'test_subtopic')
        if created:
            # Move the task
            moved = classifier.move_task(test_task_id, 'manual_review', 'test_subtopic')
            if moved:
                print("✅ Task moved successfully")
                # Move it back
                original_main = results[0]['main_topic']
                original_sub = results[0]['subtopic'] 
                classifier.move_task(test_task_id, original_main, original_sub)
                print("✅ Task moved back to original location")
            else:
                print("❌ Failed to move task")
        else:
            print("❌ Failed to create test subtopic")
    
    # Test 7: Count updating
    print("\n7️⃣  TESTING: Count updating")
    classifier.update_all_counts()
    print("✅ All counts updated successfully")
    
    # Test 8: Display summary statistics
    print("\n8️⃣  SUMMARY STATISTICS")
    classification = classifier.current_classification
    total_tasks = classification.get('total_tasks', 0)
    unique_tasks = classification.get('unique_tasks', total_tasks)
    duplicate_groups = 0
    total_duplicates = total_tasks - unique_tasks
    
    for main_topic in classification['main_topics'].values():
        for subtopic in main_topic['subtopics'].values():
            if 'task_entries' in subtopic:
                for entry in subtopic['task_entries']:
                    if entry.get('is_duplicate_group', False):
                        duplicate_groups += 1
    
    print(f"📊 Total tasks: {total_tasks}")
    print(f"📊 Unique tasks: {unique_tasks}")
    print(f"📊 Duplicates: {total_duplicates}")
    print(f"📊 Duplicate groups: {duplicate_groups}")
    print(f"📊 Deduplication efficiency: {(total_duplicates/total_tasks)*100:.1f}%")
    
    print("\n✅ ALL TESTS COMPLETED SUCCESSFULLY!")
    print("The updated human-in-the-loop classifier is fully functional with deduplication support.")

if __name__ == '__main__':
    test_updated_classifier() 