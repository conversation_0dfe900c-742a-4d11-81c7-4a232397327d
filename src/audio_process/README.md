# Audio Processing Module

A comprehensive Python module for combining multiple audio files into a single file while preserving timestamps and metadata, and then splitting them back to original files with preserved filenames.

## 🚀 Features

- **Combine Audio Files**: Merge multiple audio files from a folder into a single file
- **Preserve Metadata**: Record timestamps, durations, and original filenames
- **Split Back**: Reconstruct original files from combined audio using metadata
- **Timestamp Operations**: Query files by timestamp, extract specific segments
- **Format Support**: Works with MP3, WAV, M4A, FLAC, OGG, AAC formats
- **CLI Interface**: Command-line tools for easy usage
- **Preserve Filenames**: Original filenames are maintained throughout the process

## 📦 Installation

### Dependencies

```bash
pip install pydub librosa soundfile
```

### System Requirements

- **FFmpeg**: Required for handling various audio formats
  ```bash
  # Ubuntu/Debian
  sudo apt install ffmpeg
  
  # macOS
  brew install ffmpeg
  
  # Windows
  # Download from https://ffmpeg.org/download.html
  ```

## 🎵 Quick Start

### Basic Usage

```python
from audio_combiner import AudioCombiner

# Initialize
combiner = AudioCombiner()

# Combine all audio files in a folder
combined_path, metadata = combiner.combine_folder("path/to/audio/folder")

# Split back to original files
split_files = combiner.split_combined_audio(
    combined_path, 
    metadata, 
    "output/folder"
)
```

### Command Line Usage

```bash
# Combine audio files
python cli.py combine /path/to/audio/folder --output combined.wav

# Split combined audio back
python cli.py split combined.wav combined_metadata.json /output/folder

# Extract a time segment
python cli.py extract combined.wav metadata.json 10.5 25.3 segment.wav

# Get information about the combined audio
python cli.py info combined_metadata.json
```

## 📚 Detailed Usage

### 1. Combining Audio Files

```python
from audio_combiner import AudioCombiner

combiner = AudioCombiner()

# Basic combine
combined_path, metadata = combiner.combine_folder("audio_folder")

# With custom output and format
combined_path, metadata = combiner.combine_folder(
    folder_path="audio_folder",
    output_path="my_combined_audio.mp3",
    output_format="mp3"
)
```

**Output:**
- Combined audio file (e.g., `combined.wav`)
- Metadata JSON file (e.g., `combined_metadata.json`)

### 2. Metadata Structure

The metadata JSON contains:

```json
{
  "source_folder": "/path/to/original/folder",
  "combined_file": "combined.wav",
  "output_format": "wav",
  "total_files": 10,
  "total_duration": 125.67,
  "files": [
    {
      "filename": "audio1.mp3",
      "original_path": "/path/to/audio1.mp3",
      "start_time": 0.0,
      "end_time": 12.34,
      "duration": 12.34,
      "index": 0
    },
    // ... more files
  ]
}
```

### 3. Splitting Combined Audio

```python
# Split back to original files
split_files = combiner.split_combined_audio(
    combined_audio_path="combined.wav",
    metadata=metadata,
    output_folder="split_output",
    preserve_format=True  # Keep original formats
)

# Convert all to WAV
split_files = combiner.split_combined_audio(
    combined_audio_path="combined.wav",
    metadata=metadata,
    output_folder="split_output",
    preserve_format=False  # Convert to WAV
)
```

### 4. Timestamp Operations

```python
# Find which file contains a specific timestamp
file_info = combiner.get_file_info_by_timestamp(metadata, 45.2)
if file_info:
    print(f"Timestamp 45.2s is in: {file_info['filename']}")

# Extract a specific time segment
combiner.extract_segment_by_time(
    combined_audio_path="combined.wav",
    metadata=metadata,
    start_time=30.0,
    end_time=60.0,
    output_path="segment_30_60.wav"
)
```

### 5. Loading Metadata

```python
# Load metadata from file
metadata = combiner.load_metadata("combined_metadata.json")
```

## 🖥️ Command Line Interface

### Combine Command

```bash
python cli.py combine /path/to/audio/folder [options]

Options:
  --output, -o    Output file path (optional)
  --format, -f    Output format: wav, mp3, flac (default: wav)
```

### Split Command

```bash
python cli.py split combined.wav metadata.json /output/folder [options]

Options:
  --convert-to-wav    Convert all output files to WAV format
```

### Extract Command

```bash
python cli.py extract combined.wav metadata.json start_time end_time output.wav

Arguments:
  start_time    Start time in seconds (float)
  end_time      End time in seconds (float)
  output        Output file path
```

### Info Command

```bash
python cli.py info metadata.json [timestamp]

Arguments:
  timestamp     Optional: Show file info for specific timestamp
```

## 📋 Examples

Run the examples script to see various usage scenarios:

```bash
python examples.py
```

This will demonstrate:
- Basic combine and split operations
- Timestamp queries
- Segment extraction
- Metadata analysis

## 🔧 Advanced Features

### Custom Audio Processing

```python
# Get list of audio files (sorted)
audio_files = combiner.get_audio_files("folder_path")

# Get duration of a specific file
duration = combiner.get_audio_duration(Path("audio.mp3"))
```

### Error Handling

The module includes comprehensive error handling:
- File not found errors
- Invalid audio format errors
- Timestamp out of range errors
- Metadata corruption handling

## 📊 Supported Formats

| Format | Extension | Read | Write | Notes |
|--------|-----------|------|-------|-------|
| WAV    | .wav      | ✅   | ✅    | Lossless, recommended |
| MP3    | .mp3      | ✅   | ✅    | Requires FFmpeg |
| M4A    | .m4a      | ✅   | ✅    | Requires FFmpeg |
| FLAC   | .flac     | ✅   | ✅    | Lossless compression |
| OGG    | .ogg      | ✅   | ✅    | Open source format |
| AAC    | .aac      | ✅   | ✅    | Requires FFmpeg |

## 🚨 Important Notes

1. **Filename Preservation**: Original filenames are always preserved in metadata and restored during splitting
2. **Order Consistency**: Files are processed in alphabetical order by filename
3. **Format Conversion**: The module can convert between formats during combine/split operations
4. **Memory Usage**: Large audio collections may require significant memory
5. **FFmpeg Dependency**: Some formats require FFmpeg to be installed

## 🐛 Troubleshooting

### Common Issues

1. **"ffmpeg not found"**
   - Install FFmpeg system-wide
   - Ensure it's in your PATH

2. **"No supported audio files found"**
   - Check file extensions are supported
   - Verify files are not corrupted

3. **Memory errors with large files**
   - Process smaller batches
   - Use WAV format for better performance

4. **Timestamp misalignment**
   - Ensure metadata matches the combined audio file
   - Re-generate metadata if files were modified

## 📄 License

This module is part of the TCF-Canada project.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!
