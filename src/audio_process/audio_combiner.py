#!/usr/bin/env python3
"""
Audio Combiner Module

This module provides functionality to:
1. Combine multiple audio files from a folder into a single audio file
2. Record timestamps and metadata for each original file
3. Split the combined audio back into original files based on timestamps

Dependencies:
    pip install pydub librosa soundfile

Usage:
    from audio_combiner import AudioCombiner
    
    # Combine audio files
    combiner = AudioCombiner()
    combined_path, metadata = combiner.combine_folder("path/to/audio/folder")
    
    # Split back to original files
    combiner.split_combined_audio(combined_path, metadata, "output/folder")
"""

import os
import json
import librosa
import soundfile as sf
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from pydub import AudioSegment
from pydub.utils import which
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioCombiner:
    """
    A class to combine audio files and split them back while preserving metadata.
    """
    
    def __init__(self):
        """Initialize the AudioCombiner."""
        self.supported_formats = ['.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac']
        
        # Check if ffmpeg is available for pydub
        if not which("ffmpeg"):
            logger.warning("ffmpeg not found. Some audio formats may not be supported.")
    
    def get_audio_files(self, folder_path: str) -> List[Path]:
        """
        Get all supported audio files from a folder, sorted by name.
        
        Args:
            folder_path: Path to the folder containing audio files
            
        Returns:
            List of Path objects for audio files
        """
        folder = Path(folder_path)
        if not folder.exists():
            raise FileNotFoundError(f"Folder not found: {folder_path}")
        
        audio_files = []
        for file_path in folder.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                audio_files.append(file_path)
        
        # Sort by filename to ensure consistent order
        audio_files.sort(key=lambda x: x.name)
        
        if not audio_files:
            raise ValueError(f"No supported audio files found in {folder_path}")
        
        logger.info(f"Found {len(audio_files)} audio files")
        return audio_files
    
    def get_audio_duration(self, file_path: Path) -> float:
        """
        Get the duration of an audio file in seconds.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Duration in seconds
        """
        try:
            # Use librosa for accurate duration detection
            duration = librosa.get_duration(path=str(file_path))
            return duration
        except Exception as e:
            logger.warning(f"Could not get duration for {file_path} using librosa: {e}")
            try:
                # Fallback to pydub
                audio = AudioSegment.from_file(str(file_path))
                return len(audio) / 1000.0  # Convert ms to seconds
            except Exception as e2:
                logger.error(f"Could not get duration for {file_path}: {e2}")
                return 0.0
    
    def combine_folder(self, folder_path: str, output_path: Optional[str] = None, 
                      output_format: str = 'wav') -> Tuple[str, Dict]:
        """
        Combine all audio files in a folder into a single audio file.
        
        Args:
            folder_path: Path to folder containing audio files
            output_path: Output path for combined audio (optional)
            output_format: Output format ('wav', 'mp3', 'flac')
            
        Returns:
            Tuple of (combined_file_path, metadata_dict)
        """
        audio_files = self.get_audio_files(folder_path)
        
        # Set default output path
        if output_path is None:
            folder_name = Path(folder_path).name
            output_path = f"{folder_name}_combined.{output_format}"
        
        # Initialize metadata
        metadata = {
            'source_folder': str(Path(folder_path).absolute()),
            'combined_file': output_path,
            'output_format': output_format,
            'total_files': len(audio_files),
            'files': [],
            'total_duration': 0.0
        }
        
        # Combine audio files
        combined_audio = AudioSegment.empty()
        current_time = 0.0
        
        logger.info(f"Combining {len(audio_files)} audio files...")
        
        for i, file_path in enumerate(audio_files):
            logger.info(f"Processing {i+1}/{len(audio_files)}: {file_path.name}")
            
            try:
                # Load audio file
                audio_segment = AudioSegment.from_file(str(file_path))
                duration = len(audio_segment) / 1000.0  # Convert to seconds
                
                # Add to metadata
                file_metadata = {
                    'filename': file_path.name,
                    'original_path': str(file_path.absolute()),
                    'start_time': current_time,
                    'end_time': current_time + duration,
                    'duration': duration,
                    'index': i
                }
                metadata['files'].append(file_metadata)
                
                # Add to combined audio
                combined_audio += audio_segment
                current_time += duration
                
            except Exception as e:
                logger.error(f"Error processing {file_path}: {e}")
                continue
        
        metadata['total_duration'] = current_time
        
        # Export combined audio
        logger.info(f"Exporting combined audio to: {output_path}")
        combined_audio.export(output_path, format=output_format)
        
        # Save metadata
        metadata_path = output_path.replace(f'.{output_format}', '_metadata.json')
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"✅ Combined {len(audio_files)} files into {output_path}")
        logger.info(f"📊 Total duration: {current_time:.2f} seconds")
        logger.info(f"📄 Metadata saved to: {metadata_path}")
        
        return output_path, metadata

    def split_combined_audio(self, combined_audio_path: str, metadata: Dict,
                           output_folder: str, preserve_format: bool = True) -> List[str]:
        """
        Split a combined audio file back into original files based on metadata.

        Args:
            combined_audio_path: Path to the combined audio file
            metadata: Metadata dictionary from combine_folder()
            output_folder: Output folder for split files
            preserve_format: Whether to preserve original file formats

        Returns:
            List of paths to the split audio files
        """
        if not os.path.exists(combined_audio_path):
            raise FileNotFoundError(f"Combined audio file not found: {combined_audio_path}")

        # Create output folder
        output_path = Path(output_folder)
        output_path.mkdir(parents=True, exist_ok=True)

        # Load combined audio
        logger.info(f"Loading combined audio: {combined_audio_path}")
        combined_audio = AudioSegment.from_file(combined_audio_path)

        split_files = []

        logger.info(f"Splitting into {len(metadata['files'])} files...")

        for file_info in metadata['files']:
            filename = file_info['filename']
            start_time = file_info['start_time'] * 1000  # Convert to milliseconds
            end_time = file_info['end_time'] * 1000      # Convert to milliseconds

            logger.info(f"Extracting: {filename} ({start_time/1000:.2f}s - {end_time/1000:.2f}s)")

            # Extract audio segment
            audio_segment = combined_audio[start_time:end_time]

            # Determine output format
            if preserve_format:
                original_ext = Path(filename).suffix.lower()
                output_format = original_ext[1:]  # Remove the dot
            else:
                output_format = 'wav'
                filename = Path(filename).stem + '.wav'

            # Save split file
            output_file_path = output_path / filename
            audio_segment.export(str(output_file_path), format=output_format)
            split_files.append(str(output_file_path))

        logger.info(f"✅ Split {len(split_files)} files to: {output_folder}")
        return split_files

    def load_metadata(self, metadata_path: str) -> Dict:
        """
        Load metadata from a JSON file.

        Args:
            metadata_path: Path to the metadata JSON file

        Returns:
            Metadata dictionary
        """
        if not os.path.exists(metadata_path):
            raise FileNotFoundError(f"Metadata file not found: {metadata_path}")

        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)

        return metadata

    def get_file_info_by_timestamp(self, metadata: Dict, timestamp: float) -> Optional[Dict]:
        """
        Get file information for a specific timestamp.

        Args:
            metadata: Metadata dictionary
            timestamp: Timestamp in seconds

        Returns:
            File information dictionary or None if not found
        """
        for file_info in metadata['files']:
            if file_info['start_time'] <= timestamp <= file_info['end_time']:
                return file_info
        return None

    def extract_segment_by_time(self, combined_audio_path: str, metadata: Dict,
                               start_time: float, end_time: float,
                               output_path: str, output_format: str = 'wav') -> str:
        """
        Extract a specific time segment from the combined audio.

        Args:
            combined_audio_path: Path to the combined audio file
            metadata: Metadata dictionary
            start_time: Start time in seconds
            end_time: End time in seconds
            output_path: Output file path
            output_format: Output format

        Returns:
            Path to the extracted segment
        """
        if not os.path.exists(combined_audio_path):
            raise FileNotFoundError(f"Combined audio file not found: {combined_audio_path}")

        if start_time >= end_time:
            raise ValueError("Start time must be less than end time")

        if end_time > metadata['total_duration']:
            raise ValueError(f"End time ({end_time}s) exceeds total duration ({metadata['total_duration']}s)")

        # Load combined audio
        combined_audio = AudioSegment.from_file(combined_audio_path)

        # Extract segment (convert to milliseconds)
        start_ms = start_time * 1000
        end_ms = end_time * 1000

        audio_segment = combined_audio[start_ms:end_ms]

        # Export segment
        audio_segment.export(output_path, format=output_format)

        logger.info(f"✅ Extracted segment ({start_time:.2f}s - {end_time:.2f}s) to: {output_path}")
        return output_path
