#!/usr/bin/env python3
"""
Command Line Interface for Audio Processing

Usage examples:
    # Combine all audio files in a folder
    python cli.py combine /path/to/audio/folder --output combined.wav
    
    # Split combined audio back to original files
    python cli.py split combined.wav combined_metadata.json /path/to/output/folder
    
    # Extract a specific time segment
    python cli.py extract combined.wav combined_metadata.json 10.5 25.3 segment.wav
    
    # Get info about a timestamp
    python cli.py info combined_metadata.json 15.2
"""

import argparse
import sys
import json
from pathlib import Path
from audio_combiner import AudioCombiner

def combine_command(args):
    """Handle the combine command."""
    combiner = AudioCombiner()
    
    try:
        output_path = args.output if args.output else None
        output_format = args.format if args.format else 'wav'
        
        combined_path, metadata = combiner.combine_folder(
            args.folder, 
            output_path, 
            output_format
        )
        
        print(f"✅ Successfully combined audio files!")
        print(f"📁 Combined file: {combined_path}")
        print(f"📄 Metadata: {combined_path.replace(f'.{output_format}', '_metadata.json')}")
        print(f"📊 Total files: {metadata['total_files']}")
        print(f"⏱️ Total duration: {metadata['total_duration']:.2f} seconds")
        
    except Exception as e:
        print(f"❌ Error combining audio: {e}")
        sys.exit(1)

def split_command(args):
    """Handle the split command."""
    combiner = AudioCombiner()
    
    try:
        # Load metadata
        if args.metadata.endswith('.json'):
            metadata = combiner.load_metadata(args.metadata)
        else:
            # Try to find metadata file based on combined audio name
            metadata_path = args.combined_audio.replace('.wav', '_metadata.json').replace('.mp3', '_metadata.json')
            metadata = combiner.load_metadata(metadata_path)
        
        split_files = combiner.split_combined_audio(
            args.combined_audio,
            metadata,
            args.output_folder,
            preserve_format=not args.convert_to_wav
        )
        
        print(f"✅ Successfully split audio into {len(split_files)} files!")
        print(f"📁 Output folder: {args.output_folder}")
        
        # Show first few files
        for i, file_path in enumerate(split_files[:5]):
            print(f"   📄 {Path(file_path).name}")
        
        if len(split_files) > 5:
            print(f"   ... and {len(split_files) - 5} more files")
            
    except Exception as e:
        print(f"❌ Error splitting audio: {e}")
        sys.exit(1)

def extract_command(args):
    """Handle the extract command."""
    combiner = AudioCombiner()
    
    try:
        # Load metadata
        metadata = combiner.load_metadata(args.metadata)
        
        output_format = Path(args.output).suffix[1:] if Path(args.output).suffix else 'wav'
        
        extracted_path = combiner.extract_segment_by_time(
            args.combined_audio,
            metadata,
            args.start_time,
            args.end_time,
            args.output,
            output_format
        )
        
        duration = args.end_time - args.start_time
        print(f"✅ Successfully extracted segment!")
        print(f"📁 Output file: {extracted_path}")
        print(f"⏱️ Duration: {duration:.2f} seconds ({args.start_time:.2f}s - {args.end_time:.2f}s)")
        
    except Exception as e:
        print(f"❌ Error extracting segment: {e}")
        sys.exit(1)

def info_command(args):
    """Handle the info command."""
    combiner = AudioCombiner()
    
    try:
        # Load metadata
        metadata = combiner.load_metadata(args.metadata)
        
        print(f"📊 Combined Audio Information:")
        print(f"   📁 Source folder: {metadata['source_folder']}")
        print(f"   📄 Combined file: {metadata['combined_file']}")
        print(f"   🔢 Total files: {metadata['total_files']}")
        print(f"   ⏱️ Total duration: {metadata['total_duration']:.2f} seconds")
        print(f"   📝 Format: {metadata['output_format']}")
        
        if args.timestamp is not None:
            file_info = combiner.get_file_info_by_timestamp(metadata, args.timestamp)
            if file_info:
                print(f"\n🎯 File at timestamp {args.timestamp:.2f}s:")
                print(f"   📄 Filename: {file_info['filename']}")
                print(f"   ⏱️ Time range: {file_info['start_time']:.2f}s - {file_info['end_time']:.2f}s")
                print(f"   📏 Duration: {file_info['duration']:.2f}s")
                print(f"   📍 Index: {file_info['index']}")
            else:
                print(f"\n❌ No file found at timestamp {args.timestamp:.2f}s")
        else:
            print(f"\n📋 File List:")
            for i, file_info in enumerate(metadata['files'][:10]):  # Show first 10
                print(f"   {i+1:2d}. {file_info['filename']} ({file_info['start_time']:.2f}s - {file_info['end_time']:.2f}s)")
            
            if len(metadata['files']) > 10:
                print(f"   ... and {len(metadata['files']) - 10} more files")
        
    except Exception as e:
        print(f"❌ Error reading info: {e}")
        sys.exit(1)

def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Audio Processing CLI - Combine and split audio files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Combine command
    combine_parser = subparsers.add_parser('combine', help='Combine audio files from a folder')
    combine_parser.add_argument('folder', help='Folder containing audio files')
    combine_parser.add_argument('--output', '-o', help='Output file path (optional)')
    combine_parser.add_argument('--format', '-f', choices=['wav', 'mp3', 'flac'], 
                               default='wav', help='Output format (default: wav)')
    
    # Split command
    split_parser = subparsers.add_parser('split', help='Split combined audio back to original files')
    split_parser.add_argument('combined_audio', help='Path to combined audio file')
    split_parser.add_argument('metadata', help='Path to metadata JSON file')
    split_parser.add_argument('output_folder', help='Output folder for split files')
    split_parser.add_argument('--convert-to-wav', action='store_true', 
                             help='Convert all output files to WAV format')
    
    # Extract command
    extract_parser = subparsers.add_parser('extract', help='Extract a time segment from combined audio')
    extract_parser.add_argument('combined_audio', help='Path to combined audio file')
    extract_parser.add_argument('metadata', help='Path to metadata JSON file')
    extract_parser.add_argument('start_time', type=float, help='Start time in seconds')
    extract_parser.add_argument('end_time', type=float, help='End time in seconds')
    extract_parser.add_argument('output', help='Output file path')
    
    # Info command
    info_parser = subparsers.add_parser('info', help='Show information about combined audio')
    info_parser.add_argument('metadata', help='Path to metadata JSON file')
    info_parser.add_argument('timestamp', type=float, nargs='?', 
                            help='Show file info for specific timestamp (optional)')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Execute command
    if args.command == 'combine':
        combine_command(args)
    elif args.command == 'split':
        split_command(args)
    elif args.command == 'extract':
        extract_command(args)
    elif args.command == 'info':
        info_command(args)

if __name__ == '__main__':
    main()
