#!/usr/bin/env python3
"""
Simple runner script for converting writing exercises to PDF.
This provides a basic example of how to use the WritingToPDFConverter.
"""

from writing_to_pdf import WritingToPDFConverter


def main():
    """Run the conversion with default settings."""
    print("Converting writing exercises to PDF...")
    print("=" * 50)
    
    # Initialize converter with default paths
    converter = WritingToPDFConverter(
        input_dir="data/scraped/scraped_writing",
        output_dir="output_pdfs"
    )
    
    # Convert all individual files
    print("\n1. Converting individual JSON files to PDF...")
    individual_pdfs = converter.convert_all()
    
    # Create a combined PDF
    print("\n2. Creating combined PDF with all exercises...")
    combined_pdf = converter.create_combined_pdf()
    
    print("\n" + "=" * 50)
    print("Conversion Summary:")
    print(f"- Individual PDFs created: {len(individual_pdfs)}")
    if combined_pdf:
        print(f"- Combined PDF created: {combined_pdf}")
    print(f"- Output directory: {converter.output_dir}")
    print("\nConversion completed successfully!")


if __name__ == "__main__":
    main() 