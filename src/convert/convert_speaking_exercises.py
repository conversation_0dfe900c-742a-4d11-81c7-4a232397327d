#!/usr/bin/env python3
"""
Simple runner script for converting Task 2 speaking exercises to PDF.
This provides a basic example of how to use the SpeakingToPDFConverter.
"""

from speaking_to_pdf import SpeakingToPDFConverter


def main():
    """Run the conversion with default settings."""
    print("Converting Task 2 speaking exercises to PDF...")
    print("=" * 50)
    
    # Initialize converter with default paths
    converter = SpeakingToPDFConverter(
        input_dir="data/scraped/scraped_speaking",
        output_dir="output_pdfs"
    )
    
    # Convert all individual files
    print("\n1. Converting individual Task 2 JSON files to PDF...")
    individual_pdfs = converter.convert_all()
    
    # Create a combined PDF
    print("\n2. Creating combined PDF with all Task 2 exercises...")
    combined_pdf = converter.create_combined_pdf()
    
    print("\n" + "=" * 50)
    print("Conversion Summary:")
    print(f"- Individual Task 2 PDFs created: {len(individual_pdfs)}")
    if combined_pdf:
        print(f"- Combined Task 2 PDF created: {combined_pdf}")
    print(f"- Output directory: {converter.output_dir}")
    print("\nConversion completed successfully!")


if __name__ == "__main__":
    main() 