#!/usr/bin/env python3
"""
Script to convert speaking test JSON files to PDF format.
Handles both Task 2 (scenario-based) and Task 3 (topic-based) speaking tests.
Excludes exemplary answers and creates well-formatted PDFs.
"""

import json
import os
from pathlib import Path
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, KeepTogether
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
from reportlab.lib.colors import lightgrey
import argparse
from typing import Dict, List, Any

class SpeakingToPDFConverter:
    """Converts speaking test JSON files to PDF format."""
    
    def __init__(self, input_dir: str, output_dir: str = "output_pdfs"):
        """
        Initialize the converter.
        
        Args:
            input_dir: Directory containing JSON files
            output_dir: Directory to save PDF files
        """
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # Set up styles
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Set up custom paragraph styles for the PDF."""
        # Title style
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Subtitle style
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            spaceBefore=20,
            alignment=TA_LEFT,
            fontName='Helvetica-Bold'
        ))
        
        # Task style
        self.styles.add(ParagraphStyle(
            name='TaskStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=15,
            spaceBefore=10,
            alignment=TA_JUSTIFY,
            fontName='Helvetica'
        ))
        
        # Task number style
        self.styles.add(ParagraphStyle(
            name='TaskNumber',
            parent=self.styles['Normal'],
            fontSize=13,
            spaceAfter=8,
            spaceBefore=15,
            alignment=TA_LEFT,
            fontName='Helvetica-Bold'
        ))
        
        # Scenario style
        self.styles.add(ParagraphStyle(
            name='ScenarioStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=10,
            spaceBefore=10,
            alignment=TA_JUSTIFY,
            fontName='Helvetica',
            leftIndent=20
        ))
        
        # Question style
        self.styles.add(ParagraphStyle(
            name='QuestionStyle',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=10,
            spaceBefore=10,
            alignment=TA_JUSTIFY,
            fontName='Helvetica',
            leftIndent=20
        ))
    
    def _add_watermark(self, canvas_obj, doc):
        """
        Add watermark to each page.
        
        Args:
            canvas_obj: ReportLab canvas object
            doc: Document object
        """
        # Save the current graphics state
        canvas_obj.saveState()
        
        # Set watermark properties
        watermark_text = "chez-tcfcanada.com"
        
        # Position watermark in the center of the page
        page_width, page_height = A4
        x = page_width / 2
        y = page_height / 2
        
        # Set font and color for watermark
        canvas_obj.setFont("Helvetica-Bold", 48)
        canvas_obj.setFillColor(lightgrey)
        canvas_obj.setStrokeColor(lightgrey)
        
        # Rotate and draw the watermark text
        canvas_obj.rotate(45)  # 45-degree rotation
        
        # Adjust position for rotation
        rotated_x = (x + y) / 1.414  # Adjust for 45-degree rotation
        rotated_y = (y - x) / 1.414
        
        # Draw the watermark text
        canvas_obj.drawCentredString(rotated_x, rotated_y, watermark_text)
        
        # Restore the graphics state
        canvas_obj.restoreState()
    
    def _format_text_for_pdf(self, text: str) -> str:
        """
        Format text for PDF by handling special characters and formatting.
        
        Args:
            text: Raw text to format
            
        Returns:
            Formatted text suitable for PDF
        """
        # Replace common problematic characters
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('\n', '<br/>')
        
        return text
    
    def _process_task2_file(self, json_file: Path) -> List[Any]:
        """
        Process a Task 2 (scenario-based) JSON file.
        
        Args:
            json_file: Path to the JSON file
            
        Returns:
            List of story elements for the PDF
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"Error reading {json_file}: {e}")
            return []
        
        story = []
        
        # Add title
        month_year = data.get('month_year', json_file.stem)
        title = f"Exercices d'Expression Orale - Tâche 2 - {month_year.title()}"
        story.append(Paragraph(title, self.styles['CustomTitle']))
        story.append(Spacer(1, 20))
        
        # Process parties
        parties = data.get('parties', [])
        
        for partie in parties:
            partie_num = partie.get('partie_number', 'N/A')
            story.append(Paragraph(f"Partie {partie_num}", self.styles['CustomSubtitle']))
            
            taches = partie.get('taches', [])
            
            for i, tache in enumerate(taches, 1):
                # Create a list to hold all content for this task
                task_content = []
                
                # Add task number
                task_content.append(Paragraph(f"Scénario {i}:", self.styles['TaskNumber']))
                
                # Add scenario
                scenario = tache.get('scenario', '')
                if scenario:
                    formatted_scenario = self._format_text_for_pdf(scenario)
                    task_content.append(Paragraph(formatted_scenario, self.styles['ScenarioStyle']))
                
                # Add example questions (exemplary answers)
                example_questions = tache.get('example_questions', [])
                if example_questions:
                    task_content.append(Paragraph("Exemples de questions:", self.styles['TaskNumber']))
                    for j, question in enumerate(example_questions, 1):
                        formatted_question = self._format_text_for_pdf(f"{j}. {question}")
                        task_content.append(Paragraph(formatted_question, self.styles['QuestionStyle']))
                
                # Wrap the entire task in KeepTogether to ensure it stays on the same page
                story.append(KeepTogether(task_content))
                
                # Add space between tasks
                story.append(Spacer(1, 15))
            
            # Add space between parties
            story.append(Spacer(1, 25))
        
        return story
    
    def _process_task3_file(self, json_file: Path) -> List[Any]:
        """
        Process a Task 3 (topic-based) JSON file.
        
        Args:
            json_file: Path to the JSON file
            
        Returns:
            List of story elements for the PDF
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"Error reading {json_file}: {e}")
            return []
        
        story = []
        
        # Add title
        topic = data.get('topic', json_file.stem)
        title = f"Exercices d'Expression Orale - Tâche 3 - {topic}"
        story.append(Paragraph(title, self.styles['CustomTitle']))
        story.append(Spacer(1, 20))
        
        # Process tasks
        taches = data.get('taches', [])
        
        for tache in taches:
            task_num = tache.get('task_number', 'N/A')
            question = tache.get('question', '')
            word_count = tache.get('word_count', '')
            
            # Create a list to hold all content for this task
            task_content = []
            
            # Add task number
            task_content.append(Paragraph(f"Question {task_num}:", self.styles['TaskNumber']))
            
            # Add question (exclude exemplary_answer as requested)
            if question:
                formatted_question = self._format_text_for_pdf(question)
                task_content.append(Paragraph(formatted_question, self.styles['QuestionStyle']))
            
            # Add word count if available
            if word_count:
                task_content.append(Paragraph(f"Nombre de mots suggéré: {word_count}", self.styles['TaskStyle']))
            
            # Wrap the entire task in KeepTogether to ensure it stays on the same page
            story.append(KeepTogether(task_content))
            
            # Add space between tasks
            story.append(Spacer(1, 20))
        
        return story
    
    def convert_file(self, json_file: Path) -> str:
        """
        Convert a single Task 2 JSON file to PDF.
        
        Args:
            json_file: Path to the JSON file
            
        Returns:
            Path to the generated PDF file
        """
        # Create output filename
        pdf_filename = f"{json_file.stem}_task2_speaking.pdf"
        pdf_path = self.output_dir / pdf_filename
        
        # Create PDF document
        doc = SimpleDocTemplate(
            str(pdf_path),
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        # Process as Task 2 file
        story = self._process_task2_file(json_file)
        
        if not story:
            print(f"No content found in {json_file}")
            return ""
        
        try:
            # Build PDF with watermark
            doc.build(story, onFirstPage=self._add_watermark, onLaterPages=self._add_watermark)
            print(f"Successfully converted {json_file.name} to {pdf_filename}")
            return str(pdf_path)
        except Exception as e:
            print(f"Error creating PDF for {json_file.name}: {e}")
            return ""
    
    def convert_all(self) -> List[str]:
        """
        Convert all Task 2 JSON files in the input directory to PDF.
        
        Returns:
            List of paths to generated PDF files
        """
        # Only look for files in La_tache_2 directory
        task2_dir = self.input_dir / "La_tache_2"
        if not task2_dir.exists():
            print(f"Task 2 directory not found: {task2_dir}")
            return []
        
        json_files = list(task2_dir.glob("*.json"))
        
        if not json_files:
            print(f"No JSON files found in {task2_dir}")
            return []
        
        print(f"Found {len(json_files)} Task 2 JSON files to convert")
        generated_pdfs = []
        
        for json_file in json_files:
            pdf_path = self.convert_file(json_file)
            if pdf_path:
                generated_pdfs.append(pdf_path)
        
        print(f"\nConversion completed! Generated {len(generated_pdfs)} PDF files in {self.output_dir}")
        return generated_pdfs
    
    def create_combined_pdf(self, output_filename: str = "all_speaking_task2_exercises.pdf") -> str:
        """
        Create a single combined PDF with all Task 2 exercises.
        
        Args:
            output_filename: Name of the combined PDF file
            
        Returns:
            Path to the combined PDF file
        """
        # Only look for files in La_tache_2 directory
        task2_dir = self.input_dir / "La_tache_2"
        if not task2_dir.exists():
            print(f"Task 2 directory not found: {task2_dir}")
            return ""
        
        json_files = sorted(list(task2_dir.glob("*.json")))
        
        if not json_files:
            print(f"No JSON files found in {task2_dir}")
            return ""
        
        combined_pdf_path = self.output_dir / output_filename
        
        # Create combined PDF document
        doc = SimpleDocTemplate(
            str(combined_pdf_path),
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        
        story = []
        
        # Add main title
        story.append(Paragraph("Exercices d'Expression Orale - Tâche 2 - Collection Complète", self.styles['CustomTitle']))
        story.append(Spacer(1, 30))
        
        # Process each Task 2 file
        for i, json_file in enumerate(json_files):
            if i > 0:
                story.append(PageBreak())
            
            file_story = self._process_task2_file(json_file)
            story.extend(file_story)
        
        try:
            # Build PDF with watermark
            doc.build(story, onFirstPage=self._add_watermark, onLaterPages=self._add_watermark)
            print(f"Successfully created combined PDF: {output_filename}")
            return str(combined_pdf_path)
        except Exception as e:
            print(f"Error creating combined PDF: {e}")
            return ""


def main():
    """Main function to run the converter."""
    parser = argparse.ArgumentParser(description="Convert speaking test JSON files to PDF")
    parser.add_argument(
        "--input-dir", 
        default="data/scraped/scraped_speaking",
        help="Directory containing JSON files (default: data/scraped/scraped_speaking)"
    )
    parser.add_argument(
        "--output-dir",
        default="output_pdfs",
        help="Directory to save PDF files (default: output_pdfs)"
    )
    parser.add_argument(
        "--combined",
        action="store_true",
        help="Create a single combined PDF with all exercises"
    )
    parser.add_argument(
        "--individual",
        action="store_true",
        default=True,
        help="Create individual PDF files for each JSON file (default: True)"
    )
    
    args = parser.parse_args()
    
    # Create converter
    converter = SpeakingToPDFConverter(args.input_dir, args.output_dir)
    
    if args.individual:
        # Convert individual files
        converter.convert_all()
    
    if args.combined:
        # Create combined PDF
        converter.create_combined_pdf()


if __name__ == "__main__":
    main() 