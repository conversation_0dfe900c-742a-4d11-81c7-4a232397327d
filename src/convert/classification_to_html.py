#!/usr/bin/env python3
"""
Convert TCF classification JSON to HTML templates for Google Docs.

This script generates HTML templates for each subtopic in the classification,
formatted for Google Docs compatibility with proper page breaks.

Usage:
    python3 classification_to_html.py <classification_json> [output_html]
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any


def load_classification(file_path: str) -> Dict[str, Any]:
    """Load classification JSON file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def clean_subtopic_name(name: str) -> str:
    """Clean and format subtopic name for display."""
    # Remove prefixes and convert to title case
    name = name.replace('recommendation_', '').replace('description_', '').replace('sharing_', '').replace('looking_for_', '')
    name = name.replace('_', ' ').title()
    return name


def clean_main_topic_name(name: str) -> str:
    """Clean and format main topic name for display."""
    return name.replace('_', ' ').title()


def generate_template_for_subtopic(subtopic_name: str, main_topic_name: str) -> str:
    """Generate a template structure for a specific subtopic."""
    
    # Common greeting and closing for all templates
    base_template = """[GREETING]<br><br>

[INTRODUCTION - Context and purpose]<br><br>"""
    
    base_closing = """<br>[PERSONAL TOUCH/OPINION]<br><br>

[CLOSING]<br><br>

[SIGNATURE]"""
    
    # Generate specific content based on subtopic type
    if 'recommendation' in main_topic_name.lower():
        if 'sport' in subtopic_name.lower():
            content = """[PROPOSAL - Suggest sports activity]<br>
- Date/Time: [SPECIFIC SCHEDULE]<br>
- Location: [SPECIFIC PLACE]<br>
- Activity: [SPECIFIC SPORT/EXERCISE]<br>
- Details: [DURATION/EQUIPMENT/COST]<br><br>

[BENEFITS AND MOTIVATION]<br>
- Health benefits: [PHYSICAL/MENTAL ADVANTAGES]<br>
- Social aspects: [FRIENDSHIP/TEAMWORK]<br>
- Practical aspects: [CONVENIENCE/ACCESSIBILITY]<br><br>

[ENCOURAGEMENT AND NEXT STEPS]"""
        
        elif 'career' in subtopic_name.lower():
            content = """[PROFESSION PRESENTATION]<br>
- Job title: [YOUR PROFESSION]<br>
- Main responsibilities: [KEY DUTIES AND TASKS]<br>
- Work environment: [WORKPLACE DESCRIPTION]<br><br>

[PROFESSIONAL ADVANTAGES]<br>
- Career prospects: [GROWTH OPPORTUNITIES]<br>
- Personal satisfaction: [FULFILLMENT ASPECTS]<br>
- Practical benefits: [SALARY/SCHEDULE/STABILITY]<br><br>

[QUALIFICATIONS AND PATH]<br>
- Required education: [DEGREES/CERTIFICATIONS]<br>
- Essential skills: [TECHNICAL/SOFT SKILLS]<br>
- Experience needed: [ENTRY REQUIREMENTS]<br><br>

[ADVICE FOR STUDENTS]"""
        
        elif 'event' in subtopic_name.lower() or 'film' in subtopic_name.lower() or 'library' in subtopic_name.lower():
            content = """[EVENT DETAILS]<br>
- Event name: [SPECIFIC EVENT]<br>
- Date and time: [SCHEDULE]<br>
- Location: [VENUE AND ADDRESS]<br>
- Cost: [TICKET PRICES/FREE]<br><br>

[EVENT DESCRIPTION]<br>
- What to expect: [ACTIVITIES/PROGRAM]<br>
- Why it's interesting: [UNIQUE ASPECTS]<br>
- Who should attend: [TARGET AUDIENCE]<br><br>

[PRACTICAL INFORMATION]<br>
- How to get there: [TRANSPORTATION]<br>
- What to bring: [PREPARATION NEEDED]<br>
- Registration: [BOOKING REQUIREMENTS]<br><br>

[INVITATION AND MOTIVATION]"""
        
        else:
            content = """[RECOMMENDATION DETAILS]<br>
- What you're recommending: [SPECIFIC ITEM/SERVICE]<br>
- Where to find it: [LOCATION/CONTACT]<br>
- Key features: [MAIN CHARACTERISTICS]<br><br>

[ADVANTAGES AND BENEFITS]<br>
- Quality aspects: [WHAT MAKES IT GOOD]<br>
- Practical benefits: [CONVENIENCE/VALUE]<br>
- Personal experience: [YOUR EXPERIENCE]<br><br>

[ADDITIONAL INFORMATION]<br>
- Cost/pricing: [BUDGET CONSIDERATIONS]<br>
- Availability: [WHEN/HOW TO ACCESS]<br>
- Alternatives: [OTHER OPTIONS]"""
    
    elif 'description' in main_topic_name.lower():
        if 'city' in subtopic_name.lower():
            content = """[CITY OVERVIEW]<br>
- City name and location: [GEOGRAPHIC DETAILS]<br>
- Size and population: [BASIC STATISTICS]<br>
- General character: [ATMOSPHERE/VIBE]<br><br>

[MAIN ATTRACTIONS]<br>
- Historical sites: [MONUMENTS/MUSEUMS/HERITAGE]<br>
- Entertainment: [RESTAURANTS/SHOPPING/NIGHTLIFE]<br>
- Natural features: [PARKS/LANDSCAPES/VIEWS]<br>
- Cultural activities: [THEATERS/FESTIVALS/EVENTS]<br><br>

[PRACTICAL INFORMATION]<br>
- Transportation: [HOW TO GET AROUND]<br>
- Accommodation: [WHERE TO STAY]<br>
- Best time to visit: [SEASONAL RECOMMENDATIONS]<br>
- Local tips: [INSIDER INFORMATION]"""
        
        elif 'restaurant' in subtopic_name.lower():
            content = """[RESTAURANT DETAILS]<br>
- Name and location: [SPECIFIC ADDRESS]<br>
- Type of cuisine: [CULINARY STYLE]<br>
- Atmosphere: [AMBIANCE/DECOR/STYLE]<br>
- Price range: [BUDGET CATEGORY]<br><br>

[MENU AND SPECIALTIES]<br>
- Signature dishes: [RECOMMENDED ITEMS]<br>
- Variety offered: [MENU RANGE]<br>
- Special features: [UNIQUE OFFERINGS]<br>
- Dietary options: [VEGETARIAN/ALLERGIES]<br><br>

[PRACTICAL DETAILS]<br>
- Opening hours: [SCHEDULE]<br>
- Reservation policy: [BOOKING REQUIREMENTS]<br>
- Payment methods: [ACCEPTED CARDS/CASH]<br>
- Accessibility: [PARKING/PUBLIC TRANSPORT]"""
        
        elif 'hotel' in subtopic_name.lower():
            content = """[HOTEL INFORMATION]<br>
- Hotel name and location: [ADDRESS/AREA]<br>
- Star rating/category: [QUALITY LEVEL]<br>
- Type of accommodation: [BUSINESS/LEISURE/BUDGET]<br><br>

[FACILITIES AND SERVICES]<br>
- Room amenities: [WHAT'S INCLUDED]<br>
- Hotel facilities: [POOL/GYM/RESTAURANT/WIFI]<br>
- Special services: [CONCIERGE/ROOM SERVICE]<br>
- Accessibility: [DISABLED ACCESS/ELEVATORS]<br><br>

[PRACTICAL DETAILS]<br>
- Pricing: [RATE INFORMATION]<br>
- Booking process: [HOW TO RESERVE]<br>
- Location advantages: [PROXIMITY TO ATTRACTIONS]<br>
- Transportation: [AIRPORT/STATION ACCESS]"""
        
        elif 'office' in subtopic_name.lower():
            content = """[OFFICE/WORKPLACE DESCRIPTION]<br>
- Location and address: [SPECIFIC DETAILS]<br>
- Type of business: [INDUSTRY/SECTOR]<br>
- Office layout: [SPACE ORGANIZATION]<br>
- Work environment: [ATMOSPHERE/CULTURE]<br><br>

[FACILITIES AND EQUIPMENT]<br>
- Work spaces: [DESKS/MEETING ROOMS/COMMON AREAS]<br>
- Technology: [COMPUTERS/INTERNET/PHONE SYSTEM]<br>
- Amenities: [KITCHEN/BREAK ROOM/PARKING]<br>
- Services: [SECURITY/CLEANING/MAINTENANCE]<br><br>

[PRACTICAL INFORMATION]<br>
- Access and transportation: [HOW TO GET THERE]<br>
- Working hours: [SCHEDULE/FLEXIBILITY]<br>
- Nearby services: [RESTAURANTS/SHOPS/BANKS]<br>
- Contact information: [PHONE/EMAIL/WEBSITE]"""
        
        else:
            content = """[PLACE DESCRIPTION]<br>
- Name and location: [SPECIFIC ADDRESS]<br>
- Type of place: [CATEGORY/PURPOSE]<br>
- Physical characteristics: [SIZE/LAYOUT/DESIGN]<br>
- Atmosphere: [AMBIANCE/FEELING]<br><br>

[FEATURES AND AMENITIES]<br>
- Main facilities: [WHAT'S AVAILABLE]<br>
- Special features: [UNIQUE CHARACTERISTICS]<br>
- Services offered: [WHAT YOU CAN DO THERE]<br>
- Accessibility: [EASE OF ACCESS/PARKING]<br><br>

[PRACTICAL INFORMATION]<br>
- Opening hours: [SCHEDULE]<br>
- Cost/fees: [PRICING INFORMATION]<br>
- How to get there: [TRANSPORTATION OPTIONS]<br>
- Contact details: [PHONE/WEBSITE/EMAIL]"""
    
    elif 'sharing' in main_topic_name.lower():
        content = """[INFORMATION TO SHARE]<br>
- Main topic: [WHAT YOU'RE SHARING]<br>
- Key details: [IMPORTANT FACTS]<br>
- Context: [WHY IT'S RELEVANT]<br><br>

[DETAILED EXPLANATION]<br>
- Background information: [CONTEXT/HISTORY]<br>
- Current situation: [PRESENT STATE]<br>
- Implications: [WHAT IT MEANS]<br>
- Your perspective: [PERSONAL VIEW/EXPERIENCE]<br><br>

[PRACTICAL ASPECTS]<br>
- Action needed: [WHAT SHOULD BE DONE]<br>
- Timeline: [WHEN THINGS HAPPEN]<br>
- Next steps: [FOLLOW-UP REQUIRED]"""
    
    elif 'looking' in main_topic_name.lower():
        content = """[WHAT YOU'RE LOOKING FOR]<br>
- Specific need: [EXACT REQUIREMENT]<br>
- Purpose: [WHY YOU NEED IT]<br>
- Ideal characteristics: [PREFERRED FEATURES]<br><br>

[DETAILED REQUIREMENTS]<br>
- Essential criteria: [MUST-HAVE FEATURES]<br>
- Preferred options: [NICE-TO-HAVE FEATURES]<br>
- Budget/constraints: [LIMITATIONS]<br>
- Timeline: [WHEN YOU NEED IT]<br><br>

[ADDITIONAL INFORMATION]<br>
- Contact preferences: [HOW TO REACH YOU]<br>
- Availability: [YOUR SCHEDULE]<br>
- Compensation offered: [PAYMENT/EXCHANGE]<br>
- Special conditions: [SPECIFIC REQUIREMENTS]"""
    
    else:
        # Generic template for unknown types
        content = """[MAIN CONTENT]<br>
- Key point 1: [FIRST IMPORTANT DETAIL]<br>
- Key point 2: [SECOND IMPORTANT DETAIL]<br>
- Key point 3: [THIRD IMPORTANT DETAIL]<br><br>

[SUPPORTING DETAILS]<br>
- Additional information: [EXTRA CONTEXT]<br>
- Practical aspects: [USEFUL DETAILS]<br>
- Personal perspective: [YOUR VIEW/EXPERIENCE]"""
    
    return base_template + content + base_closing


def get_all_tasks(subtopic_data: Dict[str, Any]) -> List[str]:
    """Extract all task contents from subtopic data."""
    all_tasks = []
    task_entries = subtopic_data.get('task_entries', [])
    
    # Get all tasks from all entries
    for entry in task_entries:
        task_content = entry.get('task_content', '')
        if task_content:
            # Keep full content, don't truncate
            all_tasks.append(task_content)
    
    return all_tasks


def generate_html(classification: Dict[str, Any], output_path: str) -> None:
    """Generate complete HTML document with all templates."""
    
    task_number = classification.get('task_number', 1)
    main_topics = classification.get('main_topics', {})
    
    # Start HTML document with Google Docs compatible styles
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>TCF Tâche {task_number} - Writing Templates</title>
    <style>
        body {{
            font-family: "Times New Roman", serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 20px;
            background-color: white;
        }}
        
        .main-title {{
            color: #000080;
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
            text-decoration: underline;
            font-family: "Times New Roman", serif;
        }}
        
        h1 {{
            color: #800000;
            font-size: 14pt;
            font-weight: bold;
            margin-top: 25px;
            margin-bottom: 10px;
            font-family: "Times New Roman", serif;
        }}
        
        h2 {{
            color: #008000;
            font-size: 12pt;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 8px;
            margin-left: 20px;
            font-family: "Times New Roman", serif;
        }}
        
        h3 {{
            color: #800080;
            font-size: 12pt;
            font-weight: bold;
            margin-top: 15px;
            margin-bottom: 5px;
            margin-left: 40px;
            font-family: "Times New Roman", serif;
        }}
        
        .template-content {{
            color: #000000;
            font-size: 12pt;
            margin-left: 60px;
            margin-bottom: 10px;
            border: 1px solid #cccccc;
            padding: 10px;
            background-color: #f9f9f9;
            font-family: "Times New Roman", serif;
        }}
        
        .example-tasks {{
            color: #666666;
            font-size: 11pt;
            margin-left: 60px;
            margin-bottom: 15px;
            font-style: italic;
            line-height: 1.4;
            font-family: "Times New Roman", serif;
        }}
        
        .task-list {{
            color: #333333;
            font-size: 11pt;
            margin-left: 60px;
            margin-bottom: 15px;
            line-height: 1.4;
            font-family: "Times New Roman", serif;
        }}
        
        .task-item {{
            margin-bottom: 8px;
            padding: 5px;
            border-left: 3px solid #cccccc;
            padding-left: 10px;
            font-family: "Times New Roman", serif;
        }}
        
        .page-break {{
            page-break-before: always;
        }}
        
        .main-topic-info {{
            color: #444444;
            font-size: 11pt;
            margin-left: 20px;
            margin-bottom: 15px;
            font-style: italic;
            line-height: 1.4;
            font-family: "Times New Roman", serif;
        }}
        
        .topic-keywords {{
            color: #666666;
            font-size: 10pt;
            margin-left: 20px;
            margin-bottom: 10px;
            font-family: "Times New Roman", serif;
        }}
    </style>
</head>
<body>

<div class="main-title">TCF CANADA - TÂCHE {task_number} WRITING TEMPLATES</div>

'''
    
    # Roman numerals for main topics
    roman_numerals = ['I', 'II', 'III', 'IV', 'V', 'VI', 'VII', 'VIII', 'IX', 'X']
    main_topic_counter = 0
    
    # Sort main topics to exclude manual_review and put others first
    sorted_main_topics = []
    for topic_name, topic_data in main_topics.items():
        if topic_name != 'manual_review' and topic_data.get('total_tasks', 0) > 0:
            sorted_main_topics.append((topic_name, topic_data))
    
    # Sort by total tasks (descending)
    sorted_main_topics.sort(key=lambda x: x[1].get('total_tasks', 0), reverse=True)
    
    for topic_name, topic_data in sorted_main_topics:
        # Add main topic
        roman_num = roman_numerals[main_topic_counter] if main_topic_counter < len(roman_numerals) else str(main_topic_counter + 1)
        clean_topic_name = clean_main_topic_name(topic_name)
        html_content += f'<h1>{roman_num}. {clean_topic_name.upper()}</h1>\n\n'
        
        # Add main topic information
        total_tasks = topic_data.get('total_tasks', 0)
        unique_tasks = topic_data.get('unique_tasks', total_tasks)
        keywords = topic_data.get('keywords', [])
        
        # Add topic summary
        html_content += f'<div class="main-topic-info">'
        html_content += f'Total tasks: {unique_tasks} unique ({total_tasks} total)<br>'
        html_content += f'Subtopics: {len([s for s in topic_data.get("subtopics", {}).values() if s.get("task_count", 0) > 0])}'
        html_content += f'</div>\n\n'
        
        # Add topic keywords
        if keywords:
            keywords_text = ', '.join(keywords[:10])  # Show first 10 keywords
            if len(keywords) > 10:
                keywords_text += f' (and {len(keywords) - 10} more)'
            html_content += f'<div class="topic-keywords">Keywords: {keywords_text}</div>\n\n'
        
        # Letters for subtopics
        letters = [chr(65 + i) for i in range(26)]  # A, B, C, ...
        subtopic_counter = 0
        
        # Sort subtopics by task count
        subtopics = topic_data.get('subtopics', {})
        sorted_subtopics = [(name, data) for name, data in subtopics.items() if data.get('task_count', 0) > 0]
        sorted_subtopics.sort(key=lambda x: x[1].get('task_count', 0), reverse=True)
        
        for subtopic_name, subtopic_data in sorted_subtopics:
            # Add page break before each subtopic (except the first one)
            if main_topic_counter > 0 or subtopic_counter > 0:
                html_content += '<div class="page-break"></div>\n\n'
            
            # Add subtopic
            letter = letters[subtopic_counter] if subtopic_counter < len(letters) else str(subtopic_counter + 1)
            cleaned_subtopic_name = clean_subtopic_name(subtopic_name)
            html_content += f'<h2>{letter}. {cleaned_subtopic_name}</h2>\n\n'
            
            # Add template
            html_content += '<h3>Template:</h3>\n'
            template_content = generate_template_for_subtopic(subtopic_name, topic_name)
            html_content += f'<div class="template-content">\n{template_content}\n</div>\n\n'
            
            # Add all tasks
            tasks = get_all_tasks(subtopic_data)
            if tasks:
                task_count = subtopic_data.get('task_count', 0)
                unique_count = subtopic_data.get('unique_task_count', task_count)
                
                html_content += f'<div class="example-tasks">\nAll tasks ({unique_count} unique, {task_count} total):\n</div>\n'
                html_content += '<div class="task-list">\n'
                
                for i, task in enumerate(tasks, 1):
                    html_content += f'<div class="task-item">\n{i}. {task}\n</div>\n'
                
                html_content += '</div>\n\n'
            
            subtopic_counter += 1
        
        main_topic_counter += 1
    
    html_content += '</body>\n</html>'
    
    # Save HTML file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"HTML template generated successfully: {output_path}")
    print(f"Total main topics: {main_topic_counter}")
    print(f"Total subtopics: {sum(len([s for s in topic['subtopics'].keys() if topic['subtopics'][s].get('task_count', 0) > 0]) for topic in main_topics.values() if topic.get('total_tasks', 0) > 0)}")


def main():
    """Main function to handle command line arguments."""
    if len(sys.argv) < 2:
        print("Usage: python3 classification_to_html.py <classification_json> [output_html]")
        print("\nExamples:")
        print("  python3 classification_to_html.py data/classified/writing/reviewed_classification/tache_1.json")
        print("  python3 classification_to_html.py tache_1.json tache_1_templates.html")
        sys.exit(1)
    
    input_path = sys.argv[1]
    
    # Generate output path if not provided
    if len(sys.argv) > 2:
        output_path = sys.argv[2]
    else:
        input_file = Path(input_path)
        output_path = str(input_file.parent / f"{input_file.stem}_templates.html")
    
    # Check if input file exists
    if not Path(input_path).exists():
        print(f"Error: Input file not found: {input_path}")
        sys.exit(1)
    
    try:
        # Load classification
        print(f"Loading classification: {input_path}")
        classification = load_classification(input_path)
        
        # Check if classification section exists
        if 'classification' not in classification:
            print("Error: No 'classification' section found in the JSON file.")
            sys.exit(1)
        
        # Generate HTML
        print("Generating HTML templates...")
        generate_html(classification['classification'], output_path)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main() 