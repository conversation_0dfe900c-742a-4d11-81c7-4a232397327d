#!/usr/bin/env python3
"""
Script to convert Task 3 (topic-based) speaking test JSON files to PDF format.
Includes exemplary answers and creates well-formatted PDFs.
"""

import json
import os
from pathlib import Path
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, PageBreak, KeepTogether
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT, TA_RIGHT
from reportlab.pdfgen import canvas
from reportlab.lib.colors import lightgrey
import argparse
from typing import List, Any

class Task3ToPDFConverter:
    """Converts Task 3 speaking test JSON files to PDF format."""
    
    def __init__(self, input_dir: str, output_dir: str = "output_pdfs"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        self.styles.add(ParagraphStyle(
            name='TopicStyle',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=25,
            spaceBefore=20,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        self.styles.add(ParagraphStyle(
            name='QuestionStyle',
            parent=self.styles['Normal'],
            fontSize=13,
            spaceAfter=15,
            spaceBefore=20,
            alignment=TA_JUSTIFY,
            fontName='Helvetica-Bold'
        ))
        self.styles.add(ParagraphStyle(
            name='QuestionNumber',
            parent=self.styles['Normal'],
            fontSize=14,
            spaceAfter=10,
            spaceBefore=25,
            alignment=TA_LEFT,
            fontName='Helvetica-Bold'
        ))
        self.styles.add(ParagraphStyle(
            name='ExemplaryAnswer',
            parent=self.styles['Normal'],
            fontSize=12,
            spaceAfter=15,
            spaceBefore=15,
            alignment=TA_JUSTIFY,
            fontName='Helvetica',
            leftIndent=20
        ))
        self.styles.add(ParagraphStyle(
            name='ExemplaryAnswerTitle',
            parent=self.styles['Normal'],
            fontSize=13,
            spaceAfter=10,
            spaceBefore=15,
            alignment=TA_LEFT,
            fontName='Helvetica-Bold',
            leftIndent=20
        ))
        self.styles.add(ParagraphStyle(
            name='WordCount',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=10,
            spaceBefore=5,
            alignment=TA_RIGHT,
            fontName='Helvetica-Oblique'
        ))
    
    def _add_watermark(self, canvas_obj, doc):
        canvas_obj.saveState()
        watermark_text = "chez-tcfcanada.com"
        page_width, page_height = A4
        x = page_width / 2
        y = page_height / 2
        canvas_obj.setFont("Helvetica-Bold", 48)
        canvas_obj.setFillColor(lightgrey)
        canvas_obj.setStrokeColor(lightgrey)
        canvas_obj.rotate(45)
        rotated_x = (x + y) / 1.414
        rotated_y = (y - x) / 1.414
        canvas_obj.drawCentredString(rotated_x, rotated_y, watermark_text)
        canvas_obj.restoreState()
    
    def _format_text_for_pdf(self, text: str) -> str:
        text = text.replace('&', '&amp;')
        text = text.replace('<', '&lt;')
        text = text.replace('>', '&gt;')
        text = text.replace('\n', '<br/>')
        return text
    
    def _process_task3_file(self, json_file: Path) -> List[Any]:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            print(f"Error reading {json_file}: {e}")
            return []
        story = []
        topic = data.get('topic', json_file.stem)
        title = f"Exercices d'Expression Orale - Tâche 3 - {topic}"
        story.append(Paragraph(title, self.styles['CustomTitle']))
        story.append(Spacer(1, 20))
        taches = data.get('taches', [])
        for tache in taches:
            task_num = tache.get('task_number', 'N/A')
            question = tache.get('question', '')
            word_count = tache.get('word_count', '')
            exemplary_answer = tache.get('exemplary_answer', {})
            task_content = []
            task_content.append(Paragraph(f"Question {task_num}:", self.styles['QuestionNumber']))
            if question:
                formatted_question = self._format_text_for_pdf(question)
                task_content.append(Paragraph(formatted_question, self.styles['QuestionStyle']))
            if exemplary_answer:
                task_content.append(Paragraph("Réponse exemplaire:", self.styles['ExemplaryAnswerTitle']))
                full_text = exemplary_answer.get('full_text', '')
                if full_text:
                    formatted_answer = self._format_text_for_pdf(full_text)
                    task_content.append(Paragraph(formatted_answer, self.styles['ExemplaryAnswer']))
                paragraph_count = exemplary_answer.get('paragraph_count', 0)
                if paragraph_count > 0:
                    task_content.append(Paragraph(f"Nombre de paragraphes: {paragraph_count}", self.styles['WordCount']))
            if word_count:
                task_content.append(Paragraph(f"Nombre de mots suggéré: {word_count}", self.styles['WordCount']))
            story.append(KeepTogether(task_content))
            story.append(Spacer(1, 20))
        return story
    
    def convert_file(self, json_file: Path) -> str:
        pdf_filename = f"{json_file.stem}_task3_speaking.pdf"
        pdf_path = self.output_dir / pdf_filename
        doc = SimpleDocTemplate(
            str(pdf_path),
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        story = self._process_task3_file(json_file)
        if not story:
            print(f"No content found in {json_file}")
            return ""
        try:
            doc.build(story, onFirstPage=self._add_watermark, onLaterPages=self._add_watermark)
            print(f"Successfully converted {json_file.name} to {pdf_filename}")
            return str(pdf_path)
        except Exception as e:
            print(f"Error creating PDF for {json_file.name}: {e}")
            return ""
    
    def convert_all(self) -> List[str]:
        task3_dir = self.input_dir / "La_tache_3"
        if not task3_dir.exists():
            print(f"Task 3 directory not found: {task3_dir}")
            return []
        json_files = list(task3_dir.glob("*.json"))
        if not json_files:
            print(f"No JSON files found in {task3_dir}")
            return []
        print(f"Found {len(json_files)} Task 3 JSON files to convert")
        generated_pdfs = []
        for json_file in json_files:
            pdf_path = self.convert_file(json_file)
            if pdf_path:
                generated_pdfs.append(pdf_path)
        print(f"\nConversion completed! Generated {len(generated_pdfs)} PDF files in {self.output_dir}")
        return generated_pdfs
    
    def create_combined_pdf(self, output_filename: str = "all_speaking_task3_exercises.pdf") -> str:
        task3_dir = self.input_dir / "La_tache_3"
        if not task3_dir.exists():
            print(f"Task 3 directory not found: {task3_dir}")
            return ""
        json_files = sorted(list(task3_dir.glob("*.json")))
        if not json_files:
            print(f"No JSON files found in {task3_dir}")
            return ""
        combined_pdf_path = self.output_dir / output_filename
        doc = SimpleDocTemplate(
            str(combined_pdf_path),
            pagesize=A4,
            rightMargin=72,
            leftMargin=72,
            topMargin=72,
            bottomMargin=18
        )
        story = []
        story.append(Paragraph("Exercices d'Expression Orale - Tâche 3 - Collection Complète", self.styles['CustomTitle']))
        story.append(Spacer(1, 30))
        for i, json_file in enumerate(json_files):
            if i > 0:
                story.append(PageBreak())
            file_story = self._process_task3_file(json_file)
            story.extend(file_story)
        try:
            doc.build(story, onFirstPage=self._add_watermark, onLaterPages=self._add_watermark)
            print(f"Successfully created combined PDF: {output_filename}")
            return str(combined_pdf_path)
        except Exception as e:
            print(f"Error creating combined PDF: {e}")
            return ""

def main():
    parser = argparse.ArgumentParser(description="Convert Task 3 speaking test JSON files to PDF")
    parser.add_argument(
        "--input-dir", 
        default="data/scraped/scraped_speaking",
        help="Directory containing JSON files (default: data/scraped/scraped_speaking)"
    )
    parser.add_argument(
        "--output-dir",
        default="output_pdfs",
        help="Directory to save PDF files (default: output_pdfs)"
    )
    parser.add_argument(
        "--combined",
        action="store_true",
        help="Create a single combined PDF with all exercises"
    )
    parser.add_argument(
        "--individual",
        action="store_true",
        default=True,
        help="Create individual PDF files for each JSON file (default: True)"
    )
    args = parser.parse_args()
    converter = Task3ToPDFConverter(args.input_dir, args.output_dir)
    if args.individual:
        converter.convert_all()
    if args.combined:
        converter.create_combined_pdf()

if __name__ == "__main__":
    main() 