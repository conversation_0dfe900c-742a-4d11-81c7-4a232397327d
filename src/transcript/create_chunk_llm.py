#!/usr/bin/env python3
"""
Hybrid Chunking Script for Dehallucinated Transcriptions
Uses LLM for ABCD choices and rule-based system for conversation/question audio.
Requires: OPENAI_API_KEY environment variable set with your OpenAI API key.
Install the SDK: pip install openai
"""

import json
import os
import re
import string
import difflib
from typing import List, Dict

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI not available - LLM chunking will be disabled")

# Import rule-based chunking functions
from create_chunks import (
    create_chunks_for_abcd_choices,
    create_chunks_for_conversation_question
)

# Set OpenAI API key from environment
client = None
if OPENAI_AVAILABLE:
    client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# Function to clean trailing commas from JSON-like text
def clean_json_trailing_commas(s):
    # Remove trailing commas before } or ]
    return re.sub(r',([ \t\r\n]*[}\]])', r'\1', s)

# Prompt template for ABCD (Type 1) audio chunking
ABCD_PROMPT_TEMPLATE = """
You are a French language expert and transcription chunking assistant. Given the following transcription (called 'full_text') and its word-level timestamps, split it into logical, timestamped chunks for ABCD multiple choice audio according to these specific rules:

ABCD CHUNKING RULES:
- Each choice (A., B., C., D.) MUST be a separate chunk
- Do NOT create chunks that span multiple choices
- Do NOT create chunks that start in the middle of a choice
- If a choice is very short (less than 5 words), it's okay to keep it as a separate chunk
- Maximum 6 chunks total (1 prompt + 4 choices, or just 4 choices if no prompt)

IMPORTANT: You MUST create less than 10 chunks in total. Only use the provided 'full_text' for chunking. Do NOT use or reference any 'original_text' or other fields, even if present. All chunked text must be a substring of 'full_text'.
All content in the output chunks must be present in the provided 'full_text'; do not omit, rephrase, or add anything from the 'full_text'. Do not hallucinate, reword, or remove any part of the 'full_text'. Each chunk's text must be a verbatim substring of the provided 'full_text'.
For each chunk, use the provided words array to assign accurate 'start' and 'end' timestamps (matching the first and last word in the chunk). If a chunk's text does not match any words, set start and end to null.
Return the result as a JSON list of objects with 'start', 'end', and 'text' fields. Do NOT include any trailing commas in the JSON.

full_text:
{text}
Words (as a JSON list):
{words}
"""

# Prompt template for conversation/question (Type 2) audio chunking
CONVERSATION_PROMPT_TEMPLATE = """
You are a French language expert and transcription chunking assistant. Given the following transcription (called 'full_text') and its word-level timestamps, split it into logical, timestamped chunks for conversation/question audio according to these specific rules:

CONVERSATION/QUESTION CHUNKING RULES:
- The last sentence (especially the final question) MUST always be a separate chunk
- Split at natural sentence boundaries (periods, question marks, exclamation points)
- If any chunk is longer than 30 words, split it into smaller chunks at natural phrase boundaries
- Avoid creating very small chunks (fewer than 8 words) unless absolutely necessary
- Ensure the final question or statement is isolated in its own chunk.

IMPORTANT: You MUST create less than 10 chunks in total. Only use the provided 'full_text' for chunking. Do NOT use or reference any 'original_text' or other fields, even if present. All chunked text must be a substring of 'full_text'.
All content in the output chunks must be present in the provided 'full_text'; do not omit, rephrase, or add anything from the 'full_text'. Do not hallucinate, reword, or remove any part of the 'full_text'. Each chunk's text must be a verbatim substring of the provided 'full_text'.
For each chunk, use the provided words array to assign accurate 'start' and 'end' timestamps (matching the first and last word in the chunk). If a chunk's text does not match any words, set start and end to null.
Return the result as a JSON list of objects with 'start', 'end', and 'text' fields. Do NOT include any trailing commas in the JSON.

full_text:
{text}
Words (as a JSON list):
{words}
"""

# Post-processing: split any chunk longer than 25 words
def split_long_chunks(chunks, max_words=25):
    new_chunks = []
    for chunk in chunks:
        words = chunk['text'].split()
        if len(words) > max_words:
            start = 0
            while start < len(words):
                end = min(start + max_words, len(words))
                # Try to break at punctuation within the window
                punct_idx = None
                for i in range(end, start, -1):
                    if words[i-1][-1] in '.!?':
                        punct_idx = i
                        break
                if punct_idx and punct_idx > start:
                    end = punct_idx
                new_text = ' '.join(words[start:end])
                new_chunk = chunk.copy()
                new_chunk['text'] = new_text
                # Optionally, set start/end to None or recalculate
                new_chunks.append(new_chunk)
                start = end
        else:
            new_chunks.append(chunk)
    return new_chunks

def normalize_text(text):
    # Lowercase, remove punctuation and extra spaces
    return ''.join(c for c in text.lower() if c not in string.punctuation and not c.isspace())

def align_chunk_to_words(chunk_text, words, fuzzy_threshold=0.7):
    """
    Try to align chunk_text to a span in words using normalization and fuzzy matching.
    Returns (start, end) timestamps or (None, None) if not found.
    Enhanced with multiple fallback strategies for better timestamp accuracy.
    """
    if not chunk_text or not words:
        return None, None

    norm_chunk = normalize_text(chunk_text)
    norm_words = [normalize_text(w['word']) for w in words]
    chunk_len = len(norm_words)

    # Strategy 1: Try all possible spans in words (exact match)
    for start in range(chunk_len):
        for end in range(start+1, chunk_len+1):
            span = ''.join(norm_words[start:end])
            if norm_chunk == span:
                return words[start]['start'], words[end-1]['end']

    # Strategy 2: Try word-by-word matching for partial spans
    chunk_words = chunk_text.split()
    if len(chunk_words) >= 2:
        first_word = normalize_text(chunk_words[0])
        last_word = normalize_text(chunk_words[-1])

        first_idx = None
        last_idx = None

        for i, norm_word in enumerate(norm_words):
            if first_idx is None and first_word in norm_word:
                first_idx = i
            if last_word in norm_word:
                last_idx = i

        if first_idx is not None and last_idx is not None and last_idx >= first_idx:
            return words[first_idx]['start'], words[last_idx]['end']

    # Strategy 3: Fuzzy matching using difflib
    joined = ''.join(norm_words)
    matcher = difflib.SequenceMatcher(None, joined, norm_chunk)
    match = matcher.find_longest_match(0, len(joined), 0, len(norm_chunk))
    ratio = match.size / max(len(norm_chunk), 1)

    if ratio > fuzzy_threshold and match.size > 0:
        # Try to find the word boundaries for the matched region
        char_pos = 0
        start_word_idx = None
        end_word_idx = None

        for i, word in enumerate(words):
            word_text = normalize_text(word['word'])
            word_end = char_pos + len(word_text)

            if start_word_idx is None and match.a <= char_pos < match.a + match.size:
                start_word_idx = i
            if match.a <= word_end <= match.a + match.size:
                end_word_idx = i

            char_pos = word_end

        if start_word_idx is not None and end_word_idx is not None:
            print(f"⚠️  Fuzzy match for chunk: {chunk_text[:40]}... (ratio={ratio:.2f})")
            return words[start_word_idx]['start'], words[end_word_idx]['end']

    return None, None

def assign_timestamps_to_chunks(chunks, words):
    for chunk in chunks:
        start, end = align_chunk_to_words(chunk['text'], words)
        chunk['start'] = start
        chunk['end'] = end
        if start is None or end is None:
            print(f"⚠️  Could not align chunk to words for text: {chunk['text'][:40]}...")
    return chunks

def create_robust_fallback_chunk(text: str, words: List[Dict], sentence_index: int, total_sentences: int) -> Dict:
    """
    Create a more robust fallback chunk when exact timestamp matching fails.
    Uses word-level analysis to find better approximate timestamps.
    """
    if not words:
        return None

    # First try to find any words from the text in the word list
    text_words = text.lower().split()
    if not text_words:
        return None

    # Find the first and last words that appear in the transcript
    first_word_idx = None
    last_word_idx = None

    for i, word_obj in enumerate(words):
        word_text = normalize_text(word_obj['word'])

        # Check if any text word matches this transcript word
        for text_word in text_words:
            norm_text_word = normalize_text(text_word)
            if norm_text_word in word_text or word_text in norm_text_word:
                if first_word_idx is None:
                    first_word_idx = i
                last_word_idx = i
                break

    # If we found matching words, use their timestamps
    if first_word_idx is not None and last_word_idx is not None:
        return {
            "text": text,
            "start": round(words[first_word_idx]["start"], 2),
            "end": round(words[last_word_idx]["end"], 2)
        }

    # Fallback to position-based estimation
    words_per_sentence = len(words) / max(total_sentences, 1)
    start_idx = int(sentence_index * words_per_sentence)
    end_idx = min(int((sentence_index + 1) * words_per_sentence), len(words) - 1)

    if start_idx < len(words) and end_idx < len(words):
        return {
            "text": text,
            "start": round(words[start_idx]["start"], 2),
            "end": round(words[end_idx]["end"], 2)
        }

    return None

def create_robust_chunks_for_conversation_question(text: str, words: List[Dict]) -> List[Dict]:
    """
    Create chunks for Type 2 (conversation/question) using robust timestamp alignment.
    Final sentence is always a separate segment.
    """
    from create_chunks import split_into_sentences, break_long_sentence

    chunks = []
    # Split into sentences
    sentences = split_into_sentences(text)
    if not sentences:
        return []
    print(f"    📝 Found {len(sentences)} sentences: {[s[:30] + '...' if len(s) > 30 else s for s in sentences]}")

    # Process all but the last sentence
    for i, sentence in enumerate(sentences[:-1]):
        # Break long sentences
        sentence_chunks = break_long_sentence(sentence)
        for chunk_text in sentence_chunks:
            # Use robust timestamp alignment
            start, end = align_chunk_to_words(chunk_text, words)
            if start is not None and end is not None:
                chunk = {
                    "text": chunk_text,
                    "start": round(start, 2),
                    "end": round(end, 2)
                }
                chunks.append(chunk)
                print(f"    ✅ Added chunk: '{chunk['text'][:30]}...' ({chunk['start']}s - {chunk['end']}s)")
            else:
                # Fallback to robust estimation
                chunk = create_robust_fallback_chunk(chunk_text, words, i, len(sentences))
                if chunk:
                    chunks.append(chunk)
                    print(f"    🔧 Robust fallback chunk: '{chunk['text'][:30]}...' ({chunk['start']}s - {chunk['end']}s)")
                else:
                    print(f"    ⚠️ Could not chunk: '{chunk_text[:30]}...'")

    # Last sentence is always a separate segment
    last_sentence = sentences[-1]
    start, end = align_chunk_to_words(last_sentence, words)
    if start is not None and end is not None:
        last_chunk = {
            "text": last_sentence,
            "start": round(start, 2),
            "end": round(end, 2)
        }
        chunks.append(last_chunk)
        print(f"    ✅ Added final sentence chunk: '{last_chunk['text'][:30]}...' ({last_chunk['start']}s - {last_chunk['end']}s)")
    else:
        # Robust fallback for last sentence
        last_chunk = create_robust_fallback_chunk(last_sentence, words, len(sentences)-1, len(sentences))
        if last_chunk:
            chunks.append(last_chunk)
            print(f"    🔧 Robust fallback final chunk: '{last_chunk['text'][:30]}...' ({last_chunk['start']}s - {last_chunk['end']}s)")
        else:
            print(f"    ⚠️ Could not chunk final sentence: '{last_sentence[:30]}...'")

    # Post-process to fix any overlapping timestamps
    chunks = fix_overlapping_timestamps(chunks)

    return chunks

def fix_overlapping_timestamps(chunks: List[Dict]) -> List[Dict]:
    """
    Fix overlapping timestamps by adjusting chunk boundaries.
    Ensures chunks are in chronological order with no overlaps.
    """
    if len(chunks) <= 1:
        return chunks

    # Sort chunks by start time
    chunks.sort(key=lambda x: x['start'])

    fixed_chunks = []
    for i, chunk in enumerate(chunks):
        if i == 0:
            # First chunk - no adjustment needed
            fixed_chunks.append(chunk)
        else:
            prev_chunk = fixed_chunks[-1]

            # If current chunk starts before previous chunk ends, adjust
            if chunk['start'] < prev_chunk['end']:
                # Calculate midpoint between previous start and current end
                midpoint = (prev_chunk['start'] + chunk['end']) / 2

                # Ensure the midpoint is reasonable
                if midpoint > prev_chunk['start'] and midpoint < chunk['end']:
                    prev_chunk['end'] = round(midpoint, 2)
                    chunk['start'] = round(midpoint, 2)
                    print(f"    🔧 Fixed overlap: adjusted boundary to {midpoint:.2f}s")
                else:
                    # Fallback: set previous end to current start
                    prev_chunk['end'] = round(chunk['start'], 2)
                    print(f"    🔧 Fixed overlap: set boundary to {chunk['start']:.2f}s")

            # Ensure chunk has positive duration
            if chunk['end'] <= chunk['start']:
                chunk['end'] = chunk['start'] + 0.1  # Minimum 0.1s duration
                print(f"    🔧 Fixed negative duration: extended to {chunk['end']:.2f}s")

            fixed_chunks.append(chunk)

    return fixed_chunks

# Sanitize text to replace curly quotes and problematic Unicode punctuation with ASCII equivalents
def sanitize_text(text):
    replacements = {
        '"': '"', '"': '"', ''': "'", ''': "'",
        '—': '-', '–': '-', '…': '...',
        '«': '"', '»': '"',
    }
    for orig, repl in replacements.items():
        text = text.replace(orig, repl)
    return text

def chunk_with_llm(full_text: str, audio_type: str, words: List[Dict]=None) -> List[Dict]:
    if not OPENAI_AVAILABLE:
        print("❌ OpenAI not available - cannot use LLM chunking")
        return []

    # Choose the appropriate prompt based on audio type
    if audio_type == 'abcd_choices':
        prompt_template = ABCD_PROMPT_TEMPLATE
        print(f"  🎯 Using ABCD-specific prompt")
    else:
        prompt_template = CONVERSATION_PROMPT_TEMPLATE
        print(f"  🎯 Using conversation/question prompt")

    # Prepare the prompt with both text and words array
    words_json = json.dumps(words, ensure_ascii=False)
    prompt = prompt_template.format(text=full_text, words=words_json)
    prompt = sanitize_text(prompt)
    try:
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=1200,
            temperature=0.2,
        )
        result_text = response.choices[0].message.content
        # Clean trailing commas before parsing
        result_text_clean = clean_json_trailing_commas(result_text)
        try:
            chunks = json.loads(result_text_clean)
        except json.JSONDecodeError:
            # Try to extract JSON from the response
            match = re.search(r'\[.*\]', result_text_clean, re.DOTALL)
            if match:
                try:
                    chunks = json.loads(match.group(0))
                except Exception:
                    print("❌ Could not parse JSON from LLM output.")
                    print(result_text)
                    return []
            else:
                print("❌ No JSON found in LLM output.")
                print(result_text)
                return []
        # Warn if any chunk is missing timestamps
        for c in chunks:
            if c.get('start') is None or c.get('end') is None:
                print("⚠️  LLM did not provide timestamps for some chunks. Check the prompt or input data.")
                break
        # Post-process: split any chunk longer than 25 words
        chunks = split_long_chunks(chunks, max_words=25)
        # Robust timestamp alignment
        if words:
            chunks = assign_timestamps_to_chunks(chunks, words)
        return chunks
    except Exception as e:
        print(f"❌ OpenAI API error: {e}")
        return []

def process_single_transcription(transcription: Dict) -> Dict:
    full_text = transcription.get('full_text', '')
    words = transcription.get('words', [])
    if not full_text:
        print("  ⚠️ No text found, skipping")
        return None
    
    # Use existing audio_type field
    audio_type = transcription.get("audio_type", "conversation_question")
    print(f"  🎯 Audio type: {audio_type}")
    
    # Hybrid approach: LLM for ABCD, rule-based for conversation/question
    if audio_type == 'abcd_choices':
        print(f"  🤖 Using LLM chunking for ABCD choices")
        chunks = chunk_with_llm(full_text, audio_type, words)
        chunking_method = 'llm_gpt_4o_mini'
    else:
        print(f"  📏 Using robust rule-based chunking for conversation/question")
        chunks = create_robust_chunks_for_conversation_question(full_text, words)
        chunking_method = 'rule_based_robust'
    
    print(f"  ✅ Created {len(chunks)} chunks ({chunking_method})")
    result = transcription.copy()
    result['chunks'] = chunks
    result['chunking_method'] = chunking_method
    result['chunk_count'] = len(chunks)
    result['audio_type'] = audio_type
    if 'words' in result:
        del result['words']
    return result

def process_json_file(input_path: str, output_path: str = None):
    print(f"📁 Processing: {input_path}")
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    if isinstance(data, list):
        results = []
        for i, transcription in enumerate(data):
            print(f"\n[{i+1}/{len(data)}] Processing transcription...")
            result = process_single_transcription(transcription)
            if result:
                results.append(result)
        if output_path is None:
            output_path = input_path.replace('.json', '_chunked_llm.json')
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"💾 Saved chunked results to: {output_path}")
        print(f"📊 Processed {len(results)} transcriptions")
    else:
        result = process_single_transcription(data)
        if result:
            if output_path is None:
                output_path = input_path.replace('.json', '_chunked_llm.json')
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"💾 Saved chunked results to: {output_path}")

def main():
    sample_data = {
        "full_text": "A. Allez-y, entrez. B. Asseyez-vous, je vous en prie. C. Fermez la porte, s'il vous plaît. D. Merci pour ce café",
        "audio_type": "abcd_choices",
        "words": [
            {"word": " A.", "start": 0.0, "end": 0.34},
            {"word": " Allez", "start": 1.3, "end": 1.88},
            {"word": "-y,", "start": 1.88, "end": 2.16},
            {"word": " entrez.", "start": 2.2, "end": 3.04},
            {"word": " B.", "start": 5.86, "end": 6.44},
            {"word": " Asseyez", "start": 6.44, "end": 7.02},
            {"word": "-vous,", "start": 7.02, "end": 7.32},
            {"word": " je", "start": 7.36, "end": 7.76},
            {"word": " vous", "start": 7.76, "end": 7.88},
            {"word": " en", "start": 7.88, "end": 8.08},
            {"word": " prie.", "start": 8.08, "end": 8.42},
            {"word": " C.", "start": 11.1, "end": 11.68},
            {"word": " Fermez", "start": 11.68, "end": 12.26},
            {"word": " la", "start": 12.26, "end": 12.36},
            {"word": " porte,", "start": 12.36, "end": 12.64},
            {"word": " s", "start": 12.76, "end": 12.9},
            {"word": "'il", "start": 12.9, "end": 13.0},
            {"word": " vous", "start": 13.0, "end": 13.1},
            {"word": " plaît.", "start": 13.1, "end": 13.42},
            {"word": " D.", "start": 15.32, "end": 15.9},
            {"word": " Merci", "start": 16.78, "end": 17.36},
            {"word": " pour", "start": 17.36, "end": 17.64},
            {"word": " ce", "start": 17.64, "end": 17.76},
            {"word": " café.", "start": 17.76, "end": 18.06}
        ]
    }
    result = process_single_transcription(sample_data)
    if result:
        print(f"\n📋 Result:")
        print(f"Audio type: {result['audio_type']}")
        print(f"Chunks created: {len(result['chunks'])}")
        for i, chunk in enumerate(result['chunks']):
            print(f"\nChunk {i+1}:")
            print(f"  Text: '{chunk['text']}'")
            print(f"  Start: {chunk.get('start', '?')}s - End: {chunk.get('end', '?')}s")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_path = None
        if len(sys.argv) > 2:
            output_path = sys.argv[2]
        process_json_file(input_path, output_path)
    else:
        main() 