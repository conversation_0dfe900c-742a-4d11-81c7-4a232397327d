#!/usr/bin/env python3
"""
Dehallucination Script for TCF Audio Transcriptions
- Removes common hallucination patterns from the end of transcriptions
- Filters out words with very short duration
- For Type 2: Removes anything after the last complete sentence/question
- Validates remaining text makes sense for the audio type
- Rule-based approach optimized for French TCF content
"""

import re
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional

class Dehallucinator:
    def __init__(self):
        # Robust hallucination patterns for trailing Sous-titrage and variants
        self.hallucination_patterns = [
            # Sous-titrage with optional FR, ST', numbers, dashes, spaces, and punctuation
            r"Sous[\s\-]*titrage(?:\s*(?:FR|ST'|Société Radio-Canada|[A-Za-z]+)?[\s'-]*\d*)?[\s\-:,.!?]*$",
            r"Sous[\s\-]*titrage[\s\-:,.!?]*$",
            r"ST'\s*\d*[\s\-:,.!?]*$",               # "ST' 501" or similar
            r"FR\s*\?*[\s\-:,.!?]*$",                # "FR ?" or similar
            r"Merci[\s\-:,.!?]*$",                     # "Merci" at the end (common hallucination)
        ]
        
        # Compile patterns for efficiency
        self.compiled_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in self.hallucination_patterns]
        
        # Duration threshold for suspicious words (seconds)
        self.min_duration_threshold = 0.1
        
        # Minimum word count after cleaning
        self.min_words_after_cleaning = 3
        
    def remove_hallucination_patterns(self, text: str, audio_type: str = "unknown") -> str:
        """Remove common hallucination patterns from the end of text - conservative approach."""
        if not text:
            return text
            
        original_text = text
        cleaned_text = text.strip()
        
        # Apply patterns once (conservative approach)
        for pattern in self.compiled_patterns:
            cleaned_text = pattern.sub('', cleaned_text)
        
        # Clean up extra whitespace
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
        
        # Log what was removed
        if cleaned_text != original_text:
            removed = original_text[len(cleaned_text):].strip()
            print(f"   🧹 Removed hallucination pattern: '{removed}'")
        
        return cleaned_text
    
    def remove_incomplete_end_for_type2(self, text: str) -> str:
        """
        For Type 2 (conversation_question): Conservative removal of trailing content.
        Only removes obvious trailing words after the last question mark.
        """
        if not text:
            return text
        original_text = text

        # Find the last question mark
        last_question = text.rfind('?')
        if last_question != -1:
            # Only remove if there are obvious trailing words (not just whitespace)
            trailing_content = text[last_question + 1:].strip()
            if trailing_content and len(trailing_content.split()) <= 3:  # Only remove if 3 words or less
                cleaned_text = text[:last_question + 1].strip()
                if cleaned_text != original_text:
                    print(f"   📝 Removed trailing content: '{trailing_content}'")
                return cleaned_text
        
        # If no question mark or no obvious trailing content, keep everything
        return text
    
    def filter_short_duration_words(self, words: List[Dict], text: str) -> Tuple[List[Dict], str]:
        """Remove words with very short duration from the end."""
        if not words:
            return words, text
            
        # Find words with very short duration at the end
        short_duration_indices = []
        
        # Check from the end backwards
        for i in range(len(words) - 1, -1, -1):
            word_info = words[i]
            duration = word_info.get('end', 0) - word_info.get('start', 0)
            
            if duration < self.min_duration_threshold:
                short_duration_indices.append(i)
            else:
                # Stop when we find a word with normal duration
                break
        
        # Remove short duration words from the end
        if short_duration_indices:
            removed_words = words[short_duration_indices[0]:]
            remaining_words = words[:short_duration_indices[0]]
            
            # Reconstruct text from remaining words
            remaining_text = ' '.join([w.get('word', '').strip() for w in remaining_words])
            remaining_text = re.sub(r'\s+', ' ', remaining_text).strip()
            
            removed_text = ' '.join([w.get('word', '').strip() for w in removed_words])
            print(f"   ⏱️ Removed short duration words: '{removed_text}' (duration < {self.min_duration_threshold}s)")
            
            return remaining_words, remaining_text
        
        return words, text
    
    def validate_text_coherence(self, text: str, audio_type: str) -> Tuple[bool, str]:
        """Validate that the cleaned text makes sense for the audio type."""
        if not text or len(text.strip()) < 3:
            return False, "Text too short after cleaning"
        
        words = text.strip().split()
        if len(words) < self.min_words_after_cleaning:
            return False, f"Too few words after cleaning: {len(words)} (need ≥{self.min_words_after_cleaning})"
        
        # Check for common issues
        if text.lower().startswith('sous-titrage'):
            return False, "Still contains 'sous-titrage' at start"
        
        if text.lower().endswith('sous-titrage'):
            return False, "Still contains 'sous-titrage' at end"
        
        # Type-specific validation
        if audio_type == "abcd_choices":
            # Should contain some structure or choices
            if not re.search(r'\b[a-d]\.|\b[a-d]\s|\([a-d]\)', text.lower()):
                return False, "ABCD type but no choice patterns found"
        
        return True, "Text appears coherent"
    
    def dehallucinate(self, transcription_data: Dict, audio_type: str = "unknown") -> Dict:
        """
        Main dehallucination function.
        
        Args:
            transcription_data: Dictionary with 'full_text' and 'words' keys
            audio_type: Type of audio ('abcd_choices' or 'conversation_question')
        
        Returns:
            Dictionary with cleaned transcription data
        """
        original_text = transcription_data.get('full_text', '')
        original_words = transcription_data.get('words', [])
        
        print(f"🔍 Dehallucinating: '{original_text[:50]}...'")
        print(f"   📊 Original: {len(original_words)} words, {len(original_text)} chars")
        print(f"   🎯 Audio type: {audio_type}")
        
        # Step 1: Filter short duration words from the end
        cleaned_words, cleaned_text = self.filter_short_duration_words(original_words, original_text)
        
        # Step 2: Remove hallucination patterns from text
        cleaned_text = self.remove_hallucination_patterns(cleaned_text, audio_type)
        
        # Step 3: For Type 2, remove incomplete endings
        if audio_type == "conversation_question":
            cleaned_text = self.remove_incomplete_end_for_type2(cleaned_text)
        
        # Step 4: Validate coherence
        is_coherent, coherence_reason = self.validate_text_coherence(cleaned_text, audio_type)
        
        if not is_coherent:
            print(f"   ⚠️ Coherence check failed: {coherence_reason}")
            # Return original if cleaning made it worse
            return transcription_data
        
        # Step 5: Create result
        result = transcription_data.copy()
        result['full_text'] = cleaned_text
        result['words'] = cleaned_words
        result['dehallucination_applied'] = True
        result['original_text'] = original_text
        result['cleaning_summary'] = {
            'original_words': len(original_words),
            'cleaned_words': len(cleaned_words),
            'words_removed': len(original_words) - len(cleaned_words),
            'original_chars': len(original_text),
            'cleaned_chars': len(cleaned_text),
            'chars_removed': len(original_text) - len(cleaned_text)
        }
        
        print(f"   ✅ Cleaned: {len(cleaned_words)} words, {len(cleaned_text)} chars")
        print(f"   📝 Result: '{cleaned_text[:50]}...'")
        
        return result
    
    def process_json_file(self, json_path: str, output_path: str = None) -> None:
        """Process a JSON file containing transcriptions."""
        json_path = Path(json_path)
        if not json_path.exists():
            print(f"❌ File not found: {json_path}")
            return
        
        # Load data
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📁 Processing {json_path} ({len(data)} transcriptions)")
        
        # Process each transcription
        processed_count = 0
        for i, entry in enumerate(data):
            if 'full_text' in entry and 'words' in entry:
                audio_type = entry.get('audio_type', 'unknown')
                
                print(f"\n[{i+1}/{len(data)}] Processing entry...")
                cleaned_entry = self.dehallucinate(entry, audio_type)
                
                if cleaned_entry.get('dehallucination_applied'):
                    data[i] = cleaned_entry
                    processed_count += 1
        
        # Save results
        output_path = output_path or json_path.with_name(f"{json_path.stem}_dehallucinated.json")
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Saved dehallucinated results to: {output_path}")
        print(f"📊 Processed {processed_count}/{len(data)} transcriptions")

def main():
    """Main function for testing."""
    dehallucinator = Dehallucinator()
    
    # Test with sample data - Type 1 (ABCD)
    test_data_abcd = {
        "full_text": "A. Allez-y, entrez. B. Asseyez-vous, je vous en prie. C. Fermez la porte, s'il vous plaît. D. Merci pour ce café Sous-titrage ST' 501",
        "words": [
            {"word": "A.", "start": 0.0, "end": 0.34},
            {"word": "Allez-y,", "start": 1.3, "end": 2.16},
            {"word": "entrez.", "start": 2.2, "end": 3.04},
            {"word": "B.", "start": 5.86, "end": 6.44},
            {"word": "Asseyez-vous,", "start": 6.44, "end": 7.32},
            {"word": "je", "start": 7.36, "end": 7.76},
            {"word": "vous", "start": 7.76, "end": 7.88},
            {"word": "en", "start": 7.88, "end": 8.08},
            {"word": "prie.", "start": 8.08, "end": 8.42},
            {"word": "C.", "start": 11.1, "end": 11.68},
            {"word": "Fermez", "start": 11.68, "end": 12.26},
            {"word": "la", "start": 12.26, "end": 12.36},
            {"word": "porte,", "start": 12.36, "end": 12.64},
            {"word": "s'il", "start": 12.76, "end": 13.0},
            {"word": "vous", "start": 13.0, "end": 13.1},
            {"word": "plaît.", "start": 13.1, "end": 13.42},
            {"word": "D.", "start": 15.32, "end": 15.9},
            {"word": "Merci", "start": 16.78, "end": 17.36},
            {"word": "pour", "start": 17.36, "end": 17.64},
            {"word": "ce", "start": 17.64, "end": 17.76},
            {"word": "café", "start": 17.76, "end": 18.06},
            {"word": "Sous-titrage", "start": 18.06, "end": 18.08},  # Very short duration
            {"word": "ST'", "start": 18.08, "end": 18.09},  # Very short duration
            {"word": "501", "start": 18.09, "end": 18.10},  # Very short duration
        ],
        "audio_type": "abcd_choices"
    }
    
    # Test with sample data - Type 2 (Conversation with incomplete end)
    test_data_conversation = {
        "full_text": "Allô, bonjour M. Didier à l'appareil. Je téléphone au sujet de la livraison des articles dont la référence est 433JS. S'il n'est pas trop tard, j'aimerais ajouter deux ordinateurs supplémentaires. Il n'y a aucun problème, ce sera fait. Pourquoi M. Didier appelle-t-il ? Merci",
        "words": [
            {"word": "Allô,", "start": 0.0, "end": 0.5},
            {"word": "bonjour", "start": 0.5, "end": 1.0},
            {"word": "M.", "start": 1.0, "end": 1.2},
            {"word": "Didier", "start": 1.2, "end": 1.8},
            {"word": "à", "start": 1.8, "end": 2.0},
            {"word": "l'appareil.", "start": 2.0, "end": 2.8},
            {"word": "Je", "start": 3.0, "end": 3.2},
            {"word": "téléphone", "start": 3.2, "end": 4.0},
            {"word": "au", "start": 4.0, "end": 4.2},
            {"word": "sujet", "start": 4.2, "end": 4.8},
            {"word": "de", "start": 4.8, "end": 5.0},
            {"word": "la", "start": 5.0, "end": 5.2},
            {"word": "livraison", "start": 5.2, "end": 6.0},
            {"word": "des", "start": 6.0, "end": 6.2},
            {"word": "articles", "start": 6.2, "end": 7.0},
            {"word": "dont", "start": 7.0, "end": 7.2},
            {"word": "la", "start": 7.2, "end": 7.4},
            {"word": "référence", "start": 7.4, "end": 8.2},
            {"word": "est", "start": 8.2, "end": 8.4},
            {"word": "433JS.", "start": 8.4, "end": 9.0},
            {"word": "S'il", "start": 9.5, "end": 9.7},
            {"word": "n'est", "start": 9.7, "end": 10.0},
            {"word": "pas", "start": 10.0, "end": 10.2},
            {"word": "trop", "start": 10.2, "end": 10.4},
            {"word": "tard,", "start": 10.4, "end": 11.0},
            {"word": "j'aimerais", "start": 11.2, "end": 12.0},
            {"word": "ajouter", "start": 12.0, "end": 12.8},
            {"word": "deux", "start": 12.8, "end": 13.0},
            {"word": "ordinateurs", "start": 13.0, "end": 14.0},
            {"word": "supplémentaires.", "start": 14.0, "end": 15.0},
            {"word": "Il", "start": 15.5, "end": 15.7},
            {"word": "n'y", "start": 15.7, "end": 15.9},
            {"word": "a", "start": 15.9, "end": 16.0},
            {"word": "aucun", "start": 16.0, "end": 16.4},
            {"word": "problème,", "start": 16.4, "end": 17.0},
            {"word": "ce", "start": 17.0, "end": 17.2},
            {"word": "sera", "start": 17.2, "end": 17.4},
            {"word": "fait.", "start": 17.4, "end": 18.0},
            {"word": "Pourquoi", "start": 18.5, "end": 19.0},
            {"word": "M.", "start": 19.0, "end": 19.2},
            {"word": "Didier", "start": 19.2, "end": 19.8},
            {"word": "appelle-t-il", "start": 19.8, "end": 20.6},
            {"word": "?", "start": 20.6, "end": 21.0},
            {"word": "Merci", "start": 21.0, "end": 21.05},  # Very short duration
        ],
        "audio_type": "conversation_question"
    }
    
    print("🧪 Testing dehallucination with ABCD sample data...")
    result_abcd = dehallucinator.dehallucinate(test_data_abcd, "abcd_choices")
    
    print(f"\n📋 ABCD Summary:")
    print(f"Original: {test_data_abcd['full_text']}")
    print(f"Cleaned:  {result_abcd['full_text']}")
    print(f"Words removed: {result_abcd['cleaning_summary']['words_removed']}")
    print(f"Chars removed: {result_abcd['cleaning_summary']['chars_removed']}")
    
    print("\n" + "="*60)
    
    print("🧪 Testing dehallucination with conversation sample data...")
    result_conversation = dehallucinator.dehallucinate(test_data_conversation, "conversation_question")
    
    print(f"\n📋 Conversation Summary:")
    print(f"Original: {test_data_conversation['full_text']}")
    print(f"Cleaned:  {result_conversation['full_text']}")
    print(f"Words removed: {result_conversation['cleaning_summary']['words_removed']}")
    print(f"Chars removed: {result_conversation['cleaning_summary']['chars_removed']}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        dehallucinator = Dehallucinator()
        input_path = sys.argv[1]
        output_path = None
        if len(sys.argv) > 2:
            output_path = sys.argv[2]
        dehallucinator.process_json_file(input_path, output_path)
    else:
        main() 