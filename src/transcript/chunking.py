"""
Chunking Logic and Audio Type Detection
Handles the three types of audio content and their appropriate chunking strategies
"""

import re
from typing import List, Dict
from utils.validation import ABBREVS, post_process_transcription

# Sentence punctuation and connectors for chunking
PUNCT = {".", "?", "!"}
CONNECTORS = {
    "et", "mais", "parce", "cependant", "donc", "alors", "puis",
    "ensuite", "quand", "lorsque", "tandis", "pourtant", ","
}

def detect_audio_type(words: List[Dict]) -> str:
    """
    Detect the type of audio content based on word patterns.
    Returns: 'simple_multiple_choice', 'conversation_multiple_choice', or 'conversation_question'
    
    Enhanced logic:
    1. Simple A,B,C,D: Just options with minimal content before
    2. Conversation + A,B,C,D: Substantial conversation then options
    3. Conversation + question: Conversation ending with ? or incomplete sentence
    """
    if not words:
        return 'conversation_question'
    
    # Reconstruct text from words
    text = ''.join(w["word"] for w in words)
    
    # Look for multiple choice options with positions
    options_found = set()
    option_positions = []
    
    for i, word in enumerate(words):
        word_text = word["word"].strip()
        if re.match(r'^[A-D]\.', word_text):
            options_found.add(word_text[0])  # Store just the letter
            option_positions.append(i)
    
    # Check if we have a complete set of choices (at least A,B and ideally A,B,C,D)
    has_multiple_choices = len(options_found) >= 2
    
    if has_multiple_choices:
        # We have multiple choice options - determine which type
        first_option_pos = min(option_positions)
        
        # Calculate content before first option
        words_before_options = first_option_pos
        
        # Get text before first option to analyze content density
        if first_option_pos > 0:
            text_before_options = ''.join(w["word"] for w in words[:first_option_pos]).strip()
            sentences_before = len(re.findall(r'[.!?]+', text_before_options))
            word_count_before = len(text_before_options.split())
        else:
            word_count_before = 0
            sentences_before = 0
        
        print(f"   📊 Analysis: {word_count_before} words, {sentences_before} sentences before options")
        print(f"   📊 Options found: {sorted(options_found)} at positions {option_positions}")
        
        # Decision logic based on content before options
        if word_count_before < 15:
            # Very little content before options - likely simple multiple choice
            return 'simple_multiple_choice'
        elif word_count_before < 40:
            # Moderate content - check for conversation patterns
            if sentences_before >= 2 or '?' in text_before_options:
                return 'conversation_multiple_choice'
            else:
                return 'simple_multiple_choice'
        else:
            # Substantial content before options - definitely conversation + multiple choice
            return 'conversation_multiple_choice'
    
    else:
        # No multiple choice options found
        # Check for question patterns or incomplete sentences
        
        # Look for question marks
        has_question = '?' in text
        
        # Look for incomplete sentence patterns (common in educational content)
        incomplete_patterns = [
            r'\b(il|elle|on|nous|vous|ils|elles)\s+(est|va|fait|dit|a|sera|serait)\s*\.{2,}',
            r'\b(le|la|les|un|une|des)\s+\w+\s*\.{2,}',
            r'\b\w+\s*\.{2,}$',  # Any word followed by ellipsis at end
            r'\b\w+\s*…$'       # Any word followed by … at end
        ]
        
        has_incomplete = any(re.search(pattern, text, re.IGNORECASE) for pattern in incomplete_patterns)
        
        # Check for typical conversation/dialogue markers
        conversation_markers = [
            r'\b(je|tu|il|elle|nous|vous|ils|elles)\b',
            r'\b(dit|dis|disent|répond|réponds|répondent)\b',
            r'\b(bonjour|salut|comment|pourquoi|quand|où)\b'
        ]
        
        has_conversation = any(re.search(marker, text, re.IGNORECASE) for marker in conversation_markers)
        
        if has_question:
            print(f"   📊 Found question mark - type: conversation_question")
        elif has_incomplete:
            print(f"   📊 Found incomplete sentence pattern - type: conversation_question")
        elif has_conversation and len(words) > 10:
            print(f"   📊 Found conversation markers - type: conversation_question")
        else:
            print(f"   📊 Default classification - type: conversation_question")
        
        return 'conversation_question'

def chunk_words(
    words: List[Dict],
    *,
    min_len: int = 8,
    soft_max_len: int = 25,
    hard_max_len: int = 40,
    pause: float = 0.6
) -> List[List[Dict]]:
    """
    Split Whisper word dicts into semantic chunks optimized for three question types:
    1. Simple A,B,C,D (each choice separate chunk)
    2. Conversation + A,B,C,D (sentence chunks + each choice separate)
    3. Conversation + question (sentence chunks, preserve questions and incomplete sentences)
    """
    if not words:
        return []
    
    # First, apply A,B,C,D formatting fixes to the words themselves
    print(f"   🔧 Fixing A,B,C,D formatting in word list...")
    
    # Handle the pattern where A,B,C,D letters are separate words from their content
    i = 0
    while i < len(words) - 1:
        current_word = words[i]["word"].strip()
        next_word = words[i + 1]["word"].strip()
        
        # Check if current word is just a letter A,B,C, or D
        if re.match(r'^[A-D]$', current_word):
            # Combine the letter with the next word and add proper formatting
            combined_text = f"{current_word}. {next_word}"
            words[i]["word"] = combined_text
            print(f"      Fixed: '{current_word}' + '{next_word}' -> '{combined_text}'")
            # Remove the next word since we combined it
            words.pop(i + 1)
        else:
            # Also handle cases where they're already combined but need period/space fixes
            word_text = current_word
            if re.match(r'^[A-D][A-Z]', word_text):  # "ABonjour" -> "A. Bonjour"
                letter = word_text[0]
                rest = word_text[1:]
                new_word = f"{letter}. {rest}"
                words[i]["word"] = new_word
                print(f"      Fixed: '{word_text}' -> '{new_word}'")
            elif re.match(r'^[A-D] ', word_text):  # "A Bonjour" -> "A. Bonjour"
                letter = word_text[0]
                rest = word_text[2:]
                new_word = f"{letter}. {rest}"
                words[i]["word"] = new_word
                print(f"      Fixed: '{word_text}' -> '{new_word}'")
            elif re.match(r'^[A-D]\.', word_text) and len(word_text) > 2 and word_text[2] != ' ':  # "A.Bonjour" -> "A. Bonjour"
                letter = word_text[0]
                rest = word_text[2:]
                new_word = f"{letter}. {rest}"
                words[i]["word"] = new_word
                print(f"      Fixed: '{word_text}' -> '{new_word}'")
            i += 1
    
    # Apply post-processing cleanup
    full_text = ''.join(w["word"] for w in words)
    cleaned_text = post_process_transcription(full_text)
    
    # If text was significantly cleaned, filter the words accordingly
    if len(cleaned_text) < len(full_text) * 0.9:  # More than 10% reduction
        print(f"   🗑️ Filtering words: {len(full_text)} -> {len(cleaned_text)} chars")
        words = _filter_words_based_on_cleaned_text(words, full_text, cleaned_text)
        print(f"   ✓ Using {len(words)} carefully filtered words")
    
    if not words:
        return []
    
    # Detect audio type to determine chunking strategy
    audio_type = detect_audio_type(words)
    print(f"   📝 Audio type: {audio_type}")
    
    # Find multiple choice options positions
    choice_positions = []
    for i, word in enumerate(words):
        word_text = word["word"].strip()
        if re.match(r'^[A-D]\.', word_text):
            choice_positions.append(i)
            print(f"   🔤 Found choice at position {i}: '{word_text}'")
    
    # Determine content boundary and trim if necessary
    content_end_pos = _determine_content_boundary(words, audio_type, choice_positions)
    
    # Trim words to content boundary
    if content_end_pos < len(words):
        removed_words = [w["word"].strip() for w in words[content_end_pos:content_end_pos+5]]
        print(f"   🗑️ Trimming {len(words) - content_end_pos} words after content: {' '.join(removed_words)}...")
        words = words[:content_end_pos]
    
    if not words:
        return []
    
    # Apply type-specific chunking
    if audio_type == 'simple_multiple_choice':
        return _chunk_simple_multiple_choice(words, choice_positions)
    elif audio_type == 'conversation_multiple_choice':
        return _chunk_conversation_multiple_choice(words, choice_positions, min_len, soft_max_len, hard_max_len, pause)
    else:  # conversation_question
        return _chunk_conversation_question(words, min_len, soft_max_len, hard_max_len, pause)

def _filter_words_based_on_cleaned_text(words: List[Dict], full_text: str, cleaned_text: str) -> List[Dict]:
    """Filter words based on post-processed text, preserving educational content."""
    # Find the end of meaningful content in the original text
    meaningful_end_in_full_text = len(full_text)
    
    # Look for very specific hallucination patterns at the end
    end_hallucination_patterns = [
        'sous-titrage société radio-canada',
        'sous titrage société radio canada',
        'société radio-canada'
    ]
    
    for pattern in end_hallucination_patterns:
        pattern_pos = full_text.lower().rfind(pattern)
        if pattern_pos > 0 and pattern_pos > len(full_text) * 0.7:  # Only if in last 30%
            meaningful_end_in_full_text = pattern_pos
            print(f"   🎯 Found end hallucination pattern at position {pattern_pos}: '{pattern}'")
            break
    
    # Only filter words that come after the meaningful content
    filtered_words = []
    current_text_pos = 0
    
    for word in words:
        word_text = word["word"]
        word_start_in_text = full_text.find(word_text, current_text_pos)
        
        if word_start_in_text >= 0:
            if word_start_in_text < meaningful_end_in_full_text:
                # This word is before the hallucination cutoff - keep it
                filtered_words.append(word)
                current_text_pos = word_start_in_text + len(word_text)
            else:
                # This word is after the meaningful content - only filter if it's clearly hallucination
                word_lower = word_text.lower().strip('.,!?;:"-')
                if word_lower in ['sous-titrage', 'société', 'radio-canada', 'sous', 'titrage', 'radio', 'canada']:
                    print(f"   🗑️ Filtered end hallucination word: '{word_text}'")
                else:
                    # Not a clear hallucination word - keep it to preserve questions
                    filtered_words.append(word)
                    current_text_pos = word_start_in_text + len(word_text)
        else:
            # Word not found in cleaned text - be very selective about filtering
            word_lower = word_text.lower().strip('.,!?;:"-')
            
            # Only filter very specific hallucination words
            very_specific_hallucinations = [
                'sous-titrage', 'sous', 'titrage', 'société', 'radio-canada', 'radio', 'canada',
                'transcription', 'automatique', 'fin', 'début'
            ]
            
            if word_lower in very_specific_hallucinations:
                print(f"   🗑️ Filtered specific hallucination word: '{word_text}'")
            else:
                # Not a specific hallucination - keep it (could be part of a question)
                filtered_words.append(word)
    
    return filtered_words

def _determine_content_boundary(words: List[Dict], audio_type: str, choice_positions: List[int]) -> int:
    """Determine where meaningful content ends based on audio type."""
    content_end_pos = len(words)
    
    if audio_type == 'simple_multiple_choice':
        # Type 1: Just A,B,C,D - content ends after last choice
        if choice_positions:
            last_choice_start = choice_positions[-1]
            # Look for natural end after the choice
            for i in range(last_choice_start + 1, min(len(words), last_choice_start + 15)):
                # Check for hallucination words
                word_text = words[i]["word"].strip().lower()
                if any(hall in word_text for hall in ['sous-titrage', 'société', 'radio-canada']):
                    content_end_pos = i
                    break
            else:
                content_end_pos = min(len(words), last_choice_start + 10)
            
            print(f"   🎯 Simple multiple choice: content ends at position {content_end_pos}")
    
    elif audio_type == 'conversation_multiple_choice':
        # Type 2: Conversation + A,B,C,D - content ends after last choice
        if choice_positions:
            last_choice_start = choice_positions[-1]
            for i in range(last_choice_start + 1, min(len(words), last_choice_start + 15)):
                word_text = words[i]["word"].strip().lower()
                if any(hall in word_text for hall in ['sous-titrage', 'société', 'radio-canada']):
                    content_end_pos = i
                    break
            else:
                content_end_pos = min(len(words), last_choice_start + 10)
            
            print(f"   🎯 Conversation + choices: content ends at position {content_end_pos}")
    
    else:  # conversation_question
        # Type 3: Conversation + question - be very conservative about trimming
        last_question_pos = -1
        for i, word in enumerate(words):
            word_text = word["word"].strip()
            if word_text.endswith('?'):
                last_question_pos = i
            elif i == len(words) - 1 and re.search(r'\.\.\.$|…$', word_text):
                last_question_pos = i
        
        if last_question_pos >= 0:
            # Only trim if there's MASSIVE repetitive hallucination
            if last_question_pos + 50 < len(words):
                remaining_words = words[last_question_pos + 50:]
                remaining_text = ''.join(w["word"] for w in remaining_words[:20]).lower()
                
                if ('sous-titrage société radio-canada' in remaining_text and 
                    len(remaining_words) > 20):
                    content_end_pos = last_question_pos + 50
                    print(f"   🎯 Conversation + question: found massive hallucination after question")
                else:
                    print(f"   🎯 Conversation + question: preserving all content")
            else:
                print(f"   🎯 Conversation + question: content ends near question")
        else:
            print(f"   🎯 No clear ending found, keeping all content")
    
    return content_end_pos

def _chunk_simple_multiple_choice(words: List[Dict], choice_positions: List[int]) -> List[List[Dict]]:
    """Type 1: Simple A,B,C,D - each choice gets its own chunk"""
    chunks = []
    
    if not choice_positions:
        return [words]
    
    current_pos = 0
    
    for i, choice_start in enumerate(choice_positions):
        # Add content before this choice (if any)
        if choice_start > current_pos:
            pre_choice_words = words[current_pos:choice_start]
            if pre_choice_words:
                chunks.append(pre_choice_words)
        
        # Find the end of this choice
        if i < len(choice_positions) - 1:
            choice_end = choice_positions[i + 1]
        else:
            choice_end = len(words)
        
        # Add the choice as its own chunk
        choice_chunk = words[choice_start:choice_end]
        if choice_chunk:
            chunks.append(choice_chunk)
        
        current_pos = choice_end
    
    print(f"   🔤 Created {len(chunks)} chunks for simple multiple choice")
    return chunks

def _chunk_conversation_multiple_choice(words: List[Dict], choice_positions: List[int], min_len: int, soft_max_len: int, hard_max_len: int, pause: float) -> List[List[Dict]]:
    """Type 2: Conversation + A,B,C,D - sentence chunks for conversation, separate chunks for each choice"""
    chunks = []
    
    if not choice_positions:
        return _chunk_conversation_question(words, min_len, soft_max_len, hard_max_len, pause)
    
    # Split into conversation part and choices part
    first_choice = choice_positions[0]
    conversation_words = words[:first_choice]
    choices_words = words[first_choice:]
    
    # Chunk the conversation part normally
    if conversation_words:
        conversation_chunks = _chunk_conversation_question(conversation_words, min_len, soft_max_len, hard_max_len, pause)
        chunks.extend(conversation_chunks)
    
    # Handle choices - each choice gets its own chunk
    choice_positions_adjusted = [pos - first_choice for pos in choice_positions]
    choice_chunks = _chunk_simple_multiple_choice(choices_words, choice_positions_adjusted)
    chunks.extend(choice_chunks)
    
    print(f"   💬 Created {len(chunks)} chunks for conversation + multiple choice")
    return chunks

def _chunk_conversation_question(words: List[Dict], min_len: int, soft_max_len: int, hard_max_len: int, pause: float) -> List[List[Dict]]:
    """Type 3: Conversation + question - sentence-level chunks, preserve questions and incomplete sentences"""
    chunks = []
    current_chunk = []
    prev_end = None
    
    for i, word in enumerate(words):
        current_chunk.append(word)
        
        word_text = word["word"].strip()
        word_lower = word_text.lower()
        end_punct = word_text[-1] if word_text else ""
        is_abbrev = word_text in ABBREVS
        long_pause = prev_end is not None and word["start"] - prev_end > pause
        
        # CRITICAL: Check if this word ends with a question mark
        ends_with_question = word_text.endswith('?')
        
        # Decide if we should split here
        should_split = False
        
        # 1. PRIORITY: Question marks ALWAYS force a split
        if ends_with_question and not is_abbrev:
            should_split = True
            print(f"      🔍 Question detected: '{word_text}' - forcing chunk split")
            
        # 2. Other sentence-ending punctuation
        elif end_punct in PUNCT and not is_abbrev and not ends_with_question:
            should_split = len(current_chunk) >= min_len
            
        # 3. Long pause in audio
        elif long_pause:
            should_split = len(current_chunk) >= min_len
            
        # 4. Connector words (after soft limit)
        elif word_lower in CONNECTORS and len(current_chunk) >= soft_max_len:
            should_split = True
            
        # 5. Hard limit
        elif len(current_chunk) >= hard_max_len:
            should_split = True
        
        # 6. Special handling for incomplete sentences at the end
        elif i == len(words) - 1 and re.search(r'\.\.\.$|…$', word_text):
            should_split = True
        
        if should_split:
            chunks.append(current_chunk)
            current_chunk = []
        
        prev_end = word["end"]
    
    # Add remaining words
    if current_chunk:
        chunks.append(current_chunk)
    
    # Post-processing: Ensure any chunk with BOTH statement and question gets split
    refined_chunks = []
    for chunk in chunks:
        if len(chunk) <= 1:
            refined_chunks.append(chunk)
            continue
                
        # Check if this chunk contains MULTIPLE sentences, including a question
        question_positions = []
        for j, word in enumerate(chunk):
            word_text = word["word"].strip()
            if word_text.endswith('?'):
                question_positions.append(j)
        
        if len(question_positions) >= 1:
            # Split to isolate questions
            current_pos = 0
            
            for q_pos in question_positions:
                # Find the start of the question sentence
                question_start = q_pos
                
                # Look backwards for sentence boundary
                for k in range(q_pos - 1, -1, -1):
                    prev_word = chunk[k]["word"].strip()
                    if prev_word.endswith('.') or prev_word.endswith('!'):
                        question_start = k + 1
                        break
                    elif k == 0:
                        question_start = 0
                        break
                
                # Add content before the question (if any)
                if question_start > current_pos:
                    pre_question_chunk = chunk[current_pos:question_start]
                    if pre_question_chunk:
                        refined_chunks.append(pre_question_chunk)
                
                # Add the question as its own chunk
                question_chunk = chunk[question_start:q_pos + 1]
                if question_chunk:
                    refined_chunks.append(question_chunk)
                
                current_pos = q_pos + 1
            
            # Add any remaining content after the last question
            if current_pos < len(chunk):
                remaining_chunk = chunk[current_pos:]
                if remaining_chunk:
                    refined_chunks.append(remaining_chunk)
        else:
            refined_chunks.append(chunk)
    
    print(f"   💬 Created {len(refined_chunks)} chunks for conversation + question")
    return refined_chunks

def trim_chunks_after_question(chunks, words):
    """
    For the three question types, ensure no content appears after the meaningful end.
    CRITICAL: Be very careful not to trim legitimate final questions!
    """
    if not chunks or not words:
        return chunks
    
    audio_type = detect_audio_type(words)
    
    # For Types 1 & 2 (multiple choice), chunking already handled the trimming
    if audio_type in ['simple_multiple_choice', 'conversation_multiple_choice']:
        print(f"   ✓ No post-chunking trimming needed for {audio_type}")
        return chunks
    
    # For Type 3 (conversation + question), be very careful about trimming
    if audio_type == 'conversation_question':
        # Look for chunks that contain hallucination patterns
        hallucination_patterns = [
            'sous-titrage société radio-canada',
            'sous titrage société radio canada'
        ]
        
        for i, chunk in enumerate(chunks):
            chunk_text = chunk["text"].lower()
            
            # Check if this chunk contains hallucination
            hallucination_found = False
            for pattern in hallucination_patterns:
                if pattern in chunk_text:
                    hallucination_found = True
                    break
            
            if hallucination_found:
                # Check if this chunk also contains a question
                original_text = chunk["text"]
                question_pos = original_text.find('?')
                
                if question_pos >= 0:
                    # Split to keep only the question part
                    question_part = original_text[:question_pos + 1].strip()
                    
                    if question_part and len(question_part) > 5:
                        print(f"   🔧 Preserving question from chunk with hallucination")
                        chunks[i]["text"] = question_part
                        # Remove all chunks after this one
                        removed_count = len(chunks) - i - 1
                        if removed_count > 0:
                            print(f"   🗑️ Trimming {removed_count} chunk(s) after preserved question")
                        return chunks[:i + 1]
                    else:
                        # Question is too short, remove this chunk and everything after
                        removed_count = len(chunks) - i
                        print(f"   🗑️ Trimming {removed_count} chunk(s) containing hallucination patterns")
                        return chunks[:i]
                else:
                    # Hallucination but no question - remove it and everything after
                    removed_count = len(chunks) - i
                    print(f"   🗑️ Trimming {removed_count} chunk(s) containing hallucination patterns")
                    return chunks[:i]
        
        print(f"   ✓ No obvious hallucination patterns found, preserving all {len(chunks)} chunks")
    
    return chunks 