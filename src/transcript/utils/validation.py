"""
Transcription Validation and Post-Processing
Handles validation, hallucination detection, and text cleanup
"""

import re
from typing import List, Dict

# French abbreviations that end in a period but don't close a sentence
ABBREVS = {
    "M.", "Mme.", "Dr.", "Pr.", "p.", "pp.", "c.-à-d.", "etc.", "cf.",
    "av.", "apr.", "env.", "ex.", "janv.", "févr.", "mars.", "avr.",
    "mai.", "juin.", "juil.", "août.", "sept.", "oct.", "nov.", "déc."
}

def validate_transcription(text: str, confidence_threshold: float = 0.5, min_length: int = 3) -> bool:
    """
    Enhanced validation for transcription quality using heuristics.
    Returns True if the transcription appears valid.
    Uses ratio-based validation to avoid false positives with substantial content.
    """
    # Check for empty or too short text
    if not text or len(text.strip()) < min_length:
        return False
        
    words = text.split()
    if len(words) == 0:
        return False
    
    # For substantial content (>50 words), be more lenient about hallucination patterns
    is_substantial_content = len(words) > 50
    
    # Check for common French hallucination patterns 
    text_lower = text.lower().strip()
    
    # Very common French hallucinations
    french_hallucination_patterns = [
        "sous-titrage société radio-canada",
        "sous titrage société radio canada",
        "sous-titrage st'",
        "sous titrage st'",
        "merci beaucoup au revoir",
        "bonjour merci au revoir",
        "transcription automatique",
        "fin de transcription",
        "début de transcription"
    ]
    
    # Count hallucination patterns found
    hallucination_matches = 0
    total_hallucination_words = 0
    
    for pattern in french_hallucination_patterns:
        if pattern in text_lower:
            hallucination_matches += 1
            total_hallucination_words += len(pattern.split())
            print(f"   ⚠ Detected French hallucination pattern: '{pattern}'")
    
    # For substantial content, only reject if hallucination ratio is too high
    if is_substantial_content:
        hallucination_ratio = total_hallucination_words / len(words)
        if hallucination_ratio > 0.3:  # More than 30% hallucination words
            print(f"   ❌ High hallucination ratio: {hallucination_ratio:.1%} ({total_hallucination_words}/{len(words)} words)")
            return False
        elif hallucination_matches > 0:
            print(f"   ⚠ Contains hallucination patterns but substantial content detected ({len(words)} words)")
            print(f"   ✓ Hallucination ratio acceptable: {hallucination_ratio:.1%}")
            return True  # Accept substantial content with low hallucination ratio
    else:
        # For short content, be stricter about hallucination patterns
        if hallucination_matches > 0:
            print(f"   ❌ Short content with hallucination patterns rejected")
            return False
    
    # Check if the text consists only of common hallucination words
    hallucination_words = {
        "sous-titrage", "sous", "titrage", "société", "radio-canada", "radio", "canada",
        "merci", "bonjour", "au", "revoir", "transcription", "automatique", "fin", "début",
        "st'", "501", "502", "503", "subtitles", "caption", "captions"
    }
    
    # If more than 70% of words are hallucination words, it's likely a hallucination
    text_words_lower = [w.lower().strip('.,!?;:"-') for w in words]
    hallucination_word_count = sum(1 for w in text_words_lower if w in hallucination_words)
    hallucination_word_ratio = hallucination_word_count / len(words)
    
    if len(words) > 2 and hallucination_word_ratio > 0.7:
        print(f"   ❌ High hallucination word ratio: {hallucination_word_count}/{len(words)} ({hallucination_word_ratio:.1%})")
        return False
    elif hallucination_word_ratio > 0.3 and len(words) < 20:
        print(f"   ❌ Moderate hallucination in short content: {hallucination_word_ratio:.1%}")
        return False
        
    # Check for common hallucination patterns
    
    # 1. Check for excessive repetition of single words
    word_counts = {}
    for word in words:
        word_lower = word.lower().strip('.,!?;:')
        word_counts[word_lower] = word_counts.get(word_lower, 0) + 1
    
    # If any word appears more than 40% of the time, it's likely hallucination
    if len(words) > 5:
        max_word_freq = max(word_counts.values())
        if max_word_freq / len(words) > 0.4:
            print(f"   ❌ Excessive word repetition detected")
            return False
    
    # 2. Check for repeated sequences of words - BE MORE LENIENT FOR SUBSTANTIAL CONTENT
    if is_substantial_content:
        # For substantial content, allow more repetition (educational content often repeats concepts)
        if has_excessive_repetition(text, max_repeat=4):  # Allow up to 4 repetitions instead of 2
            print(f"   ⚠ Some repetition detected in substantial content, but allowing due to educational context")
            # Don't return False here - continue with other checks
        else:
            print(f"   ✓ Repetition levels acceptable for substantial content")
    else:
        # For short content, be stricter about repetition
        if has_excessive_repetition(text, max_repeat=2):
            print(f"   ❌ Excessive phrase repetition detected in short content")
            return False
    
    # 3. Check for nonsensical patterns
    # Long sequences of uppercase (often hallucinations)
    if re.search(r'[A-Z]{6,}', text):
        print(f"   ❌ Suspicious uppercase sequence detected")
        return False
    
    # 4. Check for reasonable word length distribution
    if len(words) > 3:
        avg_word_length = sum(len(w.strip('.,!?;:')) for w in words) / len(words)
        if avg_word_length > 20 or avg_word_length < 1:  # Unrealistic word lengths
            print(f"   ❌ Unrealistic average word length: {avg_word_length:.1f}")
            return False
    
    # 5. Check for excessive punctuation repetition
    punct_pattern = re.findall(r'[.!?]{3,}', text)
    if len(punct_pattern) > 0:
        print(f"   ❌ Excessive punctuation repetition detected")
        return False
        
    # 6. Check for single character repetition (e.g., "a a a a a")
    if re.search(r'\b(\w)\s+\1\s+\1', text):
        print(f"   ❌ Single character repetition detected")
        return False
    
    # 7. Check for gibberish patterns (consonants without vowels)
    words_no_punct = [w.strip('.,!?;:') for w in words if len(w.strip('.,!?;:')) > 2]
    if len(words_no_punct) > 0:
        vowelless_count = sum(1 for w in words_no_punct if not re.search(r'[aeiouAEIOUàéèêëïîôöûüù]', w))
        if vowelless_count / len(words_no_punct) > 0.7:  # Too many words without vowels
            print(f"   ❌ Too many words without vowels: {vowelless_count}/{len(words_no_punct)}")
            return False
    
    # If we get here, the transcription passes validation
    if is_substantial_content:
        print(f"   ✅ Substantial content validated ({len(words)} words)")
    
    return True

def has_excessive_repetition(text: str, max_repeat: int = 2) -> bool:
    """
    Enhanced detection of repetitive patterns that indicate hallucinations.
    Returns True if any phrase is repeated excessively.
    """
    words = text.split()
    n = len(words)
    
    if n < 6:  # Too short to have meaningful repetition
        return False
    
    # Check for repeated phrases of different lengths
    for size in range(2, min(8, n//3)+1):  # phrase size from 2 to 8
        seen = {}
        for i in range(n - size + 1):
            phrase = ' '.join(words[i:i+size]).lower()
            seen[phrase] = seen.get(phrase, 0) + 1
            if seen[phrase] > max_repeat:
                return True
    
    # Check for patterns like "word word word" (same word repeated)
    for i in range(n - 2):
        if words[i].lower() == words[i+1].lower() == words[i+2].lower():
            return True
    
    return False

def validate_word_timestamps(words: List[Dict]) -> bool:
    """
    Validate word-level timestamps for signs of hallucination.
    Returns True if timestamps seem realistic, False if they indicate hallucination.
    """
    if not words or len(words) < 2:
        return True
        
    suspicious_patterns = 0
    
    for i, word in enumerate(words):
        start = word.get("start", 0)
        end = word.get("end", 0)
        duration = end - start
        word_text = word.get("word", "").strip()
        probability = word.get("probability", 1.0)
        
        # Check for words with unrealistic durations
        if duration > 15 and len(word_text) < 8:
            print(f"   ⚠ Word '{word_text}' has unrealistic duration: {duration:.2f}s")
            suspicious_patterns += 1
        
        # Very short duration for long words (also suspicious)
        if duration < 0.05 and len(word_text) > 5:
            print(f"   ⚠ Word '{word_text}' has too short duration: {duration:.2f}s")
            suspicious_patterns += 1
    
        # Check for identical timestamps (multiple words at same time)
        if i > 0:
            prev_word = words[i-1]
            if start == prev_word.get("end", -1) == end:
                suspicious_patterns += 1
        
        # Check for very low probability scores
        if probability < 0.1:
            suspicious_patterns += 1
    
    # Check for large gaps between words (silence hallucination indicator)
    for i in range(1, len(words)):
        prev_end = words[i-1].get("end", 0)
        current_start = words[i].get("start", 0)
        gap = current_start - prev_end
        
        # Gap of more than 10 seconds between words is suspicious
        if gap > 10:
            print(f"   ⚠ Large gap between words: {gap:.2f}s")
            suspicious_patterns += 1
    
    # If too many suspicious patterns, consider it a hallucination
    suspicious_ratio = suspicious_patterns / len(words)
    if suspicious_ratio > 0.3:  # More than 30% of words have suspicious patterns
        print(f"   ⚠ High suspicious pattern ratio: {suspicious_ratio:.1%}")
        return False
    
    return True

def detect_silence_hallucination(text: str, audio_duration: float) -> bool:
    """
    Detect if transcription is likely a hallucination for silent/very quiet audio.
    Enhanced to work with the improved validation system and avoid false positives.
    """
    if audio_duration < 3:  # Very short audio
        return False
    
    words = text.split()
    text_lower = text.lower()
    
    # For substantial content (>50 words), be much more lenient
    is_substantial_content = len(words) > 50
    
    # Check for French subtitle/credit hallucinations (very common)
    subtitle_patterns = [
        "sous-titrage",
        "radio-canada", 
        "société radio-canada",
        "transcription automatique",
        "fin de transcription"
    ]
    
    pattern_matches = sum(1 for pattern in subtitle_patterns if pattern in text_lower)
    
    if pattern_matches > 0:
        if is_substantial_content:
            # For substantial content, check if it's mostly subtitle patterns
            subtitle_word_count = sum(len(pattern.split()) for pattern in subtitle_patterns if pattern in text_lower)
            subtitle_ratio = subtitle_word_count / len(words)
            
            if subtitle_ratio > 0.4:  # More than 40% subtitle words
                print(f"   ⚠ High subtitle pattern ratio: {subtitle_ratio:.1%}")
                return True
            else:
                print(f"   ℹ Subtitle patterns detected but content ratio acceptable: {subtitle_ratio:.1%}")
                return False
        else:
            # For short content, any subtitle pattern is suspicious
            print(f"   ⚠ Detected subtitle/credit patterns in short content")
            return True
    
    # If we have very few words for long audio, might be real silence
    words_per_second = len(words) / audio_duration
    if words_per_second < 0.3:  # Less than 0.3 words per second
        # But check if those few words are repeated (hallucination)
        if len(words) > 2 and has_excessive_repetition(text, max_repeat=1):
            print(f"   ⚠ Low word density with repetition: {words_per_second:.2f} w/s")
            return True
        elif len(words) < 10:  # Very few words for long audio
            print(f"   ⚠ Very low word count for audio duration: {len(words)} words in {audio_duration:.1f}s")
            return True
    
    # Common hallucination phrases for silence (only for shorter content)
    if not is_substantial_content:
        hallucination_phrases = [
            "merci", "merci beaucoup", "au revoir", "bonjour",
            "thank you", "thanks", "bye", "hello",
            "sous-titres", "subtitle", "caption"
        ]
        
        if any(phrase in text_lower for phrase in hallucination_phrases) and len(words) < 5:
            print(f"   ⚠ Common hallucination phrases in short content")
            return True
    
    return False

def post_process_transcription(text: str) -> str:
    """
    Enhanced cleanup of common transcription errors and hallucinations.
    Focuses on trimming end-of-transcript hallucinations while preserving all educational content.
    CRITICAL: Preserve all questions ending with '?' - these are legitimate educational content.
    """
    if not text:
        return ""
    
    original_text = text
    
    # 1. First, fix A,B,C,D formatting issues before trimming
    # Fix missing periods and spaces in multiple choice options
    text = re.sub(r'\b([A-D])([A-Z])', r'\1. \2', text)  # "ABonjour" -> "A. Bonjour"
    text = re.sub(r'\b([A-D]) ', r'\1. ', text)  # "A Bonjour" -> "A. Bonjour"
    text = re.sub(r'\b([A-D])\.([A-Z])', r'\1. \2', text)  # "A.Bonjour" -> "A. Bonjour"
    
    # 2. Enhanced repetition cleanup - handle excessive "Je ne sais pas" type repetitions
    # Look for patterns like "Je ne sais pas. Je ne sais pas. Je ne sais pas..."
    
    # Find all repetitive patterns of 2-5 words
    words = text.split()
    cleaned_words = []
    i = 0
    
    while i < len(words):
        found_repetition = False
        
        # Check for repetitive patterns of different lengths
        for pattern_len in range(2, min(6, len(words) - i + 1)):
            if i + pattern_len * 3 <= len(words):  # Need at least 3 repetitions to be suspicious
                # Get the potential pattern
                pattern = words[i:i + pattern_len]
                
                # Count how many times this pattern repeats consecutively
                repetition_count = 1
                check_pos = i + pattern_len
                
                while check_pos + pattern_len <= len(words):
                    if words[check_pos:check_pos + pattern_len] == pattern:
                        repetition_count += 1
                        check_pos += pattern_len
                    else:
                        break
                
                # If we found 4+ repetitions, this is likely a hallucination
                if repetition_count >= 4:
                    pattern_text = ' '.join(pattern)
                    print(f"   🔍 Found excessive repetition: '{pattern_text}' repeated {repetition_count} times")
                    
                    # Keep only 1-2 instances of the pattern
                    keep_instances = 2 if repetition_count > 6 else 1
                    for _ in range(keep_instances):
                        cleaned_words.extend(pattern)
                    
                    # Skip all the repetitions
                    i = check_pos
                    found_repetition = True
                    break
        
        if not found_repetition:
            cleaned_words.append(words[i])
            i += 1
    
    text = ' '.join(cleaned_words)
    
    # Continue with other post-processing steps...
    # (The rest of the function would continue with the other cleanup steps)
    
    # Final cleanup
    text = re.sub(r'\s+', ' ', text)  # Normalize spaces
    text = re.sub(r'[^\w\sÀ-ÿ.,!?;:\'"()-]', '', text)  # Remove problematic characters
    text = re.sub(r'[.!?]{3,}', '.', text)  # Fix excessive punctuation
    text = re.sub(r'[\s.,-]+$', '', text)  # Remove trailing artifacts
    
    result = text.strip()
    
    # Log significant changes
    if len(original_text) - len(result) > 20:
        print(f"   🧹 Post-processing: removed {len(original_text) - len(result)} characters")
    
    return result 