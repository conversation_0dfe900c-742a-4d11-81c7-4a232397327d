"""
Audio Processing and Preprocessing
Handles audio preprocessing for better Whisper transcription results
"""

import os
import tempfile
from pydub import AudioSegment

def preprocess_audio_for_whisper(audio_path, output_path=None):
    """
    EXTREME anti-truncation preprocessing for Whisper transcription.
    Maximum effort to prevent any truncation whatsoever:
    - Massive silence padding
    - Content duplication for context
    - Extreme volume normalization
    - Multiple format optimizations
    """
    if output_path is None:
        output_path = tempfile.mktemp(suffix=".wav")
    
    print(f"🔧 EXTREME preprocessing for: {os.path.basename(audio_path)}")
    
    try:
        # Load audio
        audio = AudioSegment.from_file(audio_path)
        original_audio = audio  # Keep original for duplication
        
        # Get duration
        duration_sec = len(audio) / 1000.0
        print(f"   Original duration: {duration_sec:.2f} seconds")
        
        # EXTREME preprocessing for all short audio (< 60 seconds)
        if duration_sec < 60:
            print("   Applying EXTREME anti-truncation preprocessing...")
            
            # 1. Normalize audio very aggressively
            audio = audio.normalize()
            
            # 2. Apply very strong compression to even out all volume levels
            audio = audio.compress_dynamic_range(threshold=-35.0, ratio=10.0, attack=0.5, release=15.0)
            
            # 3. Boost volume significantly - be very aggressive
            current_db = audio.max_dBFS
            if current_db < -8:
                boost = min(25, abs(current_db) - 3)  # Boost to around -3dB
                audio = audio + boost
                print(f"   Applied EXTREME {boost}dB volume boost (from {current_db:.1f}dB)")
            
            # 4. Add MASSIVE silence padding - much more than before
            if duration_sec < 15:
                # For very short audio, use massive padding
                silence_duration = max(12000, int(duration_sec * 2000))  # At least 12s, or 200% of duration
                print(f"   Using MASSIVE silence padding for very short audio")
            elif duration_sec < 30:
                silence_duration = max(10000, int(duration_sec * 1000))  # At least 10s, or 100% of duration
            else:
                silence_duration = max(8000, int(duration_sec * 800))   # At least 8s, or 80% of duration
            
            silence = AudioSegment.silent(duration=silence_duration)
            
            # 5. Create the padded audio
            audio = silence + audio + silence
            total_duration = len(audio) / 1000.0
            print(f"   Added {silence_duration*2/1000:.1f}s silence padding (total: {total_duration:.1f}s)")
            
            # 6. For short audio, TRIPLE the content to give Whisper maximum context
            if duration_sec < 20:
                print("   TRIPLING audio content for maximum context...")
                # Create normalized original
                original_normalized = original_audio.normalize()
                if original_normalized.max_dBFS < -8:
                    original_normalized = original_normalized + min(25, abs(original_normalized.max_dBFS) - 3)
                
                # Add the content 2 more times with pauses
                pause = AudioSegment.silent(duration=2000)  # 2 second pause
                audio = audio + pause + (silence + original_normalized + silence)
                audio = audio + pause + (silence + original_normalized + silence)
                
                total_duration = len(audio) / 1000.0
                print(f"   Total duration after tripling: {total_duration:.1f}s")
            
            # 7. For very short audio, add an extra quiet version at the end
            elif duration_sec < 30:
                print("   Adding extra quiet version for context...")
                quiet_version = original_audio.normalize() - 10  # 10dB quieter
                short_pause = AudioSegment.silent(duration=1500)  # 1.5 second pause
                audio = audio + short_pause + (silence + quiet_version + silence)
                
                total_duration = len(audio) / 1000.0
                print(f"   Total duration after adding quiet version: {total_duration:.1f}s")
        
        else:
            # For longer audio, still apply some preprocessing
            audio = audio.normalize()
            if audio.max_dBFS < -6:
                boost = min(15, abs(audio.max_dBFS) - 3)
                audio = audio + boost
            
            # Still add some silence for consistency
            silence_duration = 3000  # 3 seconds
            silence = AudioSegment.silent(duration=silence_duration)
            audio = silence + audio + silence
        
        # 8. Ensure optimal format for Whisper
        audio = audio.set_channels(1)  # Mono
        audio = audio.set_frame_rate(16000)  # Whisper's preferred rate
        audio = audio.set_sample_width(2)  # 16-bit
        
        # 9. Export with maximum quality
        audio.export(output_path, format="wav", parameters=["-q:a", "0", "-ar", "16000"])
        
        final_duration = len(audio) / 1000.0
        print(f"   Final processed duration: {final_duration:.1f}s (expansion: {final_duration/duration_sec:.1f}x)")
        
        return output_path

    except Exception as e:
        print(f"   ⚠ EXTREME preprocessing failed: {str(e)}")
        return audio_path 