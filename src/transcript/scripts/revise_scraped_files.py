#!/usr/bin/env python3
"""
<PERSON><PERSON>t to revise scraped files by replacing extracted_text with full_text and updating chunks
with our improved chunking output.
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any

def load_json_file(file_path: str) -> List[Dict]:
    """Load a JSON file and return the data."""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_json_file(data: List[Dict], file_path: str):
    """Save data to a JSON file."""
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def find_chunked_data_by_audio_path(chunked_data: List[Dict], audio_path: str) -> Dict:
    """Find chunked data by matching audio path."""
    # Extract the filename from the audio path
    filename = os.path.basename(audio_path)
    
    for item in chunked_data:
        chunked_audio_path = item.get('audio_path', '')
        chunked_filename = os.path.basename(chunked_audio_path)
        
        if chunked_filename == filename:
            return item
    
    return None

def revise_scraped_file(original_file_path: str, chunked_file_path: str, output_file_path: str):
    """Revise a scraped file by replacing extracted_text with chunked full_text, keeping the field name as extracted_text and placing it after audio_path."""
    print(f"📁 Processing: {original_file_path}")
    
    # Load original and chunked data
    original_data = load_json_file(original_file_path)
    chunked_data = load_json_file(chunked_file_path)
    
    revised_data = []
    
    for item in original_data:
        # Find corresponding chunked data
        audio_path = item.get('audio_path', '')
        chunked_item = find_chunked_data_by_audio_path(chunked_data, audio_path)
        
        if chunked_item:
            # Create revised item
            revised_item = item.copy()
            
            # Remove old extracted_text and full_text if they exist
            revised_item.pop('extracted_text', None)
            revised_item.pop('full_text', None)
            
            # Insert new extracted_text (from chunked full_text) right after audio_path
            fields = list(revised_item.items())
            new_fields = []
            inserted = False
            for k, v in fields:
                new_fields.append((k, v))
                if k == 'audio_path' and not inserted:
                    new_fields.append(('extracted_text', chunked_item.get('full_text', '')))
                    inserted = True
            revised_item = dict(new_fields)
            
            # Update chunks with new chunked data
            revised_item['chunks'] = chunked_item.get('chunks', [])
            
            revised_data.append(revised_item)
            print(f"  ✅ Revised item {item.get('question_number', 'unknown')}")
        else:
            # Keep original item if no chunked data found
            print(f"  ⚠️  No chunked data found for {audio_path}")
            revised_data.append(item)
    
    # Save revised data
    save_json_file(revised_data, output_file_path)
    print(f"💾 Saved revised file to: {output_file_path}")
    print(f"📊 Processed {len(revised_data)} items")

def process_batch(original_dir, chunked_dir, output_dir):
    for i in range(1, 38):  # 1 to 37 inclusive
        test_file = f"test{i}.json"
        chunked_file = f"transcriptions_media_test{i}_chunked_llm.json"
        original_path = Path(original_dir) / test_file
        chunked_path = Path(chunked_dir) / chunked_file
        output_path = Path(output_dir) / test_file
        if original_path.exists() and chunked_path.exists():
            revise_scraped_file(str(original_path), str(chunked_path), str(output_path))
        else:
            print(f"❌ Missing files for test {i}")
            if not original_path.exists():
                print(f"   Missing: {original_path}")
            if not chunked_path.exists():
                print(f"   Missing: {chunked_path}")

def main():
    base_dir = Path("data/scraped")
    new_base_dir = Path("data/scraped_revised")
    new_base_dir.mkdir(parents=True, exist_ok=True)

    # Free
    free_dir = new_base_dir / "scraped_listening_cleaned_free"
    free_dir.mkdir(parents=True, exist_ok=True)
    process_batch(
        original_dir=base_dir / "scraped_listening_cleaned_free",
        chunked_dir="src/transcript/output_chunked/free",
        output_dir=free_dir
    )

    # Regular
    regular_dir = new_base_dir / "scraped_listening_cleaned"
    regular_dir.mkdir(parents=True, exist_ok=True)
    process_batch(
        original_dir=base_dir / "scraped_listening_cleaned",
        chunked_dir="src/transcript/output_chunked/regular",
        output_dir=regular_dir
    )

    print(f"\n🎉 Revision complete! New files saved in: {new_base_dir}")

if __name__ == "__main__":
    main() 