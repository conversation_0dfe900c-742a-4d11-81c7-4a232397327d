#!/usr/bin/env python3
"""
Batch Dehallucination Script
Processes all JSON files in outputs/free/ and outputs/regular/ folders
Saves results in output_dehallucinated/free/ and output_dehallucinated/regular/
"""

import os
import sys
import glob
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))
from dehallucination import Dehallucinator

def main():
    dehallucinator = Dehallucinator()
    
    # Define folders to process - use correct paths
    script_dir = Path(__file__).parent
    folders = [
        str(script_dir / 'outputs' / 'free'), 
        str(script_dir / 'outputs' / 'regular')
    ]
    
    # Create output directory structure
    output_base = script_dir / 'output_dehallucinated'
    output_base.mkdir(exist_ok=True)
    (output_base / 'free').mkdir(exist_ok=True)
    (output_base / 'regular').mkdir(exist_ok=True)
    
    total_files = 0
    processed_files = 0
    
    for folder in folders:
        print(f"\n{'='*60}")
        print(f"📁 Processing folder: {folder}")
        print(f"{'='*60}")
        
        # Find all JSON files in the folder
        json_pattern = os.path.join(folder, "*.json")
        json_files = glob.glob(json_pattern)
        
        # Filter out already processed files (those with _dehallucinated in name)
        json_files = [f for f in json_files if '_dehallucinated' not in f and '_merci_fixed' not in f]
        
        print(f"📊 Found {len(json_files)} JSON files to process")
        
        for json_file in json_files:
            total_files += 1
            print(f"\n[{processed_files + 1}/{len(json_files)}] Processing: {os.path.basename(json_file)}")
            
            try:
                # Determine output folder (free or regular)
                folder_name = os.path.basename(os.path.dirname(json_file))
                output_folder = output_base / folder_name
                
                # Create output filename
                base_name = os.path.basename(json_file)
                output_file = output_folder / base_name
                
                # Process the file
                dehallucinator.process_json_file(json_file, str(output_file))
                processed_files += 1
                
                print(f"✅ Successfully processed: {os.path.basename(json_file)}")
                print(f"💾 Saved to: {output_file}")
                
            except Exception as e:
                print(f"❌ Error processing {json_file}: {str(e)}")
                continue
    
    print(f"\n{'='*60}")
    print(f"🎉 Batch processing completed!")
    print(f"📊 Total files processed: {processed_files}/{total_files}")
    print(f"📁 Dehallucinated files saved in: {output_base}/")
    print(f"   ├── {output_base}/free/")
    print(f"   └── {output_base}/regular/")
    print(f"{'='*60}")

if __name__ == "__main__":
    main() 