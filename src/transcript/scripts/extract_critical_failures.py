#!/usr/bin/env python3
"""
Extract Critical Failures
Creates a focused JSON report of cases that would fail all 3 fallback models
(Large-v3, Turbo, Medium) based on validation criteria.
"""

import json
from pathlib import Path
from datetime import datetime

def extract_critical_failures(analysis_file: str = "fallback_failure_analysis.json", 
                             output_file: str = "critical_failures.json"):
    """Extract cases that would fail all 3 models and create focused report"""
    
    try:
        with open(analysis_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading analysis file: {e}")
        return
    
    # Extract critical failures
    critical_failures = []
    
    # Get all potential issues
    potential_issues = data.get('potential_validation_issues', {}).get('issues', [])
    
    for issue in potential_issues:
        # Focus on cases that fail BOTH density AND type validation
        validation = issue.get('validation_results', {})
        density_valid = validation.get('density_valid', True)
        type_valid = validation.get('type_valid', True)
        
        # Cases that would fail all 3 models (both validations failed)
        if not density_valid and not type_valid:
            critical_failures.append({
                'audio_file': Path(issue['audio_path']).name,
                'test_name': issue['test_name'],
                'test_type': issue['test_type'],
                'text_preview': issue['text_preview'],
                'word_count': issue['word_count'],
                'estimated_duration': issue['estimated_duration'],
                'model_used': issue['model_used'],
                'audio_type': issue['audio_type'],
                'density_failure': validation['density_reason'],
                'type_failure': validation['type_reason'],
                'hallucination_indicators': issue['hallucination_indicators'],
                'full_audio_path': issue['audio_path']
            })
    
    # Also include cases with severe hallucination indicators
    severe_hallucinations = []
    for issue in potential_issues:
        indicators = issue.get('hallucination_indicators', [])
        # Cases with multiple hallucination indicators or severe ones
        if len(indicators) >= 2 or any('repetition' in ind.lower() for ind in indicators):
            severe_hallucinations.append({
                'audio_file': Path(issue['audio_path']).name,
                'test_name': issue['test_name'],
                'test_type': issue['test_type'],
                'text_preview': issue['text_preview'],
                'hallucination_indicators': indicators,
                'model_used': issue['model_used'],
                'audio_type': issue['audio_type'],
                'full_audio_path': issue['audio_path'],
                'validation_status': 'passed_validation_but_hallucinated'
            })
    
    # Create focused report
    report = {
        'metadata': {
            'generated_at': datetime.now().isoformat(),
            'source_analysis': analysis_file,
            'description': 'Critical failures that would fail all 3 fallback models'
        },
        'summary': {
            'total_critical_failures': len(critical_failures),
            'total_severe_hallucinations': len(severe_hallucinations),
            'total_problematic_cases': len(critical_failures) + len(severe_hallucinations)
        },
        'critical_failures': {
            'description': 'Cases failing both density AND type validation (would fail all 3 models)',
            'count': len(critical_failures),
            'cases': critical_failures
        },
        'severe_hallucinations': {
            'description': 'Cases with multiple or severe hallucination indicators',
            'count': len(severe_hallucinations),
            'cases': severe_hallucinations
        }
    }
    
    # Save focused report
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ CRITICAL FAILURES REPORT GENERATED: {output_file}")
        print(f"   💀 Cases failing both validations: {len(critical_failures)}")
        print(f"   🚨 Cases with severe hallucinations: {len(severe_hallucinations)}")
        print(f"   📊 Total problematic cases: {len(critical_failures) + len(severe_hallucinations)}")
        
        if critical_failures:
            print(f"\n💀 CRITICAL FAILURES (would fail all 3 models):")
            for i, failure in enumerate(critical_failures[:5], 1):
                print(f"   {i}. {failure['test_name']} - {failure['audio_file']}")
                print(f"      Density: {failure['density_failure']}")
                print(f"      Type: {failure['type_failure']}")
                print(f"      Text: {failure['text_preview']}")
        
        if severe_hallucinations:
            print(f"\n🚨 SEVERE HALLUCINATIONS:")
            for i, case in enumerate(severe_hallucinations[:3], 1):
                print(f"   {i}. {case['test_name']} - {case['audio_file']}")
                print(f"      Issues: {', '.join(case['hallucination_indicators'])}")
        
    except Exception as e:
        print(f"❌ Error saving report: {e}")

if __name__ == "__main__":
    import sys
    
    analysis_file = "fallback_failure_analysis.json"
    output_file = "critical_failures.json"
    
    if len(sys.argv) > 1:
        analysis_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    extract_critical_failures(analysis_file, output_file) 