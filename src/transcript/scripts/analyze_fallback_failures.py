#!/usr/bin/env python3
"""
Analyze Fallback Failures Script
Analyzes transcription output JSON files to identify cases where:
1. All 3 models (Large-v3, Turbo, Medium) failed validation (for enhanced output)
2. Potential problematic cases based on existing output data
3. Generate failure analysis report in JSON format
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

def validate_density(text: str, duration: float, audio_type: str) -> tuple[bool, str]:
    """
    Simulate the density validation logic from main_colab.py
    """
    if not text or not text.strip():
        return False, "Empty text"
    
    words = text.strip().split()
    word_count = len(words)
    
    # Calculate words per minute
    words_per_minute = (word_count / duration) * 60 if duration > 0 else 0
    
    # Type-aware density validation
    if audio_type == "abcd_choices":
        min_wpm = 20
        max_wpm = 200
        min_words = 8
        
        if word_count < min_words:
            return False, f"Too few words for ABCD: {word_count} words (need ≥{min_words})"
        if words_per_minute < min_wpm:
            return False, f"Too sparse for ABCD: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        if words_per_minute > max_wpm:
            return False, f"Too dense for ABCD: {words_per_minute:.1f} wpm (max {max_wpm})"
    else:
        min_wpm = 10
        max_wpm = 300
        
        if duration > 10 and words_per_minute < min_wpm:
            return False, f"Too sparse for conversation: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        if words_per_minute > max_wpm:
            return False, f"Too dense for conversation: {words_per_minute:.1f} wpm (max {max_wpm})"
    
    # Check for excessive repetition
    if word_count > 5:
        word_freq = {}
        for word in words:
            clean_word = word.lower().strip('.,!?;:')
            word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
        
        max_freq = max(word_freq.values())
        if max_freq / word_count > 0.6:
            return False, f"Excessive repetition: {max_freq}/{word_count} same word"
    
    return True, f"Valid density: {words_per_minute:.1f} wpm, {word_count} words"

def validate_abcd_choices(text: str) -> bool:
    """Validate ABCD pattern - from main_colab.py"""
    if not text or not text.strip():
        return False
    
    abcd_patterns = [
        r'(?:^|\s|[.!?])\s*([A-D])[.]\s+\w+',
        r'(?:^|\s|[.!?])\s*([A-D])\s+[A-Z]',
        r'(?:^|\s|[.!?])\s*option\s+([A-D])',
        r'(?:^|\s|[.!?])\s*choix\s+([A-D])',
        r'(?:^|\s|[.!?])\s*([A-D])[.]\s*[\w\s]+',
        r'(?:^|\s|[.!?])\s*([A-D])\s*[)]\s*\w+',
        r'(?:^|\s|[.!?])\s*[(]\s*([A-D])\s*[)]\s*\w+',
    ]
    
    all_matches = []
    for pattern in abcd_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
        all_matches.extend(matches)
    
    return len(all_matches) >= 2

def validate_conversation_question(text: str) -> bool:
    """Validate conversation question - from main_colab.py"""
    if not text or not text.strip():
        return False
    
    text = text.strip()
    text_ending = text[-100:] if len(text) > 100 else text
    
    # Check for question marks at the end
    if '?' in text_ending:
        return True
    
    # Check for incomplete sentence patterns at the very end
    incomplete_patterns = [
        r'\b(il|elle|je|tu|nous|vous|ils|elles)\s+(est|sont|a|ai|as|avons|avez|ont)\s*\.?\s*$',
        r'\b(c\'est|ce sont|voilà|voici)\s*\.?\s*$',
        r'\b(et|ou|mais|donc|car)\s*\.?\s*$',
        r'\b\w+\s+(est|sont|a|ont)\s*\.?\s*$',
        r'\b(dans|sur|avec|pour|sans|vers|chez)\s+\w+\s*\.?\s*$',
        r'\b(de|du|des|le|la|les|un|une)\s+\w+\s*\.?\s*$',
        r'\b(qui|que|dont|où)\s+\w+\s*\.?\s*$',
        r'\b(pour|sans|avec|sur|dans)\s+(le|la|les|un|une|des)\s+\w+\s*\.?\s*$',
        r'\b(alors|donc|en fait|bon|bien)\s*\.?\s*$',
        r'\b(effectivement|exactement|tout à fait)\s*\.?\s*$',
        r'\b(maintenant|aujourd\'hui|actuellement)\s*\.?\s*$',
    ]
    
    for pattern in incomplete_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            return True
    
    return False

def estimate_audio_duration(words: List[Dict]) -> float:
    """Estimate audio duration from word timestamps"""
    if not words:
        return 0.0
    
    try:
        last_word = words[-1]
        if 'end' in last_word:
            return float(last_word['end'])
        elif 'start' in last_word:
            return float(last_word['start']) + 1.0  # Add 1 second estimate
    except:
        pass
    
    # Fallback: estimate based on word count (assume ~150 words per minute)
    return len(words) / 150 * 60

def analyze_transcription_entry(entry: Dict) -> Dict:
    """Analyze a single transcription entry for potential validation failures"""
    
    # Extract basic info
    audio_path = entry.get('audio_path', '')
    text = entry.get('full_text', '')
    words = entry.get('words', [])
    model_used = entry.get('model_used', 'unknown')
    audio_type = entry.get('audio_type', 'unknown')
    
    # Estimate duration
    duration = estimate_audio_duration(words)
    
    # Check for enhanced validation data
    has_enhanced_validation = 'validation_passed' in entry
    all_models_failed = entry.get('all_models_failed_validation', False)
    
    # Perform validation checks
    density_valid, density_reason = validate_density(text, duration, audio_type)
    
    if audio_type == "abcd_choices":
        type_valid = validate_abcd_choices(text)
        type_reason = "ABCD pattern found" if type_valid else "No ABCD patterns found"
    elif audio_type == "conversation_question":
        type_valid = validate_conversation_question(text)
        type_reason = "Question/incomplete pattern found" if type_valid else "No question/incomplete pattern found"
    else:
        type_valid = True  # Unknown type, assume valid
        type_reason = f"Unknown audio type: {audio_type}"
    
    # Check for potential hallucinations
    hallucination_indicators = []
    if "sous-titrage" in text.lower():
        hallucination_indicators.append("Contains 'sous-titrage' (subtitle artifact)")
    if "merci beaucoup au revoir" in text.lower():
        hallucination_indicators.append("Contains common hallucination phrase")
    
    # Count word repetitions
    if text:
        words_list = text.lower().split()
        word_freq = {}
        for word in words_list:
            clean_word = word.strip('.,!?;:')
            word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
        
        if words_list:
            max_freq = max(word_freq.values()) if word_freq else 0
            repetition_ratio = max_freq / len(words_list) if words_list else 0
            if repetition_ratio > 0.5:
                hallucination_indicators.append(f"High word repetition: {repetition_ratio:.1%}")
    
    return {
        'audio_path': audio_path,
        'text_preview': text[:100] + '...' if len(text) > 100 else text,
        'full_text': text,
        'word_count': len(text.split()) if text else 0,
        'estimated_duration': duration,
        'model_used': model_used,
        'audio_type': audio_type,
        'has_enhanced_validation': has_enhanced_validation,
        'all_models_failed': all_models_failed,
        'validation_results': {
            'density_valid': density_valid,
            'density_reason': density_reason,
            'type_valid': type_valid,
            'type_reason': type_reason,
            'both_valid': density_valid and type_valid
        },
        'hallucination_indicators': hallucination_indicators,
        'potential_issue': not (density_valid and type_valid) or len(hallucination_indicators) > 0
    }

def analyze_json_file(file_path: str) -> Dict:
    """Analyze a single JSON transcription file"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        return {
            'file_path': file_path,
            'error': f"Failed to load JSON: {str(e)}",
            'total_entries': 0,
            'problematic_entries': []
        }
    
    if not isinstance(data, list):
        return {
            'file_path': file_path,
            'error': "JSON is not a list of entries",
            'total_entries': 0,
            'problematic_entries': []
        }
    
    problematic_entries = []
    enhanced_failures = []
    
    for entry in data:
        analysis = analyze_transcription_entry(entry)
        
        # Check if this is an enhanced validation failure
        if analysis['all_models_failed']:
            enhanced_failures.append(analysis)
        elif analysis['potential_issue']:
            problematic_entries.append(analysis)
    
    return {
        'file_path': file_path,
        'total_entries': len(data),
        'enhanced_validation_failures': len(enhanced_failures),
        'potential_issues': len(problematic_entries),
        'enhanced_failures': enhanced_failures,
        'problematic_entries': problematic_entries[:10],  # Limit to first 10 for summary
        'all_problematic_entries': problematic_entries  # Full list
    }

def analyze_all_output_files(output_dir: str = "output") -> Dict:
    """Analyze all transcription JSON files in the output directory"""
    
    output_path = Path(output_dir)
    if not output_path.exists():
        return {'error': f"Output directory {output_dir} does not exist"}
    
    results = {
        'analysis_timestamp': datetime.now().isoformat(),
        'output_directory': str(output_path.absolute()),
        'regular_tests': {},
        'free_tests': {},
        'summary': {
            'total_files': 0,
            'total_entries': 0,
            'total_enhanced_failures': 0,
            'total_potential_issues': 0,
            'files_with_issues': 0
        }
    }
    
    # Analyze regular tests
    regular_dir = output_path / "regular"
    if regular_dir.exists():
        for json_file in regular_dir.glob("transcriptions_*.json"):
            file_analysis = analyze_json_file(str(json_file))
            test_name = json_file.stem.replace('transcriptions_', '')
            results['regular_tests'][test_name] = file_analysis
            
            # Update summary
            results['summary']['total_files'] += 1
            results['summary']['total_entries'] += file_analysis.get('total_entries', 0)
            results['summary']['total_enhanced_failures'] += file_analysis.get('enhanced_validation_failures', 0)
            results['summary']['total_potential_issues'] += file_analysis.get('potential_issues', 0)
            
            if file_analysis.get('potential_issues', 0) > 0 or file_analysis.get('enhanced_validation_failures', 0) > 0:
                results['summary']['files_with_issues'] += 1
    
    # Analyze free tests
    free_dir = output_path / "free"
    if free_dir.exists():
        for json_file in free_dir.glob("transcriptions_*.json"):
            file_analysis = analyze_json_file(str(json_file))
            test_name = json_file.stem.replace('transcriptions_', '')
            results['free_tests'][test_name] = file_analysis
            
            # Update summary
            results['summary']['total_files'] += 1
            results['summary']['total_entries'] += file_analysis.get('total_entries', 0)
            results['summary']['total_enhanced_failures'] += file_analysis.get('enhanced_validation_failures', 0)
            results['summary']['total_potential_issues'] += file_analysis.get('potential_issues', 0)
            
            if file_analysis.get('potential_issues', 0) > 0 or file_analysis.get('enhanced_validation_failures', 0) > 0:
                results['summary']['files_with_issues'] += 1
    
    return results

def generate_failure_report(output_dir: str = "output", report_file: str = "fallback_failure_analysis.json"):
    """Generate comprehensive failure analysis report"""
    
    print("🔍 Analyzing Fallback Failures...")
    print(f"   📁 Output directory: {output_dir}")
    
    # Perform analysis
    analysis_results = analyze_all_output_files(output_dir)
    
    if 'error' in analysis_results:
        print(f"❌ Error: {analysis_results['error']}")
        return
    
    # Print summary
    summary = analysis_results['summary']
    print(f"\n📊 ANALYSIS SUMMARY:")
    print(f"   📄 Total files analyzed: {summary['total_files']}")
    print(f"   🎵 Total audio entries: {summary['total_entries']}")
    print(f"   💀 Enhanced validation failures: {summary['total_enhanced_failures']}")
    print(f"   ⚠️  Potential issues identified: {summary['total_potential_issues']}")
    print(f"   🚨 Files with issues: {summary['files_with_issues']}")
    
    # Generate detailed failure list
    all_failures = []
    all_issues = []
    
    # Collect from regular tests
    for test_name, test_data in analysis_results['regular_tests'].items():
        if 'enhanced_failures' in test_data:
            for failure in test_data['enhanced_failures']:
                failure['test_type'] = 'regular'
                failure['test_name'] = test_name
                all_failures.append(failure)
        
        if 'all_problematic_entries' in test_data:
            for issue in test_data['all_problematic_entries']:
                issue['test_type'] = 'regular'
                issue['test_name'] = test_name
                all_issues.append(issue)
    
    # Collect from free tests
    for test_name, test_data in analysis_results['free_tests'].items():
        if 'enhanced_failures' in test_data:
            for failure in test_data['enhanced_failures']:
                failure['test_type'] = 'free'
                failure['test_name'] = test_name
                all_failures.append(failure)
        
        if 'all_problematic_entries' in test_data:
            for issue in test_data['all_problematic_entries']:
                issue['test_type'] = 'free'
                issue['test_name'] = test_name
                all_issues.append(issue)
    
    # Create final report
    final_report = {
        'analysis_metadata': {
            'timestamp': analysis_results['analysis_timestamp'],
            'output_directory': analysis_results['output_directory'],
            'script_version': '1.0',
            'description': 'Analysis of Whisper transcription failures and validation issues'
        },
        'summary': summary,
        'enhanced_validation_failures': {
            'count': len(all_failures),
            'description': 'Cases where all 3 models (Large-v3, Turbo, Medium) failed validation',
            'failures': all_failures
        },
        'potential_validation_issues': {
            'count': len(all_issues),
            'description': 'Cases that would likely fail the enhanced validation system',
            'issues': all_issues
        },
        'detailed_analysis': analysis_results
    }
    
    # Save report
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ REPORT GENERATED: {report_file}")
        print(f"   📄 Enhanced failures: {len(all_failures)}")
        print(f"   ⚠️  Potential issues: {len(all_issues)}")
        
        if all_failures:
            print(f"\n🚨 ENHANCED VALIDATION FAILURES FOUND:")
            for i, failure in enumerate(all_failures[:5], 1):
                print(f"   {i}. {failure['test_name']} - {Path(failure['audio_path']).name}")
                print(f"      Text: {failure['text_preview']}")
        
        if all_issues:
            print(f"\n⚠️  TOP POTENTIAL ISSUES:")
            for i, issue in enumerate(all_issues[:5], 1):
                print(f"   {i}. {issue['test_name']} - {Path(issue['audio_path']).name}")
                print(f"      Issues: {', '.join(issue['hallucination_indicators'][:2])}")
                if not issue['validation_results']['both_valid']:
                    validation_issues = []
                    if not issue['validation_results']['density_valid']:
                        validation_issues.append("Density")
                    if not issue['validation_results']['type_valid']:
                        validation_issues.append("Type")
                    print(f"      Failed: {', '.join(validation_issues)} validation")
        
    except Exception as e:
        print(f"❌ Error saving report: {e}")

if __name__ == "__main__":
    import sys
    
    # Default parameters
    output_dir = "output"
    report_file = "fallback_failure_analysis.json"
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    if len(sys.argv) > 2:
        report_file = sys.argv[2]
    
    # Generate report
    generate_failure_report(output_dir, report_file) 