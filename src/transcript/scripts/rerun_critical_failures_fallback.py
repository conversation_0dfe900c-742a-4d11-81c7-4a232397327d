#!/usr/bin/env python3
"""
Rerun Critical Failures Script (Hierarchical Fallback with Short Audio Optimization)
- Loads critical_failures.json
- Uses the same hierarchical fallback logic as main_colab.py
- Customized parameters for short audio optimization
- Includes density and type validation checks
- Updates the corresponding entry in the original transcription JSON file
- Saves the updated JSON file
"""

import json
from pathlib import Path
import whisper
import librosa
import numpy as np
import tempfile
import os
import re

def load_whisper_models():
    """Load multiple Whisper models for hierarchical fallback"""
    models = {}
    model_names = ["large-v3", "turbo", "medium"]
    
    for model_name in model_names:
        try:
            models[model_name] = whisper.load_model(model_name)
            print(f"✅ Loaded Whisper {model_name} model")
        except Exception as e:
            print(f"⚠️ Could not load {model_name}: {e}")
            models[model_name] = None
    
    # Ensure we have at least one model
    if not any(model for model in models.values() if model is not None):
        print("❌ Could not load any Whisper models!")
        return None
    
    return models

def validate_density(text: str, audio_duration: float, audio_type: str) -> tuple[bool, str]:
    """
    Validate transcription density (words per minute) based on audio type.
    Optimized for short audio.
    """
    if not text or not text.strip():
        return False, "Empty text"
    
    words = text.strip().split()
    word_count = len(words)
    
    # Calculate words per minute
    words_per_minute = (word_count / audio_duration) * 60 if audio_duration > 0 else 0
    
    # Type-aware density validation (optimized for short audio)
    if audio_type == "abcd_choices":
        # Type 1: ABCD Choices - expect structured choices
        min_wpm = 15   # Lower threshold for short audio
        max_wpm = 250  # Higher threshold for short audio
        min_words = 6  # Lower minimum for short audio
        
        if word_count < min_words:
            return False, f"Too few words for ABCD: {word_count} words (need ≥{min_words})"
        
        if words_per_minute < min_wpm:
            return False, f"Too sparse for ABCD: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        
        if words_per_minute > max_wpm:
            return False, f"Too dense for ABCD: {words_per_minute:.1f} wpm (max {max_wpm})"
            
    else:
        # Type 2: Conversations/Questions - more permissive for short audio
        min_wpm = 8    # Lower threshold for short audio
        max_wpm = 350  # Higher threshold for short audio
        
        if audio_duration > 10 and words_per_minute < min_wpm:
            return False, f"Too sparse for conversation: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        
        if words_per_minute > max_wpm:
            return False, f"Too dense for conversation: {words_per_minute:.1f} wpm (max {max_wpm})"
    
    return True, f"Valid density: {words_per_minute:.1f} wpm"

def validate_abcd_choices(text: str) -> bool:
    """Validate if text contains ABCD choice patterns."""
    text_lower = text.lower()
    
    # Check for ABCD patterns
    abcd_patterns = [
        r'\b[a-d]\.',  # A. B. C. D.
        r'\b[a-d]\s',  # A B C D
        r'\([a-d]\)',  # (a) (b) (c) (d)
        r'[a-d]\)',    # a) b) c) d)
    ]
    
    for pattern in abcd_patterns:
        if re.search(pattern, text_lower):
            return True
    
    return False

def validate_conversation_question(text: str) -> bool:
    """Validate if text contains question or incomplete sentence patterns."""
    text_lower = text.lower()
    
    # Check for question patterns
    question_patterns = [
        r'\?$',  # Ends with question mark
        r'\?',   # Contains question mark
        r'^qui\b', r'^quoi\b', r'^où\b', r'^quand\b', r'^comment\b', r'^pourquoi\b',  # Question words
        r'\.\.\.$',  # Ends with ellipsis
        r'…$',       # Ends with ellipsis (unicode)
    ]
    
    for pattern in question_patterns:
        if re.search(pattern, text_lower):
            return True
    
    return False

def transcribe_with_whisper_short_audio(model, audio_path: str, model_name: str) -> dict:
    """Whisper transcription optimized for short audio files."""
    try:
        # Get audio duration for optimization
        duration = librosa.get_duration(path=audio_path)
        print(f"   📏 Audio duration: {duration:.2f} seconds")
        
        # Short audio optimized parameters
        if model_name == "turbo":
            # Turbo-specific short audio settings
            result = model.transcribe(
                audio_path,
                language="fr",
                word_timestamps=True,
                verbose=False,
                temperature=0.0,
                condition_on_previous_text=False,
                initial_prompt="Transcription française de questions à choix multiples TCF.",
                fp16=False,
                # Short audio optimizations
                no_speech_threshold=0.3,
                logprob_threshold=-1.0,
                compression_ratio_threshold=1.8,
                beam_size=3,
                best_of=3,
                suppress_tokens=[-1],
                without_timestamps=False,
                max_initial_timestamp=1.0,
            )
        else:
            # Standard models with short audio optimizations
            result = model.transcribe(
                audio_path,
                language="fr",
                word_timestamps=True,
                verbose=False,
                temperature=0.0,
                condition_on_previous_text=False,
                initial_prompt="Transcription française de questions à choix multiples TCF.",
                fp16=False,
                # Short audio optimizations
                no_speech_threshold=0.4,  # Slightly higher for standard models
                logprob_threshold=-1.0,
                compression_ratio_threshold=2.0,
                beam_size=5,
                best_of=5,
            )
        
        # Apply post-processing
        if "text" in result:
            result["text"] = post_process_transcription(result["text"])
        
        # Apply post-processing to segments too
        if "segments" in result:
            for segment in result["segments"]:
                if "text" in segment:
                    segment["text"] = post_process_transcription(segment["text"])
        
        return result
    
    except Exception as e:
        print(f"   ❌ {model_name} transcription failed: {e}")
        return None

def post_process_transcription(text: str) -> str:
    """Apply standard post-processing to transcription text."""
    if not text:
        return text
    
    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Fix common French transcription issues
    text = re.sub(r'\b([a-d])\s+([a-d])\s+([a-d])\s+([a-d])\b', r'\1. \2. \3. \4.', text)
    
    return text

def extract_words_from_result(result: dict, duration: float = None) -> list:
    """Extract word-level timestamps from Whisper result."""
    words = []
    
    if "segments" in result:
        for segment in result["segments"]:
            if "words" in segment and segment["words"]:
                # Use word-level timestamps from Whisper
                for word_info in segment["words"]:
                    words.append({
                        "word": word_info.get("word", ""),
                        "start": round(word_info.get("start", 0), 3),
                        "end": round(word_info.get("end", 0), 3),
                        "probability": word_info.get("probability", 0.0)
                    })
            else:
                # Fallback: create word entries from segment text
                segment_words = segment["text"].split()
                segment_duration = segment["end"] - segment["start"]
                word_duration = segment_duration / max(1, len(segment_words))
                
                for i, word in enumerate(segment_words):
                    word_start = segment["start"] + (i * word_duration)
                    word_end = word_start + word_duration
                    words.append({
                        "word": f" {word}" if i > 0 or not word.startswith(" ") else word,
                        "start": round(word_start, 3),
                        "end": round(word_end, 3),
                        "probability": segment.get("confidence", 0.5)
                    })
    
    return words

def transcribe_with_hierarchical_fallback(models: dict, audio_path: str, expected_audio_type: str) -> dict:
    """
    Hierarchical fallback transcription with short audio optimization.
    Same logic as main_colab.py but with short audio parameters.
    """
    # Get audio duration
    try:
        duration = librosa.get_duration(path=audio_path)
    except:
        duration = 10.0  # Default fallback
    
    print(f"   🎯 Expected audio type: {expected_audio_type}")
    print(f"   📏 Audio duration: {duration:.2f} seconds")
    
    # Model hierarchy (same as main_colab.py)
    model_hierarchy = [
        ("large-v3", "primary"),
        ("turbo", "secondary"), 
        ("medium", "fallback")
    ]
    
    validation_attempts = []
    all_model_results = {}
    density_passed_models = []  # Track models that passed density check
    
    # Try each model in hierarchy
    for model_name, model_role in model_hierarchy:
        model = models.get(model_name)
        if model is None:
            print(f"   ⚠️ {model_name} model not available, skipping...")
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "UNAVAILABLE",
                "reason": "Model not loaded"
            })
            continue
        
        print(f"\n🎵 TRYING: {model_name} ({model_role})")
        try:
            # Transcribe with the model
            result = transcribe_with_whisper_short_audio(model, audio_path, model_name)
            if not result or not result.get("text"):
                print(f"   ❌ {model_name} failed to produce text")
                validation_attempts.append({
                    "model": model_name,
                    "role": model_role,
                    "status": "FAILED",
                    "reason": "No transcription text produced"
                })
                continue
            
            text = result.get("text", "").strip()
            all_model_results[model_name] = result
            print(f"   📝 Text: '{text[:80]}...'")
            
            # STEP 1: Density Validation
            density_valid, density_reason = validate_density(text, duration, expected_audio_type)
            print(f"   📊 Density check: {'✅' if density_valid else '❌'} {density_reason}")
            
            # Track models that passed density check
            if density_valid:
                density_passed_models.append(model_name)
            
            # STEP 2: Type Validation
            type_valid = False
            type_reason = ""
            if expected_audio_type == "abcd_choices":
                type_valid = validate_abcd_choices(text)
                type_reason = "ABCD pattern found" if type_valid else "No ABCD patterns found"
            else:  # conversation_question
                type_valid = validate_conversation_question(text)
                type_reason = "Question/incomplete pattern found" if type_valid else "No question/incomplete pattern found"
            
            print(f"   🎯 Type check: {'✅' if type_valid else '❌'} {type_reason}")
            
            # If both validations pass, we have a good result
            if density_valid and type_valid:
                print(f"   🎉 {model_name} SUCCESS: Both validations passed!")
                result["model_used"] = model_name
                result["validation_status"] = "VALIDATED"
                return result
            
            # Store validation attempt
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "FAILED_VALIDATION",
                "reason": f"Density: {density_reason}, Type: {type_reason}"
            })
            
        except Exception as e:
            print(f"   ❌ {model_name} error: {e}")
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "ERROR",
                "reason": str(e)
            })
            continue
    
    # If we get here, all models failed validation
    print(f"   ⚠️ All models failed validation")
    
    # NEW LOGIC: Use the first model that passed density check (large-v3 > turbo > medium)
    if density_passed_models:
        for model_name in ["large-v3", "turbo", "medium"]:
            if model_name in density_passed_models:
                print(f"   🎯 Using {model_name} (passed density check) as fallback")
                fallback_result = all_model_results[model_name]
                fallback_result["model_used"] = f"{model_name} (density_fallback)"
                fallback_result["validation_status"] = "DENSITY_FALLBACK_USED"
                return fallback_result
    
    # If no models passed density check, use large-v3 as final fallback
    print(f"   ⚠️ No models passed density check, using large-v3 as final fallback")
    fallback_model = models.get("large-v3")
    if fallback_model and "large-v3" in all_model_results:
        fallback_result = all_model_results["large-v3"]
        fallback_result["model_used"] = "large-v3 (final_fallback)"
        fallback_result["validation_status"] = "FINAL_FALLBACK_USED"
        return fallback_result
    
    return None

def rerun_and_update():
    # Load the Whisper models
    models = load_whisper_models()
    if not models:
        return
    
    with open('critical_failures.json', 'r', encoding='utf-8') as f:
        failures = json.load(f)['critical_failures']['cases']
    print(f"🔍 Found {len(failures)} critical failures to rerun.")

    # Group by test_name and test_type for efficient file access
    file_map = {}
    for case in failures:
        test_type = case['test_type']
        test_name = case['test_name']
        key = (test_type, test_name)
        if key not in file_map:
            if test_type == 'regular':
                json_path = Path('output/regular') / f'transcriptions_{test_name}.json'
            else:
                json_path = Path('output/free') / f'transcriptions_{test_name}.json'
            file_map[key] = {'json_path': json_path, 'cases': []}
        file_map[key]['cases'].append(case)

    for (test_type, test_name), info in file_map.items():
        json_path = info['json_path']
        print(f"\n📁 Processing {json_path} ({len(info['cases'])} failures)")
        if not json_path.exists():
            print(f"❌ File not found: {json_path}")
            continue
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        updated = 0
        for case in info['cases']:
            audio_path = case['full_audio_path']
            print(f"   🔄 Rerunning: {audio_path}")
            try:
                # Get expected audio type
                audio_type = case.get("audio_type", "unknown")
                
                # Use hierarchical fallback with short audio optimization
                result = transcribe_with_hierarchical_fallback(models, audio_path, audio_type)
                
                if not result:
                    print(f"   ❌ Failed to transcribe {audio_path}")
                    continue
                
                # Extract words with timestamps
                words = extract_words_from_result(result)
                
                if not words:
                    print(f"⚠️ No words found in Whisper output for {audio_path}. Creating minimal transcription...")
                    words = [{
                        "word": result["text"] or "[no transcription]",
                        "start": 0.0,
                        "end": 1.0,
                        "probability": 0.0
                    }]
                
                # Create new entry
                new_entry = {
                    "full_text": result["text"],
                    "words": words,
                    "segments": [
                        {
                            "text": segment["text"],
                            "start": segment["start"],
                            "end": segment["end"],
                            "probability": segment.get("confidence", 1.0)
                        }
                        for segment in result.get("segments", [])
                    ],
                    "model_used": result.get("model_used", "unknown"),
                    "audio_type": audio_type,
                    "validation_status": result.get("validation_status", "unknown")
                }
                    
            except Exception as e:
                print(f"   ❌ Error transcribing {audio_path}: {e}")
                continue
                
            # Find and update the entry
            found = False
            for entry in data:
                if entry.get('audio_path') == audio_path:
                    entry.update(new_entry)
                    updated += 1
                    found = True
                    print(f"      ✅ Updated entry for {audio_path}")
                    print(f"      📝 Text: {result['text'][:100]}...")
                    break
            if not found:
                print(f"      ⚠️ No matching entry for {audio_path} in {json_path}")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        print(f"   💾 Saved updated file: {json_path} ({updated} entries updated)")

if __name__ == "__main__":
    print("🎯 Hierarchical Fallback Critical Failures Rerun Script")
    print("=" * 60)
    print("Models: large-v3 → turbo → medium")
    print("Settings: Short Audio Optimized")
    print("Timestamps: Word-level")
    print("Language: French (forced)")
    print("Validation: Density + Type checks")
    print("Fallback: Same logic as main_colab.py")
    print("=" * 60)
    rerun_and_update() 