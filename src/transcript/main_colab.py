#!/usr/bin/env python3
"""
Main Entry Point for Whisper Transcript Generation - COLAB VERSION
Uses standard Whisper large model with JSON-based question type detection
This version uses direct imports instead of relative imports for Colab compatibility
"""

import sys
import json
import os
from pathlib import Path
from typing import List, Dict

# Import our modular components - DIRECT IMPORTS FOR COLAB
# Removed French model dependency - using standard Whisper only
from utils.validation import validate_transcription, validate_word_timestamps, detect_silence_hallucination, post_process_transcription
from chunking import chunk_words, trim_chunks_after_question

# Standard imports
import whisper
from pydub import AudioSegment
import librosa
import re

# Global variables to cache the question type mappings
_question_types_cache = None

def load_question_types():
    """Load question type mappings from JSON files"""
    global _question_types_cache
    
    if _question_types_cache is not None:
        return _question_types_cache
    
    try:
        # Try to load from multiple possible locations
        possible_paths = [
            'docs/listening_question_types_regular.json',
            'docs/listening_question_types_free.json',
            'listening_question_types_regular.json',
            'listening_question_types_free.json',
            'data/listening_question_types_regular.json',
            'data/listening_question_types_free.json',
            '../data/listening_question_types_regular.json',
            '../data/listening_question_types_free.json',
            '../../data/listening_question_types_regular.json',
            '../../data/listening_question_types_free.json'
        ]
        
        regular_types = None
        free_types = None
        
        for path in possible_paths:
            if 'regular' in path and os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    regular_types = json.load(f)
                print(f"✅ Loaded regular question types from: {path}")
                break
        
        for path in possible_paths:
            if 'free' in path and os.path.exists(path):
                with open(path, 'r', encoding='utf-8') as f:
                    free_types = json.load(f)
                print(f"✅ Loaded free question types from: {path}")
                break
        
        if regular_types is None or free_types is None:
            print("⚠ Could not find question type JSON files, falling back to content detection")
            return None, None
        
        _question_types_cache = (regular_types, free_types)
        return regular_types, free_types
        
    except Exception as e:
        print(f"❌ Error loading question type mappings: {e}")
        return None, None

def detect_audio_type_from_mapping(test_name: str, question_num: int, is_free: bool = False) -> tuple[str, bool, dict]:
    """
    Get question type from pre-computed mapping based on scraped data
    Returns: (audio_type, is_valid, quality_info)
    """
    regular_types, free_types = load_question_types()
    
    if regular_types is None or free_types is None:
        # Fallback to content detection if JSON files not available
        print(f"   ⚠ JSON mappings not available, using fallback for {test_name} Q{question_num}")
        return "conversation_question", True, {"quality_score": 1}
    
    try:
        if is_free:
            question_type = free_types['tests'].get(test_name, {}).get(str(question_num), 2)
        else:
            question_type = regular_types['tests'].get(test_name, {}).get(str(question_num), 2)
        
        if question_type == 1:
            print(f"   📋 {test_name} Q{question_num} ({'free' if is_free else 'regular'}): Type 1 - A,B,C,D choices")
            return "abcd_choices", True, {"quality_score": 4, "source": "json_mapping"}
        else:
            print(f"   📋 {test_name} Q{question_num} ({'free' if is_free else 'regular'}): Type 2 - Conversation question")
            return "conversation_question", True, {"quality_score": 1, "source": "json_mapping"}
            
    except Exception as e:
        print(f"   ❌ Error getting question type from mapping: {e}")
        # Fallback to content detection
        return "conversation_question", True, {"quality_score": 1}

def extract_test_info_from_path(audio_path: str) -> tuple[str, int, bool]:
    """
    Extract test name, question number, and free status from audio path
    Examples:
    - listening_asset/media_test1/Q14.mp3 -> ("media_test1", 14, False)
    - listening_asset_free/media_test1/Q14.mp3 -> ("media_test1", 14, True)
    """
    path_parts = audio_path.replace('\\', '/').split('/')
    
    # Find the test folder (media_testX)
    test_name = None
    is_free = False
    
    for i, part in enumerate(path_parts):
        if part.startswith('media_test'):
            test_name = part
            # Check if this is from a free folder
            if i > 0 and 'free' in path_parts[i-1]:
                is_free = True
            break
    
    # Extract question number from filename
    filename = Path(audio_path).name
    question_num = extract_question_number(filename)
    
    return test_name or "unknown_test", question_num, is_free

def extract_transcriptions(folder_path, output_json):
    """
    Enhanced transcription extraction with hierarchical model fallback and JSON-based audio type detection.
    Tries large-v3 → turbo → medium until we get a result matching one of 2 expected audio types.
    """
    # Load multiple Whisper models for hierarchical fallback
    models = {}
    model_names = ["large-v3", "turbo", "medium"]
    
    for model_name in model_names:
        try:
            models[model_name] = whisper.load_model(model_name)
            print(f"✅ Loaded Whisper {model_name} model")
        except Exception as e:
            print(f"⚠ Could not load {model_name}: {e}")
            models[model_name] = None
    
    # Ensure we have at least one model
    if not any(model for model in models.values() if model is not None):
        print("❌ Could not load any Whisper models!")
        sys.exit(1)
    
    # Load question type mappings
    regular_types, free_types = load_question_types()
    if regular_types and free_types:
        print("🎯 Using JSON-based question type detection")
    else:
        print("⚠ Using fallback question type detection")
    
    results = []
    folder_path = Path(folder_path)
    test_name = folder_path.name
    
    print(f"🎯 Processing folder: {test_name}")
    
    for mp3_file in sorted(folder_path.glob("*.mp3")):
        print(f"\n🎵 Processing: {mp3_file.name}")
        
        try:
            # Get audio duration for validation
            duration = None
            try:
                audio_segment = AudioSegment.from_file(str(mp3_file))
                duration = len(audio_segment) / 1000.0
                print(f"   📏 Duration: {duration:.2f} seconds")
            except Exception as e:
                print(f"   ⚠ Could not determine duration: {e}")
            
            # Try hierarchical model fallback with JSON-based audio type detection
            result = transcribe_with_hierarchical_fallback(
                models, 
                str(mp3_file)
            )
            
            if not result:
                print(f"   ❌ Failed to transcribe {mp3_file.name}")
                continue
        
            # Extract words with timestamps
            words = extract_words_from_result(result, duration)
            
            if not words:
                print(f"⚠ No words found in Whisper output for {mp3_file}. Creating minimal transcription...")
                words = [{
                    "word": result["text"] or "[no transcription]",
                    "start": 0.0,
                    "end": duration or 1.0,
                    "probability": 0.0
                }]
            
            # Get full text and apply minimal post-processing (NO TRIMMING)
            full_text = result["text"]
            audio_type = result.get("audio_type", "unknown")
            
            if full_text:
                # Only apply standard post-processing (no trimming)
                full_text = post_process_transcription(full_text)
            else:
                full_text = "[no transcription detected]"
            
            # Run validation checks (for logging only - never skip)
            if not validate_transcription(full_text):
                print(f"⚠ Validation concerns detected for {mp3_file.name} - but processing anyway")
            else:
                print(f"✓ Validation passed for {mp3_file.name}")
            
            if not validate_word_timestamps(words):
                print(f"⚠ Word timestamp concerns detected for {mp3_file.name} - but processing anyway")
            
            if duration and detect_silence_hallucination(full_text, duration):
                print(f"⚠ Possible silence hallucination detected for {mp3_file.name} - but processing anyway")
            
            # Create result object
            audio_rel_path = f"{test_name}/{mp3_file.name}"
            
            result_obj = {
                "audio_path": str(mp3_file.resolve()),
                "audio_rel_path": audio_rel_path,
                "test_name": test_name,
                "full_text": full_text,
                "words": words,
                "model_used": result.get("model_used", "unknown"),
                "audio_type": result.get("audio_type", "unknown")
            }
            
            results.append(result_obj)
            print(f"  ✓ Added {len(words)} words to results")
                
        except Exception as e:
            print(f"   ❌ Error processing {mp3_file.name}: {str(e)}")
            # Create a minimal result even for errors
            result_obj = {
                "audio_path": str(mp3_file.resolve()),
                "audio_rel_path": f"{test_name}/{mp3_file.name}",
                "test_name": test_name,
                "full_text": f"[error: {str(e)}]",
                "words": [{
                    "word": f"[error: {str(e)}]",
                    "start": 0.0,
                    "end": 1.0,
                    "probability": 0.0
                }],
                "model_used": "error",
                "audio_type": "error"
            }
            results.append(result_obj)
            continue
        
    if not results:
        print("⚠ No files were successfully processed!")
        sys.exit(1)
    
    # Save transcription results
    with open(output_json, "w", encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Saved {len(results)} transcriptions to {output_json}")

def extract_words_from_result(result: Dict, duration: float = None) -> List[Dict]:
    """Extract word-level timestamps from Whisper result."""
    words = []
    
    if "segments" in result:
        for segment in result["segments"]:
            if "words" in segment and segment["words"]:
                # Use word-level timestamps from Whisper
                for word_info in segment["words"]:
                    words.append({
                        "word": word_info.get("word", ""),
                        "start": round(word_info.get("start", 0), 2),
                        "end": round(word_info.get("end", 0), 2),
                        "probability": word_info.get("probability", 0.0)
                    })
            else:
                # Fallback: create word entries from segment text
                segment_words = segment["text"].split()
                segment_duration = segment["end"] - segment["start"]
                word_duration = segment_duration / max(1, len(segment_words))
                
                for i, word in enumerate(segment_words):
                    word_start = segment["start"] + (i * word_duration)
                    word_end = word_start + word_duration
                    words.append({
                        "word": f" {word}" if i > 0 or not word.startswith(" ") else word,
                        "start": round(word_start, 2),
                        "end": round(word_end, 2),
                        "probability": segment.get("confidence", 0.5)
                    })
    
    return words

def transcribe_with_whisper_fallback(model, audio_path: str) -> Dict:
    """Ultra-aggressive Whisper transcription as last resort fallback."""
    try:
        # ULTRA-AGGRESSIVE parameters - capture everything possible
        result = model.transcribe(
            audio_path,
            language="fr",
            word_timestamps=True,
            verbose=False,
            temperature=0.0,
            condition_on_previous_text=False,
            
            # ULTRA-AGGRESSIVE parameters
            no_speech_threshold=0.05,       # Very low threshold - capture everything
            logprob_threshold=-3.0,         # Very low threshold - accept any confidence
            compression_ratio_threshold=8.0, # Very high threshold - allow any compression
            beam_size=20,                   # Very large beam
            best_of=20,                     # Many candidates
            
            # Maximum completeness parameters
            max_initial_timestamp=5.0,      # Allow longer initial silence
            
            # Simple but emphatic prompt
            initial_prompt="Transcription française COMPLÈTE. TOUS les mots. Ne rien omettre.",
            fp16=False,
        )
        
        if "text" in result:
            result["text"] = post_process_transcription(result["text"])
            result["fallback_used"] = True
            result["transcription_completeness"] = "fallback"
        
        return result
    
    except Exception as e:
        print(f"   ❌ Fallback transcription also failed: {e}")
        return None

def transcribe_with_whisper(model, audio_path: str) -> Dict:
    """Enhanced Whisper transcription for French audio with aggressive parameters to capture ALL content."""
    # Get audio duration for optimization
    try:
        duration = librosa.get_duration(path=audio_path)
        print(f"   Audio duration: {duration:.2f} seconds")
    except:
        duration = None
    
    try:
        # AGGRESSIVE Whisper parameters to capture ALL content
        result = model.transcribe(
            audio_path,
            language="fr",
            word_timestamps=True,
            verbose=False,
            temperature=0.0,  # Deterministic
            condition_on_previous_text=False,  # Prevent context bias
            
            # AGGRESSIVE parameters to capture more content
            no_speech_threshold=0.01,        # Very low threshold - capture everything including whispers
            logprob_threshold=-3.0,         # Very low threshold - accept lower confidence
            compression_ratio_threshold=8.0, # High threshold - allow more compression
            beam_size=20,                   # Large beam - better search
            best_of=20,                     # Many candidates - better selection
            
            # Enhanced prompt for complete French transcription
            initial_prompt="Transcription française COMPLÈTE. Inclure TOUS les mots et phrases entendus. Ne rien omettre.",
            
            # Additional parameters for completeness
            fp16=False,
            suppress_tokens=[-1],  # Don't suppress any tokens
            without_timestamps=False,  # Keep timestamps
            max_initial_timestamp=3.0,  # Allow longer initial silence
        )
        
        # Apply standard post-processing
        if "text" in result:
            result["text"] = post_process_transcription(result["text"])
        
        # Apply post-processing to segments too
        if "segments" in result:
            for segment in result["segments"]:
                if "text" in segment:
                    segment["text"] = post_process_transcription(segment["text"])
        
        # Add metadata about transcription completeness
        if "text" in result and result["text"]:
            word_count = len(result["text"].split())
            result["word_count"] = word_count
            result["transcription_completeness"] = "full" if word_count > 5 else "partial"
            print(f"   📝 Captured {word_count} words")
        
        return result
    
    except Exception as e:
        print(f"   ❌ Whisper transcription failed: {e}")
        # Try fallback with ultra-aggressive parameters
        print(f"   🔄 Trying fallback transcription...")
        return transcribe_with_whisper_fallback(model, audio_path)

def create_chunks_from_transcriptions(transcription_json, output_json):
    """Create chunks from existing transcriptions using rule-based chunking."""
    print(f"📖 Loading transcriptions from {transcription_json}")
    
    with open(transcription_json, 'r', encoding='utf-8') as f:
        transcriptions = json.load(f)
    
    results = []
    
    for transcription in transcriptions:
        audio_path = transcription.get("audio_path", "")
        audio_rel_path = transcription.get("audio_rel_path", "")
        test_name = transcription.get("test_name", "")
        full_text = transcription.get("full_text", "")
        words = transcription.get("words", [])
        model_used = transcription.get("model_used", "unknown")
        
        print(f"\n🔀 Chunking: {audio_rel_path}")
        
        if not words:
            print(f"  ⚠ No words found for {audio_rel_path}, skipping chunking")
            continue
        
        try:
            # Use rule-based chunking
            sentence_chunks = chunk_words(words)
            
            # Convert word chunks to text chunks
            chunks = []
            for chunk in sentence_chunks:
                chunk_text = ''.join(w["word"] for w in chunk).strip()
                start = round(chunk[0]["start"], 2)
                end = round(chunk[-1]["end"], 2)
                chunks.append({
                    "text": chunk_text,
                    "start": start,
                    "end": end
                })
            
            # Trim chunks after question (only if words is not empty)
            if words:
                chunks = trim_chunks_after_question(chunks, words)
            
            # Create final result object
            result_obj = {
                "audio_path": audio_path,
                "audio_rel_path": audio_rel_path,
                "test_name": test_name,
                "full_text": full_text,
                "chunks": chunks,
                "chunking_method": "rule_based",
                "chunk_count": len(chunks),
                "model_used": model_used
            }
            results.append(result_obj)
            print(f"  ✓ Created {len(chunks)} chunks")
            
        except Exception as e:
            print(f"  ⚠ Failed to chunk {audio_path}: {str(e)}")
            continue
        
    if not results:
        print("⚠ No files were successfully chunked!")
        sys.exit(1)
    
    # Save chunked results
    with open(output_json, "w", encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Saved {len(results)} chunked transcriptions to {output_json}")

def find_all_media_test_folders(assets_folder):
    """Find all media_test folders in the assets directory"""
    assets_path = Path(assets_folder)
    media_test_folders = []
    
    for item in assets_path.iterdir():
        if item.is_dir() and item.name.startswith('media_test'):
            media_test_folders.append(item)
    
    return sorted(media_test_folders)

def process_listening_assets_with_free_structure(assets_base_folder, output_folder):
    """
    Process both listening_asset and listening_asset_free folders.
    Organizes outputs into free/ and regular/ subfolders like the original structure.
    """
    assets_base_path = Path(assets_base_folder)
    output_path = Path(output_folder)
    
    # Create main output directory
    output_path.mkdir(exist_ok=True)
    
    # Define the folder mappings
    folder_configs = [
        {
            "source_folder": assets_base_path / "listening_asset",
            "output_subfolder": "regular",
            "description": "Regular (not free)"
        },
        {
            "source_folder": assets_base_path / "listening_asset_free", 
            "output_subfolder": "free",
            "description": "Free"
        }
    ]
    
    total_processed = 0
    
    for config in folder_configs:
        source_folder = config["source_folder"]
        output_subfolder = config["output_subfolder"] 
        description = config["description"]
        
        print(f"\n🎯 Processing {description} content from: {source_folder}")
        
        if not source_folder.exists():
            print(f"⚠ Folder not found: {source_folder}")
            continue
            
        # Create output subfolder (free/ or regular/)
        subfolder_output_path = output_path / output_subfolder
        subfolder_output_path.mkdir(exist_ok=True)
        
        # Find all media_test folders in this listening_asset folder
        media_test_folders = find_all_media_test_folders(source_folder)
        
        if not media_test_folders:
            print(f"⚠ No media_test folders found in {source_folder}")
            continue
            
        print(f"📁 Found {len(media_test_folders)} media_test folders")
        
        # Process each media_test folder
        for folder in media_test_folders:
            folder_name = folder.name
            print(f"\n📂 Processing {folder_name} ({description})...")
            
            # Define output file paths within the subfolder
            transcription_output = subfolder_output_path / f"transcriptions_{folder_name}.json"
            chunks_output = subfolder_output_path / f"chunks_{folder_name}.json"
            
            try:
                # Extract transcriptions
                extract_transcriptions(str(folder), str(transcription_output))
                
                # Create chunks
                create_chunks_from_transcriptions(str(transcription_output), str(chunks_output))
                
                total_processed += 1
                print(f"✅ {folder_name} completed -> {output_subfolder}/")
                
            except Exception as e:
                print(f"❌ Failed to process {folder_name}: {str(e)}")
                continue
    
    # Summary
    print(f"\n🎉 Processing Complete!")
    print(f"📊 Total media_test folders processed: {total_processed}")
    print(f"📁 Output structure:")
    print(f"   {output_folder}/")
    print(f"   ├── free/          (from listening_asset_free)")
    print(f"   │   ├── transcriptions_media_test*.json")
    print(f"   │   └── chunks_media_test*.json")
    print(f"   └── regular/       (from listening_asset)")
    print(f"       ├── transcriptions_media_test*.json")
    print(f"       └── chunks_media_test*.json")

def extract_from_both_listening_assets(assets_base_folder, output_folder):
    """
    Extract transcriptions from both listening_asset and listening_asset_free folders.
    Organizes outputs into free/ and regular/ subfolders but ONLY does extraction (no chunking).
    """
    assets_base_path = Path(assets_base_folder)
    output_path = Path(output_folder)
    
    # Create main output directory
    output_path.mkdir(exist_ok=True)
    
    # Define the folder mappings
    folder_configs = [
        {
            "source_folder": assets_base_path / "listening_asset",
            "output_subfolder": "regular",
            "description": "Regular (not free)"
        },
        {
            "source_folder": assets_base_path / "listening_asset_free", 
            "output_subfolder": "free",
            "description": "Free"
        }
    ]
    
    total_processed = 0
    
    for config in folder_configs:
        source_folder = config["source_folder"]
        output_subfolder = config["output_subfolder"] 
        description = config["description"]
        
        print(f"\n🎯 Extracting from {description} content: {source_folder}")
        
        if not source_folder.exists():
            print(f"⚠ Folder not found: {source_folder}")
            continue
            
        # Create output subfolder (free/ or regular/)
        subfolder_output_path = output_path / output_subfolder
        subfolder_output_path.mkdir(exist_ok=True)
        
        # Find all media_test folders in this listening_asset folder
        media_test_folders = find_all_media_test_folders(source_folder)
        
        if not media_test_folders:
            print(f"⚠ No media_test folders found in {source_folder}")
            continue
            
        print(f"📁 Found {len(media_test_folders)} media_test folders")
        
        # Process each media_test folder - EXTRACT ONLY
        for folder in media_test_folders:
            folder_name = folder.name
            print(f"\n📂 Extracting {folder_name} ({description})...")
            
            # Define output file path within the subfolder
            transcription_output = subfolder_output_path / f"transcriptions_{folder_name}.json"
            
            try:
                # Extract transcriptions ONLY
                extract_transcriptions(str(folder), str(transcription_output))
                
                total_processed += 1
                print(f"✅ {folder_name} extraction completed -> {output_subfolder}/")
                
            except Exception as e:
                print(f"❌ Failed to extract {folder_name}: {str(e)}")
                continue
    
    # Summary
    print(f"\n🎉 Extraction Complete!")
    print(f"📊 Total media_test folders extracted: {total_processed}")
    print(f"📁 Output structure:")
    print(f"   {output_folder}/")
    print(f"   ├── free/          (from listening_asset_free)")
    print(f"   │   └── transcriptions_media_test*.json")
    print(f"   └── regular/       (from listening_asset)")
    print(f"       └── transcriptions_media_test*.json")
    print(f"\n💡 Next step: Use 'chunk' command on individual JSON files when ready")

def validate_conversation_question(text: str) -> bool:
    """
    Validate that text looks like a proper conversation question.
    Should contain either a question mark OR incomplete sentence patterns AT THE END.
    This avoids false positives from questions in the middle of conversations.
    """
    if not text or not text.strip():
        return False
    
    text = text.strip()
    
    # Get the last 100 characters to focus on the ending
    # This helps avoid questions that appear early in conversations
    text_ending = text[-100:] if len(text) > 100 else text
    
    # Check for question marks at the end (last 50 characters)
    if '?' in text_ending:
        print(f"   ✓ Found question mark in ending: '...{text_ending[-50:]}'")
        return True
    
    # Check for incomplete sentence patterns at the very end
    incomplete_patterns = [
        r'\b(il|elle|je|tu|nous|vous|ils|elles)\s+(est|sont|a|ai|as|avons|avez|ont)\s*\.?\s*$',
        r'\b(c\'est|ce sont|voilà|voici)\s*\.?\s*$',
        r'\b(et|ou|mais|donc|car)\s*\.?\s*$',
        r'\b\w+\s+(est|sont|a|ont)\s*\.?\s*$',
        r'\b(dans|sur|avec|pour|sans|vers|chez)\s+\w+\s*\.?\s*$',  # Preposition endings
        r'\b(de|du|des|le|la|les|un|une)\s+\w+\s*\.?\s*$',         # Article endings
        r'\b(qui|que|dont|où)\s+\w+\s*\.?\s*$',                    # Relative pronouns (incomplete)
        r'\b(pour|sans|avec|sur|dans)\s+(le|la|les|un|une|des)\s+\w+\s*\.?\s*$',  # Prep + article
    ]
    
    for pattern in incomplete_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                print(f"   ✓ Found incomplete pattern at end: '...{text[match.start():]}'")
                return True
    
    # Additional check: ends with common conversation fragments
    conversation_endings = [
        r'\b(alors|donc|en fait|bon|bien)\s*\.?\s*$',              # Conversation fillers
        r'\b(effectivement|exactement|tout à fait)\s*\.?\s*$',     # Agreement words
        r'\b(maintenant|aujourd\'hui|actuellement)\s*\.?\s*$',    # Time references that trail off
    ]
    
    for pattern in conversation_endings:
        if re.search(pattern, text, re.IGNORECASE):
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                print(f"   ✓ Found conversation ending: '...{text[match.start():]}'")
                return True
    
    print(f"   ✗ No question/incomplete pattern found at end: '...{text[-50:]}'")
    return False

def validate_abcd_choices(text: str) -> bool:
    """
    Validate that text actually contains A,B,C,D choice patterns.
    This prevents accepting hallucinations or incorrect transcriptions for Type 1 questions.
    """
    if not text or not text.strip():
        return False
    
    text = text.strip()
    
    # Look for A,B,C,D patterns - SPECIFIC PATTERNS to avoid false positives
    abcd_patterns = [
        r'(?:^|\s|[.!?])\s*([A-D])[.]\s+\w+',          # "A. Word" format
        r'(?:^|\s|[.!?])\s*([A-D])\s+[A-Z]',           # "A Word" format (capital after)
        r'(?:^|\s|[.!?])\s*option\s+([A-D])',          # "option A" format  
        r'(?:^|\s|[.!?])\s*choix\s+([A-D])',           # "choix A" format (French)
        r'(?:^|\s|[.!?])\s*([A-D])[.]\s*[\w\s]+',      # "A. Multiple words"
        r'(?:^|\s|[.!?])\s*([A-D])\s*[)]\s*\w+',       # "A) Word" format
        r'(?:^|\s|[.!?])\s*[(]\s*([A-D])\s*[)]\s*\w+', # "(A) Word" format
    ]
    
    all_matches = []
    for pattern in abcd_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
        all_matches.extend(matches)
    
    if len(all_matches) >= 2:  # At least 2 choices found
        letters_found = [match.upper() for match in all_matches]
        unique_letters = list(set(letters_found))
        
        print(f"   ✓ Found A,B,C,D patterns: {unique_letters} ({len(all_matches)} total matches)")
        return True
    
    print(f"   ✗ No sufficient A,B,C,D patterns found (only {len(all_matches)} matches)")
    return False

def validate_density(text: str, audio_duration: float, audio_type: str) -> tuple[bool, str]:
    """
    Validate transcription density (words per minute) based on audio type.
    
    Args:
        text: Transcription text
        audio_duration: Duration of audio in seconds  
        audio_type: Type of audio ("abcd_choices" or "conversation_question")
    
    Returns:
        Tuple[bool, str]: (is_valid, reason)
    """
    if not text or not text.strip():
        return False, "Empty text"
    
    words = text.strip().split()
    word_count = len(words)
    
    # Calculate words per minute
    words_per_minute = (word_count / audio_duration) * 60 if audio_duration > 0 else 0
    
    # Type-aware density validation
    if audio_type == "abcd_choices":
        # Type 1: ABCD Choices - expect structured choices
        min_wpm = 20   # Minimum for meaningful ABCD choices
        max_wpm = 200  # Maximum before likely hallucination
        min_words = 8  # Less than 8 words is likely incomplete ABCD
        
        if word_count < min_words:
            return False, f"Too few words for ABCD: {word_count} words (need ≥{min_words})"
        
        if words_per_minute < min_wpm:
            return False, f"Too sparse for ABCD: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        
        if words_per_minute > max_wpm:
            return False, f"Too dense for ABCD: {words_per_minute:.1f} wpm (max {max_wpm})"
            
    else:
        # Type 2: Conversations/Questions - more permissive
        min_wpm = 10   # Lower threshold for conversations
        max_wpm = 300  # Higher threshold for natural speech
        
        if audio_duration > 10 and words_per_minute < min_wpm:
            return False, f"Too sparse for conversation: {words_per_minute:.1f} wpm (need ≥{min_wpm})"
        
        if words_per_minute > max_wpm:
            return False, f"Too dense for conversation: {words_per_minute:.1f} wpm (max {max_wpm})"
    
    # Check for excessive repetition
    if word_count > 5:
        word_freq = {}
        for word in words:
            clean_word = word.lower().strip('.,!?;:')
            word_freq[clean_word] = word_freq.get(clean_word, 0) + 1
        max_freq = max(word_freq.values())
        if max_freq / word_count > 0.6:  # More than 60% same word
            return False, f"Excessive repetition: {max_freq}/{word_count} same word"
    return True, f"Valid density: {words_per_minute:.1f} wpm, {word_count} words"

def transcribe_with_hierarchical_fallback(models: dict, audio_path: str) -> dict:
    """
    Enhanced hierarchical model fallback: Large-v3 → Turbo → Medium
    GUARANTEES full transcription by always returning the best available result.
    Validation is used for quality assessment but never blocks transcription.
    """
    # Get audio duration for density validation
    try:
        duration = librosa.get_duration(path=audio_path)
    except Exception as e:
        print(f"   ⚠ Could not determine duration: {e}")
        duration = 25.0  # Default assumption
    
    # Extract test info from audio path for JSON mapping
    test_name, question_num, is_free = extract_test_info_from_path(audio_path)
    print(f"🎯 Enhanced Hierarchical Fallback: {os.path.basename(audio_path)}")
    print(f"   Test: {test_name} Q{question_num} ({'free' if is_free else 'regular'}) - Duration: {duration:.1f}s")
    
    # Get expected audio type from JSON mapping
    expected_audio_type, is_valid, quality_info = detect_audio_type_from_mapping(test_name, question_num, is_free)
    print(f"   Expected type: {expected_audio_type}")
    
    # Define model hierarchy: Large-v3 → Turbo → Medium
    model_hierarchy = [
        ("large-v3", "PRIMARY"),
        ("turbo", "SECONDARY"), 
        ("medium", "FINAL")
    ]
    
    # Track all attempts and results
    validation_attempts = []
    all_model_results = {}
    best_result = None
    best_score = -1
    
    # Try each model in hierarchy
    for model_name, model_role in model_hierarchy:
        model = models.get(model_name)
        if model is None:
            print(f"   ⚠ {model_name} model not available, skipping...")
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "UNAVAILABLE",
                "reason": "Model not loaded"
            })
            continue
        
        print(f"\n🎵 TRYING: {model_name} ({model_role})")
        
        try:
            # Transcribe with the model
            result = transcribe_with_whisper(model, audio_path)
            if not result or not result.get("text"):
                print(f"   ❌ {model_name} failed to produce text")
                validation_attempts.append({
                    "model": model_name,
                    "role": model_role,
                    "status": "FAILED",
                    "reason": "No transcription text produced"
                })
                continue
            
            text = result.get("text", "").strip()
            all_model_results[model_name] = result  # Store for potential fallback
            print(f"   📝 Text: '{text[:80]}...'")
            
            # Calculate quality score (0-100)
            quality_score = 0
            
            # STEP 1: Density Validation (for scoring only)
            density_valid, density_reason = validate_density(text, duration, expected_audio_type)
            if density_valid:
                quality_score += 40
                print(f"   📊 Density check: ✅ {density_reason}")
            else:
                print(f"   📊 Density check: ⚠ {density_reason}")
            
            # STEP 2: Type Validation (for scoring only)
            type_valid = False
            type_reason = ""
            if expected_audio_type == "abcd_choices":
                type_valid = validate_abcd_choices(text)
                type_reason = "ABCD pattern found" if type_valid else "No ABCD patterns found"
            else:  # conversation_question
                type_valid = validate_conversation_question(text)
                type_reason = "Question/incomplete pattern found" if type_valid else "No question/incomplete pattern found"
            
            if type_valid:
                quality_score += 40
                print(f"   🎯 Type check: ✅ {type_reason}")
            else:
                print(f"   🎯 Type check: ⚠ {type_reason}")
            
            # STEP 3: Text length bonus (encourage longer transcriptions)
            word_count = len(text.split())
            if word_count >= 10:
                quality_score += 20
                print(f"   📏 Length bonus: ✅ {word_count} words")
            else:
                print(f"   📏 Length: ⚠ {word_count} words")
            
            print(f"   🏆 Quality score: {quality_score}/100")
            
            # Track this result
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "SUCCESS",
                "quality_score": quality_score,
                "density_check": density_reason,
                "type_check": type_reason,
                "word_count": word_count
            })
            
            # Update best result if this is better
            if quality_score > best_score:
                best_score = quality_score
                best_result = result
                print(f"   🎉 NEW BEST RESULT: {model_name} (score: {quality_score})")
            
            # If we get a very good result (80+), we can stop early
            if quality_score >= 80:
                print(f"   🎯 EXCELLENT RESULT: {model_name} achieved {quality_score}/100 - using this result")
                break
                
        except Exception as e:
            print(f"   ❌ {model_name} exception: {str(e)}")
            validation_attempts.append({
                "model": model_name,
                "role": model_role,
                "status": "EXCEPTION",
                "reason": str(e)
            })
            continue
    
    # GUARANTEE: Always return the best available result
    if best_result:
        print(f"\n✅ GUARANTEED TRANSCRIPTION: Using best result (score: {best_score}/100)")
        
        # Find which model produced the best result
        best_model = None
        for attempt in validation_attempts:
            if attempt.get("quality_score") == best_score:
                best_model = attempt["model"]
                break
        
        # Add metadata
        best_result["model_used"] = best_model or "unknown"
        best_result["model_role"] = "BEST_AVAILABLE"
        best_result["audio_type"] = expected_audio_type
        best_result["duration"] = duration
        best_result["validation_passed"] = best_score >= 60  # Consider 60+ as "passed"
        best_result["quality_score"] = best_score
        best_result["validation_summary"] = validation_attempts
        best_result["guaranteed_transcription"] = True
        best_result["fallback_reason"] = f"Best available result with score {best_score}/100"
        
        return best_result
    
    # Complete failure - no usable results (should rarely happen)
    print(f"\n💀 COMPLETE FAILURE: No usable transcription results available")
    return {
        "text": "[TRANSCRIPTION FAILED - NO MODELS PRODUCED TEXT]",
        "model_used": "none",
        "audio_type": expected_audio_type,
        "duration": duration,
        "validation_passed": False,
        "all_models_failed": True,
        "validation_summary": validation_attempts,
        "failure_reason": "All models failed to produce any transcription text",
        "available_models": list(models.keys())
    }

def extract_question_number(filename: str) -> int:
    """Extract question number from filename like Q14.mp3"""
    match = re.search(r'Q(\d+)', filename, re.IGNORECASE)
    if match:
        return int(match.group(1))
    return 999  # Default to high number if can't parse

def main():
    """Main command-line interface"""
    if len(sys.argv) < 3:
        print("Usage:")
        print("  python main_colab.py extract <folder_of_mp3s> <output_json>")
        print("  python main_colab.py chunk <transcription_json> <output_json>")
        print("  python main_colab.py combined <folder_of_mp3s> <output_json>")
        print("  python main_colab.py batch-assets <assets_folder> <output_folder>")
        print("  python main_colab.py batch-listening <assets_base> <output_folder>")
        print("  python main_colab.py extract-listening <assets_base> <output_folder>")
        print("  python main_colab.py batch-full <assets_base> <output_folder>")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "extract":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py extract <folder_of_mp3s> <output_json>")
            sys.exit(1)
        folder = sys.argv[2]
        output = sys.argv[3]
        extract_transcriptions(folder, output)
        
    elif command == "chunk":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py chunk <transcription_json> <output_json>")
            sys.exit(1)
        transcription_json = sys.argv[2]
        output = sys.argv[3]
        create_chunks_from_transcriptions(transcription_json, output)
        
    elif command == "combined":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py combined <folder_of_mp3s> <output_json>")
            sys.exit(1)
        folder = sys.argv[2]
        output = sys.argv[3]
        
        # Extract then chunk
        temp_transcriptions = output.replace('.json', '_transcriptions.json')
        extract_transcriptions(folder, temp_transcriptions)
        create_chunks_from_transcriptions(temp_transcriptions, output)
        
        # Clean up temporary file
        os.unlink(temp_transcriptions)
        
    elif command == "batch-assets":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py batch-assets <assets_folder> <output_folder>")
            sys.exit(1)
        assets_folder = sys.argv[2]
        output_folder = sys.argv[3]
        
        # Process all media_test folders
        media_test_folders = find_all_media_test_folders(assets_folder)
        if not media_test_folders:
            print(f"⚠ No media_test folders found in {assets_folder}")
            sys.exit(1)
            
        output_path = Path(output_folder)
        output_path.mkdir(exist_ok=True)
        
        for folder in media_test_folders:
            folder_name = folder.name
            transcription_output = output_path / f"transcriptions_{folder_name}.json"
            chunks_output = output_path / f"chunks_{folder_name}.json"
            
            print(f"\n🎯 Processing {folder_name}...")
            extract_transcriptions(str(folder), str(transcription_output))
            create_chunks_from_transcriptions(str(transcription_output), str(chunks_output))
            
        print(f"\n✅ Batch processing complete! Results in {output_folder}")
        
    elif command == "batch-listening":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py batch-listening <assets_base> <output_folder>")
            print("  This processes both listening_asset and listening_asset_free")
            print("  Organizes output into free/ and regular/ subfolders")
            sys.exit(1)
        assets_base = sys.argv[2]
        output_folder = sys.argv[3]
        
        process_listening_assets_with_free_structure(assets_base, output_folder)
        
    elif command == "extract-listening":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py extract-listening <assets_base> <output_folder>")
            print("  This extracts from both listening_asset and listening_asset_free")
            print("  Organizes output into free/ and regular/ subfolders")
            print("  EXTRACTION ONLY - no chunking")
            sys.exit(1)
        assets_base = sys.argv[2]
        output_folder = sys.argv[3]
        
        extract_from_both_listening_assets(assets_base, output_folder)
        
    elif command == "batch-full":
        if len(sys.argv) != 4:
            print("Usage: python main_colab.py batch-full <assets_base> <output_folder>")
            print("  This is an alias for batch-listening - processes both listening_asset and listening_asset_free")
            print("  Enhanced with Large-v3 → Turbo → Medium hierarchical fallback")
            print("  Each model must pass both density and type validation")
            sys.exit(1)
        assets_base = sys.argv[2]
        output_folder = sys.argv[3]
        
        print("🚀 BATCH-FULL: Enhanced processing with hierarchical fallback validation")
        print("   Models: Large-v3 → Turbo → Medium")
        print("   Validation: Both density AND type checks required")
        print("   Failure tracking: Detailed notes when all three models fail")
        
        process_listening_assets_with_free_structure(assets_base, output_folder)
        
    else:
        print(f"Unknown command: {command}")
        sys.exit(1)

if __name__ == "__main__":
    main() 