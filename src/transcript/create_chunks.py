#!/usr/bin/env python3
"""
Chunking Script for Dehallucinated Transcriptions
Creates timestamped chunks from full text based on audio type and sentence structure.
"""

import json
import re
import os
from typing import List, Dict, Tuple
from pathlib import Path
import string

# Sentence punctuation and connectors for chunking (from existing chunking.py)
PUNCT = {".", "?", "!"}
CONNECTORS = {
    "et", "mais", "parce", "cependant", "donc", "alors", "puis",
    "ensuite", "quand", "lorsque", "tandis", "pourtant", ","
}

def detect_audio_type(text: str) -> str:
    """
    Detect the type of audio content based on text patterns.
    Returns: 'abcd_choices' or 'conversation_question'
    """
    if not text:
        return 'conversation_question'
    
    # Look for multiple choice options
    options_found = set()
    option_matches = re.findall(r'\b[A-D]\.', text)
    options_found.update(match[0] for match in option_matches)
    
    # Check if we have multiple choice options
    if len(options_found) >= 2:
        return 'abcd_choices'
    else:
        return 'conversation_question'

def split_into_sentences(text: str) -> List[str]:
    """
    Split text into sentences, handling edge cases and preserving structure.
    Enhanced to handle abbreviations, titles, and other non-sentence-ending periods.
    Also splits at '. ' followed by a capital letter if not an abbreviation.
    Improved: Avoid splitting after abbreviations like 'M.' when followed by a capitalized name (e.g., 'M. Didier').
    """
    if not text:
        return []
    
    # Clean up extra whitespace
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Common French abbreviations and titles that shouldn't end sentences
    ABBREVIATIONS = {
        'M.', 'Mme.', 'Mlle.', 'Dr.', 'Pr.', 'Prof.', 'Mgr.', 'St.', 'Ste.',
        'av.', 'boul.', 'c.-à-d.', 'cf.', 'chap.', 'col.', 'éd.', 'ex.', 'fig.',
        'ibid.', 'id.', 'i.e.', 'l.c.', 'n.', 'n°', 'p.', 'pp.', 'q.v.', 's.',
        's.d.', 's.l.', 't.', 'tél.', 'vol.', 'vs.', 'v.', 'v°', 'v.g.'
    }
    
    sentences = []
    current_sentence = ""
    parts = re.split(r'([.!?]+)', text)
    i = 0
    while i < len(parts):
        if i + 1 < len(parts):
            sentence_part = parts[i].strip()
            punctuation = parts[i + 1]
            if sentence_part:
                should_split = True
                if punctuation == '.':
                    words_before_period = sentence_part.split()
                    if words_before_period:
                        last_word = words_before_period[-1]
                        # If last word is an abbreviation, check next part
                        if last_word in ABBREVIATIONS:
                            # Look ahead: if next part starts with capital, likely a name, do not split
                            if i + 2 < len(parts):
                                next_part = parts[i + 2].strip()
                                if next_part and next_part[0].isupper():
                                    should_split = False
                        elif re.match(r'^[A-Z]\.$', last_word):
                            should_split = False
                        elif re.match(r'^[A-Z][a-z]+\.$', last_word):
                            should_split = False
                        if not should_split and i + 2 < len(parts):
                            next_part = parts[i + 2].strip()
                            if next_part and next_part[0].isupper():
                                should_split = False
                            else:
                                should_split = True
                if should_split:
                    current_sentence += sentence_part + punctuation
                    sentences.append(current_sentence.strip())
                    current_sentence = ""
                else:
                    current_sentence += sentence_part + punctuation
        else:
            if parts[i].strip():
                current_sentence += parts[i].strip()
        i += 2
    if current_sentence.strip():
        sentences.append(current_sentence.strip())
    # Post-process: merge any fragments that were incorrectly split
    merged_sentences = []
    i = 0
    while i < len(sentences):
        current = sentences[i]
        if (i + 1 < len(sentences) and 
            current.strip().endswith('.') and 
            sentences[i + 1].strip() and 
            sentences[i + 1].strip()[0].isupper()):
            words = current.split()
            if words:
                last_word = words[-1]
                if (last_word in ABBREVIATIONS or 
                    re.match(r'^[A-Z]\.$', last_word) or 
                    re.match(r'^[A-Z][a-z]+\.$', last_word)):
                    merged = current + " " + sentences[i + 1]
                    merged_sentences.append(merged)
                    i += 2
                    continue
        merged_sentences.append(current)
        i += 1
    # If only one sentence and it contains both a period and a question mark, forcibly split at the last period before the question
    if len(merged_sentences) == 1 and '.' in merged_sentences[0] and '?' in merged_sentences[0]:
        s = merged_sentences[0]
        last_period = s.rfind('.')
        last_question = s.rfind('?')
        if last_period != -1 and last_question != -1 and last_period < last_question:
            before = s[:last_period+1].strip()
            after = s[last_period+1:].strip()
            merged_sentences = [before, after]
    return [s for s in merged_sentences if s.strip()]

def break_long_sentence(sentence: str, max_words: int = 25) -> List[str]:
    """
    Break down very long sentences using existing logic.
    """
    if not sentence:
        return []
    
    words = sentence.split()
    if len(words) <= max_words:
        return [sentence]
    
    # Try to break at natural connectors
    chunks = []
    current_chunk = []
    current_word_count = 0
    
    for word in words:
        current_chunk.append(word)
        current_word_count += 1
        
        # Check if we should break here
        should_break = False
        
        # Break at sentence endings
        if word.endswith(('.', '!', '?')):
            should_break = True
        # Break at connectors if we have enough words
        elif current_word_count >= max_words and word.lower() in CONNECTORS:
            should_break = True
        # Force break if too long
        elif current_word_count >= max_words * 1.5:
            should_break = True
        
        if should_break and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = []
            current_word_count = 0
    
    # Add remaining words
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    
    return chunks

def extract_choices(text: str) -> List[str]:
    """
    Extract A, B, C, D choices from text.
    """
    choices = []
    
    # Split by choice letters and reconstruct
    parts = re.split(r'\b([A-D]\.)', text)
    
    for i in range(1, len(parts), 2):  # Skip first part (before first choice)
        if i + 1 < len(parts):
            choice_letter = parts[i]
            choice_content = parts[i + 1].strip()
            if choice_content:
                # Clean up the choice content
                choice_content = re.sub(r'^\s*[A-D]\.\s*', '', choice_content)  # Remove any leading choice letter
                choices.append(choice_letter + " " + choice_content)
    
    return choices

def find_text_timestamp(text: str, words: List[Dict]) -> Dict:
    """
    Find the start and end timestamps for a given text within the words list.
    More robust version that ensures we don't miss content.
    """
    if not text or not words:
        return None
    
    # Clean the text for matching
    clean_text = re.sub(r'\s+', ' ', text.strip())
    
    # Find the text in the full word sequence
    full_text = ''.join(w["word"] for w in words)
    
    # Try to find the text position
    text_pos = full_text.find(clean_text)
    if text_pos == -1:
        # Try with more flexible matching
        text_words = clean_text.split()
        if len(text_words) < 2:
            return None
        
        # Find approximate position
        start_word_idx = -1
        end_word_idx = -1
        
        for i, word in enumerate(words):
            word_text = word["word"].strip()
            if start_word_idx == -1 and text_words[0] in word_text:
                start_word_idx = i
            elif start_word_idx != -1:
                # Check if we've found the end
                if text_words[-1] in word_text:
                    end_word_idx = i
                    break
        
        if start_word_idx != -1 and end_word_idx != -1:
            return {
                "text": clean_text,
                "start": round(words[start_word_idx]["start"], 2),
                "end": round(words[end_word_idx]["end"], 2)
            }
    else:
        # Found exact match
        current_pos = 0
        start_word_idx = -1
        end_word_idx = -1
        
        for i, word in enumerate(words):
            word_text = word["word"]
            word_end = current_pos + len(word_text)
            
            if start_word_idx == -1 and text_pos >= current_pos and text_pos < word_end:
                start_word_idx = i
            
            if start_word_idx != -1 and text_pos + len(clean_text) <= word_end:
                end_word_idx = i
                break
            
            current_pos = word_end
        
        if start_word_idx != -1 and end_word_idx != -1:
            return {
                "text": clean_text,
                "start": round(words[start_word_idx]["start"], 2),
                "end": round(words[end_word_idx]["end"], 2)
            }
    
    # If we still can't find it, try a more aggressive approach
    # Find the best approximate match
    text_words = clean_text.split()
    if len(text_words) >= 2:
        # Find first and last word positions
        first_word = text_words[0]
        last_word = text_words[-1]
        
        start_word_idx = -1
        end_word_idx = -1
        
        for i, word in enumerate(words):
            word_text = word["word"].strip()
            if start_word_idx == -1 and first_word in word_text:
                start_word_idx = i
            if last_word in word_text:
                end_word_idx = i
        
        if start_word_idx != -1 and end_word_idx != -1 and end_word_idx >= start_word_idx:
            return {
                "text": clean_text,
                "start": round(words[start_word_idx]["start"], 2),
                "end": round(words[end_word_idx]["end"], 2)
            }
    
    return None

def create_chunks_for_abcd_choices(text: str, words: List[Dict]) -> List[Dict]:
    """    Create chunks for Type 1 (ABCD choices) - each choice is a segment.
    Optionally, chunk the prompt (before A.) as its own segment.
    """
    chunks = []
    # Extract choices
    choices = extract_choices(text)
    if not choices:
        # Fallback: treat as regular text
        return create_chunks_for_conversation_question(text, words)
    print(f"    🔤 Found {len(choices)} choices: {[c[:20] + '...' if len(c) > 20 else c for c in choices]}")
    # Optionally, extract the prompt (before A.)
    first_choice_idx = text.find(choices[0])
    if first_choice_idx > 0:
        prompt = text[:first_choice_idx].strip()
        if prompt:
            prompt_chunk = find_text_timestamp(prompt, words)
            if not prompt_chunk:
                prompt_chunk = create_fallback_chunk(prompt, words, 0, 1)
            if prompt_chunk:
                chunks.append(prompt_chunk)
                print(f"    ✅ Added prompt chunk: '{prompt_chunk['text'][:30]}...' ({prompt_chunk['start']}s - {prompt_chunk['end']}s)")
    # Create chunks for each choice
    for i, choice in enumerate(choices):
        choice_chunk = find_text_timestamp(choice, words)
        if not choice_chunk:
            choice_chunk = create_fallback_chunk(choice, words, i, len(choices))
            if choice_chunk:
                print(f"    🔧 Fallback chunk for: '{choice[:30]}...' ({choice_chunk['start']}s - {choice_chunk['end']}s)")
        if choice_chunk:
            chunks.append(choice_chunk)
            print(f"    ✅ Added choice: '{choice_chunk['text'][:30]}...' ({choice_chunk['start']}s - {choice_chunk['end']}s)")
        else:
            print(f"    ⚠️ Could not chunk choice: '{choice[:30]}...'")
    return chunks

def create_chunks_for_conversation_question(text: str, words: List[Dict]) -> List[Dict]:
    """
    Create chunks for Type 2 (conversation/question) - final sentence is always a separate segment.
    """
    chunks = []
    # Split into sentences
    sentences = split_into_sentences(text)
    if not sentences:
        return []
    print(f"    📝 Found {len(sentences)} sentences: {[s[:30] + '...' if len(s) > 30 else s for s in sentences]}")
    # All but the last sentence
    for i, sentence in enumerate(sentences[:-1]):
        # Break long sentences
        sentence_chunks = break_long_sentence(sentence)
        for chunk_text in sentence_chunks:
            chunk = find_text_timestamp(chunk_text, words)
            if not chunk:
                chunk = create_fallback_chunk(chunk_text, words, i, len(sentences))
            if chunk:
                chunks.append(chunk)
                print(f"    ✅ Added chunk: '{chunk['text'][:30]}...' ({chunk['start']}s - {chunk['end']}s)")
            else:
                print(f"    ⚠️ Could not chunk: '{chunk_text[:30]}...'")
    # Last sentence is always a separate segment
    last_sentence = sentences[-1]
    last_chunk = find_text_timestamp(last_sentence, words)
    if not last_chunk:
        last_chunk = create_fallback_chunk(last_sentence, words, len(sentences)-1, len(sentences))
    if last_chunk:
        chunks.append(last_chunk)
        print(f"    ✅ Added final sentence chunk: '{last_chunk['text'][:30]}...' ({last_chunk['start']}s - {last_chunk['end']}s)")
    else:
        print(f"    ⚠️ Could not chunk final sentence: '{last_sentence[:30]}...'")
    return chunks

def create_fallback_chunk(text: str, words: List[Dict], sentence_index: int, total_sentences: int) -> Dict:
    """
    Create a fallback chunk when exact timestamp matching fails.
    """
    if not words:
        return None
    
    # Estimate position based on sentence index
    words_per_sentence = len(words) / total_sentences
    start_idx = int(sentence_index * words_per_sentence)
    end_idx = min(int((sentence_index + 1) * words_per_sentence), len(words) - 1)
    
    if start_idx < len(words) and end_idx < len(words):
        return {
            "text": text,
            "start": round(words[start_idx]["start"], 2),
            "end": round(words[end_idx]["end"], 2)
        }
    
    return None

def find_missing_content(full_text: str, existing_chunks: List[Dict], words: List[Dict]) -> List[Dict]:
    """
    Find content that wasn't captured by existing chunks and create fallback chunks.
    """
    missing_chunks = []
    
    # Get all text from existing chunks
    covered_text = ' '.join(chunk['text'] for chunk in existing_chunks)
    
    # Split full text into sentences
    sentences = split_into_sentences(full_text)
    
    for sentence in sentences:
        # Check if this sentence is already covered
        if sentence not in covered_text and len(sentence.strip()) > 5:
            # Try to find timestamp for this missing sentence
            chunk = find_text_timestamp(sentence, words)
            if chunk:
                missing_chunks.append(chunk)
            else:
                # Create fallback chunk
                fallback = create_fallback_chunk(sentence, words, 0, 1)
                if fallback:
                    missing_chunks.append(fallback)
    
    return missing_chunks

def find_last_question(text: str, words: List[Dict]) -> Dict:
    """
    Find the last question in the text and return its chunk.
    """
    if not text or not words:
        return None
    
    # Split into sentences and find the last question
    sentences = split_into_sentences(text)
    
    for sentence in reversed(sentences):
        if sentence.strip().endswith('?'):
            return find_text_timestamp(sentence, words)
    
    return None

def split_long_chunk(chunk, words, max_duration=10.0, min_words=5):
    """
    If a chunk is too long, split it at the largest pause or by word count.
    Returns a list of smaller chunks.
    """
    start = chunk['start']
    end = chunk['end']
    text = chunk['text']
    duration = end - start
    if duration <= max_duration:
        return [chunk]

    # Find the words that correspond to this chunk
    chunk_words = []
    for w in words:
        if w['start'] >= start and w['end'] <= end:
            chunk_words.append(w)
    if len(chunk_words) < min_words:
        return [chunk]  # Too few words to split

    # Find the largest pause between words
    pauses = []
    for i in range(1, len(chunk_words)):
        pause = chunk_words[i]['start'] - chunk_words[i-1]['end']
        pauses.append((pause, i))
    # Find the largest pause above 0.7s
    pauses_over = [(p, idx) for p, idx in pauses if p > 0.7]
    if pauses_over:
        # Split at the largest pause
        _, split_idx = max(pauses_over)
        first_words = chunk_words[:split_idx]
        second_words = chunk_words[split_idx:]
        first_text = ''.join(w['word'] for w in first_words).strip()
        second_text = ''.join(w['word'] for w in second_words).strip()
        return [
            {
                'text': first_text,
                'start': first_words[0]['start'],
                'end': first_words[-1]['end']
            },
            {
                'text': second_text,
                'start': second_words[0]['start'],
                'end': second_words[-1]['end']
            }
        ]
    # Otherwise, split by word count (half)
    mid = len(chunk_words) // 2
    first_words = chunk_words[:mid]
    second_words = chunk_words[mid:]
    first_text = ''.join(w['word'] for w in first_words).strip()
    second_text = ''.join(w['word'] for w in second_words).strip()
    return [
        {
            'text': first_text,
            'start': first_words[0]['start'],
            'end': first_words[-1]['end']
        },
        {
            'text': second_text,
            'start': second_words[0]['start'],
            'end': second_words[-1]['end']
        }
    ]

def postprocess_split_long_chunks(chunks, words, max_duration=10.0):
    """
    For each chunk, if its duration is too long, split it further.
    """
    new_chunks = []
    for chunk in chunks:
        duration = chunk['end'] - chunk['start']
        if duration > max_duration:
            split_chunks = split_long_chunk(chunk, words, max_duration=max_duration)
            new_chunks.extend(split_chunks)
        else:
            new_chunks.append(chunk)
    return new_chunks

def filter_words_to_full_text(words, full_text):
    """
    Return only the words that appear in the cleaned full_text, in order.
    """
    filtered = []
    text = full_text
    for w in words:
        word_stripped = w['word'].strip()
        # Try to find the word in the text (case-insensitive, ignore extra spaces)
        idx = text.lower().find(word_stripped.lower())
        if idx != -1:
            filtered.append(w)
            # Remove up to and including this word from text to avoid duplicate matches
            text = text[idx + len(word_stripped):]
    return filtered

def normalize_text_for_compare(t):
    return t.lower().translate(str.maketrans('', '', string.punctuation)).replace(' ', '')

def ensure_final_question_is_separate(chunks, words, full_text):
    """
    Ensure the final question is always a separate segment, and all content in full_text is chunked.
    If timestamp matching fails, split by word alignment.
    Remove duplicate/overlapping chunks for the final question.
    Do not allow the pre-question chunk to include the question sentence.
    Remove any chunk (except the final question chunk) that contains the final question as a substring (normalized).
    """
    if not chunks:
        return chunks
    last_chunk = chunks[-1]
    text = last_chunk['text']
    # Use sentence splitter to find the last question
    sentences = split_into_sentences(text)
    question_sentence = None
    pre_question = None
    for s in reversed(sentences):
        if s.strip().endswith('?'):
            question_sentence = s.strip()
            break
    if question_sentence:
        idx = text.rfind(question_sentence)
        pre_question = text[:idx].strip()
        # Remove the question sentence from pre_question if present
        if pre_question.endswith(question_sentence):
            pre_question = pre_question[:-len(question_sentence)].strip()
        new_chunks = chunks[:-1]
        before_chunk = None
        question_chunk = None
        if pre_question:
            before_chunk = find_text_timestamp(pre_question, words)
            if not before_chunk:
                before_chunk = create_fallback_chunk(pre_question, words, 0, 1)
        question_chunk = find_text_timestamp(question_sentence, words)
        if not question_chunk:
            question_chunk = create_fallback_chunk(question_sentence, words, 0, 1)
        # Only add the full question sentence chunk (with the question mark)
        if before_chunk:
            new_chunks.append(before_chunk)
        if question_chunk:
            new_chunks.append(question_chunk)
        # Remove any chunk that is a substring of another chunk or is just a question mark
        unique_chunks = []
        seen_texts = set()
        for c in new_chunks:
            t = c['text'].strip()
            if t == '?' or any(t != other['text'].strip() and t in other['text'].strip() for other in new_chunks):
                continue
            if t not in seen_texts:
                unique_chunks.append(c)
                seen_texts.add(t)
        # Remove any chunk (except the final question chunk) that contains the normalized final question as a substring
        if question_sentence:
            question_text_norm = normalize_text_for_compare(question_sentence)
            filtered_chunks = []
            for c in unique_chunks:
                t_norm = normalize_text_for_compare(c['text'])
                if t_norm == question_text_norm or question_text_norm not in t_norm:
                    filtered_chunks.append(c)
                else:
                    # This chunk contains the final question as a substring, skip it
                    continue
            unique_chunks = filtered_chunks
        # After splitting, verify all content is covered
        covered = ''.join(c['text'] for c in unique_chunks).replace(' ', '').replace('\n', '')
        target = full_text.replace(' ', '').replace('\n', '')
        if covered != target:
            missing = full_text
            # Remove the question sentence from missing
            missing = missing.replace(question_sentence, '')
            for c in unique_chunks:
                missing = missing.replace(c['text'], '')
            # Only add fallback if it does NOT contain the normalized final question
            if missing.strip():
                missing_norm = normalize_text_for_compare(missing.strip())
                if question_sentence:
                    question_text_norm = normalize_text_for_compare(question_sentence)
                    if question_text_norm in missing_norm:
                        # Do not add fallback chunk if it contains the question
                        return unique_chunks
                fallback = create_fallback_chunk(missing.strip(), words, 0, 1)
                if fallback:
                    unique_chunks.append(fallback)
        return unique_chunks
    return chunks

def process_single_transcription(transcription: Dict) -> Dict:
    """
    Process a single transcription and create chunks.
    """
    full_text = transcription.get('full_text', '')
    words = transcription.get('words', [])
    
    if not full_text or not words:
        print("  ⚠️ No text or words found, skipping")
        return None
    
    # Filter words to only those in full_text
    words = filter_words_to_full_text(words, full_text)
    
    print(f"  📝 Text: '{full_text[:50]}...'")
    print(f"  📊 Words: {len(words)}")
    
    # Detect audio type
    audio_type = detect_audio_type(full_text)
    print(f"  🎯 Audio type: {audio_type}")
    
    # Create chunks based on type
    if audio_type == 'abcd_choices':
        chunks = create_chunks_for_abcd_choices(full_text, words)
    else:
        chunks = create_chunks_for_conversation_question(full_text, words)
    
    # Post-process: split long chunks
    chunks = postprocess_split_long_chunks(chunks, words, max_duration=10.0)
    
    # Ensure final question is always a separate segment
    if audio_type == 'conversation_question':
        chunks = ensure_final_question_is_separate(chunks, words, full_text)
        # Guarantee all full_text is covered (only for conversation type)
        covered = ''.join(c['text'] for c in chunks).replace(' ', '').replace('\n', '')
        target = full_text.replace(' ', '').replace('\n', '')
        if covered != target:
            # Add a fallback chunk for any missing content
            missing = full_text
            for c in chunks:
                missing = missing.replace(c['text'], '')
            if missing.strip():
                fallback = create_fallback_chunk(missing.strip(), words, 0, 1)
                if fallback:
                    print(f"  🔧 Added fallback chunk for missing content: '{missing.strip()[:30]}...'" )
                    chunks.append(fallback)
    print(f"  ✅ Created {len(chunks)} chunks")
    
    # Create result object - remove words array to save space
    result = transcription.copy()
    result['chunks'] = chunks
    result['chunking_method'] = 'rule_based'
    result['chunk_count'] = len(chunks)
    result['audio_type'] = audio_type
    
    # Remove the words array to save space in output
    if 'words' in result:
        del result['words']
    
    return result

def process_json_file(input_path: str, output_path: str = None):
    """
    Process a single JSON file and create chunks.
    """
    print(f"📁 Processing: {input_path}")
    
    # Load the JSON file
    with open(input_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if isinstance(data, list):
        # Multiple transcriptions
        results = []
        for i, transcription in enumerate(data):
            print(f"\n[{i+1}/{len(data)}] Processing transcription...")
            result = process_single_transcription(transcription)
            if result:
                results.append(result)
        
        # Save results
        if output_path is None:
            output_path = input_path.replace('.json', '_chunked.json')
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 Saved chunked results to: {output_path}")
        print(f"📊 Processed {len(results)} transcriptions")
        
    else:
        # Single transcription
        result = process_single_transcription(data)
        if result:
            if output_path is None:
                output_path = input_path.replace('.json', '_chunked.json')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Saved chunked results to: {output_path}")

def main():
    """
    Main function for testing.
    """
    # Test with a sample from the provided data
    sample_data = {
        "full_text": "A. Allez-y, entrez. B. Asseyez-vous, je vous en prie. C. Fermez la porte, s'il vous plaît. D. Merci pour ce café",
        "words": [
            {"word": " A.", "start": 0.0, "end": 0.34},
            {"word": " Allez", "start": 1.3, "end": 1.88},
            {"word": "-y,", "start": 1.88, "end": 2.16},
            {"word": " entrez.", "start": 2.2, "end": 3.04},
            {"word": " B.", "start": 5.86, "end": 6.44},
            {"word": " Asseyez", "start": 6.44, "end": 7.02},
            {"word": "-vous,", "start": 7.02, "end": 7.32},
            {"word": " je", "start": 7.36, "end": 7.76},
            {"word": " vous", "start": 7.76, "end": 7.88},
            {"word": " en", "start": 7.88, "end": 8.08},
            {"word": " prie.", "start": 8.08, "end": 8.42},
            {"word": " C.", "start": 11.1, "end": 11.68},
            {"word": " Fermez", "start": 11.68, "end": 12.26},
            {"word": " la", "start": 12.26, "end": 12.36},
            {"word": " porte,", "start": 12.36, "end": 12.64},
            {"word": " s", "start": 12.76, "end": 12.9},
            {"word": "'il", "start": 12.9, "end": 13.0},
            {"word": " vous", "start": 13.0, "end": 13.1},
            {"word": " plaît.", "start": 13.1, "end": 13.42},
            {"word": " D.", "start": 15.32, "end": 15.9},
            {"word": " Merci", "start": 16.78, "end": 17.36},
            {"word": " pour", "start": 17.36, "end": 17.64},
            {"word": " ce", "start": 17.64, "end": 17.76},
            {"word": " café.", "start": 17.76, "end": 18.06}
        ]
    }
    
    result = process_single_transcription(sample_data)
    
    if result:
        print(f"\n📋 Result:")
        print(f"Audio type: {result['audio_type']}")
        print(f"Chunks created: {len(result['chunks'])}")
        
        for i, chunk in enumerate(result['chunks']):
            print(f"\nChunk {i+1}:")
            print(f"  Text: '{chunk['text']}'")
            print(f"  Time: {chunk['start']}s - {chunk['end']}s")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        input_path = sys.argv[1]
        output_path = None
        if len(sys.argv) > 2:
            output_path = sys.argv[2]
        process_json_file(input_path, output_path)
    else:
        main()
