# TCF-Canada Transcript Processing System

A comprehensive audio transcription and processing pipeline for French language learning content, specifically designed for TCF (Test de Connaissance du Français) Canada audio materials.

## 🏗️ Architecture Overview

This system processes audio files through multiple stages to generate accurate, chunked transcriptions with proper timestamps for different types of French audio content.

### Processing Pipeline

```
Audio Files → Whisper Transcription → Dehallucination → Chunking → Final Output
```

## 📁 Directory Structure

```
src/transcript/
├── README.md                    # This file
├── .gitignore                   # Git ignore rules
├── main_colab.py               # Main entry point for transcription
├── dehallucination.py          # Hallucination detection and correction
├── chunking.py                 # Legacy chunking system
├── create_chunks.py            # Rule-based chunking implementation
├── create_chunk_llm.py         # LLM + robust rule-based chunking
├── docs/                       # Documentation and configuration
│   ├── listening_question_types_free.json
│   └── listening_question_types_regular.json
├── scripts/                    # Batch processing and utility scripts
│   ├── batch_chunk_llm.py
│   ├── batch_dehallucination.py
│   ├── analyze_fallback_failures.py
│   ├── extract_critical_failures.py
│   ├── rerun_critical_failures_fallback.py
│   └── revise_scraped_files.py
├── utils/                      # Utility modules
│   ├── audio_processing.py
│   └── validation.py
├── outputs/                    # Raw transcription outputs
│   ├── free/
│   └── regular/
├── output_dehallucinated/      # Cleaned transcriptions
│   ├── free/
│   └── regular/
└── output_chunked/            # Final chunked outputs
    ├── free/
    └── regular/
```

## 🚀 Quick Start

### Prerequisites

```bash
pip install whisper openai pydub librosa
```

### Environment Setup

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### Basic Usage

1. **Single File Transcription**:
```python
from main_colab import process_audio_file

result = process_audio_file("path/to/audio.wav", "test1", "free")
```

2. **Batch Processing**:
```bash
cd src/transcript/scripts
python3 batch_dehallucination.py
python3 batch_chunk_llm.py
```

## 🔧 Core Components

### 1. Main Transcription (`main_colab.py`)

**Purpose**: Primary entry point for audio transcription using Whisper
**Key Features**:
- Multi-model fallback (Large-v3 → Turbo → Medium)
- Audio preprocessing for better results
- Question type detection
- Validation and quality checks

**Usage**:
```python
result = process_audio_file(audio_path, test_name, test_type)
```

### 2. Dehallucination (`dehallucination.py`)

**Purpose**: Removes Whisper hallucinations and improves transcript quality
**Key Features**:
- Pattern-based hallucination detection
- Repetition removal
- Text normalization
- Quality validation

**Usage**:
```python
from dehallucination import Dehallucinator
dehallucinator = Dehallucinator()
cleaned = dehallucinator.process_file("input.json", "output.json")
```

### 3. Chunking System

#### Legacy Chunking (`chunking.py`)
- Basic word-level chunking
- Simple timestamp alignment

#### Rule-Based Chunking (`create_chunks.py`)
- Sentence-based chunking
- ABCD choice detection
- Better timestamp accuracy

#### Robust LLM Chunking (`create_chunk_llm.py`)
- **Recommended**: Most advanced chunking system
- LLM-powered ABCD choice chunking
- Robust rule-based conversation/question chunking
- Multi-strategy timestamp alignment
- Automatic overlap detection and fixing

**Usage**:
```python
from create_chunk_llm import process_single_transcription
result = process_single_transcription(transcription_data)
```

## 📊 Audio Types

### ABCD Choices
- Multiple choice questions with options A, B, C, D
- Each choice becomes a separate chunk
- Uses LLM for intelligent parsing

### Conversation/Question
- Dialogue or narrative content
- Sentence-based chunking
- Final question always isolated
- Uses robust rule-based chunking

## 🛠️ Utility Scripts

### Batch Processing
- `batch_dehallucination.py`: Process all raw transcriptions
- `batch_chunk_llm.py`: Generate chunks for all dehallucinated files

### Analysis & Debugging
- `analyze_fallback_failures.py`: Identify transcription failures
- `extract_critical_failures.py`: Extract problematic cases
- `rerun_critical_failures_fallback.py`: Retry failed transcriptions

### Maintenance
- `revise_scraped_files.py`: Update existing transcription files

## 🔍 Quality Assurance

### Validation Checks
- **Density validation**: Ensures reasonable word-per-second ratios
- **Silence detection**: Identifies potential hallucinations
- **Timestamp validation**: Verifies chronological order
- **Content validation**: Checks for repetitions and artifacts

### Fallback System
- Multiple Whisper model attempts
- Graceful degradation on failures
- Comprehensive error logging

## 📈 Performance Optimization

### Robust Timestamp Alignment
- Multi-strategy text matching
- Fuzzy matching with confidence scoring
- Automatic overlap detection and resolution
- Positive duration guarantee

### Audio Preprocessing
- Silence padding to prevent truncation
- Volume normalization
- Format optimization for Whisper

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Test robust chunking (if test files exist)
python3 -c "
from create_chunk_llm import create_robust_chunks_for_conversation_question
print('✅ Robust chunking system ready')
"
```

## 📝 Configuration

### Question Type Mapping
- `docs/listening_question_types_free.json`: Free test configurations
- `docs/listening_question_types_regular.json`: Regular test configurations

### Environment Variables
- `OPENAI_API_KEY`: Required for LLM chunking
- Optional: Whisper model paths and configurations

## 🚨 Troubleshooting

### Common Issues

1. **OpenAI API Errors**: Ensure valid API key is set
2. **Audio Format Issues**: Use WAV format for best results
3. **Memory Issues**: Process files individually for large batches
4. **Timestamp Overlaps**: Use robust chunking system (automatic fix)

### Debug Mode
Enable detailed logging by setting debug flags in the respective modules.

## 🔄 Workflow Examples

### Complete Processing Pipeline
```bash
# 1. Generate raw transcriptions
python3 main_colab.py

# 2. Clean transcriptions
python3 scripts/batch_dehallucination.py

# 3. Generate chunks
python3 scripts/batch_chunk_llm.py

# 4. Analyze results
python3 scripts/analyze_fallback_failures.py
```

### Single File Processing
```python
# Complete pipeline for one file
from main_colab import process_audio_file
from dehallucination import Dehallucinator
from create_chunk_llm import process_single_transcription

# Step 1: Transcribe
raw_result = process_audio_file("audio.wav", "test1", "free")

# Step 2: Dehallucinate
dehallucinator = Dehallucinator()
clean_result = dehallucinator.process_transcription(raw_result)

# Step 3: Chunk
final_result = process_single_transcription(clean_result)
```

## 📚 Additional Resources

- **Whisper Documentation**: https://github.com/openai/whisper
- **OpenAI API**: https://platform.openai.com/docs
- **TCF Canada**: Official test information and formats

## 🤝 Contributing

When adding new features:
1. Follow the existing code structure
2. Add appropriate validation and error handling
3. Update this README with new functionality
4. Test with sample audio files

## 📄 License

This system is part of the TCF-Canada application. See the main project license for details.
