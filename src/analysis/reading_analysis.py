import os
import sys
import json
from tqdm import tqdm

PROJECT_ROOT = "/Users/<USER>/Documents/TCF-Canada"
sys.path.append(PROJECT_ROOT)

from src.analysis.llm_caller import QwenCaller
from src.utils.data_iterator import DataIterator
from src.utils.io import append_item_to_json


SYSTEM_PROMPT = """
You are a French language learning assistant specialized in helping students understand reading comprehension questions. 
When given a passage, question, choices (A–D), and a correct answer, you will always respond using the following structured format: 
--- 
1. Correct Answer Explanation - Answer: [Correct letter] - [In English, why it's correct with evidence from the text] 
2. Incorrect Options Analysis - A. [Explanation] - B. [Explanation] - C. [Explanation] - D. [Explanation] 
--- 
Always write in English unless otherwise specified. Keep explanations clear, accurate, and learner-friendly.
"""

USER_PROMPT_TEMPLATE = """
Here is a French reading comprehension question. Please analyze it using the structure provided by the system. 
Passage: {passage}
Question: {question}
Choices:
A. {a}
B. {b}
C. {c}
D. {d}
Correct Answer: {answer}
"""

API_KEY = "sk-f1f99f93623040e9bad367c2ca5c6cb3"


def construct_user_prompt(item: dict, answer: str) -> str:
    return USER_PROMPT_TEMPLATE.format(
        passage=item["extracted_text"],
        question=item["question_text"],
        a=item["choices"]["A"],
        b=item["choices"]["B"],
        c=item["choices"]["C"],
        d=item["choices"]["D"],
        answer=answer
    )

def generate_english_analysis(qwen_caller, user_prompt: str, model: str = "qwen-plus") -> str:
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt}
    ]
    return qwen_caller.call_qwen_api(messages, model=model)

def force_english(qwen_caller, user_prompt: str, text: str, model: str = "qwen-plus") -> str:
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt},  # Original prompt
        {"role": "assistant", "content": text},    # Model's response
        {"role": "user", "content": f"In English"}
    ]
    return qwen_caller.call_qwen_api(messages, model=model)

def translate_text(qwen_caller, user_prompt: str, text: str, language: str, model: str = "qwen-plus") -> str:
    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt},  # Original prompt
        {"role": "assistant", "content": text},    # Model's response
        {"role": "user", "content": f"Please translate the entire analysis into {language}, keeping the structure exactly the same."}
    ]
    return qwen_caller.call_qwen_api(messages, model=model)


def save_analysis_to_file(output_path: str, source_file: str, item: dict, analyses: dict):
    os.makedirs(output_path, exist_ok=True)

    file_name = os.path.basename(source_file)
    output_file_name = os.path.join(output_path, file_name)

    output = {
        "source_file": source_file,
        "question_number": item["question_number"],
        **analyses  # includes analysis_en, analysis_cn, analysis_fr
    }

    append_item_to_json(output_file_name, output, verbose=False)

def is_question_already_processed(file_path: str, question_number: int) -> bool:
    if not os.path.exists(file_path):
        return False

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        return any(
            entry.get("question_number") == question_number and
            entry.get("analysis_en") and entry.get("analysis_cn") and entry.get("analysis_fr")
            for entry in data
        )
    except (json.JSONDecodeError, FileNotFoundError, KeyError):
        return False


if __name__ == "__main__":
    # Processes all question-answer pairs in a folder, generates analysis in English, translates to Chinese and French,
    # and saves the results to JSON files in the specified output path.
    FORCE_ENGLISH = True # when the analysis_en generated is always in French
    answer_file_path = os.path.join(PROJECT_ROOT, "data/scraped/scraped_answer/reading_correct_answer.json")
    question_folder_path = os.path.join(PROJECT_ROOT, "data/scraped/scraped_reading_cleaned")
    output_path = os.path.join(PROJECT_ROOT, "data/analysis/reading_analysis")
    model = "qwen-plus"
    print(f"Using {model}")

    reading_iterator = DataIterator(answer_file_path, question_folder_path)
    qwen_caller = QwenCaller(api_key=API_KEY)

    total_items = sum(1 for _ in DataIterator(answer_file_path, question_folder_path))
    print(f"Total items: {total_items}")
    for item, answer, source_file in tqdm(reading_iterator, total=total_items, desc="Processing Questions", unit="question"):
        # Skip if already processed
        file_name = os.path.basename(source_file)
        output_file_name = os.path.join(output_path, file_name)

        question_number = item["question_number"]
        if is_question_already_processed(output_file_name, question_number):
            print(f"Skipping file {file_name} question {question_number} - already processed.")
            continue

        user_prompt = construct_user_prompt(item, answer)

        analysis_en = generate_english_analysis(qwen_caller, user_prompt, model)
        if FORCE_ENGLISH:
            analysis_en = force_english(qwen_caller, user_prompt, analysis_en)
        analysis_cn = translate_text(qwen_caller, user_prompt, analysis_en, "Chinese", model)
        analysis_fr = translate_text(qwen_caller, user_prompt, analysis_en, "French", model)

        analyses = {
            "analysis_en": analysis_en,
            "analysis_cn": analysis_cn,
            "analysis_fr": analysis_fr,
        }

        # for analysis in analyses.values():
        #     print(analysis)

        save_analysis_to_file(output_path, source_file, item, analyses)

    print("Done.")