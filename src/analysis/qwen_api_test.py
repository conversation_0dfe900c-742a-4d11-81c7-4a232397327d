from openai import OpenAI


client = OpenAI(
    api_key="sk-f1f99f93623040e9bad367c2ca5c6cb3",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# Test Reading Analysis
if False:
    SYSTEM_PROMPT = """
    "You are a French language learning assistant specialized in helping students understand reading comprehension questions. 
    When given a passage, question, choices (A–D), and a correct answer, you will always respond using the following structured format: 
    --- 
    1. Question Summary [Brief explanation of what the question is asking] 
    2. Correct Answer Explanation - Answer: [Correct letter] - [Why it's correct with evidence from the text] 
    3. Incorrect Options Analysis - A. [Explanation] - B. [Explanation] - C. [Explanation] - D. [Explanation] 
    4. Vocabulary & Grammar Notes - [Vocabulary or grammar point 1] - [Vocabulary or grammar point 2] 
    --- 
    Always write in English unless otherwise specified. Keep explanations clear, accurate, and learner-friendly."
    """

    USER_PROMPT_TEMPLATE = """
    Here is a French reading comprehension question. Please analyze it using the structure provided by the system.
    Passage: {passage}
    Question: {question}
    Choices:
    A. {a}
    B. {b}
    C. {c}
    D. {d}
    Correct Answer: {correct}
    """

    user_prompt = USER_PROMPT_TEMPLATE.format(
        passage="Sophie est étudiante en biologie. Elle passe beaucoup de temps à la bibliothèque pour préparer ses examens.",
        question="Où Sophie prépare-t-elle ses examens ?",
        a="Dans un café",
        b="À la bibliothèque",
        c="Dans un parc",
        d="Chez elle",
        correct="B"
    )

    # print(SYSTEM_PROMPT)
    # print(user_prompt)

    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt},
    ]

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    # print(completion.model_dump_json())
    response_1 = completion.choices[0].message.content
    print(response_1)

    messages.append({"role": "assistant", "content": response_1})
    messages.append({"role": "user", "content": "Please translate the entire analysis into Chinese, keeping the structure exactly the same."})

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    response_2 = completion.choices[0].message.content
    print(response_2)

# Test Listening Analysis (image questions)
if False:
    SYSTEM_PROMPT = """
    You are a French language learning assistant specialized in helping students understand listening comprehension questions.
    When given an image (if applicable), a transcript of an audio clip, choices (A–D, if applicable), and a correct answer, you will always respond using the following structured format: 
    --- 
    1. Correct Answer Explanation - Answer: [Correct letter] - [Why it's correct with evidence from the image (if applicable) and the transcript] 
    2. Incorrect Options Analysis - A. [Explanation] - B. [Explanation] - C. [Explanation] - D. [Explanation] 
    --- 
    Always write in English unless otherwise specified. Keep explanations clear, accurate, and learner-friendly.
    """

    USER_PROMPT_TEMPLATE_IMAGE = """
    Here is a French listening comprehension question involving an image and audio instructions. Please analyze it using the structure provided. 
    Especially why it's correct with evidence from the image. 
    Transcript: {transcript}
    Correct Answer: {correct}
    Image Description: {image_description}
    """

    image_understaing_message = [
        {"role": "user", "content": [
            {"type": "image_url",
             "image_url": {"url": "https://tcf-canada.oss-cn-beijing.aliyuncs.com/assets/listening_asset_free/media_test1/Q1.webp"}},
            {"type": "text", "text": "what is in this image"},
        ]}
    ]

    completion = client.chat.completions.create(
        model="qwen-vl-plus",
        messages=image_understaing_message,
    )
    image_description = completion.choices[0].message.content

    user_prompt = USER_PROMPT_TEMPLATE_IMAGE.format(
        transcript="1. Oh, regarde, il neige. N'oublie pas. A. Ton sac. B. Tes lunettes. C. Tes clés. D. Ton manteau.",
        correct="D",
        image_description=image_description,
    )

    print(SYSTEM_PROMPT)
    print(user_prompt)

    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": [
            {"type": "image_url",
             "image_url": {"url": "https://tcf-canada.oss-cn-beijing.aliyuncs.com/assets/listening_asset_free/media_test1/Q1.webp"}},
            {"type": "text", "text": user_prompt},
        ]}
    ]

    completion = client.chat.completions.create(
        model="qwen-vl-plus",
        messages=messages,
    )
    # print(completion.model_dump_json())
    response_1 = completion.choices[0].message.content
    print(response_1)

    messages.append({"role": "assistant", "content": response_1})
    messages.append({"role": "user",
                     "content": "Please translate the entire analysis into Chinese, keeping the structure exactly the same."})

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    response_2 = completion.choices[0].message.content
    print(response_2)

# Test Listening Analysis (choices in audio)
if False:
    SYSTEM_PROMPT = """
    You are a French language learning assistant specialized in helping students understand listening comprehension questions.
    When given an image (if applicable), a transcript of an audio clip, choices (A–D, if applicable), and a correct answer, you will always respond using the following structured format: 
    --- 
    1. Correct Answer Explanation - Answer: [Correct letter] - [Why it's correct with evidence from the image (if applicable) and the transcript] 
    2. Incorrect Options Analysis - A. [Explanation] - B. [Explanation] - C. [Explanation] - D. [Explanation] 
    --- 
    Always write in English unless otherwise specified. Keep explanations clear, accurate, and learner-friendly.
    """

    USER_PROMPT_TEMPLATE = """
    Here is a French listening comprehension question involving audio instructions. Please analyze it using the structure provided.
    Audio Instruction: {transcript}
    Correct Answer: {answer}
    """

    user_prompt = USER_PROMPT_TEMPLATE.format(
        transcript="Je t'invite chez moi ce soir. J'organise une fête pour mon anniversaire. A. Non merci, je veux juste du gâteau. B. D'accord, je peux venir avec une amie ? C. Pardon, je souhaite connaître l'heure. D. Désolé, je préfère partir tout de suite.",
        answer="B"
    )

    print(SYSTEM_PROMPT)
    print(user_prompt)


    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt}
    ]

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    # print(completion.model_dump_json())
    response_1 = completion.choices[0].message.content
    print(response_1)

    messages.append({"role": "assistant", "content": response_1})
    messages.append({"role": "user",
                     "content": "Please translate the entire analysis into Chinese, keeping the structure exactly the same."})

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    response_2 = completion.choices[0].message.content
    print(response_2)

# Test Listening Analysis (choices in text)
if True:
    SYSTEM_PROMPT = """
    You are a French language learning assistant specialized in helping students understand listening comprehension questions.
    When given an image (if applicable), a transcript of an audio clip, choices (A–D, if applicable), and a correct answer, you will always respond using the following structured format: 
    --- 
    1. Correct Answer Explanation - Answer: [Correct letter] - [Why it's correct with evidence from the image (if applicable) and the transcript] 
    2. Incorrect Options Analysis - A. [Explanation] - B. [Explanation] - C. [Explanation] - D. [Explanation] 
    --- 
    Always write in English unless otherwise specified. Keep explanations clear, accurate, and learner-friendly.
    """

    USER_PROMPT_TEMPLATE = """
    Here is a French listening comprehension question involving audio instructions. Please analyze it using the structure provided.
    Audio Instruction: {transcript}
    Choices:
    A. {a}
    B. {b}
    C. {c}
    D. {d}
    Correct Answer: {answer}
    """

    user_prompt = USER_PROMPT_TEMPLATE.format(
        transcript="Excusez-moi, monsieur, je suis un peu perdue. Est-ce que vous pouvez me dire où se trouve la station de métro la plus proche, s'il vous plaît ? Oui, elle est juste là, au bout de la rue et à gauche. Ah, est-ce qu'il y a un bus d'ici pour aller au musée du Louvre ? Prenez le bus 62, c'est direct et il s'arrête juste devant le musée. Merci. Je vous en prie. Quel est l'objet de cette conversation ?",
        a="Donner un conseil.",
        b="Vérifier une information.",
        c="Présenter des excuses.",
        d="Indiquer un itinéraire.",
        answer="D"
    )

    print(SYSTEM_PROMPT)
    print(user_prompt)


    messages = [
        {"role": "system", "content": SYSTEM_PROMPT},
        {"role": "user", "content": user_prompt}
    ]

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    # print(completion.model_dump_json())
    response_1 = completion.choices[0].message.content
    print(response_1)

    messages.append({"role": "assistant", "content": response_1})
    messages.append({"role": "user",
                     "content": "Please translate the entire analysis into Chinese, keeping the structure exactly the same."})

    completion = client.chat.completions.create(
        model="qwen-plus",
        messages=messages,
    )
    response_2 = completion.choices[0].message.content
    print(response_2)