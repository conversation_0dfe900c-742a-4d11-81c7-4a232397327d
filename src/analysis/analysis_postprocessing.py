import os
import json
import re

def process_json_files(folder_path, replacement_map):
    """
    Process JSON files in a folder:
    - Remove items where 'analysis_en' is not likely in English (doesn't contain 'the' or 'The').
    - Replace substrings in 'analysis_fr' based on a mapping.


    Args:
        folder_path (str): Path to the folder containing JSON files.
        replacement_map (dict): Dictionary of {old_string: new_string} replacements.
    """
    word_pattern = re.compile(r'\b(the|this)\b', re.IGNORECASE)

    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith('.json'):
                file_path = os.path.join(root, file)

                # Load JSON data
                with open(file_path, 'r', encoding='utf-8') as f:
                    try:
                        data = json.load(f)
                    except json.JSONDecodeError as e:
                        print(f"Error decoding JSON from {file_path}: {e}")
                        continue

                # Filter and modify data
                if isinstance(data, list):
                    modified_data = []
                    for item in data:
                        if not isinstance(item, dict):
                            continue

                        # Check if analysis_en is likely English
                        analysis_en = item.get("analysis_en", "")
                        if not word_pattern.search(analysis_en):
                            print("Removed", file_path, item.get("question_number"))
                            continue  # Skip/delete this item

                        # Apply replacements to analysis_fr
                        analysis_fr = item.get("analysis_fr", "")
                        for old_str, new_str in replacement_map.items():
                            analysis_fr = analysis_fr.replace(old_str, new_str)
                        item["analysis_fr"] = analysis_fr

                        modified_data.append(item)

                    # Sort by question_number if it exists
                    def sort_key(x):
                        qn = x.get("question_number")
                        try:
                            return int(qn)
                        except (TypeError, ValueError):
                            return float('inf')  # Put invalid numbers at the end

                    modified_data.sort(key=sort_key)

                    # Write back
                    # if len(modified_data) != len(data):
                    #     print(f"Processed: {file_path}")
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump(modified_data, f, ensure_ascii=False, indent=2)
                else:
                    print(f"Unexpected structure in {file_path}, expected a list.")

if __name__ == "__main__":
    # - Remove items where 'analysis_en' is not likely in English (doesn't contain 'the' or 'this').
    # - Replace substrings in 'analysis_fr' based on a mapping.
    replacement_mapping = {
        "Correct Answer Explanation": "Explication de la bonne réponse",
        "Incorrect Options Analysis": "Analyse des réponses incorrectes"
    }
    process_json_files("/Users/<USER>/Documents/TCF-Canada/data/analysis/listening_analysis_free", replacement_mapping)