from openai import OpenAI


class QwenCaller:
    def __init__(self, api_key):
        if not api_key:
            raise ValueError("API key not provided")

        self.api_key = api_key
        self.client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )

    def call_qwen_api(self, messages, model="qwen-plus"):
        completion = self.client.chat.completions.create(model=model, messages=messages)
        # print(completion.model_dump_json())
        return completion.choices[0].message.content