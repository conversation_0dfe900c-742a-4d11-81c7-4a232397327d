# LLM Answer Checker for TCF Reading Comprehension

This directory contains scripts to automatically check TCF reading comprehension answers using Large Language Models.

## Features

- **Automatic Answer Generation**: Uses LLM to analyze French texts and answer multiple-choice questions
- **Multiple LLM Providers**: Supports OpenAI GPT, Anthropic Claude, and local models
- **Exact Format Matching**: Generates predictions in the same format as your correct answers JSON
- **Detailed Wrong Answer Analysis**: Identifies all incorrect predictions with question numbers, predicted vs correct answers, and patterns for easy manual review
- **Accuracy Evaluation**: Compares predictions with correct answers and provides detailed statistics
- **Batch Processing**: Processes all test files efficiently
- **Demo Mode**: Test the system without API keys using simulated responses

## Files

- `llm_answer_checker.py` - Main LLM-based answer checker (requires API keys)
- `demo_checker.py` - Demo version with simulated responses (no API keys needed)
- `run_demo.py` - Easy-to-use menu for running different configurations
- `requirements.txt` - Python dependencies
- `README.md` - This documentation

## Quick Start (Demo Mode)

Test the system without API keys:

```bash
# Run demo on a few tests
python src/answer_check/demo_checker.py --limit 3

# Run demo on all tests with comparison
python src/answer_check/demo_checker.py
```

This will:
1. Process reading tests using simulated LLM responses
2. Generate predictions in the correct format
3. Compare with actual correct answers
4. Show accuracy statistics

## Full LLM Mode

### Setup

1. Install dependencies:
```bash
pip install openai anthropic  # Choose based on your preferred provider
```

2. Set your API key:
```bash
# For OpenAI
export OPENAI_API_KEY="your-api-key-here"

# For Anthropic
export ANTHROPIC_API_KEY="your-api-key-here"
```

### Usage Examples

#### Basic Usage (OpenAI GPT-4)
```bash
python src/answer_check/llm_answer_checker_listening.py \
  --input-dir data/scraped/scraped_listening_cleaned \
  --correct-answers data/scraped/scraped_answer/listening_correct_answer.json
```

#### Using Anthropic Claude
```bash
python src/answer_check/llm_answer_checker.py \
  --input-dir data/scraped/scraped_reading_cleaned \
  --llm-provider anthropic \
  --model claude-3-haiku-20240307 \
  --correct-answers data/scraped/scraped_answer/reading_correct_answer.json
```

#### Custom Output File
```bash
python src/answer_check/llm_answer_checker.py \
  --input-dir data/scraped/scraped_reading_cleaned \
  --output-file my_predictions.json \
  --correct-answers data/scraped/scraped_answer/reading_correct_answer.json
```

#### Process Only Free Tests
```bash
python src/answer_check/llm_answer_checker.py \
  --input-dir data/scraped/scraped_reading_cleaned_free \
  --correct-answers data/scraped/scraped_answer/reading_correct_answer.json
```

## Output Format

The system generates predictions in the exact same format as your correct answers:

```json
{
  "test1": {
    "1": "B",
    "2": "A", 
    "3": "C",
    ...
  },
  "test2": {
    "1": "D",
    "2": "B",
    ...
  }
}
```

Two files are created:
- `predictions_[timestamp].json` - With metadata
- `clean_predictions_[timestamp].json` - Clean format (same as correct answers)

## Wrong Answer Analysis for Manual Review

The system now provides detailed analysis of incorrect predictions to help you manually review and understand the errors:

### Console Output
When comparing with correct answers, you'll see:
```
Overall Accuracy: 73.2%
Correct: 1114/1521
Wrong: 407

Most common wrong patterns:
  A→B: 45 times
  C→D: 38 times
  B→A: 32 times

Examples of wrong answers for manual review:
  test1 Q3: Predicted A, Correct C
  test1 Q7: Predicted B, Correct C
  test2 Q15: Predicted D, Correct A
  ... and 397 more wrong answers
  See full details in the comparison JSON file
```

### Detailed JSON Output
The comparison file contains complete wrong answer details:

```json
{
  "wrong_answers": {
    "test1": [
      {
        "question_number": "3",
        "predicted_answer": "A",
        "correct_answer": "C",
        "prediction_pattern": "A→C"
      },
      ...
    ]
  },
  "summary": {
    "total_wrong": 407,
    "wrong_by_test": {
      "test1": 15,
      "test2": 12,
      ...
    },
    "most_common_wrong_patterns": {
      "A→B": 45,
      "C→D": 38,
      ...
    }
  }
}
```

### How to Use for Manual Review

1. **Run the checker with comparison**:
   ```bash
   python src/answer_check/demo_checker.py --correct-answers data/scraped/scraped_answer/reading_correct_answer.json
   ```

2. **Review the console output** for quick overview of wrong patterns

3. **Open the comparison JSON file** (e.g., `comparison_demo_predictions_[timestamp].json`) to see:
   - Exact question numbers that were wrong
   - What was predicted vs what was correct  
   - Which tests had the most errors
   - Common error patterns

4. **Use the question numbers** to look up the original questions in your test files for deeper analysis

5. **Focus your manual review** on the most common error patterns or tests with highest error rates

## Command Line Options

### Main LLM Checker (`llm_answer_checker.py`)

- `--input-dir, -i` - Directory with test JSON files (required)
- `--output-file, -o` - Output file for predictions 
- `--llm-provider` - Choose: openai, anthropic, local (default: openai)
- `--model` - Specific model (e.g., gpt-4, claude-3-sonnet-20240229)
- `--api-key` - API key (or use environment variable)
- `--correct-answers` - File with correct answers for comparison
- `--test-limit` - Limit number of tests (for testing)

### Demo Checker (`demo_checker.py`)

- `--input-dir, -i` - Directory with test JSON files
- `--output-file, -o` - Output file for predictions
- `--correct-answers` - File with correct answers for comparison  
- `--limit` - Limit number of tests to process

## Expected Accuracy

- **Real LLM (GPT-4/Claude)**: Expected 60-85% accuracy on TCF reading comprehension
- **Demo Mode**: ~25% accuracy (random with simple heuristics)

## Cost Estimation

For OpenAI GPT-4 processing all 39 tests (~1,521 questions):
- Estimated cost: $15-30 USD
- Processing time: ~15-25 minutes
- Questions per minute: ~60-100

For Anthropic Claude (Haiku/Sonnet):
- Generally more cost-effective
- Similar processing time

## Tips for Best Results

1. **Use GPT-4 or Claude-3-Sonnet** for best accuracy
2. **Process in batches** if you have rate limits
3. **Review failed predictions** - they default to "A"
4. **Compare results** across different models
5. **Use demo mode first** to test your data format
6. **Use wrong answer analysis** to focus manual review efforts

## Troubleshooting

### Common Issues

1. **API Key Not Found**
   ```
   Solution: Set environment variable or use --api-key
   ```

2. **Rate Limiting**
   ```
   Solution: The script includes delays, but you may need longer pauses
   ```

3. **JSON Parsing Errors**
   ```
   Solution: LLM didn't return valid JSON - check your prompt/model
   ```

4. **File Not Found**
   ```
   Solution: Check your input directory and file paths
   ```

### Validation

Always check that:
- Input test files are valid JSON
- Correct answers file exists and matches format
- Output predictions have all expected test names and question numbers

## Example Output

```
Found 39 test files to process
Using openai with model gpt-4
============================================================
Processing test1...
  Question 1... ✓ B (confidence: 0.92)
  Question 2... ✓ A (confidence: 0.87)
  ...
Processing test2...
  ...
============================================================
Processing completed in 847.32 seconds
Total questions: 1521
Successful predictions: 1521
Failed predictions: 0
Success rate: 100.0%

Comparing with correct answers...
Overall Accuracy: 73.2%
Correct: 1114/1521
Wrong: 407

Most common wrong patterns:
  A→B: 45 times
  C→D: 38 times

Examples of wrong answers for manual review:
  test1 Q3: Predicted A, Correct C
  test1 Q7: Predicted B, Correct C
  ... and 397 more wrong answers

Detailed comparison results with ALL wrong answers saved to comparison_predictions_20241201_143025.json
```

This system provides a robust foundation for automated TCF answer checking with detailed error analysis for manual review. You can extend it with additional features like confidence thresholding, answer explanation analysis, or custom prompting strategies.