#!/usr/bin/env python3
"""
Simple script to run the demo LLM answer checker with common configurations.
This makes it easier to test the system without typing long command lines.
"""

import subprocess
import sys
from pathlib import Path

def run_demo_quick():
    """Run demo on just a few tests for quick testing."""
    print("🚀 Running QUICK demo (only 3 tests)...")
    cmd = [
        "python3", "src/answer_check/demo_checker.py",
        "--limit", "3"
    ]
    subprocess.run(cmd)

def run_demo_full():
    """Run demo on all tests with full comparison."""
    print("🚀 Running FULL demo (all tests)...")
    cmd = [
        "python3", "src/answer_check/demo_checker.py"
    ]
    subprocess.run(cmd)

def run_demo_free_only():
    """Run demo on only the free test files."""
    print("🚀 Running demo on FREE tests only...")
    cmd = [
        "python3", "src/answer_check/demo_checker.py",
        "--input-dir", "data/scraped/scraped_reading_cleaned_free"
    ]
    subprocess.run(cmd)

def run_real_llm_openai():
    """Run with real OpenAI GPT (requires API key)."""
    print("🚀 Running with REAL OpenAI GPT-4...")
    print("⚠️  This requires OPENAI_API_KEY environment variable")
    
    response = input("Do you have your OpenAI API key set? (y/n): ")
    if response.lower() != 'y':
        print("Please set your API key with: export OPENAI_API_KEY='your-key-here'")
        return
    
    cmd = [
        "python3", "src/answer_check/llm_answer_checker.py",
        "--input-dir", "data/scraped/scraped_reading_cleaned_free",  # Start with free tests
        "--llm-provider", "openai",
        "--model", "gpt-4",
        "--correct-answers", "data/scraped/scraped_answer/reading_correct_answer.json"
    ]
    subprocess.run(cmd)

def run_real_llm_anthropic():
    """Run with real Anthropic Claude (requires API key)."""
    print("🚀 Running with REAL Anthropic Claude...")
    print("⚠️  This requires ANTHROPIC_API_KEY environment variable")
    
    response = input("Do you have your Anthropic API key set? (y/n): ")
    if response.lower() != 'y':
        print("Please set your API key with: export ANTHROPIC_API_KEY='your-key-here'")
        return
    
    cmd = [
        "python3", "src/answer_check/llm_answer_checker.py",
        "--input-dir", "data/scraped/scraped_reading_cleaned_free",  # Start with free tests
        "--llm-provider", "anthropic",
        "--model", "claude-3-haiku-20240307",  # Cheaper model for testing
        "--correct-answers", "data/scraped/scraped_answer/reading_correct_answer.json"
    ]
    subprocess.run(cmd)

def main():
    """Main menu for running different configurations."""
    
    print("\n" + "="*60)
    print("🤖 TCF READING COMPREHENSION LLM ANSWER CHECKER")
    print("="*60)
    print("\nChoose an option:")
    print("1. Demo Mode - Quick Test (3 tests, ~30 seconds)")
    print("2. Demo Mode - Full Test (all tests, ~90 seconds)")
    print("3. Demo Mode - Free Tests Only")
    print("4. Real LLM - OpenAI GPT-4 (requires API key)")
    print("5. Real LLM - Anthropic Claude (requires API key)")
    print("6. Exit")
    
    while True:
        try:
            choice = input("\nEnter your choice (1-6): ").strip()
            
            if choice == "1":
                run_demo_quick()
                break
            elif choice == "2":
                run_demo_full()
                break
            elif choice == "3":
                run_demo_free_only()
                break
            elif choice == "4":
                run_real_llm_openai()
                break
            elif choice == "5":
                run_real_llm_anthropic()
                break
            elif choice == "6":
                print("👋 Goodbye!")
                sys.exit(0)
            else:
                print("❌ Invalid choice. Please enter 1-6.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            sys.exit(0)
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main() 