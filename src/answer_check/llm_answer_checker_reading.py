#!/usr/bin/env python3
"""
LLM Answer Checker for TCF Reading Comprehension Tests

This script uses Large Language Models to automatically answer reading comprehension
questions from TCF tests and generates predictions in the same format as the 
correct answers JSON file.

Features:
- Processes all test files from scraped data
- Uses LLM to analyze text and answer questions
- Generates predictions in the exact format: {"test1": {"1": "A", "2": "B", ...}}
- Supports multiple LLM providers (OpenAI, Anthropic, local models)
- Includes confidence scoring and reasoning
- Batch processing for efficiency
"""

import json
import os
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse
from dataclasses import dataclass
from datetime import datetime

# LLM client imports - uncomment based on your preference
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import anthropic
    ANTHROPIC_AVAILABLE = True
except ImportError:
    ANTHROPIC_AVAILABLE = False

@dataclass
class TestQuestion:
    """Represents a single test question with all its components."""
    question_number: str
    extracted_text: str
    question_text: str
    choices: Dict[str, str]
    image_path: Optional[str] = None

@dataclass
class LLMResponse:
    """Represents the LLM's response to a question."""
    answer: str
    confidence: float
    reasoning: str

class LLMAnswerChecker:
    """Main class for LLM-based answer checking."""
    
    def __init__(self, llm_provider: str = "openai", model_name: str = None, api_key: str = None):
        """
        Initialize the LLM Answer Checker.
        
        Args:
            llm_provider: "openai", "anthropic", or "local"
            model_name: Specific model to use (e.g., "gpt-4", "claude-3-sonnet")
            api_key: API key for the service (if needed)
        """
        self.llm_provider = llm_provider.lower()
        self.api_key = api_key or os.getenv(f"{llm_provider.upper()}_API_KEY")
        
        # Set default models
        if model_name is None:
            if self.llm_provider == "openai":
                self.model_name = "gpt-4o-mini"
            elif self.llm_provider == "anthropic":
                self.model_name = "claude-3-haiku-20240307"
            else:
                self.model_name = "llama2"  # Default for local
        else:
            self.model_name = model_name
        
        # Initialize the client
        self.client = self._initialize_client()
        
        # Statistics
        self.total_questions = 0
        self.successful_predictions = 0
        self.failed_predictions = 0
        
    def _initialize_client(self):
        """Initialize the appropriate LLM client."""
        if self.llm_provider == "openai":
            if not OPENAI_AVAILABLE:
                raise ImportError("OpenAI library not installed. Run: pip install openai")
            if not self.api_key:
                raise ValueError("OpenAI API key not found. Set OPENAI_API_KEY environment variable.")
            return openai.OpenAI(api_key=self.api_key)
            
        elif self.llm_provider == "anthropic":
            if not ANTHROPIC_AVAILABLE:
                raise ImportError("Anthropic library not installed. Run: pip install anthropic")
            if not self.api_key:
                raise ValueError("Anthropic API key not found. Set ANTHROPIC_API_KEY environment variable.")
            return anthropic.Anthropic(api_key=self.api_key)
            
        elif self.llm_provider == "local":
            # For local models, you might use ollama, llamacpp, etc.
            print("Local LLM support - implement based on your local setup")
            return None
        else:
            raise ValueError(f"Unsupported LLM provider: {self.llm_provider}")
    
    def load_test_data(self, test_file_path: str) -> List[TestQuestion]:
        """Load test questions from a JSON file."""
        try:
            with open(test_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            questions = []
            for item in data:
                question = TestQuestion(
                    question_number=item['question_number'],
                    extracted_text=item['extracted_text'],
                    question_text=item['question_text'],
                    choices=item['choices'],
                    image_path=item.get('image_path')
                )
                questions.append(question)
            
            return questions
        except Exception as e:
            print(f"Error loading test data from {test_file_path}: {e}")
            return []
    
    def create_prompt(self, question: TestQuestion) -> str:
        """Create an optimized prompt for cost savings - minimal output."""
        
        # Simplified prompt for O3 models - they work better with direct instructions
        if self.model_name.startswith("o3"):
            prompt = f"""Read this French text and answer the question.

TEXT:
{question.extracted_text}

QUESTION:
{question.question_text}

OPTIONS:
"""
            for choice_letter, choice_text in question.choices.items():
                prompt += f"{choice_letter}) {choice_text}\n"
            
            prompt += "\nAnswer with just the letter: A, B, C, or D"
        else:
            # Original prompt for other models
            prompt = f"""Texte: {question.extracted_text}

Question: {question.question_text}

Choix:
"""
            for choice_letter, choice_text in question.choices.items():
                prompt += f"{choice_letter}) {choice_text}\n"
            
            prompt += """
Répondre avec la lettre seulement (A, B, C, ou D)."""

        return prompt
    
    def query_llm(self, prompt: str) -> Optional[str]:
        """Query the LLM with minimal output for cost optimization."""
        try:
            if self.llm_provider == "openai":
                # O3 models use different parameter names and message structure
                if self.model_name.startswith("o3"):
                    # Simplified approach for O3 - just user message
                    response = self.client.chat.completions.create(
                        model=self.model_name,
                        messages=[
                            {"role": "user", "content": prompt}
                        ],
                        max_completion_tokens=5  # Very small since we only need one letter
                    )
                else:
                    response = self.client.chat.completions.create(
                        model=self.model_name,
                        messages=[
                            {"role": "system", "content": "Réponds avec une lettre seulement."},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.1,
                        max_tokens=5  # Other models use this parameter
                    )
                
                content = response.choices[0].message.content.strip()
                
                # Debug output for O3 models
                if self.model_name.startswith("o3"):
                    print(f"DEBUG O3 response: '{content}'")
                
                # Clean up the response and extract the letter
                if content:
                    # Try to find A, B, C, or D in the response
                    content_upper = content.upper()
                    for char in content_upper:
                        if char in ['A', 'B', 'C', 'D']:
                            return char
                    
                    # If no single letter found, check for patterns like "A)", "A.", "(A"
                    import re
                    pattern_match = re.search(r'[ABCD]', content_upper)
                    if pattern_match:
                        return pattern_match.group()
                
                print(f"Warning: Could not extract valid answer from response: '{content}'")
                return None
                
            elif self.llm_provider == "anthropic":
                message = self.client.messages.create(
                    model=self.model_name,
                    max_tokens=500,
                    temperature=0.1,
                    messages=[
                        {"role": "user", "content": prompt}
                    ]
                )
                content = message.content[0].text
                
                # Parse response for Anthropic (same logic as O3)
                if content:
                    content_upper = content.upper()
                    for char in content_upper:
                        if char in ['A', 'B', 'C', 'D']:
                            return char
                return None
                
            else:
                # For local models, implement based on your setup
                raise NotImplementedError(f"Local LLM querying not implemented yet")
                
        except Exception as e:
            print(f"ERROR querying LLM: {e}")
            # Check for common API key issues
            if "api key" in str(e).lower() or "unauthorized" in str(e).lower() or "401" in str(e):
                print("🔑 API KEY ISSUE: Please check your OpenAI API key:")
                print("  1. Get your key from: https://platform.openai.com/account/api-keys")
                print("  2. Set it with: export OPENAI_API_KEY='sk-your-real-key-here'")
                print(f"  3. Current key starts with: {self.api_key[:10] if self.api_key else 'None'}...")
            return None
    
    def process_question(self, question: TestQuestion) -> Optional[LLMResponse]:
        """Process a single question and get the LLM's answer."""
        prompt = self.create_prompt(question)
        response = self.query_llm(prompt)
        
        # Validate that the answer is one of the valid choices
        if response and response in question.choices:
            return LLMResponse(
                answer=response,
                confidence=1.0,
                reasoning="Direct response from LLM"
            )
        elif response:
            print(f"Invalid answer '{response}' for question {question.question_number}")
            return None
        else:
            return None
    
    def process_test_file(self, test_file_path: str, test_name: str) -> Dict[str, str]:
        """Process an entire test file and return predictions."""
        print(f"Processing {test_name}...")
        
        questions = self.load_test_data(test_file_path)
        if not questions:
            print(f"No questions loaded from {test_file_path}")
            return {}
        
        predictions = {}
        
        for question in questions:
            self.total_questions += 1
            print(f"  Question {question.question_number}...", end=" ")
            
            response = self.process_question(question)
            
            if response:
                predictions[question.question_number] = response.answer
                self.successful_predictions += 1
                print(f"✓ {response.answer} (confidence: {response.confidence:.2f})")
            else:
                # Fallback to random choice if LLM fails
                predictions[question.question_number] = "A"  # Default fallback
                self.failed_predictions += 1
                print("✗ Failed (defaulting to A)")
            
            # Add small delay to avoid rate limiting
            time.sleep(0.1)
        
        return predictions
    
    def process_all_tests(self, test_directory: str, output_file: str = None) -> Dict[str, Dict[str, str]]:
        """Process all test files in a directory."""
        test_dir = Path(test_directory)
        if not test_dir.exists():
            raise FileNotFoundError(f"Test directory not found: {test_directory}")
        
        all_predictions = {}
        
        # Get all test JSON files
        test_files = sorted([f for f in test_dir.glob("test*.json")])
        
        print(f"Found {len(test_files)} test files to process")
        print(f"Using {self.llm_provider} with model {self.model_name}")
        print("=" * 60)
        
        start_time = time.time()
        
        for test_file in test_files:
            test_name = test_file.stem  # e.g., "test1" from "test1.json"
            predictions = self.process_test_file(str(test_file), test_name)
            
            if predictions:
                all_predictions[test_name] = predictions
        
        end_time = time.time()
        
        # Print statistics
        print("=" * 60)
        print(f"Processing completed in {end_time - start_time:.2f} seconds")
        print(f"Total questions: {self.total_questions}")
        print(f"Successful predictions: {self.successful_predictions}")
        print(f"Failed predictions: {self.failed_predictions}")
        print(f"Success rate: {(self.successful_predictions/self.total_questions*100):.1f}%")
        
        # Save results
        if output_file:
            self.save_predictions(all_predictions, output_file)
        
        return all_predictions
    
    def save_predictions(self, predictions: Dict[str, Dict[str, str]], output_file: str):
        """Save predictions to JSON file in the same format as correct answers."""
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Add metadata
        output_data = {
            "metadata": {
                "generated_by": "LLM Answer Checker",
                "llm_provider": self.llm_provider,
                "model_name": self.model_name,
                "generated_at": datetime.now().isoformat(),
                "total_questions": self.total_questions,
                "success_rate": f"{(self.successful_predictions/self.total_questions*100):.1f}%"
            },
            "predictions": predictions
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"Predictions saved to {output_path}")
        
        # Also save in the exact same format as the correct answers (without metadata)
        clean_output_file = output_path.parent / f"clean_{output_path.name}"
        with open(clean_output_file, 'w', encoding='utf-8') as f:
            json.dump(predictions, f, ensure_ascii=False, indent=2)
        
        print(f"Clean predictions (same format as correct answers) saved to {clean_output_file}")
    
    def compare_with_correct_answers(self, predictions: Dict[str, Dict[str, str]], 
                                   correct_answers_file: str) -> Dict[str, Any]:
        """Compare predictions with correct answers and calculate accuracy."""
        try:
            with open(correct_answers_file, 'r', encoding='utf-8') as f:
                correct_answers = json.load(f)
        except FileNotFoundError:
            print(f"Correct answers file not found: {correct_answers_file}")
            return {}
        
        results = {
            "total_questions": 0,
            "correct_predictions": 0,
            "accuracy": 0.0,
            "test_results": {},
            "wrong_answers": {},  # New: detailed wrong answers for manual review
            "summary": {
                "total_wrong": 0,
                "wrong_by_test": {},
                "most_common_wrong_patterns": {}
            }
        }
        
        for test_name in predictions:
            if test_name not in correct_answers:
                continue
                
            test_correct = 0
            test_total = 0
            test_wrong_answers = []
            
            for question_num in predictions[test_name]:
                if question_num in correct_answers[test_name]:
                    test_total += 1
                    results["total_questions"] += 1
                    
                    predicted = predictions[test_name][question_num]
                    correct = correct_answers[test_name][question_num]
                    
                    if predicted == correct:
                        test_correct += 1
                        results["correct_predictions"] += 1
                    else:
                        # Record wrong answer for manual review
                        wrong_answer_info = {
                            "question_number": question_num,
                            "predicted_answer": predicted,
                            "correct_answer": correct,
                            "prediction_pattern": f"{predicted}→{correct}"
                        }
                        test_wrong_answers.append(wrong_answer_info)
                        results["summary"]["total_wrong"] += 1
            
            if test_total > 0:
                test_accuracy = (test_correct / test_total) * 100
                results["test_results"][test_name] = {
                    "correct": test_correct,
                    "total": test_total,
                    "wrong": test_total - test_correct,
                    "accuracy": test_accuracy
                }
                
                # Store wrong answers for this test
                if test_wrong_answers:
                    results["wrong_answers"][test_name] = test_wrong_answers
                    results["summary"]["wrong_by_test"][test_name] = len(test_wrong_answers)
        
        if results["total_questions"] > 0:
            results["accuracy"] = (results["correct_predictions"] / results["total_questions"]) * 100
        
        # Analyze patterns in wrong answers
        pattern_count = {}
        for test_wrong_list in results["wrong_answers"].values():
            for wrong_answer in test_wrong_list:
                pattern = wrong_answer["prediction_pattern"]
                pattern_count[pattern] = pattern_count.get(pattern, 0) + 1
        
        # Sort patterns by frequency
        results["summary"]["most_common_wrong_patterns"] = dict(
            sorted(pattern_count.items(), key=lambda x: x[1], reverse=True)[:10]
        )
        
        return results

def main():
    """Main function with command-line interface."""
    parser = argparse.ArgumentParser(description="LLM-based TCF Reading Comprehension Answer Checker")
    parser.add_argument("--input-dir", "-i", 
                       help="Directory containing test JSON files")
    parser.add_argument("--single-file", "-s",
                       help="Single JSON file to process (alternative to input-dir)")
    parser.add_argument("--output-file", "-o", 
                       help="Output file for predictions (default: predictions_TIMESTAMP.json)")
    parser.add_argument("--llm-provider", choices=["openai", "anthropic", "local"], 
                       default="openai", help="LLM provider to use")
    parser.add_argument("--model", help="Specific model name to use")
    parser.add_argument("--api-key", help="API key for the LLM service")
    parser.add_argument("--correct-answers", help="File with correct answers for comparison")
    parser.add_argument("--test-limit", type=int, help="Limit number of tests to process (for testing)")
    
    args = parser.parse_args()
    
    # Validate arguments
    if not args.input_dir and not args.single_file:
        parser.error("Either --input-dir or --single-file must be specified")
    if args.input_dir and args.single_file:
        parser.error("Cannot specify both --input-dir and --single-file")
    
    # Set default output file if not provided
    if not args.output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.single_file:
            test_name = Path(args.single_file).stem
            args.output_file = f"predictions_{test_name}_{args.llm_provider}_{timestamp}.json"
        else:
            args.output_file = f"predictions_{args.llm_provider}_{timestamp}.json"
    
    try:
        # Initialize the checker
        checker = LLMAnswerChecker(
            llm_provider=args.llm_provider,
            model_name=args.model,
            api_key=args.api_key
        )
        
        # Process tests
        if args.single_file:
            # Process single file
            test_name = Path(args.single_file).stem
            print(f"Processing single file: {args.single_file}")
            print(f"Test name: {test_name}")
            print(f"Using {args.llm_provider} with model {checker.model_name}")
            print("=" * 60)
            
            predictions = checker.process_test_file(args.single_file, test_name)
            all_predictions = {test_name: predictions} if predictions else {}
            
            # Save results
            if all_predictions:
                checker.save_predictions(all_predictions, args.output_file)
        else:
            # Process all files in directory
            all_predictions = checker.process_all_tests(args.input_dir, args.output_file)
        
        # Compare with correct answers if provided
        if args.correct_answers:
            print("\nComparing with correct answers...")
            comparison_results = checker.compare_with_correct_answers(all_predictions, args.correct_answers)
            
            print(f"Overall Accuracy: {comparison_results['accuracy']:.1f}%")
            print(f"Correct: {comparison_results['correct_predictions']}/{comparison_results['total_questions']}")
            print(f"Wrong: {comparison_results['summary']['total_wrong']}")
            
            # Show most common wrong patterns
            if comparison_results['summary']['most_common_wrong_patterns']:
                print(f"\nMost common wrong patterns:")
                for pattern, count in list(comparison_results['summary']['most_common_wrong_patterns'].items())[:5]:
                    print(f"  {pattern}: {count} times")
            
            # Show some examples of wrong answers for manual review
            if comparison_results['summary']['total_wrong'] > 0:
                print(f"\nExamples of wrong answers for manual review:")
                example_count = 0
                for test_name, wrong_list in comparison_results['wrong_answers'].items():
                    if example_count >= 10:  # Limit to 10 examples in console
                        break
                    for wrong_answer in wrong_list[:3]:  # Show first 3 from each test
                        if example_count >= 10:
                            break
                        print(f"  {test_name} Q{wrong_answer['question_number']}: Predicted {wrong_answer['predicted_answer']}, Correct {wrong_answer['correct_answer']}")
                        example_count += 1
                
                if comparison_results['summary']['total_wrong'] > 10:
                    print(f"  ... and {comparison_results['summary']['total_wrong'] - 10} more wrong answers")
                    print(f"  See full details in the comparison JSON file")
            
            # Save comparison results
            comparison_file = Path(args.output_file).parent / f"comparison_{Path(args.output_file).name}"
            with open(comparison_file, 'w', encoding='utf-8') as f:
                json.dump(comparison_results, f, ensure_ascii=False, indent=2)
            print(f"\nDetailed comparison results with ALL wrong answers saved to {comparison_file}")
        
    except Exception as e:
        print(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main()) 