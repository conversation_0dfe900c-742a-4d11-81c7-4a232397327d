# LLM Answer Checker Requirements

# Core dependencies (always needed)
typing-extensions>=4.0.0

# LLM Provider Libraries (install based on your choice)
# OpenAI GPT models
openai>=1.0.0

# Anthropic Claude models  
anthropic>=0.18.0

# Optional: Local model support
# ollama  # Uncomment if using Ollama for local models
# llama-cpp-python  # Uncomment if using llama.cpp

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0 