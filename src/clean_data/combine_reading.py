import json
import os
from pathlib import Path



def combine_reading_files():
    # Paths
    base_dir = Path("data")
    scraped_dir = base_dir / "scraped"
    combined_dir = base_dir / "combined" / "reading"
    
    # Create combined directory if it doesn't exist
    combined_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all reading files
    reading_dir = scraped_dir / "scraped_reading_free"
    reading_files = list(reading_dir.glob("tcf_test40.json"))
    
    for file_path in reading_files:
        test_name = file_path.stem  # e.g., "tcf_test39"
        test_number = test_name.replace('tcf_test', '')
        media_test_name = f"media_test{test_number}"
        
        # Read the reading file
        with open(file_path, 'r', encoding='utf-8') as f:
            reading_data = json.load(f)
        
        # Process each question
        combined_data = []
        for item in reading_data:
            # Extract question number
            q_num = item["question_number"]
            # Update image path to match actual file naming
            new_image_path = f"downloaded_images_{test_number}/Q{q_num}_test{test_number}.png"
            # Create question with specified attributes
            question = {
                "question_number": q_num,
                "image_path": new_image_path,
                "extracted_text": item["text"],
                "question_text": item["question_text"],
                "choices": item["choices"]
            }
            combined_data.append(question)
        
        # Sort questions by number
        combined_data.sort(key=lambda x: int(x["question_number"]))
        
        # Save combined file
        output_path = combined_dir / f"test{test_number}.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, ensure_ascii=False, indent=2)
        
        print(f"Created combined file: {output_path}")

if __name__ == "__main__":
    combine_reading_files() 