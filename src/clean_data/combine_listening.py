import json
import os
from pathlib import Path

def clean_text(text):
    # Remove extra spaces
    text = ' '.join(text.split())
    # Fix apostrophes
    text = text.replace(" '", "'")
    return text

def combine_listening_files():
    # Paths
    base_dir = Path("data")
    scraped_dir = base_dir / "scraped"
    combined_dir = base_dir / "combined" / "listening"
    
    # Create combined directory if it doesn't exist
    combined_dir.mkdir(parents=True, exist_ok=True)
    
    # Get all listening text files
    listening_text_dir = scraped_dir / "scraped_listening_text"
    listening_files = list(listening_text_dir.glob("media_test*.json"))
    
    for file_path in listening_files:
        test_name = file_path.stem  # e.g., "media_test1"
        test_number = test_name.replace('media_test', '')
        tcf_test_name = f"tcf_test{test_name.replace('media_test', '')}"  # Convert to tcf_test format
        
        # Read the listening text file
        with open(file_path, 'r', encoding='utf-8') as f:
            listening_data = json.load(f)
            
        # Read the corresponding listening file for audio and image paths
        listening_path = scraped_dir / "scraped_listening" / f"{tcf_test_name}.json"
        with open(listening_path, 'r', encoding='utf-8') as f:
            listening_info = json.load(f)
            
        # Create a mapping of question numbers to their audio, image paths, and choices
        question_paths = {}
        for item in listening_info:
            q_num = item["question_number"]
            question_paths[q_num] = {
                "audio_path": item["audio_path"],
                "image_path": item.get("image_path", None),
                "choices": item.get("choices", {"A": "", "B": "", "C": "", "D": ""})
            }
        
        # Process each question
        combined_data = []
        for item in listening_data:
            # Extract question number from audio path
            q_num = item["audio_path"].split("/")[-1].replace(".mp3", "").replace("Q", "")
            
            # Clean and process chunks
            chunks = [
                {
                    "text": clean_text(chunk["text"]),
                    "start": chunk["start"],
                    "end": chunk["end"]
                }
                for chunk in item["chunks"]
            ]
            
            # Get the correct paths and choices from the listening info
            paths = question_paths.get(q_num, {"audio_path": None, "image_path": None, "choices": {"A": "", "B": "", "C": "", "D": ""}})
            
            # Create question with specified attributes
            question = {
                "question_number": q_num,
                "image_path": paths["image_path"],
                "audio_path": paths["audio_path"],
                "extracted_text": clean_text(item["full_text"]),
                "chunks": chunks,
                "choices": paths["choices"]
            }
            combined_data.append(question)
        
        # Sort questions by number
        combined_data.sort(key=lambda x: int(x["question_number"]))
        
        # Save combined file
        output_path = combined_dir / f"test{test_number}.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(combined_data, f, ensure_ascii=False, indent=2)
        
        print(f"Created combined file: {output_path}")

if __name__ == "__main__":
    combine_listening_files() 