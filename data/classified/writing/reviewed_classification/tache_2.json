{"metadata": {"checkpoint_name": "finalized", "description": "", "tache_number": 2, "created_timestamp": "2025-07-21T19:21:19.548230", "total_modifications": 750, "modifications_log": [{"action": "move_task", "task_id": "juillet-2024_c10_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:14:51.237255"}, {"action": "move_task", "task_id": "juillet-2024_c31_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:15:05.868785"}, {"action": "move_task", "task_id": "fevrier-2025_c25_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:15:45.911523"}, {"action": "move_task", "task_id": "novembre-2024_c4_t1", "from": "description_places > description_city", "to": "manual_review > manual_review_general", "timestamp": "2025-06-01T01:16:05.542144"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_university", "timestamp": "2025-06-01T01:17:03.412027"}, {"action": "move_task", "task_id": "aout-2024_c1_t1", "from": "description_places > description_general_place", "to": "description_places > description_university", "timestamp": "2025-06-01T01:19:09.732772"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_restaurant", "timestamp": "2025-06-01T01:19:39.091178"}, {"action": "move_task", "task_id": "decembre-2024_c14_t1", "from": "description_places > description_general_place", "to": "description_places > description_restaurant", "timestamp": "2025-06-01T01:19:50.470555"}, {"action": "move_task", "task_id": "avril-2025_c15_t1", "from": "description_places > description_general_place", "to": "description_places > description_restaurant", "timestamp": "2025-06-01T01:19:58.477242"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_hotel", "timestamp": "2025-06-01T01:23:25.198657"}, {"action": "move_task", "task_id": "avril-2025_c20_t1", "from": "description_places > description_general_place", "to": "description_places > description_hotel", "timestamp": "2025-06-01T01:23:56.231553"}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_general_place", "new_name": "description_countryside", "timestamp": "2025-06-01T01:24:39.351421"}, {"action": "move_task", "task_id": "octobre-2024_c10_t1", "from": "description_places > description_tourist_place", "to": "description_places > description_city", "timestamp": "2025-06-01T01:25:41.640160"}, {"action": "move_task", "task_id": "juillet-2024_c24_t1", "from": "description_places > description_office", "to": "description_places > description_hotel", "timestamp": "2025-06-01T01:26:51.692091"}, {"timestamp": "2025-06-01T04:07:21.730153", "action": "move_to_buffer", "task_id": "octobre-2024_c5_t1", "source_main_topic": "description_places", "source_subtopic": "description_countryside", "task_content": "écrivez un message à votre ami pour lui décrire le..."}, {"timestamp": "2025-06-01T04:07:45.177418", "action": "move_to_buffer", "task_id": "septembre-2024_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_countryside", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-01T04:08:11.365157", "action": "move_to_buffer", "task_id": "novembre-2024_c20_t1", "source_main_topic": "description_places", "source_subtopic": "description_office", "task_content": "Votre ami souhaite commencer à faire du sport. Réd..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_gym", "timestamp": "2025-06-01T04:08:29.029923"}, {"timestamp": "2025-06-01T04:08:38.395617", "action": "move_from_buffer", "task_id": "novembre-2024_c20_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Votre ami souhaite commencer à faire du sport. Réd..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_tourist_place", "new_name": "description_park", "timestamp": "2025-06-01T04:09:59.565130"}, {"timestamp": "2025-06-01T04:11:06.867540", "action": "move_to_buffer", "task_id": "septembre-2024_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:11:11.684642", "action": "move_to_buffer", "task_id": "septembre-2024_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Votre ami veut se mettre au sport. Vous lui envoye..."}, {"timestamp": "2025-06-01T04:11:33.054703", "action": "move_to_buffer", "task_id": "novembre-2024_c14_t1", "source_main_topic": "description_places", "source_subtopic": "description_venue", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:11:53.125678", "action": "move_from_buffer", "task_id": "septembre-2024_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:12:05.573177", "action": "move_from_buffer", "task_id": "novembre-2024_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:12:23.312665", "action": "move_from_buffer", "task_id": "septembre-2024_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Votre ami veut se mettre au sport. Vous lui envoye..."}, {"timestamp": "2025-06-01T04:12:40.013489", "action": "move_from_buffer", "task_id": "octobre-2024_c5_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "écrivez un message à votre ami pour lui décrire le..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_venue", "new_name": "description_birthday_party", "timestamp": "2025-06-01T04:13:46.261413"}, {"timestamp": "2025-06-01T04:14:45.605202", "action": "move_to_buffer", "task_id": "juillet-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:14:57.733399", "action": "move_to_buffer", "task_id": "juillet-2024_c14_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:15:05.416025", "action": "move_to_buffer", "task_id": "juillet-2024_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:15:11.060099", "action": "move_to_buffer", "task_id": "juillet-2024_c20_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "<PERSON><PERSON>, Tu as commencé ton nouveau travail ! C’est ..."}, {"timestamp": "2025-06-01T04:15:21.572774", "action": "move_to_buffer", "task_id": "juillet-2024_c21_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-01T04:15:28.893483", "action": "move_to_buffer", "task_id": "juillet-2024_c22_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_wedding_place", "timestamp": "2025-06-01T04:15:54.928809"}, {"timestamp": "2025-06-01T04:16:09.272966", "action": "move_from_buffer", "task_id": "juillet-2024_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_shopping_place", "timestamp": "2025-06-01T04:16:23.548089"}, {"timestamp": "2025-06-01T04:16:34.510073", "action": "move_from_buffer", "task_id": "juillet-2024_c19_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:16:55.467309", "action": "move_from_buffer", "task_id": "juillet-2024_c22_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-01T04:16:59.388641", "action": "move_from_buffer", "task_id": "juillet-2024_c21_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-01T04:17:28.288066", "action": "move_from_buffer", "task_id": "juillet-2024_c20_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON><PERSON>, Tu as commencé ton nouveau travail ! C’est ..."}, {"timestamp": "2025-06-01T04:17:35.687206", "action": "move_from_buffer", "task_id": "septembre-2024_c10_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-01T04:18:05.914433", "action": "move_to_buffer", "task_id": "juillet-2024_c37_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:13.807425", "action": "move_to_buffer", "task_id": "decembre-2024_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:18.680330", "action": "move_to_buffer", "task_id": "decembre-2024_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:18:22.445585", "action": "move_to_buffer", "task_id": "janvier-2025_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:18:37.519705", "action": "move_to_buffer", "task_id": "janvier-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:18:41.526780", "action": "move_to_buffer", "task_id": "janvier-2025_c5_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:18:45.694592", "action": "move_to_buffer", "task_id": "janvier-2025_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-01T04:18:49.784460", "action": "move_to_buffer", "task_id": "janvier-2025_c11_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:06.228947", "action": "move_from_buffer", "task_id": "decembre-2024_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:14.039584", "action": "move_from_buffer", "task_id": "decembre-2024_c19_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:19.369378", "action": "move_from_buffer", "task_id": "juillet-2024_c37_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:19:22.711594", "action": "move_from_buffer", "task_id": "janvier-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:19:29.713770", "action": "move_from_buffer", "task_id": "janvier-2025_c5_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:37.665925", "action": "move_from_buffer", "task_id": "janvier-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:19:50.540563", "action": "move_from_buffer", "task_id": "janvier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_countryside", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_countryside", "new_name": "description_outdoor_activity", "timestamp": "2025-06-01T04:20:11.154282"}, {"timestamp": "2025-06-01T04:20:22.604959", "action": "move_from_buffer", "task_id": "janvier-2025_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:20:31.731440", "action": "move_to_buffer", "task_id": "janvier-2025_c12_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "L’été est arrivé ! Je vous propose de faire un piq..."}, {"timestamp": "2025-06-01T04:20:39.181278", "action": "move_to_buffer", "task_id": "janvier-2025_c13_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Salut je vais venir en vacances dans ton pays tu p..."}, {"timestamp": "2025-06-01T04:20:41.557516", "action": "move_to_buffer", "task_id": "janvier-2025_c16_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:20:47.932444", "action": "move_to_buffer", "task_id": "aout-2024_c3_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:20:53.612184", "action": "move_to_buffer", "task_id": "fevrier-2025_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Léa t’a envoyé un message pour te proposer un piqu..."}, {"timestamp": "2025-06-01T04:21:03.212723", "action": "move_to_buffer", "task_id": "fevrier-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-01T04:21:07.258036", "action": "move_to_buffer", "task_id": "avril-2025_c3_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Votre ami(e) désire explorer la région dans laquel..."}, {"timestamp": "2025-06-01T04:21:25.190452", "action": "move_from_buffer", "task_id": "janvier-2025_c13_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Salut je vais venir en vacances dans ton pays tu p..."}, {"timestamp": "2025-06-01T04:21:34.268209", "action": "move_from_buffer", "task_id": "aout-2024_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Répondez au courriel de votre ami Lucas pour lui d..."}, {"timestamp": "2025-06-01T04:21:36.802691", "action": "move_from_buffer", "task_id": "janvier-2025_c16_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez ..."}, {"timestamp": "2025-06-01T04:21:45.147548", "action": "move_from_buffer", "task_id": "avril-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) désire explorer la région dans laquel..."}, {"timestamp": "2025-06-01T04:22:00.524493", "action": "move_from_buffer", "task_id": "fevrier-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Léa t’a envoyé un message pour te proposer un piqu..."}, {"timestamp": "2025-06-01T04:22:04.003638", "action": "move_from_buffer", "task_id": "janvier-2025_c12_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "L’été est arrivé ! Je vous propose de faire un piq..."}, {"timestamp": "2025-06-01T04:22:52.864955", "action": "move_to_buffer", "task_id": "novembre-2024_c1_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Écrire un message à votre amie pour lui proposer u..."}, {"timestamp": "2025-06-01T04:22:56.822772", "action": "move_to_buffer", "task_id": "novembre-2024_c2_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-01T04:22:59.405795", "action": "move_to_buffer", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:02.643008", "action": "move_to_buffer", "task_id": "novembre-2024_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:23:05.685274", "action": "move_to_buffer", "task_id": "mars-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Rédigez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-01T04:23:08.084934", "action": "move_to_buffer", "task_id": "Mai-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-01T04:23:16.954611", "action": "move_to_buffer", "task_id": "octobre-2024_c1_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-01T04:23:20.022306", "action": "move_to_buffer", "task_id": "octobre-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:29.209839", "action": "move_to_buffer", "task_id": "octobre-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_birthday_party", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:54.793719", "action": "move_from_buffer", "task_id": "juillet-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:23:57.745448", "action": "move_from_buffer", "task_id": "fevrier-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-01T04:24:01.527170", "action": "move_from_buffer", "task_id": "novembre-2024_c1_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Écrire un message à votre amie pour lui proposer u..."}, {"timestamp": "2025-06-01T04:24:04.890343", "action": "move_from_buffer", "task_id": "novembre-2024_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:13.597219", "action": "move_from_buffer", "task_id": "mars-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_birthday_party", "task_content": "Rédigez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-01T04:24:21.400493", "action": "move_from_buffer", "task_id": "novembre-2024_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-01T04:24:35.178792", "action": "move_from_buffer", "task_id": "Mai-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-01T04:24:46.537477", "action": "move_from_buffer", "task_id": "octobre-2024_c1_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_help", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-01T04:24:50.537261", "action": "move_from_buffer", "task_id": "octobre-2024_c17_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:53.452643", "action": "move_from_buffer", "task_id": "octobre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-01T04:24:58.863318", "action": "move_from_buffer", "task_id": "novembre-2024_c2_t1", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherch..."}, {"timestamp": "2025-06-02T19:25:37.442524", "action": "move_to_buffer", "task_id": "juillet-2024_c6_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"timestamp": "2025-06-02T19:26:06.827260", "action": "move_task", "task_id": "juillet-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_housing", "new_name": "looking_for_roommate", "timestamp": "2025-06-02T19:26:27.540842"}, {"timestamp": "2025-06-02T19:26:43.051177", "action": "move_to_buffer", "task_id": "janvier-2025_c14_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_roommate", "task_content": "Vous allez déménager à Nice, en France. Vous écriv..."}, {"timestamp": "2025-06-02T19:27:04.747533", "action": "move_to_buffer", "task_id": "avril-2025_c2_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_roommate", "task_content": "Vous avez trouvé une annonce en ligne pour louer u..."}, {"timestamp": "2025-06-02T19:28:35.545794", "action": "move_to_buffer", "task_id": "juillet-2024_c11_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "task_content": "Votre ami <PERSON> vient de s’installer dans votre vi..."}, {"timestamp": "2025-06-02T19:28:53.151793", "action": "move_task", "task_id": "juillet-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_birthday_party", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T19:29:02.971231", "action": "move_task", "task_id": "juillet-2024_c23_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_sports_help", "timestamp": "2025-06-02T19:30:15.372298"}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_sports_help", "new_name": "looking_for_sport_help", "timestamp": "2025-06-02T19:30:28.531233"}, {"timestamp": "2025-06-02T19:31:07.437459", "action": "move_to_buffer", "task_id": "decembre-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:10.606577", "action": "move_to_buffer", "task_id": "janvier-2025_c7_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:18.198983", "action": "move_to_buffer", "task_id": "fevrier-2025_c14_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Rédigez un message à publier dans le journal de vo..."}, {"timestamp": "2025-06-02T19:31:22.907576", "action": "move_to_buffer", "task_id": "novembre-2024_c9_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_language_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:47.837745", "action": "move_from_buffer", "task_id": "janvier-2025_c7_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"timestamp": "2025-06-02T19:31:54.960819", "action": "move_from_buffer", "task_id": "fevrier-2025_c14_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Rédigez un message à publier dans le journal de vo..."}, {"timestamp": "2025-06-02T19:31:59.374112", "action": "move_from_buffer", "task_id": "novembre-2024_c9_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_apartment", "timestamp": "2025-06-02T19:32:50.846191"}, {"timestamp": "2025-06-02T19:32:57.938950", "action": "move_from_buffer", "task_id": "avril-2025_c2_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_apartment", "task_content": "Vous avez trouvé une annonce en ligne pour louer u..."}, {"timestamp": "2025-06-02T19:33:05.263803", "action": "move_from_buffer", "task_id": "janvier-2025_c14_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_apartment", "task_content": "Vous allez déménager à Nice, en France. Vous écriv..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_transportation", "timestamp": "2025-06-02T19:34:02.112454"}, {"timestamp": "2025-06-02T19:34:09.924247", "action": "move_from_buffer", "task_id": "juillet-2024_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de s’installer dans votre vi..."}, {"timestamp": "2025-06-02T19:34:22.683814", "action": "move_from_buffer", "task_id": "decembre-2024_c15_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sport_help", "task_content": "Écrivez un message dans le journal de votre univer..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_gift", "timestamp": "2025-06-02T19:34:50.563855"}, {"timestamp": "2025-06-02T19:34:54.508303", "action": "move_from_buffer", "task_id": "juillet-2024_c6_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_birthday_party", "new_name": "description_party", "timestamp": "2025-06-02T20:07:00.360553"}, {"timestamp": "2025-06-02T20:07:27.639244", "action": "move_task", "task_id": "juillet-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T20:07:34.734633", "action": "move_task", "task_id": "juillet-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre ami va fêter son anniversaire. Écrivez un me..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_housekeeping", "timestamp": "2025-06-02T20:07:53.818444"}, {"timestamp": "2025-06-02T20:07:58.121786", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_help", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T20:16:18.290943", "action": "move_task", "task_id": "decembre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Écrivez un message pour inviter vos amis à une fêt..."}, {"timestamp": "2025-06-02T20:16:30.635908", "action": "move_task", "task_id": "fevrier-2025_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Tu loues un appartement qui est trop grand pour to..."}, {"timestamp": "2025-06-02T20:16:40.756828", "action": "move_task", "task_id": "fevrier-2025_c19_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T20:16:50.389570", "action": "move_task", "task_id": "fevrier-2025_c20_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_sport", "timestamp": "2025-06-02T20:18:37.190102"}, {"timestamp": "2025-06-02T20:18:49.743534", "action": "move_task", "task_id": "fevrier-2025_c22_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T20:18:59.468678", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_services", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T20:19:04.323917", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_services", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T20:19:23.406768", "action": "move_task", "task_id": "fevrier-2025_c23_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous êtes locataire d’un appartement qui vous semb..."}, {"timestamp": "2025-06-02T20:20:07.218573", "action": "move_task", "task_id": "avril-2025_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_outdoor_activity", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T20:20:23.711673", "action": "move_task", "task_id": "avril-2025_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Vous avez découvert une plateforme en ligne mettan..."}, {"timestamp": "2025-06-02T21:04:42.814294", "action": "move_to_buffer", "task_id": "fevrier-2025_c27_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:04:55.643358", "action": "move_to_buffer", "task_id": "avril-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:05:00.552715", "action": "move_task", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:05:07.944448", "action": "move_to_buffer", "task_id": "novembre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie...."}, {"timestamp": "2025-06-02T21:05:10.190932", "action": "move_to_buffer", "task_id": "novembre-2024_c15_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "“ <PERSON><PERSON>, j’ai appris que tu vas à une salle de spo..."}, {"timestamp": "2025-06-02T21:05:11.435424", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:05:12.786193", "action": "move_to_buffer", "task_id": "Mai-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Votre anniversaire approche. Vos amis vous posent ..."}, {"timestamp": "2025-06-02T21:05:14.146442", "action": "move_to_buffer", "task_id": "octobre-2024_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à vos amis pour leur dire que v..."}, {"timestamp": "2025-06-02T21:05:15.298233", "action": "move_to_buffer", "task_id": "octobre-2024_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:05:16.414312", "action": "move_to_buffer", "task_id": "octobre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:05:17.597338", "action": "move_to_buffer", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T21:05:19.127279", "action": "move_to_buffer", "task_id": "octobre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami qui a accepté de s’..."}, {"timestamp": "2025-06-02T21:05:20.302799", "action": "move_to_buffer", "task_id": "fevrier-2025_c25_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:05:21.684052", "action": "move_to_buffer", "task_id": "novembre-2024_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous voulez communiquer avec quelqu’un en français..."}, {"timestamp": "2025-06-02T21:05:22.878006", "action": "move_to_buffer", "task_id": "septembre-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-02T21:05:23.989019", "action": "move_to_buffer", "task_id": "fevrier-2025_c20_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"timestamp": "2025-06-02T21:05:25.257410", "action": "move_to_buffer", "task_id": "fevrier-2025_c22_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:05:26.523111", "action": "move_to_buffer", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:05:47.529225", "action": "move_from_buffer", "task_id": "Mai-2025_c1_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre anniversaire approche. Vos amis vous posent ..."}, {"timestamp": "2025-06-02T21:06:17.937528", "action": "move_from_buffer", "task_id": "juillet-2024_c31_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:06:23.510672", "action": "move_from_buffer", "task_id": "fevrier-2025_c22_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"timestamp": "2025-06-02T21:06:31.566013", "action": "move_from_buffer", "task_id": "fevrier-2025_c20_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez mis en ligne une offre de location pour ..."}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "description_apartment", "timestamp": "2025-06-02T21:07:16.972570"}, {"timestamp": "2025-06-02T21:07:22.183372", "action": "move_from_buffer", "task_id": "septembre-2024_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_apartment", "task_content": "Vous partez en voyage et vous laissez votre appart..."}, {"timestamp": "2025-06-02T21:07:27.009317", "action": "move_from_buffer", "task_id": "novembre-2024_c4_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Vous voulez communiquer avec quelqu’un en français..."}, {"timestamp": "2025-06-02T21:07:32.983734", "action": "move_from_buffer", "task_id": "octobre-2024_c13_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "écrivez un message à votre ami qui a accepté de s’..."}, {"timestamp": "2025-06-02T21:08:03.816702", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:08:14.779669", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "description_places", "source_subtopic": "description_hotel", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:08:20.164176", "action": "move_from_buffer", "task_id": "octobre-2024_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "écrivez un message à vos amis pour leur dire que v..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_outdoor_activity", "new_name": "description_trip", "timestamp": "2025-06-02T21:09:34.829598"}, {"timestamp": "2025-06-02T21:09:56.785124", "action": "move_to_buffer", "task_id": "juillet-2024_c22_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:07.073608", "action": "move_to_buffer", "task_id": "juillet-2024_c21_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-02T21:10:12.983956", "action": "move_to_buffer", "task_id": "janvier-2025_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:25.481503", "action": "move_from_buffer", "task_id": "juillet-2024_c22_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:28.411768", "action": "move_from_buffer", "task_id": "janvier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Je cherche un endroit pour déjeuner en plein air c..."}, {"timestamp": "2025-06-02T21:10:54.837843", "action": "move_from_buffer", "task_id": "juillet-2024_c21_t1", "target_main_topic": "description_places", "target_subtopic": "description_trip", "task_content": "Votre ami vous propose de faire du camping. Écrive..."}, {"timestamp": "2025-06-02T21:11:09.089459", "action": "move_from_buffer", "task_id": "novembre-2024_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "“ <PERSON><PERSON>, j’ai appris que tu vas à une salle de spo..."}, {"timestamp": "2025-06-02T21:11:27.904112", "action": "move_from_buffer", "task_id": "avril-2025_c18_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Rédigez un message à votre ami Cédric pour lui fou..."}, {"action": "rename_subtopic", "main_topic": "description_places", "old_name": "description_trip", "new_name": "description_camping", "timestamp": "2025-06-02T21:12:01.745093"}, {"action": "create_subtopic", "main_topic": "description_places", "subtopic_name": "descripiton_trip", "timestamp": "2025-06-02T21:12:13.443757"}, {"timestamp": "2025-06-02T21:12:35.651488", "action": "move_to_buffer", "task_id": "juillet-2024_c33_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:12:37.336234", "action": "move_to_buffer", "task_id": "novembre-2024_c18_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:12:41.937538", "action": "move_to_buffer", "task_id": "juillet-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:43.424128", "action": "move_to_buffer", "task_id": "fevrier-2025_c10_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-02T21:12:48.753742", "action": "move_to_buffer", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:51.108455", "action": "move_to_buffer", "task_id": "Mai-2025_c4_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-02T21:12:52.366136", "action": "move_to_buffer", "task_id": "octobre-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:53.793436", "action": "move_to_buffer", "task_id": "octobre-2024_c9_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:12:57.226060", "action": "move_to_buffer", "task_id": "fevrier-2025_c19_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T21:12:59.426355", "action": "move_to_buffer", "task_id": "avril-2025_c8_t1", "source_main_topic": "description_places", "source_subtopic": "description_camping", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T21:13:10.787354", "action": "move_from_buffer", "task_id": "novembre-2024_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_apartment", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:16.057666", "action": "move_task", "task_id": "novembre-2024_c6_t1", "source_main_topic": "description_places", "source_subtopic": "description_apartment", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:23.475876", "action": "move_from_buffer", "task_id": "octobre-2024_c17_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:29.566307", "action": "move_from_buffer", "task_id": "Mai-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous envisagez de partir en week-end avec vos amis..."}, {"timestamp": "2025-06-02T21:13:31.184873", "action": "move_from_buffer", "task_id": "octobre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:33.823474", "action": "move_from_buffer", "task_id": "fevrier-2025_c19_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Rédigez un message à votre ami(e) dans lequel vous..."}, {"timestamp": "2025-06-02T21:13:35.902549", "action": "move_from_buffer", "task_id": "fevrier-2025_c10_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez l’intention de partir en week-end avec v..."}, {"timestamp": "2025-06-02T21:13:38.605596", "action": "move_from_buffer", "task_id": "novembre-2024_c18_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:13:40.541434", "action": "move_from_buffer", "task_id": "juillet-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous voulez partir en week-end avec vos amis le mo..."}, {"timestamp": "2025-06-02T21:13:42.323477", "action": "move_from_buffer", "task_id": "juillet-2024_c33_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez passé un week-end à la campagne. Écrivez..."}, {"timestamp": "2025-06-02T21:13:44.956394", "action": "move_from_buffer", "task_id": "avril-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Vous avez décidé d’offrir un voyage à votre ami po..."}, {"timestamp": "2025-06-02T21:13:54.511027", "action": "move_from_buffer", "task_id": "novembre-2024_c12_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie...."}, {"timestamp": "2025-06-02T21:14:29.571767", "action": "move_from_buffer", "task_id": "fevrier-2025_c27_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:14:30.815671", "action": "move_from_buffer", "task_id": "octobre-2024_c12_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:14:32.546463", "action": "move_from_buffer", "task_id": "juillet-2024_c10_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T21:14:33.657413", "action": "move_from_buffer", "task_id": "octobre-2024_c8_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:14:34.654759", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:14:35.789983", "action": "move_from_buffer", "task_id": "fevrier-2025_c25_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:14:58.089663", "action": "move_task", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"action": "rename_main_topic", "old_name": "looking_for_service", "new_name": "looking_for_organizing_help", "timestamp": "2025-06-02T21:15:32.682691"}, {"action": "rename_main_topic", "old_name": "looking_for_organizing_help", "new_name": "looking_for_service", "timestamp": "2025-06-02T21:15:50.605101"}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_orgianizing_help", "timestamp": "2025-06-02T21:16:18.372561"}, {"timestamp": "2025-06-02T21:16:26.329735", "action": "move_task", "task_id": "octobre-2024_c8_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:17:09.454981", "action": "move_task", "task_id": "juillet-2024_c12_t1", "source_main_topic": "description_places", "source_subtopic": "description_party", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Invitez vos amis à célébrer votre anniversaire tou..."}, {"timestamp": "2025-06-02T21:17:16.824954", "action": "move_task", "task_id": "juillet-2024_c17_t1", "source_main_topic": "description_places", "source_subtopic": "description_party", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_orgianizing_help", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_orgianizing_help", "new_name": "looking_for_orgianizing_party", "timestamp": "2025-06-02T21:17:33.824550"}, {"timestamp": "2025-06-02T21:17:46.845784", "action": "move_task", "task_id": "juillet-2024_c30_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous allez déménager. Des amis ont accepté de vous..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_help", "new_name": "looking_for_renovation_help", "timestamp": "2025-06-02T21:17:56.737051"}, {"timestamp": "2025-06-02T21:18:09.291551", "action": "move_task", "task_id": "decembre-2024_c13_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:18:12.109449", "action": "move_task", "task_id": "decembre-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:18:14.344005", "action": "move_task", "task_id": "janvier-2025_c18_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous voulez organiser une fête. Écrivez un message..."}, {"timestamp": "2025-06-02T21:18:18.990991", "action": "move_task", "task_id": "fevrier-2025_c24_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’informer de vot..."}, {"timestamp": "2025-06-02T21:18:21.264932", "action": "move_task", "task_id": "fevrier-2025_c28_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez célébrer votre anniversaire dans un..."}, {"timestamp": "2025-06-02T21:18:22.926610", "action": "move_task", "task_id": "fevrier-2025_c32_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:18:26.717226", "action": "move_task", "task_id": "novembre-2024_c5_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"timestamp": "2025-06-02T21:18:30.184425", "action": "move_task", "task_id": "novembre-2024_c13_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:18:31.477460", "action": "move_task", "task_id": "novembre-2024_c17_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez commandé un objet sur Internet et après ..."}, {"timestamp": "2025-06-02T21:18:33.746730", "action": "move_task", "task_id": "novembre-2024_c19_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Ecrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:18:36.574499", "action": "move_task", "task_id": "mars-2025_c1_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui souhait..."}, {"timestamp": "2025-06-02T21:18:37.867555", "action": "move_task", "task_id": "mars-2025_c5_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:18:45.667255", "action": "move_task", "task_id": "octobre-2024_c15_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T21:18:47.950344", "action": "move_task", "task_id": "octobre-2024_c18_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"timestamp": "2025-06-02T21:18:51.125286", "action": "move_task", "task_id": "octobre-2024_c1_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-02T21:18:53.496807", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_renovation_help", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"action": "create_subtopic", "main_topic": "looking_for_service", "subtopic_name": "looking_for_relocation_help", "timestamp": "2025-06-02T21:19:04.625308"}, {"timestamp": "2025-06-02T21:19:18.174490", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:24.929145", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "looking_for_service", "source_subtopic": "looking_for_gift", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:37.655191", "action": "move_task", "task_id": "juillet-2024_c29_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_housekeeping", "task_content": "Votre ami C<PERSON> a accepté de garder votre maison ..."}, {"timestamp": "2025-06-02T21:19:45.245668", "action": "move_task", "task_id": "octobre-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Ecrivez un message à votre ami(e) pour lui faire p..."}, {"timestamp": "2025-06-02T21:19:52.433126", "action": "move_task", "task_id": "octobre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_orgianizing_party", "new_name": "looking_for_organizing_party", "timestamp": "2025-06-02T21:20:12.502294"}, {"timestamp": "2025-06-02T21:20:20.348801", "action": "move_task", "task_id": "octobre-2024_c15_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_organizing_party", "task_content": "Vous organisez un événement. Écrivez une lettre à ..."}, {"timestamp": "2025-06-02T21:20:28.185222", "action": "move_task", "task_id": "novembre-2024_c19_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Ecrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:20:36.384849", "action": "move_task", "task_id": "novembre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"timestamp": "2025-06-02T21:20:43.118567", "action": "move_task", "task_id": "novembre-2024_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "“<PERSON><PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le C..."}, {"timestamp": "2025-06-02T21:20:49.330576", "action": "move_task", "task_id": "fevrier-2025_c28_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez célébrer votre anniversaire dans un..."}, {"timestamp": "2025-06-02T21:21:01.311233", "action": "move_task", "task_id": "fevrier-2025_c24_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Rédigez un message à un ami pour l’informer de vot..."}, {"timestamp": "2025-06-02T21:21:11.523099", "action": "move_task", "task_id": "janvier-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_organizing_party", "task_content": "Vous voulez organiser une fête. Écrivez un message..."}, {"timestamp": "2025-06-02T21:21:32.494550", "action": "move_task", "task_id": "juillet-2024_c30_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_relocation_help", "task_content": "Vous allez déménager. Des amis ont accepté de vous..."}, {"timestamp": "2025-06-02T21:22:37.234212", "action": "move_task", "task_id": "decembre-2024_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Vous souhaitez fêter votre anniversaire dans un re..."}, {"action": "create_subtopic", "main_topic": "sharing_information", "subtopic_name": "sharing_customer_feedback", "timestamp": "2025-06-02T21:23:18.180798"}, {"timestamp": "2025-06-02T21:23:26.525260", "action": "move_task", "task_id": "mars-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:23:33.279037", "action": "move_task", "task_id": "mars-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui souhait..."}, {"timestamp": "2025-06-02T21:23:40.258902", "action": "move_task", "task_id": "novembre-2024_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Vous avez commandé un objet sur Internet et après ..."}, {"timestamp": "2025-06-02T21:23:41.827357", "action": "move_task", "task_id": "fevrier-2025_c32_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_customer_feedback", "task_content": "Rédiger un e-mail au service client pour signaler ..."}, {"timestamp": "2025-06-02T21:23:50.450943", "action": "move_task", "task_id": "decembre-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Écrivez un message à votre ami(e) qui souhaite sui..."}, {"timestamp": "2025-06-02T21:24:53.831105", "action": "move_task", "task_id": "decembre-2024_c5_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_entertainment", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrivez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T21:25:00.394963", "action": "move_task", "task_id": "decembre-2024_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Écrivez un courriel à vos amis pour les inviter à ..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_entertainment", "new_name": "recommendation_sport", "timestamp": "2025-06-02T21:25:21.378354"}, {"timestamp": "2025-06-02T21:25:24.589393", "action": "move_task", "task_id": "juillet-2024_c2_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_general", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:25:40.025922", "action": "move_task", "task_id": "juillet-2024_c26_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:09.621158", "action": "move_to_buffer", "task_id": "juillet-2024_c27_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_general", "task_content": "Vous voulez passer un week-end avec vos amis et fa..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_general", "new_name": "recommendation_career", "timestamp": "2025-06-02T21:26:22.733294"}, {"timestamp": "2025-06-02T21:26:30.549185", "action": "move_to_buffer", "task_id": "janvier-2025_c8_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:33.011923", "action": "move_to_buffer", "task_id": "fevrier-2025_c13_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) souhaite découvrir votre région. Rédi..."}, {"timestamp": "2025-06-02T21:26:36.588724", "action": "move_to_buffer", "task_id": "avril-2025_c19_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Rédigez un message destiné à un ami pour lui propo..."}, {"timestamp": "2025-06-02T21:26:39.219325", "action": "move_to_buffer", "task_id": "novembre-2024_c10_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:26:41.079104", "action": "move_to_buffer", "task_id": "juillet-2024_c2_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_career", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:26:46.838338", "action": "move_from_buffer", "task_id": "juillet-2024_c2_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Vous souhaitez faire du sport et vous voulez que v..."}, {"timestamp": "2025-06-02T21:26:49.465506", "action": "move_from_buffer", "task_id": "avril-2025_c19_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Rédigez un message destiné à un ami pour lui propo..."}, {"timestamp": "2025-06-02T21:27:11.809460", "action": "move_from_buffer", "task_id": "juillet-2024_c27_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Vous voulez passer un week-end avec vos amis et fa..."}, {"timestamp": "2025-06-02T21:27:22.503184", "action": "move_from_buffer", "task_id": "janvier-2025_c8_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:27:24.296096", "action": "move_from_buffer", "task_id": "fevrier-2025_c13_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) souhaite découvrir votre région. Rédi..."}, {"timestamp": "2025-06-02T21:27:25.724445", "action": "move_from_buffer", "task_id": "novembre-2024_c10_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Votre ami(e) veut découvrir la région dans laquell..."}, {"action": "rename_subtopic", "main_topic": "looking_for_service", "old_name": "looking_for_other_service", "new_name": "looking_for_sale_buy", "timestamp": "2025-06-02T21:28:31.954646"}, {"timestamp": "2025-06-02T21:29:10.346944", "action": "move_task", "task_id": "decembre-2024_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:29:23.620130", "action": "move_to_buffer", "task_id": "decembre-2024_c10_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:29:25.186762", "action": "move_to_buffer", "task_id": "aout-2024_c4_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:29:26.764740", "action": "move_to_buffer", "task_id": "fevrier-2025_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire. T..."}, {"timestamp": "2025-06-02T21:29:28.155270", "action": "move_to_buffer", "task_id": "septembre-2024_c5_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:29:37.413726", "action": "move_task", "task_id": "fevrier-2025_c29_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_services", "target_main_topic": "recommendation", "target_subtopic": "recommendation_marketplace", "task_content": "Les enseignants de votre quartier projettent d’org..."}, {"timestamp": "2025-06-02T21:29:44.303687", "action": "move_task", "task_id": "fevrier-2025_c29_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_marketplace", "target_main_topic": "recommendation", "target_subtopic": "recommendation_career", "task_content": "Les enseignants de votre quartier projettent d’org..."}, {"timestamp": "2025-06-02T21:29:49.836593", "action": "move_to_buffer", "task_id": "juillet-2024_c26_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_sport", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:29:59.224532", "action": "move_to_buffer", "task_id": "decembre-2024_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_sport", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:30:11.759847", "action": "move_to_buffer", "task_id": "janvier-2025_c3_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:16.155894", "action": "move_to_buffer", "task_id": "janvier-2025_c15_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:18.954743", "action": "move_to_buffer", "task_id": "fevrier-2025_c11_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:30:24.522956", "action": "move_to_buffer", "task_id": "avril-2025_c1_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "<PERSON><PERSON><PERSON>, Je viens bientôt en vacances dans ta régi..."}, {"timestamp": "2025-06-02T21:30:27.872451", "action": "move_to_buffer", "task_id": "novembre-2024_c7_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:29.134814", "action": "move_to_buffer", "task_id": "mars-2025_c12_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:30:49.426167", "action": "move_from_buffer", "task_id": "novembre-2024_c7_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:30:56.380027", "action": "move_from_buffer", "task_id": "mars-2025_c12_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:31:07.296350", "action": "move_from_buffer", "task_id": "avril-2025_c1_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "<PERSON><PERSON><PERSON>, Je viens bientôt en vacances dans ta régi..."}, {"timestamp": "2025-06-02T21:31:14.471523", "action": "move_from_buffer", "task_id": "fevrier-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville e..."}, {"timestamp": "2025-06-02T21:31:18.241059", "action": "move_from_buffer", "task_id": "janvier-2025_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:31:22.715693", "action": "move_from_buffer", "task_id": "janvier-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre ami <PERSON> vient de d<PERSON>emménager dans votre vi..."}, {"timestamp": "2025-06-02T21:31:27.950916", "action": "move_from_buffer", "task_id": "decembre-2024_c3_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:31:35.834571", "action": "move_from_buffer", "task_id": "juillet-2024_c26_t1", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "votre ami(e) veut découvrir la région dans laquell..."}, {"timestamp": "2025-06-02T21:31:37.722871", "action": "move_from_buffer", "task_id": "septembre-2024_c5_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:31:46.342995", "action": "move_from_buffer", "task_id": "fevrier-2025_c3_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_gift", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire. T..."}, {"timestamp": "2025-06-02T21:31:58.297247", "action": "move_from_buffer", "task_id": "decembre-2024_c10_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "Je cherche un vélo en bon état et bon marché. Cont..."}, {"timestamp": "2025-06-02T21:31:59.741583", "action": "move_from_buffer", "task_id": "aout-2024_c4_t1", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_sale_buy", "task_content": "« Je cherche un vélo en bon état et bon marché. Co..."}, {"timestamp": "2025-06-02T21:32:06.263578", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:32:07.265409", "action": "move_to_buffer", "task_id": "octobre-2024_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:32:08.292174", "action": "move_to_buffer", "task_id": "fevrier-2025_c25_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:32:09.146520", "action": "move_to_buffer", "task_id": "fevrier-2025_c27_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:32:10.179625", "action": "move_to_buffer", "task_id": "juillet-2024_c31_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_services", "new_name": "recommendation_talk", "timestamp": "2025-06-02T21:33:11.204757"}, {"timestamp": "2025-06-02T21:33:36.547322", "action": "move_from_buffer", "task_id": "juillet-2024_c31_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_travel", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"timestamp": "2025-06-02T21:33:42.816628", "action": "move_task", "task_id": "juillet-2024_c31_t1", "source_main_topic": "recommendation", "source_subtopic": "recommendation_travel", "target_main_topic": "recommendation", "target_subtopic": "recommendation_talk", "task_content": "La bibliothèque de votre ville organise une rencon..."}, {"action": "rename_subtopic", "main_topic": "recommendation", "old_name": "recommendation_talk", "new_name": "recommendation_library_event", "timestamp": "2025-06-02T21:34:12.148255"}, {"timestamp": "2025-06-02T21:34:20.551085", "action": "move_from_buffer", "task_id": "fevrier-2025_c27_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour encourager à i..."}, {"timestamp": "2025-06-02T21:34:33.080928", "action": "move_from_buffer", "task_id": "fevrier-2025_c25_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_library_event", "task_content": "Rédigez un message à un ami pour l’inviter à une r..."}, {"timestamp": "2025-06-02T21:34:59.423657", "action": "move_from_buffer", "task_id": "octobre-2024_c12_t1", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "écrivez un message à votre ami pour l’inviter à pr..."}, {"timestamp": "2025-06-02T21:35:39.893412", "action": "move_to_buffer", "task_id": "fevrier-2025_c33_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:35:48.591906", "action": "move_to_buffer", "task_id": "mars-2025_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:35:49.964216", "action": "move_to_buffer", "task_id": "mars-2025_c14_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_event_experience", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T21:36:07.987854", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T21:36:09.549786", "action": "move_from_buffer", "task_id": "fevrier-2025_c33_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:36:11.487089", "action": "move_from_buffer", "task_id": "mars-2025_c4_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T21:36:13.871179", "action": "move_from_buffer", "task_id": "mars-2025_c14_t1", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:02:34.398490", "action": "delete_subtopic", "main_topic_name": "recommendation", "subtopic_name": "recommendation_marketplace", "deleted_tasks_count": 0, "deleted_tasks": []}, {"timestamp": "2025-06-02T22:02:54.205954", "action": "delete_subtopic", "main_topic_name": "recommendation", "subtopic_name": "recommendation_travel", "deleted_tasks_count": 0, "deleted_tasks": []}, {"timestamp": "2025-06-02T22:04:03.183639", "action": "move_to_buffer", "task_id": "mars-2025_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_general_info", "task_content": "<PERSON><PERSON><PERSON><PERSON> une réponse à un courriel d’un ami demanda..."}, {"timestamp": "2025-06-02T22:04:18.221623", "action": "move_to_buffer", "task_id": "fevrier-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:04:20.761253", "action": "move_to_buffer", "task_id": "fevrier-2025_c15_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:04:26.462186", "action": "move_to_buffer", "task_id": "fevrier-2025_c31_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:04:33.551053", "action": "move_to_buffer", "task_id": "avril-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous pratiquez un sport dans un club et vous venez..."}, {"timestamp": "2025-06-02T22:04:39.396064", "action": "move_to_buffer", "task_id": "mars-2025_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:04:40.709460", "action": "move_to_buffer", "task_id": "octobre-2024_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:04:43.986712", "action": "move_to_buffer", "task_id": "octobre-2024_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "écrivez un message à votre ami pour lui raconter v..."}, {"timestamp": "2025-06-02T22:04:47.135587", "action": "move_to_buffer", "task_id": "septembre-2024_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:04:48.404056", "action": "move_to_buffer", "task_id": "septembre-2024_c9_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_information_general", "task_content": "Ecrivez un message à vos amis pour les inviter à v..."}, {"timestamp": "2025-06-02T22:04:59.461962", "action": "move_to_buffer", "task_id": "avril-2025_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_personal_news", "task_content": "Rédigez un courriel à vos amis pour les convier à ..."}, {"timestamp": "2025-06-02T22:05:01.893592", "action": "move_to_buffer", "task_id": "Mai-2025_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_personal_news", "task_content": "Vous habitez dans une nouvelle ville depuis un moi..."}, {"action": "rename_subtopic", "main_topic": "sharing_information", "old_name": "sharing_event_experience", "new_name": "sharing_job_interview", "timestamp": "2025-06-02T22:05:32.345430"}, {"action": "rename_subtopic", "main_topic": "sharing_information", "old_name": "sharing_information_general", "new_name": "sharing_sport_experience", "timestamp": "2025-06-02T22:06:56.652483"}, {"timestamp": "2025-06-02T22:07:04.113818", "action": "move_from_buffer", "task_id": "octobre-2024_c3_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:07:05.677357", "action": "move_from_buffer", "task_id": "mars-2025_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"timestamp": "2025-06-02T22:07:08.262574", "action": "move_from_buffer", "task_id": "septembre-2024_c7_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous faites du sport dans un club. Vous venez de r..."}, {"timestamp": "2025-06-02T22:07:09.621649", "action": "move_from_buffer", "task_id": "avril-2025_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "Vous pratiquez un sport dans un club et vous venez..."}, {"timestamp": "2025-06-02T22:08:00.305460", "action": "move_from_buffer", "task_id": "septembre-2024_c9_t1", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Ecrivez un message à vos amis pour les inviter à v..."}, {"timestamp": "2025-06-02T22:08:17.313020", "action": "move_from_buffer", "task_id": "fevrier-2025_c15_t1", "target_main_topic": "description_places", "target_subtopic": "description_camping", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:08:26.902867", "action": "move_from_buffer", "task_id": "fevrier-2025_c6_t1", "target_main_topic": "description_places", "target_subtopic": "description_camping", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle ..."}, {"timestamp": "2025-06-02T22:08:36.765831", "action": "move_from_buffer", "task_id": "mars-2025_c3_t1", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON><PERSON><PERSON><PERSON> une réponse à un courriel d’un ami demanda..."}, {"timestamp": "2025-06-02T22:08:50.151584", "action": "move_from_buffer", "task_id": "avril-2025_c11_t1", "target_main_topic": "description_places", "target_subtopic": "description_party", "task_content": "Rédigez un courriel à vos amis pour les convier à ..."}, {"timestamp": "2025-06-02T22:08:57.267138", "action": "move_to_buffer", "task_id": "novembre-2024_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T22:08:59.299145", "action": "move_to_buffer", "task_id": "fevrier-2025_c33_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:00.315063", "action": "move_to_buffer", "task_id": "mars-2025_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:01.349190", "action": "move_to_buffer", "task_id": "mars-2025_c14_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:09:14.675253", "action": "move_from_buffer", "task_id": "fevrier-2025_c33_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:16.310356", "action": "move_from_buffer", "task_id": "mars-2025_c4_t1", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "Écrire un message à un(e) ami(e) pour raconter son..."}, {"timestamp": "2025-06-02T22:09:29.875813", "action": "move_from_buffer", "task_id": "mars-2025_c14_t1", "target_main_topic": "description_places", "target_subtopic": "description_transportation", "task_content": "Votre amie Anna prévoit de passer un week-end dans..."}, {"timestamp": "2025-06-02T22:09:48.480161", "action": "move_from_buffer", "task_id": "Mai-2025_c2_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_general_info", "task_content": "Vous habitez dans une nouvelle ville depuis un moi..."}, {"timestamp": "2025-06-02T22:10:01.726805", "action": "move_from_buffer", "task_id": "octobre-2024_c6_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "écrivez un message à votre ami pour lui raconter v..."}, {"timestamp": "2025-06-02T22:10:22.281323", "action": "move_from_buffer", "task_id": "novembre-2024_c16_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévision prépare un reportage sur le spor..."}, {"timestamp": "2025-06-02T22:10:25.107448", "action": "move_from_buffer", "task_id": "fevrier-2025_c31_t1", "target_main_topic": "sharing_information", "target_subtopic": "sharing_sport_experience", "task_content": "France Télévisions réalise un reportage sur le spo..."}, {"action": "restore_missing_task", "task_id": "juillet-2024_c10_t1", "original_location": "manual_review > manual_review_general", "restored_to": "manual_review > manual_review_general", "timestamp": "2025-06-02T22:18:42.263684", "reason": "Task was missing from current checkpoint"}, {"action": "restore_missing_task", "task_id": "decembre-2024_c18_t1", "original_location": "manual_review > manual_review_general", "restored_to": "manual_review > manual_review_general", "timestamp": "2025-06-02T22:18:42.263694", "reason": "Task was missing from current checkpoint"}, {"timestamp": "2025-06-02T22:28:47.289895", "action": "move_task", "task_id": "juillet-2024_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Un évènement sportif aura lieu dans votre ville bi..."}, {"timestamp": "2025-06-02T22:28:52.320998", "action": "move_task", "task_id": "decembre-2024_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "Écrivez un message à votre ami pour le convaincre ..."}, {"timestamp": "2025-06-02T22:29:41.218148", "action": "move_task", "task_id": "juillet-2024_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:29:54.733805", "action": "move_task", "task_id": "juillet-2024_c3_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_sport", "task_content": "« Salut ! Je sais que tu fais du sport depuis le m..."}, {"timestamp": "2025-06-02T22:29:59.762934", "action": "move_task", "task_id": "juillet-2024_c13_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_film_event", "timestamp": "2025-06-02T22:30:30.567827"}, {"timestamp": "2025-06-02T22:30:37.805162", "action": "move_task", "task_id": "juillet-2024_c35_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:30:43.511623", "action": "move_task", "task_id": "aout-2024_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:30:45.128362", "action": "move_task", "task_id": "janvier-2025_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:30:51.806934", "action": "move_task", "task_id": "fevrier-2025_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:30:54.044587", "action": "move_task", "task_id": "fevrier-2025_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Tu aimerais assister à un festival de cinéma organ..."}, {"timestamp": "2025-06-02T22:30:56.606560", "action": "move_task", "task_id": "fevrier-2025_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:30:59.530211", "action": "move_task", "task_id": "fevrier-2025_c9_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon ..."}, {"timestamp": "2025-06-02T22:31:01.602727", "action": "move_task", "task_id": "fevrier-2025_c12_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-02T22:31:04.995858", "action": "move_task", "task_id": "fevrier-2025_c16_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:31:08.124716", "action": "move_task", "task_id": "fevrier-2025_c30_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:10.705591", "action": "move_task", "task_id": "avril-2025_c5_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:13.047203", "action": "move_task", "task_id": "avril-2025_c10_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T22:31:15.337463", "action": "move_task", "task_id": "avril-2025_c16_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Votre ami vous demande des informations sur votre ..."}, {"timestamp": "2025-06-02T22:31:25.142798", "action": "move_task", "task_id": "avril-2025_c17_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédigez un email pour inviter un ami à passer une ..."}, {"timestamp": "2025-06-02T22:31:27.674249", "action": "move_task", "task_id": "mars-2025_c2_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message à un ami intéressé par des cour..."}, {"timestamp": "2025-06-02T22:31:30.685627", "action": "move_task", "task_id": "mars-2025_c7_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:31:44.215931", "action": "move_task", "task_id": "mars-2025_c13_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "description_places", "target_subtopic": "description_university", "task_content": "Vous avez reçu un message d’un ami Alex qui vous d..."}, {"timestamp": "2025-06-02T22:31:50.601828", "action": "move_task", "task_id": "octobre-2024_c4_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "vous avez mis en ligne une annonce pour la locatio..."}, {"timestamp": "2025-06-02T22:31:52.949862", "action": "move_task", "task_id": "septembre-2024_c1_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"action": "create_subtopic", "main_topic": "recommendation", "subtopic_name": "recommendation_concert", "timestamp": "2025-06-02T22:32:14.772151"}, {"timestamp": "2025-06-02T22:32:26.956100", "action": "move_task", "task_id": "septembre-2024_c6_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_study_info", "target_main_topic": "recommendation", "target_subtopic": "recommendation_concert", "task_content": "Vous souhaitez aller au concert de votre artiste p..."}, {"timestamp": "2025-06-02T22:32:47.324483", "action": "move_task", "task_id": "fevrier-2025_c5_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:32:50.514940", "action": "move_task", "task_id": "fevrier-2025_c17_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:32:52.802472", "action": "move_task", "task_id": "fevrier-2025_c18_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:32:55.000754", "action": "move_task", "task_id": "fevrier-2025_c21_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:32:56.498800", "action": "move_task", "task_id": "mars-2025_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_work_news", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez..."}, {"timestamp": "2025-06-02T22:33:17.178546", "action": "move_task", "task_id": "juillet-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "recommendation", "target_subtopic": "recommendation_film_event", "task_content": "Vous souhaitez assister à un festival de cinéma da..."}, {"timestamp": "2025-06-02T22:33:36.112101", "action": "move_task", "task_id": "aout-2024_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:33:52.662595", "action": "move_task", "task_id": "fevrier-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:33:56.471334", "action": "move_task", "task_id": "fevrier-2025_c7_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:34:07.824046", "action": "move_task", "task_id": "janvier-2025_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:34:21.293904", "action": "move_task", "task_id": "fevrier-2025_c9_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_hotel", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon ..."}, {"timestamp": "2025-06-02T22:34:32.448835", "action": "move_task", "task_id": "fevrier-2025_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_shopping_place", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans to..."}, {"timestamp": "2025-06-02T22:34:39.710017", "action": "move_task", "task_id": "avril-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:34:46.006680", "action": "move_task", "task_id": "avril-2025_c10_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_restaurant", "task_content": "Envoyez un courriel à vos amis pour les inviter à ..."}, {"timestamp": "2025-06-02T22:34:55.224918", "action": "move_task", "task_id": "avril-2025_c16_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Votre ami vous demande des informations sur votre ..."}, {"timestamp": "2025-06-02T22:35:52.041193", "action": "move_task", "task_id": "fevrier-2025_c18_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:35:57.082271", "action": "move_task", "task_id": "fevrier-2025_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "sharing_information", "target_subtopic": "sharing_job_interview", "task_content": "Envoyez un message à votre ami(e) pour lui partage..."}, {"timestamp": "2025-06-02T22:36:33.471167", "action": "move_task", "task_id": "mars-2025_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez..."}, {"timestamp": "2025-06-02T22:36:42.590721", "action": "move_task", "task_id": "septembre-2024_c1_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "Vous avez publié une annonce pour la location de v..."}, {"timestamp": "2025-06-02T22:36:44.471536", "action": "move_task", "task_id": "octobre-2024_c4_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_roommate", "task_content": "vous avez mis en ligne une annonce pour la locatio..."}, {"timestamp": "2025-06-02T22:36:55.222861", "action": "move_task", "task_id": "mars-2025_c2_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "looking_for_service", "target_subtopic": "looking_for_language_help", "task_content": "Rédiger un message à un ami intéressé par des cour..."}, {"timestamp": "2025-06-02T22:37:12.403987", "action": "move_task", "task_id": "juillet-2024_c13_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Je suis votre amie Anna et je compte passer un wee..."}, {"timestamp": "2025-06-02T22:37:29.242014", "action": "move_task", "task_id": "fevrier-2025_c12_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_wedding_place", "task_content": "Vous avez invité votre ami Cédric à votre mariage ..."}, {"timestamp": "2025-06-02T22:37:45.853246", "action": "move_task", "task_id": "fevrier-2025_c30_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:37:48.766592", "action": "move_task", "task_id": "mars-2025_c7_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_gym", "task_content": "Rédiger un message en réponse à un ami qui demande..."}, {"timestamp": "2025-06-02T22:37:53.452811", "action": "move_task", "task_id": "fevrier-2025_c5_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:37:54.939649", "action": "move_task", "task_id": "fevrier-2025_c21_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_office", "task_content": "<PERSON> t’a envoyé un message pour savoir comment se p..."}, {"timestamp": "2025-06-02T22:38:16.445956", "action": "move_task", "task_id": "juillet-2024_c36_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "L’anniversaire de votre ami approche à grands pas ..."}, {"timestamp": "2025-06-02T22:38:18.360789", "action": "move_task", "task_id": "avril-2025_c14_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous venez d’emménager dans une nouvelle ville. Ré..."}, {"timestamp": "2025-06-02T22:38:19.966312", "action": "move_task", "task_id": "novembre-2024_c11_t1", "source_main_topic": "sharing_information", "source_subtopic": "sharing_travel_experience", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Vous avez récemment déména<PERSON> dans une nouvelle vil..."}, {"timestamp": "2025-06-02T22:38:42.338524", "action": "move_task", "task_id": "juillet-2024_c36_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "descripiton_trip", "task_content": "L’anniversaire de votre ami approche à grands pas ..."}, {"timestamp": "2025-06-02T22:38:49.862988", "action": "move_task", "task_id": "avril-2025_c14_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Vous venez d’emménager dans une nouvelle ville. Ré..."}, {"timestamp": "2025-06-02T22:38:51.337409", "action": "move_task", "task_id": "novembre-2024_c11_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Vous avez récemment déména<PERSON> dans une nouvelle vil..."}, {"timestamp": "2025-06-02T22:39:01.829857", "action": "move_task", "task_id": "avril-2025_c17_t1", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "description_places", "target_subtopic": "description_city", "task_content": "Rédigez un email pour inviter un ami à passer une ..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "cultural_blog", "new_name": "general_event_blog", "timestamp": "2025-06-04T23:46:43.606683"}, {"timestamp": "2025-06-04T23:46:58.298108", "action": "move_to_buffer", "task_id": "septembre-2024_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez assisté à un événement intitulé « Une se..."}, {"timestamp": "2025-06-04T23:47:11.466368", "action": "move_to_buffer", "task_id": "avril-2025_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Partagez votre expérience lors de l’événement « Un..."}, {"timestamp": "2025-06-04T23:47:16.983599", "action": "move_to_buffer", "task_id": "fevrier-2025_c26_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Partagez votre expérience lors de l’événement “Une..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "without_car_blog", "timestamp": "2025-06-04T23:47:38.148607"}, {"timestamp": "2025-06-04T23:47:48.531485", "action": "move_from_buffer", "task_id": "septembre-2024_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "without_car_blog", "task_content": "Vous avez assisté à un événement intitulé « Une se..."}, {"timestamp": "2025-06-04T23:47:53.035263", "action": "move_from_buffer", "task_id": "avril-2025_c9_t2", "target_main_topic": "blog_experiences", "target_subtopic": "without_car_blog", "task_content": "Partagez votre expérience lors de l’événement « Un..."}, {"timestamp": "2025-06-04T23:47:55.478351", "action": "move_from_buffer", "task_id": "fevrier-2025_c26_t2", "target_main_topic": "blog_experiences", "target_subtopic": "without_car_blog", "task_content": "Partagez votre expérience lors de l’événement “Une..."}, {"timestamp": "2025-06-04T23:48:31.751348", "action": "move_to_buffer", "task_id": "octobre-2024_c15_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez assisté à un événement sportif. Racontez..."}, {"timestamp": "2025-06-04T23:48:33.316386", "action": "move_to_buffer", "task_id": "octobre-2024_c19_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez assisté à une fête traditionnelle, dans ..."}, {"timestamp": "2025-06-04T23:48:39.326070", "action": "move_to_buffer", "task_id": "fevrier-2025_c25_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Rédigez un article de blog relatant votre expérien..."}, {"timestamp": "2025-06-04T23:48:40.734773", "action": "move_to_buffer", "task_id": "mars-2025_c3_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Rédiger un témoignage sur votre participation à l’..."}, {"timestamp": "2025-06-04T23:48:47.100149", "action": "move_to_buffer", "task_id": "juillet-2024_c31_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez participé à un événement nommé “La semai..."}, {"timestamp": "2025-06-04T23:49:01.255465", "action": "move_from_buffer", "task_id": "mars-2025_c3_t2", "target_main_topic": "blog_experiences", "target_subtopic": "without_car_blog", "task_content": "Rédiger un témoignage sur votre participation à l’..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "food_event_blog", "timestamp": "2025-06-04T23:49:10.174836"}, {"timestamp": "2025-06-04T23:49:20.914687", "action": "move_from_buffer", "task_id": "fevrier-2025_c25_t2", "target_main_topic": "blog_experiences", "target_subtopic": "food_event_blog", "task_content": "Rédigez un article de blog relatant votre expérien..."}, {"timestamp": "2025-06-04T23:49:23.436244", "action": "move_from_buffer", "task_id": "juillet-2024_c31_t2", "target_main_topic": "blog_experiences", "target_subtopic": "food_event_blog", "task_content": "Vous avez participé à un événement nommé “La semai..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "festival_blog", "timestamp": "2025-06-04T23:49:44.387630"}, {"timestamp": "2025-06-04T23:49:50.406031", "action": "move_from_buffer", "task_id": "octobre-2024_c19_t2", "target_main_topic": "blog_experiences", "target_subtopic": "festival_blog", "task_content": "Vous avez assisté à une fête traditionnelle, dans ..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "sport_blog", "timestamp": "2025-06-04T23:50:03.959245"}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "food_event_blog", "new_name": "food_blog", "timestamp": "2025-06-04T23:50:14.215899"}, {"timestamp": "2025-06-04T23:50:21.895615", "action": "move_from_buffer", "task_id": "octobre-2024_c15_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous avez assisté à un événement sportif. Racontez..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "career_blog", "timestamp": "2025-06-05T00:16:39.193612"}, {"timestamp": "2025-06-05T00:16:45.034688", "action": "move_to_buffer", "task_id": "septembre-2024_c6_t2", "source_main_topic": "blog_experiences", "source_subtopic": "education_blog", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T00:16:51.237775", "action": "move_from_buffer", "task_id": "septembre-2024_c6_t2", "target_main_topic": "blog_experiences", "target_subtopic": "career_blog", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "education_blog", "new_name": "environment_blog", "timestamp": "2025-06-05T00:17:08.857836"}, {"timestamp": "2025-06-05T00:17:25.039288", "action": "move_to_buffer", "task_id": "novembre-2024_c8_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Dans votre blog, Racontez votre expérience de l’ap..."}, {"timestamp": "2025-06-05T00:17:27.238404", "action": "move_to_buffer", "task_id": "fevrier-2025_c12_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Sur votre blog, partagez votre expérience d’appren..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "language_blog", "timestamp": "2025-06-05T00:17:37.176824"}, {"timestamp": "2025-06-05T00:17:45.602074", "action": "move_from_buffer", "task_id": "novembre-2024_c8_t2", "target_main_topic": "blog_experiences", "target_subtopic": "language_blog", "task_content": "Dans votre blog, Racontez votre expérience de l’ap..."}, {"timestamp": "2025-06-05T00:17:47.235966", "action": "move_from_buffer", "task_id": "fevrier-2025_c12_t2", "target_main_topic": "blog_experiences", "target_subtopic": "language_blog", "task_content": "Sur votre blog, partagez votre expérience d’appren..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "entertainment_blog", "new_name": "mustic_school_blog", "timestamp": "2025-06-05T00:18:20.030478"}, {"timestamp": "2025-06-05T00:18:26.981448", "action": "move_to_buffer", "task_id": "fevrier-2025_c20_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Vous avez récemment débuté vos études à l’universi..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "study_aboard_blog", "timestamp": "2025-06-05T00:19:27.941323"}, {"timestamp": "2025-06-05T00:19:34.077272", "action": "move_from_buffer", "task_id": "fevrier-2025_c20_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Vous avez récemment débuté vos études à l’universi..."}, {"timestamp": "2025-06-05T00:19:50.582751", "action": "move_to_buffer", "task_id": "mars-2025_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Rédigez un message à vos amis pour partager votre ..."}, {"timestamp": "2025-06-05T00:19:52.659188", "action": "move_task", "task_id": "juillet-2024_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "target_main_topic": "complaints_claims", "target_subtopic": "travel_complaints", "task_content": "Vous avez déjà étudié dans une université à l’étra..."}, {"timestamp": "2025-06-05T00:20:03.338708", "action": "move_to_buffer", "task_id": "juillet-2024_c2_t2", "source_main_topic": "complaints_claims", "source_subtopic": "travel_complaints", "task_content": "Vous avez déjà étudié dans une université à l’étra..."}, {"timestamp": "2025-06-05T00:20:20.669585", "action": "move_from_buffer", "task_id": "juillet-2024_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Vous avez déjà étudié dans une université à l’étra..."}, {"timestamp": "2025-06-05T00:20:25.676265", "action": "move_from_buffer", "task_id": "mars-2025_c9_t2", "target_main_topic": "blog_experiences", "target_subtopic": "mustic_school_blog", "task_content": "Rédigez un message à vos amis pour partager votre ..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "competiton_blog", "timestamp": "2025-06-05T00:24:27.175809"}, {"timestamp": "2025-06-05T00:24:31.885675", "action": "move_to_buffer", "task_id": "octobre-2024_c1_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous avez participé à un concours pour gagner un s..."}, {"timestamp": "2025-06-05T00:24:35.577808", "action": "move_to_buffer", "task_id": "octobre-2024_c8_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous avez participé à un concours de cuisine, vous..."}, {"timestamp": "2025-06-05T00:24:39.654628", "action": "move_from_buffer", "task_id": "octobre-2024_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Vous avez participé à un concours pour gagner un s..."}, {"timestamp": "2025-06-05T00:24:41.011721", "action": "move_from_buffer", "task_id": "octobre-2024_c8_t2", "target_main_topic": "blog_experiences", "target_subtopic": "career_blog", "task_content": "Vous avez participé à un concours de cuisine, vous..."}, {"timestamp": "2025-06-05T00:24:44.934527", "action": "move_to_buffer", "task_id": "octobre-2024_c8_t2", "source_main_topic": "blog_experiences", "source_subtopic": "career_blog", "task_content": "Vous avez participé à un concours de cuisine, vous..."}, {"timestamp": "2025-06-05T00:24:49.083762", "action": "move_from_buffer", "task_id": "octobre-2024_c8_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Vous avez participé à un concours de cuisine, vous..."}, {"timestamp": "2025-06-05T00:25:04.567394", "action": "move_to_buffer", "task_id": "fevrier-2025_c13_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Rédigez un article de blog dans lequel vous racont..."}, {"timestamp": "2025-06-05T00:25:06.501416", "action": "move_to_buffer", "task_id": "novembre-2024_c10_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous avez participé à un cours de sport dans une s..."}, {"timestamp": "2025-06-05T00:25:08.058771", "action": "move_to_buffer", "task_id": "fevrier-2025_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Tu viens de commencer une nouvelle activité de loi..."}, {"timestamp": "2025-06-05T00:25:12.954333", "action": "move_from_buffer", "task_id": "novembre-2024_c10_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous avez participé à un cours de sport dans une s..."}, {"timestamp": "2025-06-05T00:25:14.764816", "action": "move_from_buffer", "task_id": "fevrier-2025_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Tu viens de commencer une nouvelle activité de loi..."}, {"timestamp": "2025-06-05T00:25:16.993172", "action": "move_from_buffer", "task_id": "fevrier-2025_c13_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Rédigez un article de blog dans lequel vous racont..."}, {"timestamp": "2025-06-05T00:25:21.988198", "action": "move_to_buffer", "task_id": "fevrier-2025_c23_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous avez récemment débuté une nouvelle activité d..."}, {"timestamp": "2025-06-05T00:25:32.283392", "action": "move_to_buffer", "task_id": "juillet-2024_c16_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous venez de commencer une nouvelle activité de l..."}, {"timestamp": "2025-06-05T00:25:35.383930", "action": "move_to_buffer", "task_id": "avril-2025_c4_t2", "source_main_topic": "blog_experiences", "source_subtopic": "hobby_blog", "task_content": "Vous avez été sélectionné(e) pour un concours où l..."}, {"timestamp": "2025-06-05T00:26:02.973496", "action": "move_from_buffer", "task_id": "fevrier-2025_c23_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous avez récemment débuté une nouvelle activité d..."}, {"timestamp": "2025-06-05T00:26:04.729712", "action": "move_from_buffer", "task_id": "juillet-2024_c16_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous venez de commencer une nouvelle activité de l..."}, {"timestamp": "2025-06-05T00:26:06.239447", "action": "move_from_buffer", "task_id": "avril-2025_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous avez été sélectionné(e) pour un concours où l..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "hobby_blog", "new_name": "flea_market_blog", "timestamp": "2025-06-05T00:26:24.632162"}, {"timestamp": "2025-06-05T00:26:29.320931", "action": "move_to_buffer", "task_id": "fevrier-2025_c24_t2", "source_main_topic": "blog_experiences", "source_subtopic": "flea_market_blog", "task_content": "Rédigez un article de blog sur votre artiste préfé..."}, {"timestamp": "2025-06-05T00:26:39.263899", "action": "move_to_buffer", "task_id": "juillet-2024_c10_t2", "source_main_topic": "blog_experiences", "source_subtopic": "flea_market_blog", "task_content": "Vous avez passé un cours de cuisine. Rédigez un ar..."}, {"timestamp": "2025-06-05T00:26:50.444764", "action": "move_from_buffer", "task_id": "fevrier-2025_c24_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Rédigez un article de blog sur votre artiste préfé..."}, {"timestamp": "2025-06-05T00:27:00.195039", "action": "move_from_buffer", "task_id": "juillet-2024_c10_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Vous avez passé un cours de cuisine. Rédigez un ar..."}, {"timestamp": "2025-06-05T00:27:32.986703", "action": "move_to_buffer", "task_id": "juillet-2024_c10_t2", "source_main_topic": "blog_experiences", "source_subtopic": "competiton_blog", "task_content": "Vous avez passé un cours de cuisine. Rédigez un ar..."}, {"timestamp": "2025-06-05T00:27:36.332412", "action": "move_from_buffer", "task_id": "juillet-2024_c10_t2", "target_main_topic": "blog_experiences", "target_subtopic": "food_blog", "task_content": "Vous avez passé un cours de cuisine. Rédigez un ar..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "lifestyle_blog", "new_name": "lifestyle_change_blog", "timestamp": "2025-06-05T00:28:21.651408"}, {"timestamp": "2025-06-05T00:28:34.636300", "action": "move_to_buffer", "task_id": "juillet-2024_c30_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Participez à notre concours pour gagner un séjour ..."}, {"timestamp": "2025-06-05T00:28:45.423678", "action": "move_from_buffer", "task_id": "juillet-2024_c30_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Participez à notre concours pour gagner un séjour ..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "mustic_school_blog", "new_name": "concert_blog", "timestamp": "2025-06-05T00:29:01.364531"}, {"timestamp": "2025-06-05T00:29:14.491179", "action": "move_to_buffer", "task_id": "septembre-2024_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Répondez en commentaire à une publication sur Face..."}, {"timestamp": "2025-06-05T00:29:22.658201", "action": "move_to_buffer", "task_id": "octobre-2024_c12_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez participé à un échange scolaire dans une..."}, {"timestamp": "2025-06-05T00:29:31.064112", "action": "move_from_buffer", "task_id": "octobre-2024_c12_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Vous avez participé à un échange scolaire dans une..."}, {"timestamp": "2025-06-05T00:29:32.657454", "action": "move_from_buffer", "task_id": "septembre-2024_c9_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Répondez en commentaire à une publication sur Face..."}, {"timestamp": "2025-06-05T00:30:24.814991", "action": "move_to_buffer", "task_id": "novembre-2024_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Exprimez votre admiration pour une personnalité, c..."}, {"timestamp": "2025-06-05T00:30:41.478210", "action": "move_to_buffer", "task_id": "novembre-2024_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Écrivez dans un article de blog pour raconter votr..."}, {"timestamp": "2025-06-05T00:30:51.582437", "action": "move_from_buffer", "task_id": "novembre-2024_c9_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Écrivez dans un article de blog pour raconter votr..."}, {"timestamp": "2025-06-05T00:31:17.514339", "action": "move_to_buffer", "task_id": "fevrier-2025_c11_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédigez un article de blog pour exprimer votre adm..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "admiration_opinions", "timestamp": "2025-06-05T00:31:45.919569"}, {"timestamp": "2025-06-05T00:31:52.147465", "action": "move_from_buffer", "task_id": "novembre-2024_c7_t2", "target_main_topic": "forum_opinions", "target_subtopic": "admiration_opinions", "task_content": "Exprimez votre admiration pour une personnalité, c..."}, {"timestamp": "2025-06-05T00:31:53.592708", "action": "move_from_buffer", "task_id": "fevrier-2025_c11_t2", "target_main_topic": "forum_opinions", "target_subtopic": "admiration_opinions", "task_content": "Rédigez un article de blog pour exprimer votre adm..."}, {"timestamp": "2025-06-05T00:33:41.199646", "action": "move_to_buffer", "task_id": "novembre-2024_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Écrivez dans un article de blog pour raconter votr..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "foreign_country_blog", "timestamp": "2025-06-05T00:33:59.853937"}, {"timestamp": "2025-06-05T00:34:15.237663", "action": "move_from_buffer", "task_id": "novembre-2024_c9_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Écrivez dans un article de blog pour raconter votr..."}, {"timestamp": "2025-06-05T00:34:32.918266", "action": "move_to_buffer", "task_id": "novembre-2024_c11_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Que pensez-vous de l’installation d’une télévision..."}, {"timestamp": "2025-06-05T00:34:35.878346", "action": "move_to_buffer", "task_id": "novembre-2024_c17_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez visité une exposition de votre artiste p..."}, {"timestamp": "2025-06-05T00:34:40.051791", "action": "move_to_buffer", "task_id": "Mai-2025_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez participé à une fête traditionnelle dans..."}, {"timestamp": "2025-06-05T00:34:46.223439", "action": "move_to_buffer", "task_id": "Mai-2025_c4_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Participez à la discussion sur le site “voyage.int..."}, {"timestamp": "2025-06-05T00:34:49.064694", "action": "move_to_buffer", "task_id": "fevrier-2025_c4_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Tu as accueilli un(e) étudiant(e) étranger(e) chez..."}, {"timestamp": "2025-06-05T00:35:07.546851", "action": "move_from_buffer", "task_id": "Mai-2025_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "festival_blog", "task_content": "Vous avez participé à une fête traditionnelle dans..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "artist_blog", "timestamp": "2025-06-05T00:35:19.249234"}, {"timestamp": "2025-06-05T00:35:25.305754", "action": "move_from_buffer", "task_id": "novembre-2024_c17_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "Vous avez visité une exposition de votre artiste p..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "host_foreign_student_blog", "timestamp": "2025-06-05T00:36:10.746878"}, {"timestamp": "2025-06-05T00:36:15.005364", "action": "move_from_buffer", "task_id": "fevrier-2025_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "host_foreign_student_blog", "task_content": "Tu as accueilli un(e) étudiant(e) étranger(e) chez..."}, {"timestamp": "2025-06-05T00:36:26.338827", "action": "move_from_buffer", "task_id": "novembre-2024_c11_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Que pensez-vous de l’installation d’une télévision..."}, {"timestamp": "2025-06-05T00:36:28.744954", "action": "move_from_buffer", "task_id": "Mai-2025_c4_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Participez à la discussion sur le site “voyage.int..."}, {"timestamp": "2025-06-05T00:40:36.436030", "action": "move_to_buffer", "task_id": "fevrier-2025_c14_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédigez un article de blog dans lequel vous racont..."}, {"timestamp": "2025-06-05T00:40:39.944831", "action": "move_to_buffer", "task_id": "fevrier-2025_c27_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Racontez votre séjour académique à l’étranger à tr..."}, {"timestamp": "2025-06-05T00:40:46.144964", "action": "move_to_buffer", "task_id": "fevrier-2025_c32_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédiger un article sur la visite d’une exposition ..."}, {"timestamp": "2025-06-05T00:40:47.817194", "action": "move_to_buffer", "task_id": "mars-2025_c10_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédigez un article sur votre blog personnel pour p..."}, {"timestamp": "2025-06-05T00:40:51.224972", "action": "move_to_buffer", "task_id": "juillet-2024_c1_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez accueilli un(e) étudiant(e) étranger(e) ..."}, {"timestamp": "2025-06-05T00:40:59.588361", "action": "move_to_buffer", "task_id": "juillet-2024_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez participé à une action pour la « Journée..."}, {"timestamp": "2025-06-05T00:41:14.191179", "action": "move_from_buffer", "task_id": "juillet-2024_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "environment_blog", "task_content": "Vous avez participé à une action pour la « Journée..."}, {"timestamp": "2025-06-05T00:41:21.782952", "action": "move_to_buffer", "task_id": "decembre-2024_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "« école De Musique ! Cours Gratuits, Concerts, Jeu..."}, {"timestamp": "2025-06-05T00:41:39.336258", "action": "move_from_buffer", "task_id": "fevrier-2025_c32_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "Rédiger un article sur la visite d’une exposition ..."}, {"timestamp": "2025-06-05T00:41:49.923286", "action": "move_from_buffer", "task_id": "mars-2025_c10_t2", "target_main_topic": "blog_experiences", "target_subtopic": "flea_market_blog", "task_content": "Rédigez un article sur votre blog personnel pour p..."}, {"timestamp": "2025-06-05T00:41:59.304814", "action": "move_from_buffer", "task_id": "juillet-2024_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "host_foreign_student_blog", "task_content": "Vous avez accueilli un(e) étudiant(e) étranger(e) ..."}, {"timestamp": "2025-06-05T00:42:11.629795", "action": "move_from_buffer", "task_id": "fevrier-2025_c27_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Racontez votre séjour académique à l’étranger à tr..."}, {"timestamp": "2025-06-05T00:42:15.892011", "action": "move_from_buffer", "task_id": "fevrier-2025_c14_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Rédigez un article de blog dans lequel vous racont..."}, {"timestamp": "2025-06-05T00:42:28.873980", "action": "move_from_buffer", "task_id": "decembre-2024_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "concert_blog", "task_content": "« école De Musique ! Cours Gratuits, Concerts, Jeu..."}, {"timestamp": "2025-06-05T00:42:39.932756", "action": "move_to_buffer", "task_id": "avril-2025_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Vous avez participé à un programme dans une école ..."}, {"timestamp": "2025-06-05T00:42:42.167772", "action": "move_to_buffer", "task_id": "avril-2025_c15_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Partagez dans un blog votre expérience d’apprentis..."}, {"timestamp": "2025-06-05T00:42:43.495026", "action": "move_to_buffer", "task_id": "juillet-2024_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "environment_blog", "task_content": "Vous avez participé à une action pour la « Journée..."}, {"timestamp": "2025-06-05T00:42:49.841707", "action": "move_from_buffer", "task_id": "juillet-2024_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "environment_blog", "task_content": "Vous avez participé à une action pour la « Journée..."}, {"timestamp": "2025-06-05T00:43:01.266374", "action": "move_from_buffer", "task_id": "avril-2025_c15_t2", "target_main_topic": "blog_experiences", "target_subtopic": "language_blog", "task_content": "Partagez dans un blog votre expérience d’apprentis..."}, {"timestamp": "2025-06-05T00:43:03.255017", "action": "move_from_buffer", "task_id": "avril-2025_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "language_blog", "task_content": "Vous avez participé à un programme dans une école ..."}, {"timestamp": "2025-06-05T00:43:19.540214", "action": "move_to_buffer", "task_id": "fevrier-2025_c30_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédiger un message décrivant une expérience avec l..."}, {"timestamp": "2025-06-05T00:43:23.856280", "action": "move_to_buffer", "task_id": "juillet-2024_c11_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous aimez une personne célèbre ou non par ses act..."}, {"timestamp": "2025-06-05T00:43:28.613414", "action": "move_to_buffer", "task_id": "juillet-2024_c36_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Un internaute a publié le message suivant : « Je v..."}, {"timestamp": "2025-06-05T00:43:43.259778", "action": "move_to_buffer", "task_id": "decembre-2024_c18_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Écrivez un article de blog sur votre expérience d’..."}, {"timestamp": "2025-06-05T00:43:46.604629", "action": "move_to_buffer", "task_id": "avril-2025_c6_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Vous avez déjà vécu en colocation avec des amis. L..."}, {"timestamp": "2025-06-05T00:43:52.165390", "action": "move_to_buffer", "task_id": "avril-2025_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Une personne a publié sur Facebook un message au s..."}, {"timestamp": "2025-06-05T00:43:54.519441", "action": "move_to_buffer", "task_id": "avril-2025_c8_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Un internaute exprime des inquiétudes concernant s..."}, {"timestamp": "2025-06-05T00:43:58.712975", "action": "move_to_buffer", "task_id": "avril-2025_c14_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T00:44:01.559554", "action": "move_to_buffer", "task_id": "avril-2025_c19_t2", "source_main_topic": "blog_experiences", "source_subtopic": "travel_blog", "task_content": "Rédigez un article destiné à un blog dans lequel v..."}, {"timestamp": "2025-06-05T00:44:16.255682", "action": "move_from_buffer", "task_id": "decembre-2024_c18_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Écrivez un article de blog sur votre expérience d’..."}, {"timestamp": "2025-06-05T00:44:33.026193", "action": "move_from_buffer", "task_id": "juillet-2024_c36_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Un internaute a publié le message suivant : « Je v..."}, {"timestamp": "2025-06-05T00:44:43.497058", "action": "move_from_buffer", "task_id": "avril-2025_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Une personne a publié sur Facebook un message au s..."}, {"timestamp": "2025-06-05T00:44:49.937984", "action": "move_from_buffer", "task_id": "avril-2025_c19_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Rédigez un article destiné à un blog dans lequel v..."}, {"timestamp": "2025-06-05T00:45:04.849990", "action": "move_from_buffer", "task_id": "avril-2025_c14_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T00:45:17.183391", "action": "move_to_buffer", "task_id": "avril-2025_c14_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T00:45:20.681340", "action": "move_from_buffer", "task_id": "avril-2025_c8_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Un internaute exprime des inquiétudes concernant s..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "install_tv_bedroom_opinions", "timestamp": "2025-06-05T00:45:46.296719"}, {"timestamp": "2025-06-05T00:45:57.688780", "action": "move_from_buffer", "task_id": "avril-2025_c14_t2", "target_main_topic": "forum_opinions", "target_subtopic": "install_tv_bedroom_opinions", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T00:46:05.705071", "action": "move_from_buffer", "task_id": "juillet-2024_c11_t2", "target_main_topic": "forum_opinions", "target_subtopic": "admiration_opinions", "task_content": "Vous aimez une personne célèbre ou non par ses act..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "online_trainning_opinions", "timestamp": "2025-06-05T00:46:25.193150"}, {"timestamp": "2025-06-05T00:46:35.602686", "action": "move_from_buffer", "task_id": "fevrier-2025_c30_t2", "target_main_topic": "forum_opinions", "target_subtopic": "install_tv_bedroom_opinions", "task_content": "Rédiger un message décrivant une expérience avec l..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "colocation_opinions", "timestamp": "2025-06-05T00:47:13.925621"}, {"timestamp": "2025-06-05T00:47:20.483713", "action": "move_from_buffer", "task_id": "avril-2025_c6_t2", "target_main_topic": "forum_opinions", "target_subtopic": "colocation_opinions", "task_content": "Vous avez déjà vécu en colocation avec des amis. L..."}, {"timestamp": "2025-06-05T00:51:15.355587", "action": "move_to_buffer", "task_id": "septembre-2024_c6_t2", "source_main_topic": "blog_experiences", "source_subtopic": "career_blog", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T00:51:19.276771", "action": "move_to_buffer", "task_id": "mars-2025_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "social_blog", "task_content": "Rédiger un article de blog partageant votre expéri..."}, {"timestamp": "2025-06-05T00:51:23.641197", "action": "move_from_buffer", "task_id": "septembre-2024_c6_t2", "target_main_topic": "blog_experiences", "target_subtopic": "work_blog", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T00:51:25.310088", "action": "move_from_buffer", "task_id": "mars-2025_c2_t2", "target_main_topic": "blog_experiences", "target_subtopic": "work_blog", "task_content": "Rédiger un article de blog partageant votre expéri..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "work_blog", "new_name": "work_related_blog", "timestamp": "2025-06-05T00:51:28.747534"}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "travel_complaint_opinions", "timestamp": "2025-06-05T00:52:18.775008"}, {"timestamp": "2025-06-05T00:52:29.614508", "action": "move_to_buffer", "task_id": "fevrier-2025_c22_t2", "source_main_topic": "complaints_claims", "source_subtopic": "travel_complaints", "task_content": "Rédigez une lettre de réclamation pour exprimer vo..."}, {"timestamp": "2025-06-05T00:52:32.267806", "action": "move_to_buffer", "task_id": "octobre-2024_c13_t2", "source_main_topic": "complaints_claims", "source_subtopic": "travel_complaints", "task_content": "Envoyez une réclamation par courrier à une agence ..."}, {"timestamp": "2025-06-05T00:52:35.057725", "action": "move_to_buffer", "task_id": "juillet-2024_c29_t2", "source_main_topic": "complaints_claims", "source_subtopic": "travel_complaints", "task_content": "Vous faites une réclamation par rapport aux mauvai..."}, {"timestamp": "2025-06-05T00:52:52.831935", "action": "move_from_buffer", "task_id": "fevrier-2025_c22_t2", "target_main_topic": "forum_opinions", "target_subtopic": "travel_complaint_opinions", "task_content": "Rédigez une lettre de réclamation pour exprimer vo..."}, {"timestamp": "2025-06-05T00:52:55.025634", "action": "move_from_buffer", "task_id": "juillet-2024_c29_t2", "target_main_topic": "forum_opinions", "target_subtopic": "travel_complaint_opinions", "task_content": "Vous faites une réclamation par rapport aux mauvai..."}, {"timestamp": "2025-06-05T00:52:57.889329", "action": "move_from_buffer", "task_id": "octobre-2024_c13_t2", "target_main_topic": "forum_opinions", "target_subtopic": "travel_complaint_opinions", "task_content": "Envoyez une réclamation par courrier à une agence ..."}, {"timestamp": "2025-06-05T00:53:30.993657", "action": "delete_main_topic", "topic_name": "complaints_claims", "deleted_tasks_count": 0}, {"timestamp": "2025-06-05T00:53:52.093484", "action": "delete_subtopic", "main_topic_name": "blog_experiences", "subtopic_name": "career_blog", "deleted_tasks_count": 0}, {"timestamp": "2025-06-05T00:54:02.505088", "action": "delete_subtopic", "main_topic_name": "blog_experiences", "subtopic_name": "social_blog", "deleted_tasks_count": 0}, {"timestamp": "2025-06-05T00:54:36.629923", "action": "move_to_buffer", "task_id": "avril-2025_c13_t2", "source_main_topic": "personal_sharing", "source_subtopic": "family_sharing", "task_content": "Vous avez récemment assisté à une fête de famille...."}, {"timestamp": "2025-06-05T00:55:07.629770", "action": "move_to_buffer", "task_id": "juillet-2024_c21_t2", "source_main_topic": "providing_recommendations", "source_subtopic": "travel_recommendations", "task_content": "Deux amis souhaitent passer deux semaines de congé..."}, {"timestamp": "2025-06-05T00:55:37.688730", "action": "move_from_buffer", "task_id": "juillet-2024_c21_t2", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Deux amis souhaitent passer deux semaines de congé..."}, {"timestamp": "2025-06-05T00:56:34.735798", "action": "move_from_buffer", "task_id": "avril-2025_c13_t2", "target_main_topic": "blog_experiences", "target_subtopic": "general_event_blog", "task_content": "Vous avez récemment assisté à une fête de famille...."}, {"timestamp": "2025-06-05T00:57:24.735047", "action": "delete_main_topic", "topic_name": "providing_recommendations", "deleted_tasks_count": 0}, {"timestamp": "2025-06-05T00:57:49.313148", "action": "move_task", "task_id": "septembre-2024_c7_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "target_main_topic": "forum_opinions", "target_subtopic": "colocation_opinions", "task_content": "<< Le site «colocation.com» recherche des témoigne..."}, {"timestamp": "2025-06-05T00:57:59.788420", "action": "move_to_buffer", "task_id": "novembre-2024_c15_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez lu sur un forum un débat concernant les ..."}, {"timestamp": "2025-06-05T00:58:07.818348", "action": "move_to_buffer", "task_id": "fevrier-2025_c6_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez récemment visité un nouveau pays pendant..."}, {"timestamp": "2025-06-05T00:58:24.458315", "action": "move_to_buffer", "task_id": "juillet-2024_c22_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez visité un nouveau pays pendant vos vacan..."}, {"timestamp": "2025-06-05T00:58:56.834322", "action": "move_from_buffer", "task_id": "novembre-2024_c15_t2", "target_main_topic": "forum_opinions", "target_subtopic": "online_trainning_opinions", "task_content": "Vous avez lu sur un forum un débat concernant les ..."}, {"timestamp": "2025-06-05T00:59:42.596814", "action": "move_from_buffer", "task_id": "fevrier-2025_c6_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Vous avez récemment visité un nouveau pays pendant..."}, {"timestamp": "2025-06-05T00:59:44.567523", "action": "move_from_buffer", "task_id": "juillet-2024_c22_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Vous avez visité un nouveau pays pendant vos vacan..."}, {"timestamp": "2025-06-05T01:00:03.134673", "action": "move_to_buffer", "task_id": "fevrier-2025_c30_t2", "source_main_topic": "forum_opinions", "source_subtopic": "install_tv_bedroom_opinions", "task_content": "Rédiger un message décrivant une expérience avec l..."}, {"timestamp": "2025-06-05T01:00:06.521759", "action": "move_from_buffer", "task_id": "fevrier-2025_c30_t2", "target_main_topic": "forum_opinions", "target_subtopic": "online_trainning_opinions", "task_content": "Rédiger un message décrivant une expérience avec l..."}, {"timestamp": "2025-06-05T01:00:45.606950", "action": "move_to_buffer", "task_id": "novembre-2024_c12_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Vous avez lu ce message sur un site internet : Je ..."}, {"timestamp": "2025-06-05T01:01:05.649198", "action": "move_to_buffer", "task_id": "fevrier-2025_c18_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "« Jeu concours : » « Remportez deux billets pour l..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "study_aborad_opinions", "timestamp": "2025-06-05T01:01:34.715380"}, {"timestamp": "2025-06-05T01:01:45.270723", "action": "move_from_buffer", "task_id": "novembre-2024_c12_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Vous avez lu ce message sur un site internet : Je ..."}, {"timestamp": "2025-06-05T01:02:01.020019", "action": "move_from_buffer", "task_id": "fevrier-2025_c18_t2", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "« Jeu concours : » « Remportez deux billets pour l..."}, {"timestamp": "2025-06-05T01:02:14.684172", "action": "move_to_buffer", "task_id": "septembre-2024_c9_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Répondez en commentaire à une publication sur Face..."}, {"timestamp": "2025-06-05T01:02:22.435746", "action": "move_to_buffer", "task_id": "juillet-2024_c36_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Un internaute a publié le message suivant : « Je v..."}, {"timestamp": "2025-06-05T01:02:25.732097", "action": "move_to_buffer", "task_id": "avril-2025_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Une personne a publié sur Facebook un message au s..."}, {"timestamp": "2025-06-05T01:02:32.135132", "action": "move_to_buffer", "task_id": "avril-2025_c8_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "Un internaute exprime des inquiétudes concernant s..."}, {"timestamp": "2025-06-05T01:02:34.255092", "action": "move_from_buffer", "task_id": "juillet-2024_c36_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Un internaute a publié le message suivant : « Je v..."}, {"timestamp": "2025-06-05T01:02:37.165694", "action": "move_from_buffer", "task_id": "avril-2025_c7_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Une personne a publié sur Facebook un message au s..."}, {"timestamp": "2025-06-05T01:02:41.302253", "action": "move_from_buffer", "task_id": "avril-2025_c8_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Un internaute exprime des inquiétudes concernant s..."}, {"timestamp": "2025-06-05T01:02:43.562949", "action": "move_from_buffer", "task_id": "septembre-2024_c9_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Répondez en commentaire à une publication sur Face..."}, {"timestamp": "2025-06-05T01:03:05.746658", "action": "move_to_buffer", "task_id": "janvier-2025_c16_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "“<PERSON><PERSON> internautes, J’ai 19 ans, je vais bi<PERSON><PERSON> p..."}, {"timestamp": "2025-06-05T01:03:19.026839", "action": "move_to_buffer", "task_id": "avril-2025_c1_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> avez pris des cours dans notre salle..."}, {"timestamp": "2025-06-05T01:03:24.427096", "action": "move_to_buffer", "task_id": "janvier-2025_c18_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez participé à une compétition sportive, ra..."}, {"timestamp": "2025-06-05T01:03:26.498006", "action": "move_to_buffer", "task_id": "avril-2025_c12_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez participé à une compétition sportive et ..."}, {"timestamp": "2025-06-05T01:03:31.788513", "action": "move_to_buffer", "task_id": "novembre-2024_c11_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Que pensez-vous de l’installation d’une télévision..."}, {"timestamp": "2025-06-05T01:03:46.900247", "action": "move_to_buffer", "task_id": "avril-2025_c14_t2", "source_main_topic": "forum_opinions", "source_subtopic": "install_tv_bedroom_opinions", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T01:03:52.979116", "action": "move_from_buffer", "task_id": "novembre-2024_c11_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Que pensez-vous de l’installation d’une télévision..."}, {"timestamp": "2025-06-05T01:03:59.231845", "action": "move_from_buffer", "task_id": "avril-2025_c14_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Dans un article de blog, exprimez votre opinion su..."}, {"timestamp": "2025-06-05T01:04:27.795684", "action": "move_from_buffer", "task_id": "janvier-2025_c18_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Vous avez participé à une compétition sportive, ra..."}, {"timestamp": "2025-06-05T01:04:31.537359", "action": "move_from_buffer", "task_id": "avril-2025_c12_t2", "target_main_topic": "blog_experiences", "target_subtopic": "competiton_blog", "task_content": "Vous avez participé à une compétition sportive et ..."}, {"timestamp": "2025-06-05T01:04:39.822759", "action": "move_from_buffer", "task_id": "janvier-2025_c16_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "“<PERSON><PERSON> internautes, J’ai 19 ans, je vais bi<PERSON><PERSON> p..."}, {"timestamp": "2025-06-05T01:04:50.230683", "action": "move_from_buffer", "task_id": "avril-2025_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> avez pris des cours dans notre salle..."}, {"action": "rename_subtopic", "main_topic": "forum_opinions", "old_name": "lifestyle_opinions", "new_name": "living_with_elderly_opinions", "timestamp": "2025-06-05T01:06:42.500981"}, {"timestamp": "2025-06-05T01:07:07.840764", "action": "move_task", "task_id": "novembre-2024_c19_t2", "source_main_topic": "blog_experiences", "source_subtopic": "work_related_blog", "target_main_topic": "forum_opinions", "target_subtopic": "living_with_elderly_opinions", "task_content": "Vous travaillez dans une association qui aidents l..."}, {"timestamp": "2025-06-05T01:07:25.745344", "action": "move_task", "task_id": "mars-2025_c2_t2", "source_main_topic": "blog_experiences", "source_subtopic": "work_related_blog", "target_main_topic": "forum_opinions", "target_subtopic": "living_with_elderly_opinions", "task_content": "Rédiger un article de blog partageant votre expéri..."}, {"timestamp": "2025-06-05T01:07:32.383193", "action": "move_to_buffer", "task_id": "janvier-2025_c12_t2", "source_main_topic": "forum_opinions", "source_subtopic": "living_with_elderly_opinions", "task_content": "“Sal<PERSON>, tu vas bien ? Nous avons deux semaines de ..."}, {"timestamp": "2025-06-05T01:07:55.825953", "action": "move_from_buffer", "task_id": "janvier-2025_c12_t2", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "“Sal<PERSON>, tu vas bien ? Nous avons deux semaines de ..."}, {"timestamp": "2025-06-05T01:09:51.296592", "action": "move_to_buffer", "task_id": "fevrier-2025_c10_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "COURRIER DES LECTEURS Abandonner tout pour partir ..."}, {"timestamp": "2025-06-05T01:10:08.160650", "action": "move_to_buffer", "task_id": "mars-2025_c11_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Une étudiante de 19 ans souhaite partir à l’étrang..."}, {"timestamp": "2025-06-05T01:10:10.029935", "action": "move_to_buffer", "task_id": "janvier-2025_c4_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Une étudiante qui à 19 ans veut aller à l’étranger..."}, {"timestamp": "2025-06-05T01:10:22.588460", "action": "move_to_buffer", "task_id": "janvier-2025_c13_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Vous avez participé à une émission de télévision. ..."}, {"timestamp": "2025-06-05T01:10:30.693363", "action": "move_to_buffer", "task_id": "decembre-2024_c14_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Vous avez visité une ville que vous ne connaissiez..."}, {"timestamp": "2025-06-05T01:10:39.545316", "action": "move_to_buffer", "task_id": "avril-2025_c17_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-06-05T01:10:57.102028", "action": "move_from_buffer", "task_id": "fevrier-2025_c10_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "COURRIER DES LECTEURS Abandonner tout pour partir ..."}, {"timestamp": "2025-06-05T01:11:05.000618", "action": "move_from_buffer", "task_id": "mars-2025_c11_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Une étudiante de 19 ans souhaite partir à l’étrang..."}, {"timestamp": "2025-06-05T01:11:06.916484", "action": "move_from_buffer", "task_id": "janvier-2025_c4_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "Une étudiante qui à 19 ans veut aller à l’étranger..."}, {"timestamp": "2025-06-05T01:11:29.898604", "action": "move_from_buffer", "task_id": "decembre-2024_c14_t2", "target_main_topic": "blog_experiences", "target_subtopic": "foreign_country_blog", "task_content": "Vous avez visité une ville que vous ne connaissiez..."}, {"timestamp": "2025-06-05T01:11:43.098795", "action": "move_from_buffer", "task_id": "avril-2025_c17_t2", "target_main_topic": "blog_experiences", "target_subtopic": "work_related_blog", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-06-05T01:12:18.345545", "action": "move_from_buffer", "task_id": "janvier-2025_c13_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Vous avez participé à une émission de télévision. ..."}, {"timestamp": "2025-06-05T01:12:30.223672", "action": "move_to_buffer", "task_id": "mars-2025_c8_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Rédigez un message sur un forum pour partager votr..."}, {"action": "rename_subtopic", "main_topic": "forum_opinions", "old_name": "living_with_elderly_opinions", "new_name": "living_with_elderly/children_opinions", "timestamp": "2025-06-05T01:12:47.423211"}, {"timestamp": "2025-06-05T01:12:50.458990", "action": "move_to_buffer", "task_id": "novembre-2024_c2_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Vous faites partie d’une association de quartier q..."}, {"timestamp": "2025-06-05T01:12:51.908227", "action": "move_to_buffer", "task_id": "fevrier-2025_c1_t2", "source_main_topic": "forum_opinions", "source_subtopic": "travel_opinions", "task_content": "Tu fais partie d’une association de quartier qui o..."}, {"timestamp": "2025-06-05T01:12:56.420777", "action": "move_from_buffer", "task_id": "novembre-2024_c2_t2", "target_main_topic": "forum_opinions", "target_subtopic": "living_with_elderly/children_opinions", "task_content": "Vous faites partie d’une association de quartier q..."}, {"timestamp": "2025-06-05T01:12:58.147447", "action": "move_from_buffer", "task_id": "fevrier-2025_c1_t2", "target_main_topic": "forum_opinions", "target_subtopic": "living_with_elderly/children_opinions", "task_content": "Tu fais partie d’une association de quartier qui o..."}, {"timestamp": "2025-06-05T01:13:11.605974", "action": "move_from_buffer", "task_id": "mars-2025_c8_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Rédigez un message sur un forum pour partager votr..."}, {"timestamp": "2025-06-05T01:27:40.631638", "action": "cleanup_empty_topics", "deleted_subtopics": ["forum_opinions > travel_opinions", "forum_opinions > food_opinions", "forum_opinions > install_tv_bedroom_opinions", "personal_sharing > host_experience", "personal_sharing > family_sharing", "personal_sharing > activity_sharing", "personal_sharing > admiration_sharing", "manual_review > manual_review_general"], "deleted_main_topics": ["personal_sharing", "manual_review"], "total_deleted": 10}, {"timestamp": "2025-06-05T01:28:16.612243", "action": "move_task", "task_id": "aout-2024_c4_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "business_correspondence", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Vous avez passé une journée à la campagne avec vos..."}, {"timestamp": "2025-06-05T01:28:31.726720", "action": "move_task", "task_id": "mars-2025_c8_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "target_main_topic": "formal_correspondence", "target_subtopic": "travel_correspondence", "task_content": "Rédigez un message sur un forum pour partager votr..."}, {"timestamp": "2025-06-05T01:28:33.424076", "action": "move_task", "task_id": "aout-2024_c4_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "target_main_topic": "formal_correspondence", "target_subtopic": "travel_correspondence", "task_content": "Vous avez passé une journée à la campagne avec vos..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "leave_city_opinions", "timestamp": "2025-06-05T01:29:33.814507"}, {"timestamp": "2025-06-05T01:29:44.102786", "action": "move_task", "task_id": "novembre-2024_c13_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "target_main_topic": "formal_correspondence", "target_subtopic": "invitation_correspondence", "task_content": "Vous avez quitté la ville afin de vous installer à..."}, {"timestamp": "2025-06-05T01:29:50.091323", "action": "move_task", "task_id": "novembre-2024_c13_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "invitation_correspondence", "target_main_topic": "formal_correspondence", "target_subtopic": "travel_correspondence", "task_content": "Vous avez quitté la ville afin de vous installer à..."}, {"timestamp": "2025-06-05T01:30:05.428982", "action": "move_task", "task_id": "novembre-2024_c13_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "forum_opinions", "target_subtopic": "living_with_elderly/children_opinions", "task_content": "Vous avez quitté la ville afin de vous installer à..."}, {"timestamp": "2025-06-05T01:30:18.752869", "action": "move_to_buffer", "task_id": "novembre-2024_c13_t2", "source_main_topic": "forum_opinions", "source_subtopic": "living_with_elderly/children_opinions", "task_content": "Vous avez quitté la ville afin de vous installer à..."}, {"timestamp": "2025-06-05T01:30:29.506835", "action": "move_from_buffer", "task_id": "novembre-2024_c13_t2", "target_main_topic": "forum_opinions", "target_subtopic": "leave_city_opinions", "task_content": "Vous avez quitté la ville afin de vous installer à..."}, {"timestamp": "2025-06-05T01:30:38.807293", "action": "move_to_buffer", "task_id": "fevrier-2025_c28_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Après avoir quitté la ville, vous vous êtes instal..."}, {"timestamp": "2025-06-05T01:30:52.842977", "action": "move_to_buffer", "task_id": "avril-2025_c10_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Sur votre blog, expliquez les raisons qui vous ont..."}, {"timestamp": "2025-06-05T01:30:57.064825", "action": "move_from_buffer", "task_id": "fevrier-2025_c28_t2", "target_main_topic": "forum_opinions", "target_subtopic": "leave_city_opinions", "task_content": "Après avoir quitté la ville, vous vous êtes instal..."}, {"timestamp": "2025-06-05T01:30:58.894956", "action": "move_from_buffer", "task_id": "avril-2025_c10_t2", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Sur votre blog, expliquez les raisons qui vous ont..."}, {"timestamp": "2025-06-05T01:31:08.747711", "action": "move_to_buffer", "task_id": "mars-2025_c13_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "social_media_opinions", "timestamp": "2025-06-05T01:31:22.760822"}, {"timestamp": "2025-06-05T01:31:29.873337", "action": "move_from_buffer", "task_id": "mars-2025_c13_t2", "target_main_topic": "forum_opinions", "target_subtopic": "social_media_opinions", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau..."}, {"timestamp": "2025-06-05T01:31:34.164990", "action": "move_to_buffer", "task_id": "juillet-2024_c27_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Écrivez un article sur votre blog pour raconter po..."}, {"action": "create_subtopic", "main_topic": "forum_opinions", "subtopic_name": "change_diet_opinions", "timestamp": "2025-06-05T01:31:49.050708"}, {"timestamp": "2025-06-05T01:31:56.481468", "action": "move_from_buffer", "task_id": "juillet-2024_c27_t2", "target_main_topic": "forum_opinions", "target_subtopic": "change_diet_opinions", "task_content": "Écrivez un article sur votre blog pour raconter po..."}, {"timestamp": "2025-06-05T01:32:29.626205", "action": "move_to_buffer", "task_id": "mars-2025_c1_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "task_content": "Rédiger un message racontant un événement marquant..."}, {"timestamp": "2025-06-05T01:32:34.813836", "action": "move_task", "task_id": "decembre-2024_c12_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "lifestyle_change_blog", "task_content": "Vous avez passé des vacances au Canada par le biai..."}, {"timestamp": "2025-06-05T01:32:42.135589", "action": "move_to_buffer", "task_id": "decembre-2024_c12_t2", "source_main_topic": "blog_experiences", "source_subtopic": "lifestyle_change_blog", "task_content": "Vous avez passé des vacances au Canada par le biai..."}, {"timestamp": "2025-06-05T01:32:48.945492", "action": "move_from_buffer", "task_id": "decembre-2024_c12_t2", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Vous avez passé des vacances au Canada par le biai..."}, {"timestamp": "2025-06-05T01:33:03.052676", "action": "move_from_buffer", "task_id": "mars-2025_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "general_event_blog", "task_content": "Rédiger un message racontant un événement marquant..."}, {"timestamp": "2025-06-05T01:33:12.769736", "action": "cleanup_empty_topics", "deleted_subtopics": ["blog_experiences > lifestyle_change_blog"], "deleted_main_topics": [], "total_deleted": 1}, {"timestamp": "2025-06-05T01:34:25.842912", "action": "move_to_buffer", "task_id": "septembre-2024_c1_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "work_correspondence", "task_content": "Vous venez de commencer les cours à l’université à..."}, {"timestamp": "2025-06-05T01:34:29.854810", "action": "move_to_buffer", "task_id": "septembre-2024_c4_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "work_correspondence", "task_content": "Vous avez été invité(e) à une fête en famille. Vou..."}, {"timestamp": "2025-06-05T01:34:35.916177", "action": "move_to_buffer", "task_id": "septembre-2024_c5_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "work_correspondence", "task_content": "Écrivez un message à vos amis pour leur partager v..."}, {"timestamp": "2025-06-05T01:34:45.756420", "action": "move_to_buffer", "task_id": "novembre-2024_c4_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "work_correspondence", "task_content": "écrivez un message à vos amis pour leur raconter à..."}, {"timestamp": "2025-06-05T01:34:51.167054", "action": "move_from_buffer", "task_id": "septembre-2024_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "general_event_blog", "task_content": "Vous avez été invité(e) à une fête en famille. Vou..."}, {"timestamp": "2025-06-05T01:34:55.132692", "action": "move_from_buffer", "task_id": "novembre-2024_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "general_event_blog", "task_content": "écrivez un message à vos amis pour leur raconter à..."}, {"timestamp": "2025-06-05T01:35:10.836636", "action": "move_from_buffer", "task_id": "septembre-2024_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "study_aboard_blog", "task_content": "Vous venez de commencer les cours à l’université à..."}, {"timestamp": "2025-06-05T01:35:25.388709", "action": "move_from_buffer", "task_id": "septembre-2024_c5_t2", "target_main_topic": "blog_experiences", "target_subtopic": "work_related_blog", "task_content": "Écrivez un message à vos amis pour leur partager v..."}, {"timestamp": "2025-06-05T01:35:54.937709", "action": "move_to_buffer", "task_id": "fevrier-2025_c17_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "information_correspondence", "task_content": "Vous avez publié une annonce en ligne pour louer v..."}, {"timestamp": "2025-06-05T01:36:21.132855", "action": "move_task", "task_id": "fevrier-2025_c29_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "invitation_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "concert_blog", "task_content": "Rédigez un message à votre ami(e) pour l’inviter à..."}, {"timestamp": "2025-06-05T01:36:25.691469", "action": "move_task", "task_id": "decembre-2024_c16_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "invitation_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "concert_blog", "task_content": "Écrivez un courriel à votre ami pour l’inviter à v..."}, {"timestamp": "2025-06-05T01:36:40.456328", "action": "move_task", "task_id": "aout-2024_c1_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "forum_opinions", "target_subtopic": "social_media_opinions", "task_content": "Vous avez décidé de ne plus utiliser les réseaux s..."}, {"timestamp": "2025-06-05T01:36:53.719946", "action": "move_task", "task_id": "octobre-2024_c6_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "“Gagnez deux billets pour la destination de votre ..."}, {"timestamp": "2025-06-05T01:37:03.334752", "action": "move_task", "task_id": "octobre-2024_c9_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "COURRIER DES LECTEURS Tout quitter pour partir en ..."}, {"timestamp": "2025-06-05T01:37:17.414175", "action": "move_task", "task_id": "octobre-2024_c11_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "work_related_blog", "task_content": "écrivez un message à vos amis pour leur parler de ..."}, {"timestamp": "2025-06-05T01:37:40.870208", "action": "move_task", "task_id": "novembre-2024_c1_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Vos amis veulent passer une congé de 2 semaines ch..."}, {"timestamp": "2025-06-05T01:37:44.275544", "action": "move_task", "task_id": "novembre-2024_c3_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Écrivez un message à un ami pour lui partager vos ..."}, {"timestamp": "2025-06-05T01:37:50.045795", "action": "move_task", "task_id": "novembre-2024_c16_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Vous avez passé des vacances dans une belle région..."}, {"timestamp": "2025-06-05T01:38:18.629001", "action": "move_task", "task_id": "janvier-2025_c14_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "travel_correspondence", "target_main_topic": "blog_experiences", "target_subtopic": "travel_blog", "task_content": "Nouveau message “<PERSON><PERSON>, tu vas bien ? J’ai 2 semai..."}, {"timestamp": "2025-06-05T01:38:55.943338", "action": "move_from_buffer", "task_id": "fevrier-2025_c17_t2", "target_main_topic": "formal_correspondence", "target_subtopic": "invitation_correspondence", "task_content": "Vous avez publié une annonce en ligne pour louer v..."}, {"timestamp": "2025-06-05T01:39:22.444532", "action": "move_task", "task_id": "fevrier-2025_c17_t2", "source_main_topic": "formal_correspondence", "source_subtopic": "invitation_correspondence", "target_main_topic": "formal_correspondence", "target_subtopic": "information_correspondence", "task_content": "Vous avez publié une annonce en ligne pour louer v..."}, {"action": "rename_subtopic", "main_topic": "formal_correspondence", "old_name": "travel_correspondence", "new_name": "eating_habits", "timestamp": "2025-06-05T01:39:59.665523"}, {"action": "rename_main_topic", "old_name": "formal_correspondence", "new_name": "sharing_informations", "timestamp": "2025-06-05T01:40:12.150163"}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "book_fair_blog", "timestamp": "2025-06-05T01:41:57.713194"}, {"timestamp": "2025-06-05T01:42:04.600188", "action": "move_task", "task_id": "octobre-2024_c7_t2", "source_main_topic": "sharing_informations", "source_subtopic": "work_correspondence", "target_main_topic": "sharing_informations", "target_subtopic": "information_correspondence", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:42:11.996956", "action": "move_task", "task_id": "octobre-2024_c7_t2", "source_main_topic": "sharing_informations", "source_subtopic": "information_correspondence", "target_main_topic": "sharing_informations", "target_subtopic": "eating_habits", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:42:23.024761", "action": "move_to_buffer", "task_id": "octobre-2024_c7_t2", "source_main_topic": "sharing_informations", "source_subtopic": "eating_habits", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:42:32.634932", "action": "move_from_buffer", "task_id": "octobre-2024_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:42:40.869505", "action": "move_to_buffer", "task_id": "octobre-2024_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "artist_blog", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:42:49.169253", "action": "move_from_buffer", "task_id": "octobre-2024_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "book_fair_blog", "task_content": "écrivez un message à vos amis pour leur raconter v..."}, {"timestamp": "2025-06-05T01:43:15.175065", "action": "move_task", "task_id": "Mai-2025_c3_t2", "source_main_topic": "sharing_informations", "source_subtopic": "work_correspondence", "target_main_topic": "forum_opinions", "target_subtopic": "general_opinions", "task_content": "Vous avez assisté à un concert incroyable ? Écrive..."}, {"timestamp": "2025-06-05T01:43:26.211277", "action": "move_to_buffer", "task_id": "Mai-2025_c3_t2", "source_main_topic": "forum_opinions", "source_subtopic": "general_opinions", "task_content": "Vous avez assisté à un concert incroyable ? Écrive..."}, {"timestamp": "2025-06-05T01:43:34.904498", "action": "move_from_buffer", "task_id": "Mai-2025_c3_t2", "target_main_topic": "blog_experiences", "target_subtopic": "concert_blog", "task_content": "Vous avez assisté à un concert incroyable ? Écrive..."}, {"timestamp": "2025-06-05T01:43:57.312001", "action": "move_to_buffer", "task_id": "octobre-2024_c10_t2", "source_main_topic": "sharing_informations", "source_subtopic": "work_correspondence", "task_content": "Vous venez d’avoir un nouveau travail. Envoyez un ..."}, {"timestamp": "2025-06-05T01:43:58.904625", "action": "move_to_buffer", "task_id": "juillet-2024_c24_t2", "source_main_topic": "sharing_informations", "source_subtopic": "work_correspondence", "task_content": "Vous êtes parti(e) travailler à l’étranger. Vous e..."}, {"timestamp": "2025-06-05T01:44:07.202054", "action": "move_to_buffer", "task_id": "janvier-2025_c17_t2", "source_main_topic": "sharing_informations", "source_subtopic": "work_correspondence", "task_content": "Vous avez assisté à une fête de famille. Envoyez u..."}, {"action": "rename_subtopic", "main_topic": "blog_experiences", "old_name": "work_related_blog", "new_name": "new_job_blog", "timestamp": "2025-06-05T01:44:21.307976"}, {"timestamp": "2025-06-05T01:44:26.557030", "action": "move_from_buffer", "task_id": "octobre-2024_c10_t2", "target_main_topic": "blog_experiences", "target_subtopic": "new_job_blog", "task_content": "Vous venez d’avoir un nouveau travail. Envoyez un ..."}, {"timestamp": "2025-06-05T01:44:29.725431", "action": "move_from_buffer", "task_id": "juillet-2024_c24_t2", "target_main_topic": "blog_experiences", "target_subtopic": "language_blog", "task_content": "Vous êtes parti(e) travailler à l’étranger. Vous e..."}, {"timestamp": "2025-06-05T01:44:35.122802", "action": "move_to_buffer", "task_id": "juillet-2024_c24_t2", "source_main_topic": "blog_experiences", "source_subtopic": "language_blog", "task_content": "Vous êtes parti(e) travailler à l’étranger. Vous e..."}, {"timestamp": "2025-06-05T01:44:42.724195", "action": "move_from_buffer", "task_id": "juillet-2024_c24_t2", "target_main_topic": "blog_experiences", "target_subtopic": "new_job_blog", "task_content": "Vous êtes parti(e) travailler à l’étranger. Vous e..."}, {"action": "create_subtopic", "main_topic": "blog_experiences", "subtopic_name": "family_party_blog", "timestamp": "2025-06-05T01:45:02.614515"}, {"timestamp": "2025-06-05T01:45:10.402035", "action": "move_from_buffer", "task_id": "janvier-2025_c17_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez assisté à une fête de famille. Envoyez u..."}, {"timestamp": "2025-06-05T01:45:35.680900", "action": "move_to_buffer", "task_id": "mars-2025_c14_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez récemment assisté à une fête de quartier..."}, {"timestamp": "2025-06-05T01:45:38.567573", "action": "move_to_buffer", "task_id": "juillet-2024_c13_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez assisté à une fête de voisins du quartie..."}, {"timestamp": "2025-06-05T01:45:39.948070", "action": "move_to_buffer", "task_id": "decembre-2024_c7_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez assisté à une fete entre voisins dans vo..."}, {"timestamp": "2025-06-05T01:45:41.827758", "action": "move_to_buffer", "task_id": "avril-2025_c13_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez récemment assisté à une fête de famille...."}, {"timestamp": "2025-06-05T01:45:47.334636", "action": "move_to_buffer", "task_id": "septembre-2024_c4_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "Vous avez été invité(e) à une fête en famille. Vou..."}, {"timestamp": "2025-06-05T01:45:50.689026", "action": "move_to_buffer", "task_id": "novembre-2024_c4_t2", "source_main_topic": "blog_experiences", "source_subtopic": "general_event_blog", "task_content": "écrivez un message à vos amis pour leur raconter à..."}, {"timestamp": "2025-06-05T01:46:03.132219", "action": "move_from_buffer", "task_id": "mars-2025_c14_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez récemment assisté à une fête de quartier..."}, {"timestamp": "2025-06-05T01:46:04.640480", "action": "move_from_buffer", "task_id": "juillet-2024_c13_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez assisté à une fête de voisins du quartie..."}, {"timestamp": "2025-06-05T01:46:06.175465", "action": "move_from_buffer", "task_id": "decembre-2024_c7_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez assisté à une fete entre voisins dans vo..."}, {"timestamp": "2025-06-05T01:46:07.385777", "action": "move_from_buffer", "task_id": "avril-2025_c13_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez récemment assisté à une fête de famille...."}, {"timestamp": "2025-06-05T01:46:08.543217", "action": "move_from_buffer", "task_id": "septembre-2024_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "Vous avez été invité(e) à une fête en famille. Vou..."}, {"timestamp": "2025-06-05T01:46:09.935492", "action": "move_from_buffer", "task_id": "novembre-2024_c4_t2", "target_main_topic": "blog_experiences", "target_subtopic": "family_party_blog", "task_content": "écrivez un message à vos amis pour leur raconter à..."}, {"timestamp": "2025-06-05T01:47:35.491046", "action": "move_to_buffer", "task_id": "avril-2025_c16_t2", "source_main_topic": "blog_experiences", "source_subtopic": "new_job_blog", "task_content": "Rédiger un article pour le journal de l’entreprise..."}, {"timestamp": "2025-06-05T01:47:46.398621", "action": "move_to_buffer", "task_id": "septembre-2024_c6_t2", "source_main_topic": "blog_experiences", "source_subtopic": "new_job_blog", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T01:47:51.415832", "action": "move_to_buffer", "task_id": "avril-2025_c17_t2", "source_main_topic": "blog_experiences", "source_subtopic": "new_job_blog", "task_content": "Après avoir participé à une journée de formation a..."}, {"action": "rename_subtopic", "main_topic": "sharing_informations", "old_name": "invitation_correspondence", "new_name": "countryside_trip", "timestamp": "2025-06-05T01:48:38.914279"}, {"timestamp": "2025-06-05T01:48:53.079009", "action": "move_from_buffer", "task_id": "avril-2025_c16_t2", "target_main_topic": "sharing_informations", "target_subtopic": "work_correspondence", "task_content": "Rédiger un article pour le journal de l’entreprise..."}, {"action": "rename_main_topic", "old_name": "sharing_informations", "new_name": "Others", "timestamp": "2025-06-05T01:49:06.803316"}, {"timestamp": "2025-06-05T01:49:20.485977", "action": "move_from_buffer", "task_id": "avril-2025_c17_t2", "target_main_topic": "Others", "target_subtopic": "information_correspondence", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-06-05T01:49:23.154130", "action": "move_from_buffer", "task_id": "septembre-2024_c6_t2", "target_main_topic": "Others", "target_subtopic": "information_correspondence", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T01:49:40.810778", "action": "move_task", "task_id": "mars-2025_c8_t2", "source_main_topic": "Others", "source_subtopic": "eating_habits", "target_main_topic": "Others", "target_subtopic": "countryside_trip", "task_content": "Rédigez un message sur un forum pour partager votr..."}, {"timestamp": "2025-06-05T01:49:46.892445", "action": "move_task", "task_id": "aout-2024_c4_t2", "source_main_topic": "Others", "source_subtopic": "eating_habits", "target_main_topic": "Others", "target_subtopic": "countryside_trip", "task_content": "Vous avez passé une journée à la campagne avec vos..."}, {"action": "create_subtopic", "main_topic": "Others", "subtopic_name": "picnic_with_colleagues", "timestamp": "2025-06-05T01:50:34.586207"}, {"action": "create_subtopic", "main_topic": "Others", "subtopic_name": "introduce_careers", "timestamp": "2025-06-05T01:51:04.700982"}, {"timestamp": "2025-06-05T01:51:14.897931", "action": "move_to_buffer", "task_id": "avril-2025_c16_t2", "source_main_topic": "Others", "source_subtopic": "work_correspondence", "task_content": "Rédiger un article pour le journal de l’entreprise..."}, {"timestamp": "2025-06-05T01:51:19.100963", "action": "move_from_buffer", "task_id": "avril-2025_c16_t2", "target_main_topic": "Others", "target_subtopic": "picnic_with_colleagues", "task_content": "Rédiger un article pour le journal de l’entreprise..."}, {"timestamp": "2025-06-05T01:51:22.144878", "action": "move_task", "task_id": "septembre-2024_c6_t2", "source_main_topic": "Others", "source_subtopic": "information_correspondence", "target_main_topic": "Others", "target_subtopic": "work_correspondence", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"timestamp": "2025-06-05T01:51:30.111823", "action": "move_task", "task_id": "septembre-2024_c6_t2", "source_main_topic": "Others", "source_subtopic": "work_correspondence", "target_main_topic": "Others", "target_subtopic": "introduce_careers", "task_content": "Les professeurs de l’école de votre quartier souha..."}, {"action": "create_subtopic", "main_topic": "Others", "subtopic_name": "trainning_day_post", "timestamp": "2025-06-05T01:51:53.737077"}, {"timestamp": "2025-06-05T01:51:58.061597", "action": "move_to_buffer", "task_id": "fevrier-2025_c17_t2", "source_main_topic": "Others", "source_subtopic": "information_correspondence", "task_content": "Vous avez publié une annonce en ligne pour louer v..."}, {"timestamp": "2025-06-05T01:51:59.560881", "action": "move_to_buffer", "task_id": "avril-2025_c17_t2", "source_main_topic": "Others", "source_subtopic": "information_correspondence", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-06-05T01:52:06.154224", "action": "move_from_buffer", "task_id": "avril-2025_c17_t2", "target_main_topic": "Others", "target_subtopic": "trainning_day_post", "task_content": "Après avoir participé à une journée de formation a..."}, {"action": "create_subtopic", "main_topic": "Others", "subtopic_name": "rent_apartment", "timestamp": "2025-06-05T01:52:24.227115"}, {"timestamp": "2025-06-05T01:52:26.706630", "action": "move_from_buffer", "task_id": "fevrier-2025_c17_t2", "target_main_topic": "Others", "target_subtopic": "rent_apartment", "task_content": "Vous avez publié une annonce en ligne pour louer v..."}, {"timestamp": "2025-06-05T01:54:17.492161", "action": "move_to_buffer", "task_id": "octobre-2024_c1_t2", "source_main_topic": "blog_experiences", "source_subtopic": "competiton_blog", "task_content": "Vous avez participé à un concours pour gagner un s..."}, {"timestamp": "2025-06-05T01:54:31.261805", "action": "move_to_buffer", "task_id": "fevrier-2025_c24_t2", "source_main_topic": "blog_experiences", "source_subtopic": "competiton_blog", "task_content": "Rédigez un article de blog sur votre artiste préfé..."}, {"timestamp": "2025-06-05T01:54:36.151146", "action": "move_to_buffer", "task_id": "juillet-2024_c30_t2", "source_main_topic": "blog_experiences", "source_subtopic": "competiton_blog", "task_content": "Participez à notre concours pour gagner un séjour ..."}, {"timestamp": "2025-06-05T01:55:02.668474", "action": "move_from_buffer", "task_id": "fevrier-2025_c24_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "Rédigez un article de blog sur votre artiste préfé..."}, {"timestamp": "2025-06-05T01:55:04.127315", "action": "move_from_buffer", "task_id": "octobre-2024_c1_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "Vous avez participé à un concours pour gagner un s..."}, {"timestamp": "2025-06-05T01:55:05.658057", "action": "move_from_buffer", "task_id": "juillet-2024_c30_t2", "target_main_topic": "blog_experiences", "target_subtopic": "artist_blog", "task_content": "Participez à notre concours pour gagner un séjour ..."}, {"timestamp": "2025-06-05T01:58:10.339808", "action": "move_to_buffer", "task_id": "janvier-2025_c16_t2", "source_main_topic": "blog_experiences", "source_subtopic": "study_aboard_blog", "task_content": "“<PERSON><PERSON> internautes, J’ai 19 ans, je vais bi<PERSON><PERSON> p..."}, {"timestamp": "2025-06-05T01:58:40.074181", "action": "move_from_buffer", "task_id": "janvier-2025_c16_t2", "target_main_topic": "forum_opinions", "target_subtopic": "study_aborad_opinions", "task_content": "“<PERSON><PERSON> internautes, J’ai 19 ans, je vais bi<PERSON><PERSON> p..."}, {"action": "merge_classifications", "added_duplicates": 1, "added_new_to_manual_review": 2, "timestamp": "2025-07-02T16:03:54.968745"}, {"timestamp": "2025-07-02T16:05:35.185909", "action": "move_task", "task_id": "juin-2024_c3_t2", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "forum_opinions", "target_subtopic": "social_media_opinions", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-07-02T16:05:39.539625", "action": "move_task", "task_id": "juin-2024_c3_t2", "source_main_topic": "forum_opinions", "source_subtopic": "social_media_opinions", "target_main_topic": "manual_review", "target_subtopic": "manual_review_general", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-07-02T16:05:43.103991", "action": "move_task", "task_id": "juin-2024_c2_t2", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "forum_opinions", "target_subtopic": "social_media_opinions", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau..."}, {"timestamp": "2025-07-02T16:06:02.084908", "action": "move_task", "task_id": "juin-2024_c3_t2", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "Others", "target_subtopic": "trainning_day_post", "task_content": "Après avoir participé à une journée de formation a..."}, {"timestamp": "2025-07-15T12:28:56.315176", "action": "move_task", "task_id": "juillet-2025_c2_t2", "source_main_topic": "manual_review", "source_subtopic": "manual_review_general", "target_main_topic": "blog_experiences", "target_subtopic": "sport_blog", "task_content": "Vous avez récemment suivi un cours de sport dans u..."}, {"action": "update_counts", "old_total_tasks": 203, "old_unique_tasks": 140, "new_total_tasks": 206, "new_unique_tasks": 101, "timestamp": "2025-07-18T19:41:05.287544"}, {"timestamp": "2025-07-18T20:02:22.250411", "action": "cleanup_empty_topics", "deleted_subtopics": ["Others > information_correspondence", "Others > work_correspondence", "manual_review > manual_review_general"], "deleted_main_topics": ["manual_review"], "total_deleted": 4}, {"action": "rename_main_topic", "old_name": "Others", "new_name": "Other", "timestamp": "2025-07-20T00:10:40.798823"}]}, "classification": {"task_number": 2, "method": "predefined_classification_with_deduplication_with_new_tasks_merged", "total_tasks": 206, "unique_tasks": 101, "n_main_topics": 4, "main_topics": {"blog_experiences": {"topic_id": 0, "keywords": ["blog", "article", "site web", "personnel", "écrivez un article", "rédigez un article", "racon<PERSON>z", "expérience", "souvenirs"], "total_tasks": 127, "unique_tasks": 88, "subtopics": {"travel_blog": {"subtopic_id": 0, "task_count": 13, "unique_task_count": 12, "task_ids": [], "keywords": ["voyage", "pays", "<PERSON><PERSON><PERSON>", "vacances", "visité", "destination"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c10_t2"], "representative_id": "septembre-2024_c10_t2", "task_content": "Ecrivez un article de blog sur votre souvenir de voyage que vous avez aimé le plus.", "month_years": ["septembre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ecrivez un article de blog sur votre souvenir de voyage que vous avez aimé le plus"}, {"task_ids": ["fevrier-2025_c8_t2"], "representative_id": "fevrier-2025_c8_t2", "task_content": "<PERSON> et <PERSON>la t’ont envoyé un message pour te dire qu’ils viendront visiter ton pays pendant leurs vacances en janvier. Ils cherchent à découvrir des sites historiques et des plats typiques. Tu leur réponds en leur faisant des suggestions, en justifiant ton choix par ton propre vécu et en partageant des endroits et des expériences de tes dernières vacances. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "aaron et perla t’ont envoyé un message pour te dire qu’ils viendront visiter ton pays pendant leurs vacances en janvier. ils cherchent à découvrir des sites historiques et des plats typiques. tu leur réponds en leur faisant des suggestions, en justifiant ton choix par ton propre vécu et en partageant des endroits et des expériences de tes dernières vacances"}, {"task_ids": ["fevrier-2025_c31_t2", "mars-2025_c6_t2"], "representative_id": "fevrier-2025_c31_t2", "task_content": "Rédiger un message à des amis pour raconter un séjour dans une belle région du pays, en décrivant l’expérience vécue et en expliquant les raisons de son appréciation. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["31", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédiger un message à des amis pour raconter un séjour dans une belle région du pays, en décrivant l’expérience vécue et en expliquant les raisons de son appréciation"}, {"task_ids": ["juillet-2024_c21_t2"], "representative_id": "juillet-2024_c21_t2", "task_content": "Deux amis souhaitent passer deux semaines de congé chez vous. Écrivez-leur un message pour leur proposer des lieux à visiter (endroits historiques, restaurants, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["21"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "deux amis souhaitent passer deux semaines de congé chez vous. écrivez-leur un message pour leur proposer des lieux à visiter (endroits historiques, restaurants, etc.)"}, {"task_ids": ["fevrier-2025_c18_t2"], "representative_id": "fevrier-2025_c18_t2", "task_content": "« Jeu concours : » « Remportez deux billets pour la destination de votre choix avec la compagnie Air Tropiques. Partagez votre plus beau voyage sur notre forum. » Rédigez un message sur le forum d’Air Tropiques dans lequel vous décrivez votre voyage le plus mémorable (date, destination, activités, etc.) et expliquez en quoi cette expérience a été particulièrement spéciale pour vous. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« jeu concours : » « remportez deux billets pour la destination de votre choix avec la compagnie air tropiques. partagez votre plus beau voyage sur notre forum. » rédigez un message sur le forum d’air tropiques dans lequel vous décrivez votre voyage le plus mémorable (date, destination, activités, etc.) et expliquez en quoi cette expérience a été particulièrement spéciale pour vous"}, {"task_ids": ["janvier-2025_c12_t2"], "representative_id": "janvier-2025_c12_t2", "task_content": "“Sal<PERSON>, tu vas bien ? Nous avons deux semaines de vacances en janvier. Nous allons venir visiter ton pays. Nous cherchons à visiter des sites historiques et à découvrir des plats nouveaux. Qu’est-ce que tu nous conseilles ? Aaron & Perla” Vous répondez à Aaron et Perla pour leur faire des propositions et justifiez votre choix en racontant vos dernières vacances . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "“salut, tu vas bien ? nous avons deux semaines de vacances en janvier. nous allons venir visiter ton pays. nous cherchons à visiter des sites historiques et à découvrir des plats nouveaux. qu’est-ce que tu nous conseilles ? aaron & perla” vous répondez à aaron et perla pour leur faire des propositions et justifiez votre choix en racontant vos dernières vacances"}, {"task_ids": ["decembre-2024_c12_t2"], "representative_id": "decembre-2024_c12_t2", "task_content": "Vous avez passé des vacances au Canada par le biais d’une agence de voyage. Écrivez un commentaire pour raconter votre expérience que vous avez vécue durant ce voyage. (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez passé des vacances au canada par le biais d’une agence de voyage. écrivez un commentaire pour raconter votre expérience que vous avez vécue durant ce voyage"}, {"task_ids": ["octobre-2024_c6_t2"], "representative_id": "octobre-2024_c6_t2", "task_content": "“Gagnez deux billets pour la destination de votre choix avec la compagnie Air Tropiques. Racontez votre plus beau voyage sur notre forum” Écrivez un message sur le forum de la compagnie Air Tropiques. Vous racontez votre plus beau voyage (destination, activités, …) et vous dites pourquoi…", "month_years": ["octobre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "“gagnez deux billets pour la destination de votre choix avec la compagnie air tropiques. racontez votre plus beau voyage sur notre forum” écrivez un message sur le forum de la compagnie air tropiques. vous racontez votre plus beau voyage (destination, activités, …) et vous dites pourquoi…"}, {"task_ids": ["novembre-2024_c1_t2"], "representative_id": "novembre-2024_c1_t2", "task_content": "Vos amis veulent passer une congé de 2 semaines chez vous. Vous leur écrivez un message pour leur proposer des lieux à visiter. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vos amis veulent passer une congé de 2 semaines chez vous. vous leur écrivez un message pour leur proposer des lieux à visiter"}, {"task_ids": ["novembre-2024_c3_t2"], "representative_id": "novembre-2024_c3_t2", "task_content": "Écrivez un message à un ami pour lui partager vos expériences et impressions lors de votre récent voyage dans un pays que vous avez visité. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à un ami pour lui partager vos expériences et impressions lors de votre récent voyage dans un pays que vous avez visité"}, {"task_ids": ["novembre-2024_c16_t2"], "representative_id": "novembre-2024_c16_t2", "task_content": "Vous avez passé des vacances dans une belle région de votre pays. Vous écrivez un message à vos amis dans lequel vous décrivez votre expérience, vous expliquer pourquoi vous avez beaucoup aimé ce séjour. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez passé des vacances dans une belle région de votre pays. vous écrivez un message à vos amis dans lequel vous décrivez votre expérience, vous expliquer pourquoi vous avez beaucoup aimé ce séjour"}, {"task_ids": ["janvier-2025_c14_t2"], "representative_id": "janvier-2025_c14_t2", "task_content": "Nouveau message “<PERSON><PERSON>, tu vas bien ? J’ai 2 semaines de vacances en janvier. Je vais venir visiter ton pays. Je cherche à visiter des sites historiques et à découvrir des plats nouveaux. Qu’est-ce que tu me conseilles ? Marina.” Répondez à ce message de votre amie . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "nouveau message “salut, tu vas bien ? j’ai 2 semaines de vacances en janvier. je vais venir visiter ton pays. je cherche à visiter des sites historiques et à découvrir des plats nouveaux. qu’est-ce que tu me conseilles ? marina.” répondez à ce message de votre amie"}], "name_en": "Travel Blog", "name_fr": "blog de voyage", "name_zh": "旅行博客", "original_name": "travel_blog"}, "general_event_blog": {"subtopic_id": 3, "task_count": 5, "unique_task_count": 5, "task_ids": [], "keywords": ["fête", "festival", "traditionnel", "culture", "événement"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c8_t2"], "representative_id": "septembre-2024_c8_t2", "task_content": "Vous avez participé à un événement qui vous a marqué (anniversaire, mariage, etc…). Racontez votre souvenir", "month_years": ["septembre-2024"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à un événement qui vous a marqué (anniversaire, mariage, etc…). racontez votre souvenir"}, {"task_ids": ["octobre-2024_c5_t2"], "representative_id": "octobre-2024_c5_t2", "task_content": "écrivez un article de blog sur un événement auquel vous avez assisté dernièrement (fête, festival, …)", "month_years": ["octobre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un article de blog sur un événement auquel vous avez assisté dernièrement (fête, festival, …)"}, {"task_ids": ["novembre-2024_c20_t2"], "representative_id": "novembre-2024_c20_t2", "task_content": "Vous avez participé à un événement qui vous a marqué (anniversaire, mariage, etc.). Racontez votre souvenir en décrivant ce qui vous a le plus marqué. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à un événement qui vous a marqué (anniversaire, mariage, etc.). racontez votre souvenir en décrivant ce qui vous a le plus marqué"}, {"task_ids": ["fevrier-2025_c19_t2"], "representative_id": "fevrier-2025_c19_t2", "task_content": "Rédigez un article de blog relatant un événement récent auquel vous avez assisté, que ce soit un festival, une cérémonie, une fête, etc. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article de blog relatant un événement récent auquel vous avez assisté, que ce soit un festival, une cérémonie, une fête, etc"}, {"task_ids": ["mars-2025_c1_t2"], "representative_id": "mars-2025_c1_t2", "task_content": "Rédiger un message racontant un événement marquant auquel vous avez participé, en mettant en avant les moments forts et les émotions ressenties . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un message racontant un événement marquant auquel vous avez participé, en mettant en avant les moments forts et les émotions ressenties"}], "name_en": "General Event Blog", "name_fr": "blog d'événements généraux", "name_zh": "常规事件博客", "original_name": "general_event_blog"}, "without_car_blog": {"subtopic_id": 8, "task_count": 9, "unique_task_count": 4, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c2_t2", "aout-2024_c3_t2", "novembre-2024_c14_t2", "juillet-2024_c37_t2", "decembre-2024_c4_t2", "decembre-2024_c19_t2"], "representative_id": "septembre-2024_c2_t2", "task_content": "Vous avez assisté à un événement intitulé « Une semaine sans voiture ». Racontez votre expérience et donnez votre impression sur cette initiative. Décrivez le déroulement de l’événement (dates, lieu, activités proposées).", "month_years": ["septembre-2024", "aout-2024", "novembre-2024", "juillet-2024", "decembre-2024", "decembre-2024"], "combination_numbers": ["2", "3", "14", "37", "4", "19"], "is_duplicate_group": true, "duplicate_count": 6, "clean_content": "vous avez assisté à un événement intitulé « une semaine sans voiture ». racontez votre expérience et donnez votre impression sur cette initiative. décrivez le déroulement de l’événement (dates, lieu, activités proposées)"}, {"task_ids": ["avril-2025_c9_t2"], "representative_id": "avril-2025_c9_t2", "task_content": "Partagez votre expérience lors de l’événement « Une semaine sans voiture », incluant les dates, le lieu et les activités proposées, tout en donnant votre avis sur cette initiative. (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "partagez votre expérience lors de l’événement « une semaine sans voiture », incluant les dates, le lieu et les activités proposées, tout en donnant votre avis sur cette initiative"}, {"task_ids": ["fevrier-2025_c26_t2"], "representative_id": "fevrier-2025_c26_t2", "task_content": "Partagez votre expérience lors de l’événement “Une semaine sans voiture”. Décrivez son organisation, les dates, le lieu ainsi que les activités proposées, et donnez votre avis sur cette initiative. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["26"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "partagez votre expérience lors de l’événement “une semaine sans voiture”. décrivez son organisation, les dates, le lieu ainsi que les activités proposées, et donnez votre avis sur cette initiative"}, {"task_ids": ["mars-2025_c3_t2"], "representative_id": "mars-2025_c3_t2", "task_content": "Rédiger un témoignage sur votre participation à l’événement “Une semaine sans voiture”, en partageant votre expérience, vos impressions et les activités auxquelles vous avez pris part . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un témoignage sur votre participation à l’événement “une semaine sans voiture”, en partageant votre expérience, vos impressions et les activités auxquelles vous avez pris part"}], "name_en": "Blog Excluding Cars", "name_fr": "blog sans voiture", "name_zh": "没有汽车博客", "original_name": "without_car_blog"}, "festival_blog": {"subtopic_id": 10, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c19_t2", "juillet-2024_c8_t2"], "representative_id": "octobre-2024_c19_t2", "task_content": "Vous avez assisté à une fête traditionnelle, dans votre pays ou à l’étranger. Rédigez un article pour votre blog dans lequel vous décrirez cette fête. Expliquez également pourquoi vous avez apprécié cet événement.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["19", "8"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez assisté à une fête traditionnelle, dans votre pays ou à l’étranger. rédigez un article pour votre blog dans lequel vous décrirez cette fête. expliquez également pourquoi vous avez apprécié cet événement"}, {"task_ids": ["Mai-2025_c2_t2"], "representative_id": "Mai-2025_c2_t2", "task_content": "Vous avez participé à une fête traditionnelle dans votre pays ou à l’étranger. Parlez-en dans votre blog et expliquez pourquoi elle vous a plu . (120 mots minimum/150 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à une fête traditionnelle dans votre pays ou à l’étranger. parlez-en dans votre blog et expliquez pourquoi elle vous a plu"}], "name_en": "Festival Blog", "name_fr": "blog de festival", "name_zh": "节日博客", "original_name": "festival_blog"}, "sport_blog": {"subtopic_id": 11, "task_count": 14, "unique_task_count": 9, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c15_t2", "juillet-2024_c17_t2"], "representative_id": "octobre-2024_c15_t2", "task_content": "Vous avez assisté à un événement sportif. Racontez votre expérience sur le journal de votre site web (environnement, préparatifs, supporters, etc).", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["15", "17"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez assisté à un événement sportif. racontez votre expérience sur le journal de votre site web (environnement, préparatifs, supporters, etc)"}, {"task_ids": ["novembre-2024_c10_t2", "juillet-2024_c26_t2", "janvier-2025_c8_t2"], "representative_id": "novembre-2024_c10_t2", "task_content": "Vous avez participé à un cours de sport dans une salle. Écrivez un article de blog parlant de  cette expérience et en exprimant également votre avis par rapport à cette salle. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025"], "combination_numbers": ["10", "26", "8"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous avez participé à un cours de sport dans une salle. écrivez un article de blog parlant de cette expérience et en exprimant également votre avis par rapport à cette salle"}, {"task_ids": ["fevrier-2025_c2_t2"], "representative_id": "fevrier-2025_c2_t2", "task_content": "Tu viens de commencer une nouvelle activité de loisir (sport, danse, etc.). Sur ton blog, tu partages ton ressenti et ton expérience. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu viens de commencer une nouvelle activité de loisir (sport, danse, etc.). sur ton blog, tu partages ton ressenti et ton expérience"}, {"task_ids": ["fevrier-2025_c13_t2", "avril-2025_c3_t2"], "representative_id": "fevrier-2025_c13_t2", "task_content": "Rédigez un article de blog dans lequel vous racontez votre participation à un cours de sport dans une salle. Décrivez votre expérience, donnez votre avis sur la salle et partagez vos impressions sur l’ambiance, l’équipement et l’encadrement . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "avril-2025"], "combination_numbers": ["13", "3"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un article de blog dans lequel vous racontez votre participation à un cours de sport dans une salle. décrivez votre expérience, donnez votre avis sur la salle et partagez vos impressions sur l’ambiance, l’équipement et l’encadrement"}, {"task_ids": ["fevrier-2025_c23_t2"], "representative_id": "fevrier-2025_c23_t2", "task_content": "Vous avez récemment débuté une nouvelle activité de loisir, comme un sport ou la danse. Rédigez un article sur votre blog pour partager votre expérience . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["23"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment débuté une nouvelle activité de loisir, comme un sport ou la danse. rédigez un article sur votre blog pour partager votre expérience"}, {"task_ids": ["juillet-2024_c16_t2", "juillet-2024_c28_t2"], "representative_id": "juillet-2024_c16_t2", "task_content": "Vous venez de commencer une nouvelle activité de loisir (sport, danse, etc.). Écrivez un article sur votre blog pour parler de cette expérience.", "month_years": ["juillet-2024", "juillet-2024"], "combination_numbers": ["16", "28"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous venez de commencer une nouvelle activité de loisir (sport, danse, etc.). écrivez un article sur votre blog pour parler de cette expérience"}, {"task_ids": ["avril-2025_c4_t2"], "representative_id": "avril-2025_c4_t2", "task_content": "Vous avez été sélectionné(e) pour un concours où le prix est un séjour de deux semaines dans votre ville favorite. Le thème du concours était “Mon artiste préféré”. Rédigez un article de blog pour parler de l’artiste que vous aimez le plus . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez été sélectionné(e) pour un concours où le prix est un séjour de deux semaines dans votre ville favorite. le thème du concours était “mon artiste préféré”. rédigez un article de blog pour parler de l’artiste que vous aimez le plus"}, {"task_ids": ["avril-2025_c1_t2"], "representative_id": "avril-2025_c1_t2", "task_content": "<PERSON><PERSON><PERSON>, Vous avez pris des cours dans notre salle de sport. Donnez-nous votre avis sur notre site Internet ! www.masalledesport.org Sur le site Internet de la salle de sport, vous répondez à ce message. Vous racontez cette expérience et vous donnez votre avis . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "bonjour, vous avez pris des cours dans notre salle de sport. donnez-nous votre avis sur notre site internet ! www.masalledesport.org sur le site internet de la salle de sport, vous répondez à ce message. vous racontez cette expérience et vous donnez votre avis"}, {"task_ids": ["juillet-2025_c2_t2"], "representative_id": "juillet-2025_c2_t2", "task_content": "Vous avez récemment suivi un cours de sport dans une salle.Rédigez un article de blog pour raconter votre expérience et donner votre avis sur l’établissement.\n(120 mots minimum/150 mots maximum)", "month_years": ["juillet-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1}], "name_en": "Sports Blog", "name_fr": "blog sport", "name_zh": "体育博客", "original_name": "sport_blog"}, "food_blog": {"subtopic_id": 9, "task_count": 3, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c25_t2"], "representative_id": "fevrier-2025_c25_t2", "task_content": "Rédigez un article de blog relatant votre expérience lors de l’événement « La Semaine du Goût », auquel vous avez participé. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["25"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article de blog relatant votre expérience lors de l’événement « la semaine du goût », auquel vous avez participé"}, {"task_ids": ["juillet-2024_c31_t2"], "representative_id": "juillet-2024_c31_t2", "task_content": "Vous avez participé à un événement nommé “La semaine du goût”. Écrivez un article de blog pour raconter cette expérience.", "month_years": ["juillet-2024"], "combination_numbers": ["31"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à un événement nommé “la semaine du goût”. écrivez un article de blog pour raconter cette expérience"}, {"task_ids": ["juillet-2024_c10_t2"], "representative_id": "juillet-2024_c10_t2", "task_content": "Vous avez passé un cours de cuisine. Rédigez un article de blog pour décrire ce souvenir en indiquant les détails.", "month_years": ["juillet-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez passé un cours de cuisine. rédigez un article de blog pour décrire ce souvenir en indiquant les détails"}], "name_en": "Food Blog", "name_fr": "blog culinaire", "name_zh": "美食博客", "original_name": "food_blog"}, "environment_blog": {"subtopic_id": 2, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": ["université", "étudiant", "étudié", "école", "apprentissage", "langue"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c2_t2", "decembre-2024_c6_t2"], "representative_id": "octobre-2024_c2_t2", "task_content": "Vous avez assisté à une soirée écologique pour protéger la planète qui avait lieu dans votre université. Racontez-là dans votre blog et expliquez pourquoi vous l’avez aimée.", "month_years": ["octobre-2024", "decembre-2024"], "combination_numbers": ["2", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez assisté à une soirée écologique pour protéger la planète qui avait lieu dans votre université. racontez-là dans votre blog et expliquez pourquoi vous l’avez aimée"}, {"task_ids": ["juillet-2024_c7_t2"], "representative_id": "juillet-2024_c7_t2", "task_content": "Vous avez participé à une action pour la « Journée mondiale du nettoyage de notre planète ». Vous avez ramassé des déchets dans un lieu public (plage, forêt, rue, etc.) avec d’autres personnes. Vous racontez cette expérience à vos amis, vous expliquez pourquoi il est important de participer à ce type d’action.", "month_years": ["juillet-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à une action pour la « journée mondiale du nettoyage de notre planète ». vous avez ramassé des déchets dans un lieu public (plage, forêt, rue, etc.) avec d’autres personnes. vous racontez cette expérience à vos amis, vous expliquez pourquoi il est important de participer à ce type d’action"}], "name_en": "Environment Blog", "name_fr": "blog sur l'environnement", "name_zh": "环境博客", "original_name": "environment_blog"}, "language_blog": {"subtopic_id": 13, "task_count": 6, "unique_task_count": 4, "task_ids": ["juillet-2025_c1_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c8_t2", "juillet-2024_c14_t2", "janvier-2025_c2_t2"], "representative_id": "novembre-2024_c8_t2", "task_content": "Dans votre blog, Racontez votre expérience de l’apprentissage d’une langue étrangère (vous écrivez sur un forum internet en racontant votre expérience en apprenant une langue étrangère). (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025"], "combination_numbers": ["8", "14", "2"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "dans votre blog, racontez votre expérience de l’apprentissage d’une langue étrangère (vous écrivez sur un forum internet en racontant votre expérience en apprenant une langue étrangère)"}, {"task_ids": ["fevrier-2025_c12_t2"], "representative_id": "fevrier-2025_c12_t2", "task_content": "Sur votre blog, partagez votre expérience d’apprentissage d’une langue étrangère. Rédigez un message sur un forum internet en racontant votre parcours et les défis rencontrés en apprenant cette langue. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "sur votre blog, partagez votre expérience d’apprentissage d’une langue étrangère. rédigez un message sur un forum internet en racontant votre parcours et les défis rencontrés en apprenant cette langue"}, {"task_ids": ["avril-2025_c15_t2"], "representative_id": "avril-2025_c15_t2", "task_content": "Partagez dans un blog votre expérience d’apprentissage d’une langue étrangère. (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "partagez dans un blog votre expérience d’apprentissage d’une langue étrangère"}, {"task_ids": ["avril-2025_c2_t2"], "representative_id": "avril-2025_c2_t2", "task_content": "Vous avez participé à un programme dans une école de langue. Rédigez un article sur votre site pour raconter cette expérience . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à un programme dans une école de langue. rédigez un article sur votre site pour raconter cette expérience"}], "tasks": [{"id": "juillet-2025_c1_t2", "task_content": "Vous avez participé à un programme dans une école de langue.Rédigez un article sur votre site pour raconter cette expérience.\n(120 mots minimum/150 mots maximum)", "month_year": "juillet-2025", "combination_number": "1"}], "name_en": "Language Blog", "name_fr": "blog de langue", "name_zh": "语言博客", "original_name": "language_blog"}, "study_aboard_blog": {"subtopic_id": 14, "task_count": 8, "unique_task_count": 6, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c20_t2"], "representative_id": "fevrier-2025_c20_t2", "task_content": "Vous avez récemment débuté vos études à l’université de Montréal. Rédigez un message à votre ami pour lui partager votre expérience de la première semaine, en donnant votre avis sur l’université et les cours . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment débuté vos études à l’université de montréal. rédigez un message à votre ami pour lui partager votre expérience de la première semaine, en donnant votre avis sur l’université et les cours"}, {"task_ids": ["juillet-2024_c2_t2"], "representative_id": "juillet-2024_c2_t2", "task_content": "Vous avez déjà étudié dans une université à l’étranger. Écrivez un article sur votre Blog pour raconter cette expérience.", "month_years": ["juillet-2024"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez déjà étudié dans une université à l’étranger. écrivez un article sur votre blog pour raconter cette expérience"}, {"task_ids": ["octobre-2024_c12_t2"], "representative_id": "octobre-2024_c12_t2", "task_content": "Vous avez participé à un échange scolaire dans une école à l’étranger. Racontez à vos amis ce que vous avez aimé", "month_years": ["octobre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à un échange scolaire dans une école à l’étranger. racontez à vos amis ce que vous avez aimé"}, {"task_ids": ["decembre-2024_c18_t2"], "representative_id": "decembre-2024_c18_t2", "task_content": "Écrivez un article de blog sur votre expérience d’études à l’étranger (séjour, activités,…) (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un article de blog sur votre expérience d’études à l’étranger (séjour, activités,…)"}, {"task_ids": ["avril-2025_c19_t2"], "representative_id": "avril-2025_c19_t2", "task_content": "Rédigez un article destiné à un blog dans lequel vous relatez votre expérience universitaire à l’étranger. (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article destiné à un blog dans lequel vous relatez votre expérience universitaire à l’étranger"}, {"task_ids": ["septembre-2024_c1_t2", "aout-2024_c2_t2", "octobre-2024_c4_t2"], "representative_id": "septembre-2024_c1_t2", "task_content": "Vous venez de commencer les cours à l’université à Montréal. Écrivez un message à votre ami pour lui raconter votre première semaine à l’université.", "month_years": ["septembre-2024", "aout-2024", "octobre-2024"], "combination_numbers": ["1", "2", "4"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous venez de commencer les cours à l’université à montréal. écrivez un message à votre ami pour lui raconter votre première semaine à l’université"}], "name_en": "Study Abroad Blog", "name_fr": "blog d'études à l'étranger", "name_zh": "留学博客", "original_name": "study_aboard_blog"}, "competiton_blog": {"subtopic_id": 15, "task_count": 4, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c8_t2", "juillet-2024_c12_t2"], "representative_id": "octobre-2024_c8_t2", "task_content": "Vous avez participé à un concours de cuisine, vous allez décrire vos souvenirs dans votre blog en indiquant les détails.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["8", "12"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez participé à un concours de cuisine, vous allez décrire vos souvenirs dans votre blog en indiquant les détails"}, {"task_ids": ["janvier-2025_c18_t2"], "representative_id": "janvier-2025_c18_t2", "task_content": "Vous avez participé à une compétition sportive, racontez votre expérience sur un site internet et donnez votre avis (lieu, date, organisation) . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à une compétition sportive, racontez votre expérience sur un site internet et donnez votre avis (lieu, date, organisation)"}, {"task_ids": ["avril-2025_c12_t2"], "representative_id": "avril-2025_c12_t2", "task_content": "Vous avez participé à une compétition sportive et souhaitez partager votre expérience sur un site internet. Décrivez le déroulement de l’événement (lieu, date, organisation) et donnez votre avis sur votre participation . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à une compétition sportive et souhaitez partager votre expérience sur un site internet. décrivez le déroulement de l’événement (lieu, date, organisation) et donnez votre avis sur votre participation"}], "name_en": "Competition Blog", "name_fr": "blog de compétition", "name_zh": "竞赛博客", "original_name": "competiton_blog"}, "flea_market_blog": {"subtopic_id": 7, "task_count": 7, "unique_task_count": 3, "task_ids": [], "keywords": ["loisir", "activité", "danse", "cuisine", "cours", "concours"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["aout-2024_c6_t2", "octobre-2024_c16_t2", "juillet-2024_c4_t2", "janvier-2025_c19_t2", "decembre-2024_c1_t2"], "representative_id": "aout-2024_c6_t2", "task_content": "Vous avez participé à une brocante (achat / vente de produits d’occasion) dans votre ville. Sur votre blog personnel, racontez pourquoi vous avez aimé cette activité.", "month_years": ["aout-2024", "octobre-2024", "juillet-2024", "janvier-2025", "decembre-2024"], "combination_numbers": ["6", "16", "4", "19", "1"], "is_duplicate_group": true, "duplicate_count": 5, "clean_content": "vous avez participé à une brocante (achat / vente de produits d’occasion) dans votre ville. sur votre blog personnel, racontez pourquoi vous avez aimé cette activité"}, {"task_ids": ["avril-2025_c11_t2"], "representative_id": "avril-2025_c11_t2", "task_content": "Vous avez pris part à une brocante dans votre ville, où vous avez vendu ou acheté des objets d’occasion. Sur votre blog personnel, partagez votre expérience en expliquant pourquoi vous avez apprécié cette activité . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez pris part à une brocante dans votre ville, où vous avez vendu ou acheté des objets d’occasion. sur votre blog personnel, partagez votre expérience en expliquant pourquoi vous avez apprécié cette activité"}, {"task_ids": ["mars-2025_c10_t2"], "representative_id": "mars-2025_c10_t2", "task_content": "Rédigez un article sur votre blog personnel pour partager votre expérience lors d’une brocante. Expliquez ce qui vous a plu, que ce soit la découverte d’objets uniques, l’ambiance ou les échanges avec les participants . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article sur votre blog personnel pour partager votre expérience lors d’une brocante. expliquez ce qui vous a plu, que ce soit la découverte d’objets uniques, l’ambiance ou les échanges avec les participants"}], "name_en": "Flea Market Blog", "name_fr": "blog de marché aux puces", "name_zh": "跳蚤市场博客", "original_name": "flea_market_blog"}, "concert_blog": {"subtopic_id": 5, "task_count": 12, "unique_task_count": 9, "task_ids": [], "keywords": ["spectacle", "concert", "<PERSON><PERSON><PERSON><PERSON>", "film", "sport"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c3_t2", "decembre-2024_c8_t2", "decembre-2024_c11_t2"], "representative_id": "septembre-2024_c3_t2", "task_content": "Vous venez d’assister au concert de votre artiste favori. Vous écrivez un article sur votre blog personnel, pour partager cette expérience et inciter vos amis et les autres à assister à son prochain concert.", "month_years": ["septembre-2024", "decembre-2024", "decembre-2024"], "combination_numbers": ["3", "8", "11"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous venez d’assister au concert de votre artiste favori. vous écrivez un article sur votre blog personnel, pour partager cette expérience et inciter vos amis et les autres à assister à son prochain concert"}, {"task_ids": ["aout-2024_c5_t2"], "representative_id": "aout-2024_c5_t2", "task_content": "« Ecole De Musique ! Cours Gratuits, Concerts, Jeux. <PERSON><PERSON><PERSON><PERSON><PERSON>, À partir de 9 Heures » Vous avez participé à cet évènement. Vous écrivez à vos amis pour raconter votre expérience et vous donnez votre opinion sur cette journée.", "month_years": ["aout-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« ecole de musique ! cours gratuits, concerts, jeux. rendez-vous vendredi, à partir de 9 heures » vous avez participé à cet évènement. vous écrivez à vos amis pour raconter votre expérience et vous donnez votre opinion sur cette journée"}, {"task_ids": ["novembre-2024_c5_t2", "juillet-2024_c23_t2"], "representative_id": "novembre-2024_c5_t2", "task_content": "Vous êtes allés voir un spectacle (film, pièce de théâtre, concert, etc.) avec des amis. Vous l’avez aimé. Sur votre blog, vous racontez votre soirée et vous expliquez pourquoi vous avez aimé le spectacle. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024"], "combination_numbers": ["5", "23"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous êtes allés voir un spectacle (film, pièce de théâtre, concert, etc.) avec des amis. vous l’avez aimé. sur votre blog, vous racontez votre soirée et vous expliquez pourquoi vous avez aimé le spectacle"}, {"task_ids": ["fevrier-2025_c9_t2"], "representative_id": "fevrier-2025_c9_t2", "task_content": "Vous avez passé une soirée agréable avec des amis en assistant à un spectacle (film, pièce de théâtre, concert, etc.) que vous avez adoré. Vous en parlez sur votre blog en expliquant ce qui vous a particulièrement marqué . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez passé une soirée agréable avec des amis en assistant à un spectacle (film, pièce de théâtre, concert, etc.) que vous avez adoré. vous en parlez sur votre blog en expliquant ce qui vous a particulièrement marqué"}, {"task_ids": ["mars-2025_c9_t2"], "representative_id": "mars-2025_c9_t2", "task_content": "Rédigez un message à vos amis pour partager votre expérience lors d’un événement organisé par une école de musique. Décrivez les activités proposées et donnez votre avis sur cette journée . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à vos amis pour partager votre expérience lors d’un événement organisé par une école de musique. décrivez les activités proposées et donnez votre avis sur cette journée"}, {"task_ids": ["decembre-2024_c2_t2"], "representative_id": "decembre-2024_c2_t2", "task_content": "« école De Musique ! Cours Gratuits, Concerts, Jeux. <PERSON><PERSON><PERSON><PERSON><PERSON>, À partir de 9 Heures » Vous avez participé à cet événement . Vous écrivez à vos amis pour raconter votre expérience et vous donnez votre opinion sur cette journée. (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« école de musique ! cours gratuits, concerts, jeux. rendez-vous vendredi, à partir de 9 heures » vous avez participé à cet événement . vous écrivez à vos amis pour raconter votre expérience et vous donnez votre opinion sur cette journée"}, {"task_ids": ["fevrier-2025_c29_t2"], "representative_id": "fevrier-2025_c29_t2", "task_content": "Rédigez un message à votre ami(e) pour l’inviter à venir avec vous au concert de votre artiste favori, en lui fournissant toutes les informations nécessaires (heure, prix des billets, lieu, etc.) . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à votre ami(e) pour l’inviter à venir avec vous au concert de votre artiste favori, en lui fournissant toutes les informations nécessaires (heure, prix des billets, lieu, etc.)"}, {"task_ids": ["decembre-2024_c16_t2"], "representative_id": "decembre-2024_c16_t2", "task_content": "Écrivez un courriel à votre ami pour l’inviter à vous accompagner au concert de votre musicien préféré en lui précisant tous les détails (heure, tarif, lieu…) (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un courriel à votre ami pour l’inviter à vous accompagner au concert de votre musicien préféré en lui précisant tous les détails (heure, tarif, lieu…)"}, {"task_ids": ["Mai-2025_c3_t2"], "representative_id": "Mai-2025_c3_t2", "task_content": "Vous avez assisté à un concert incroyable ? Écrivez un article pour raconter ce que vous avez vécu et invitez vos amis à découvrir cet artiste en live ! (120 mots minimum/150 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez assisté à un concert incroyable ? écrivez un article pour raconter ce que vous avez vécu et invitez vos amis à découvrir cet artiste en live"}], "name_en": "Concert Blog", "name_fr": "blog de concert", "name_zh": "音乐会博客", "original_name": "concert_blog"}, "foreign_country_blog": {"subtopic_id": 16, "task_count": 11, "unique_task_count": 6, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c9_t2", "juillet-2024_c25_t2", "janvier-2025_c7_t2", "decembre-2024_c15_t2"], "representative_id": "novembre-2024_c9_t2", "task_content": "Écrivez dans un article de blog pour raconter votre arrivée dans un pays étranger en donnant vos impressions. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025", "decembre-2024"], "combination_numbers": ["9", "25", "7", "15"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "écrivez dans un article de blog pour raconter votre arrivée dans un pays étranger en donnant vos impressions"}, {"task_ids": ["fevrier-2025_c27_t2"], "representative_id": "fevrier-2025_c27_t2", "task_content": "Racontez votre séjour académique à l’étranger à travers un article de blog. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "racontez votre séjour académique à l’étranger à travers un article de blog"}, {"task_ids": ["fevrier-2025_c14_t2"], "representative_id": "fevrier-2025_c14_t2", "task_content": "Rédigez un article de blog dans lequel vous racontez votre arrivée dans un pays étranger. Partagez vos premières impressions, vos découvertes et vos éventuelles difficultés d’adaptation . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article de blog dans lequel vous racontez votre arrivée dans un pays étranger. partagez vos premières impressions, vos découvertes et vos éventuelles difficultés d’adaptation"}, {"task_ids": ["fevrier-2025_c6_t2", "fevrier-2025_c15_t2"], "representative_id": "fevrier-2025_c6_t2", "task_content": "Vous avez récemment visité un nouveau pays pendant vos vacances. Sur un site internet, vous partagez votre expérience en détaillant votre séjour et en exprimant votre opinion sur ce pays. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["6", "15"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez récemment visité un nouveau pays pendant vos vacances. sur un site internet, vous partagez votre expérience en détaillant votre séjour et en exprimant votre opinion sur ce pays"}, {"task_ids": ["juillet-2024_c22_t2", "janvier-2025_c6_t2"], "representative_id": "juillet-2024_c22_t2", "task_content": "Vous avez visité un nouveau pays pendant vos vacances. Sur un site internet, racontez votre expérience et donnez votre opinion sur ce pays.", "month_years": ["juillet-2024", "janvier-2025"], "combination_numbers": ["22", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez visité un nouveau pays pendant vos vacances. sur un site internet, racontez votre expérience et donnez votre opinion sur ce pays"}, {"task_ids": ["decembre-2024_c14_t2"], "representative_id": "decembre-2024_c14_t2", "task_content": "Vous avez visité une ville que vous ne connaissiez pas. Vous avez envie de partager votre découverte. Vous postez un message sur un site Internet dédié aux voyages. Racontez votre expérience et expliquez ce qui vous a plu et ce qui vous a déplu dans la ville. (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez visité une ville que vous ne connaissiez pas. vous avez envie de partager votre découverte. vous postez un message sur un site internet dédié aux voyages. racontez votre expérience et expliquez ce qui vous a plu et ce qui vous a déplu dans la ville"}], "name_en": "Foreign Country Blog", "name_fr": "blog de pays étranger", "name_zh": "外国国家博客", "original_name": "foreign_country_blog"}, "artist_blog": {"subtopic_id": 17, "task_count": 7, "unique_task_count": 5, "task_ids": ["juillet-2025_c4_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c17_t2"], "representative_id": "novembre-2024_c17_t2", "task_content": "Vous avez visité une exposition de votre artiste préféré. Rédigez un article exprimant votre expérience lors de la visite. Décrivez ce que vous avez vu et vos impressions. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez visité une exposition de votre artiste préféré. rédigez un article exprimant votre expérience lors de la visite. décrivez ce que vous avez vu et vos impressions"}, {"task_ids": ["fevrier-2025_c32_t2", "mars-2025_c5_t2"], "representative_id": "fevrier-2025_c32_t2", "task_content": "Rédiger un article sur la visite d’une exposition dédiée à un artiste préféré, en partageant les impressions et en décrivant les œuvres découvertes. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["32", "5"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédiger un article sur la visite d’une exposition dédiée à un artiste préféré, en partageant les impressions et en décrivant les œuvres découvertes"}, {"task_ids": ["fevrier-2025_c24_t2"], "representative_id": "fevrier-2025_c24_t2", "task_content": "Rédigez un article de blog sur votre artiste préféré dans le cadre d’un concours dont le thème est « Mon artiste préféré », permettant de gagner un séjour de deux semaines dans votre ville favorite. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un article de blog sur votre artiste préféré dans le cadre d’un concours dont le thème est « mon artiste préféré », permettant de gagner un séjour de deux semaines dans votre ville favorite"}, {"task_ids": ["octobre-2024_c1_t2", "juillet-2024_c32_t2"], "representative_id": "octobre-2024_c1_t2", "task_content": "Vous avez participé à un concours pour gagner un séjour de deux semaines dans votre ville préféré. Le thème de ce concours est “Mon artiste préféré“. Écrivez un article de blog pour parler de votre artiste préféré.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["1", "32"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez participé à un concours pour gagner un séjour de deux semaines dans votre ville préféré. le thème de ce concours est “mon artiste préféré“. écrivez un article de blog pour parler de votre artiste préféré"}, {"task_ids": ["juillet-2024_c30_t2"], "representative_id": "juillet-2024_c30_t2", "task_content": "Participez à notre concours pour gagner un séjour pour deux personnes dans la ville de votre choix. Rédigez un article sur le thème : « La vie de mon artiste préféré(e) ». Vous participez à ce concours. Vous expliquez pourquoi vous avez choisi cet(te) artiste et vous racontez sa vie.", "month_years": ["juillet-2024"], "combination_numbers": ["30"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "participez à notre concours pour gagner un séjour pour deux personnes dans la ville de votre choix. rédigez un article sur le thème : « la vie de mon artiste préféré(e) ». vous participez à ce concours. vous expliquez pourquoi vous avez choisi cet(te) artiste et vous racontez sa vie"}], "tasks": [{"id": "juillet-2025_c4_t2", "task_content": "Vous avez participé à un concours pour gagner un séjour de deux semaines dans votre ville préféré. Le thème de ce concours est “Mon artiste préféré“.Écrivez un article de blog pour parler de votre artiste préféré.", "month_year": "juillet-2025", "combination_number": "4"}], "name_en": "Artist Blog", "name_fr": "blog d'artiste", "name_zh": "艺术家博客", "original_name": "artist_blog"}, "host_foreign_student_blog": {"subtopic_id": 18, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c4_t2"], "representative_id": "fevrier-2025_c4_t2", "task_content": "Tu as accueilli un(e) étudiant(e) étranger(e) chez toi pendant une semaine. Sur ton blog, tu partages cette expérience en racontant comment s’est déroulée cette semaine et en expliquant pourquoi tu l’as appréciée. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu as accueilli un(e) étudiant(e) étranger(e) chez toi pendant une semaine. sur ton blog, tu partages cette expérience en racontant comment s’est déroulée cette semaine et en expliquant pourquoi tu l’as appréciée"}, {"task_ids": ["juillet-2024_c1_t2", "juillet-2024_c35_t2"], "representative_id": "juillet-2024_c1_t2", "task_content": "Vous avez accueilli un(e) étudiant(e) étranger(e) pendant une semaine chez vous. Sur votre blog, vous écrivez un article pour raconter cette semaine. Vous expliquez pourquoi vous avez aimé cette expérience.", "month_years": ["juillet-2024", "juillet-2024"], "combination_numbers": ["1", "35"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez accueilli un(e) étudiant(e) étranger(e) pendant une semaine chez vous. sur votre blog, vous écrivez un article pour raconter cette semaine. vous expliquez pourquoi vous avez aimé cette expérience"}], "name_en": "Foreign Student Blog Host", "name_fr": "blog d'étudiant étranger hôte", "name_zh": "接待外国学生博客", "original_name": "host_foreign_student_blog"}, "book_fair_blog": {"subtopic_id": 16, "task_count": 2, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c7_t2", "juillet-2024_c34_t2"], "representative_id": "octobre-2024_c7_t2", "task_content": "écrivez un message à vos amis pour leur raconter votre expérience lors d’un salon du livre (conférence, expositions et rencontre avec les auteurs…)", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["7", "34"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "écrivez un message à vos amis pour leur raconter votre expérience lors d’un salon du livre (conférence, expositions et rencontre avec les auteurs…)"}], "name_en": "Book Fair Blog", "name_fr": "blog de la foire du livre", "name_zh": "书展博客", "original_name": "book_fair_blog"}, "new_job_blog": {"subtopic_id": 1, "task_count": 6, "unique_task_count": 5, "task_ids": [], "keywords": ["travail", "travailler", "professionnel", "entreprise", "emploi"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c20_t2"], "representative_id": "avril-2025_c20_t2", "task_content": "Vous travaillez désormais à l’étranger. Écrivez à vos amis pour leur parler de cette nouvelle expérience professionnelle et de ce que vous avez particulièrement apprécié dans ce nouveau contexte . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous travaillez désormais à l’étranger. écrivez à vos amis pour leur parler de cette nouvelle expérience professionnelle et de ce que vous avez particulièrement apprécié dans ce nouveau contexte"}, {"task_ids": ["septembre-2024_c5_t2", "decembre-2024_c10_t2"], "representative_id": "septembre-2024_c5_t2", "task_content": "Écrivez un message à vos amis pour leur partager votre expérience de travail temporaire effectué durant les vacances d’été.", "month_years": ["septembre-2024", "decembre-2024"], "combination_numbers": ["5", "10"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "écrivez un message à vos amis pour leur partager votre expérience de travail temporaire effectué durant les vacances d’été"}, {"task_ids": ["octobre-2024_c11_t2"], "representative_id": "octobre-2024_c11_t2", "task_content": "écrivez un message à vos amis pour leur parler de votre expérience professionnelle à l’étranger", "month_years": ["octobre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à vos amis pour leur parler de votre expérience professionnelle à l’étranger"}, {"task_ids": ["octobre-2024_c10_t2"], "representative_id": "octobre-2024_c10_t2", "task_content": "Vous venez d’avoir un nouveau travail. Envoyez un courriel à vos amis pour leur raconter comment vous avez passé votre première semaine de travail (entreprise, poste, tâches, etc).", "month_years": ["octobre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez d’avoir un nouveau travail. envoyez un courriel à vos amis pour leur raconter comment vous avez passé votre première semaine de travail (entreprise, poste, tâches, etc)"}, {"task_ids": ["juillet-2024_c24_t2"], "representative_id": "juillet-2024_c24_t2", "task_content": "Vous êtes parti(e) travailler à l’étranger. Vous envoyez un message à vos amis pour raconter cette nouvelle expérience professionnelle. Vous expliquez ce que vous avez le plus aimé.", "month_years": ["juillet-2024"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes parti(e) travailler à l’étranger. vous envoyez un message à vos amis pour raconter cette nouvelle expérience professionnelle. vous expliquez ce que vous avez le plus aimé"}], "name_en": "New Job Blog", "name_fr": "nouveau blog d'emploi", "name_zh": "新工作博客", "original_name": "new_job_blog"}, "family_party_blog": {"subtopic_id": 17, "task_count": 11, "unique_task_count": 7, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["janvier-2025_c17_t2"], "representative_id": "janvier-2025_c17_t2", "task_content": "Vous avez assisté à une fête de famille. Envoyez un message à vos amis pour leur raconter cette fête et expliquez ce que vous avez le plus apprécié . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez assisté à une fête de famille. envoyez un message à vos amis pour leur raconter cette fête et expliquez ce que vous avez le plus apprécié"}, {"task_ids": ["mars-2025_c14_t2"], "representative_id": "mars-2025_c14_t2", "task_content": "Vous avez récemment assisté à une fête de quartier entre voisins. Sur votre blog, racontez votre expérience et expliquez pourquoi vous avez aimé cet événement . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment assisté à une fête de quartier entre voisins. sur votre blog, racontez votre expérience et expliquez pourquoi vous avez aimé cet événement"}, {"task_ids": ["juillet-2024_c13_t2", "janvier-2025_c1_t2"], "representative_id": "juillet-2024_c13_t2", "task_content": "Vous avez assisté à une fête de voisins du quartier, écrivez un blog pour montrer pourquoi vous avez aimé cette fête.", "month_years": ["juillet-2024", "janvier-2025"], "combination_numbers": ["13", "1"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez assisté à une fête de voisins du quartier, écrivez un blog pour montrer pourquoi vous avez aimé cette fête"}, {"task_ids": ["decembre-2024_c7_t2"], "representative_id": "decembre-2024_c7_t2", "task_content": "Vous avez assisté à une fete entre voisins dans votre quartier. Dans votre blog décrivez cet événement et racontez pourquoi vous l’avez aimé. (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez assisté à une fete entre voisins dans votre quartier. dans votre blog décrivez cet événement et racontez pourquoi vous l’avez aimé"}, {"task_ids": ["avril-2025_c13_t2"], "representative_id": "avril-2025_c13_t2", "task_content": "Vous avez récemment assisté à une fête de famille. Rédigez un message à vos amis pour leur raconter l’événement et partager ce qui vous a le plus marqué . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment assisté à une fête de famille. rédigez un message à vos amis pour leur raconter l’événement et partager ce qui vous a le plus marqué"}, {"task_ids": ["septembre-2024_c4_t2", "octobre-2024_c14_t2", "juillet-2024_c5_t2", "decembre-2024_c9_t2"], "representative_id": "septembre-2024_c4_t2", "task_content": "Vous avez été invité(e) à une fête en famille. Vous envoyez un message à vos amis pour raconter cette fête. Vous expliquez ce que vous avez le plus aimé.", "month_years": ["septembre-2024", "octobre-2024", "juillet-2024", "decembre-2024"], "combination_numbers": ["4", "14", "5", "9"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous avez été invité(e) à une fête en famille. vous envoyez un message à vos amis pour raconter cette fête. vous expliquez ce que vous avez le plus aimé"}, {"task_ids": ["novembre-2024_c4_t2"], "representative_id": "novembre-2024_c4_t2", "task_content": "écrivez un message à vos amis pour leur raconter à propos d’une fête à laquelle vous avez assisté en famille. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à vos amis pour leur raconter à propos d’une fête à laquelle vous avez assisté en famille"}], "name_en": "Family Party Blog", "name_fr": "blog de fête en famille", "name_zh": "家庭聚会博客", "original_name": "family_party_blog"}}, "name_en": "Blog Experiences", "name_fr": "expériences de blog", "name_zh": "博客经历", "original_name": "blog_experiences"}, "forum_opinions": {"topic_id": 2, "keywords": ["forum", "site internet", "répondez", "opinion", "avis", "pensez-vous", "bonne idée", "mauvaise idée", "témoignage"], "total_tasks": 58, "unique_tasks": 38, "subtopics": {"general_opinions": {"subtopic_id": 3, "task_count": 11, "unique_task_count": 8, "task_ids": [], "keywords": ["opinion", "avis", "pensez", "témoignage"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c3_t2"], "representative_id": "juillet-2024_c3_t2", "task_content": "Vous avez lu ce message sur un forum internet. Vous répondez à Paul et Naïma. Dans votre message, vous donnez votre opinion sur le choix de Paul et de Naïma et vous racontez comment vous feriez si vous étiez à leur place.", "month_years": ["juillet-2024"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez lu ce message sur un forum internet. vous répondez à paul et naïma. dans votre message, vous donnez votre opinion sur le choix de paul et de naïma et vous racontez comment vous feriez si vous étiez à leur place"}, {"task_ids": ["Mai-2025_c4_t2"], "representative_id": "Mai-2025_c4_t2", "task_content": "Participez à la discussion sur le site “voyage.internaute.fr” en répondant à la question : est-ce une bonne ou une mauvaise idée de tout quitter pour voyager pendant un an ? Partagez votre point de vue en vous appuyant sur des exemples tirés de votre propre expérience . (120 mots minimum/150 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "participez à la discussion sur le site “voyage.internaute.fr” en répondant à la question : est-ce une bonne ou une mauvaise idée de tout quitter pour voyager pendant un an ? partagez votre point de vue en vous appuyant sur des exemples tirés de votre propre expérience"}, {"task_ids": ["novembre-2024_c11_t2"], "representative_id": "novembre-2024_c11_t2", "task_content": "Que pensez-vous de l’installation d’une télévision dans la chambre ? Partagez votre avis dans un article de blog. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "que pensez-vous de l’installation d’une télévision dans la chambre ? partagez votre avis dans un article de blog"}, {"task_ids": ["avril-2025_c14_t2"], "representative_id": "avril-2025_c14_t2", "task_content": "Dans un article de blog, exprimez votre opinion sur l’installation d’une télévision dans une chambre. (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "dans un article de blog, exprimez votre opinion sur l’installation d’une télévision dans une chambre"}, {"task_ids": ["fevrier-2025_c10_t2"], "representative_id": "fevrier-2025_c10_t2", "task_content": "COURRIER DES LECTEURS Abandonner tout pour partir en voyage pendant un an : est-ce une bonne ou une mauvaise décision ? Répondez sur notre site Internet à l’adresse : ‘voyage.internaute.fr’. Rédigez un message pour répondre à la question en partageant des exemples tirés de votre propre expérience. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "courrier des lecteurs abandonner tout pour partir en voyage pendant un an : est-ce une bonne ou une mauvaise décision ? répondez sur notre site internet à l’adresse : ‘voyage.internaute.fr’. rédigez un message pour répondre à la question en partageant des exemples tirés de votre propre expérience"}, {"task_ids": ["janvier-2025_c13_t2"], "representative_id": "janvier-2025_c13_t2", "task_content": "Vous avez participé à une émission de télévision. Vous souhaitez partager votre expérience.vous rédigez un article sur votre site internet. Vous racontez votre participation (types d’émissions, personnes rencontrées, etc) et vous donnez vos impressions . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez participé à une émission de télévision. vous souhaitez partager votre expérience.vous rédigez un article sur votre site internet. vous racontez votre participation (types d’émissions, personnes rencontrées, etc) et vous donnez vos impressions"}, {"task_ids": ["avril-2025_c10_t2"], "representative_id": "avril-2025_c10_t2", "task_content": "Sur votre blog, expliquez les raisons qui vous ont poussé à quitter la ville pour vous installer à la campagne. Présentez les avantages de votre nouvelle vie rurale . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "sur votre blog, expliquez les raisons qui vous ont poussé à quitter la ville pour vous installer à la campagne. présentez les avantages de votre nouvelle vie rurale"}, {"task_ids": ["octobre-2024_c9_t2", "octobre-2024_c17_t2", "novembre-2024_c6_t2", "juillet-2024_c9_t2"], "representative_id": "octobre-2024_c9_t2", "task_content": "COURRIER DES LECTEURS Tout quitter pour partir en voyage pendant un an: bonne ou mauvaise idée ? Répondez sur notre site Internet voyage.internaute.fr Vous écrivez un message sur ce site internet Vous répondez à la question posée en prenant des exemples de votre vie personnelle.", "month_years": ["octobre-2024", "octobre-2024", "novembre-2024", "juillet-2024"], "combination_numbers": ["9", "17", "6", "9"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "courrier des lecteurs tout quitter pour partir en voyage pendant un an: bonne ou mauvaise idée ? répondez sur notre site internet voyage.internaute.fr vous écrivez un message sur ce site internet vous répondez à la question posée en prenant des exemples de votre vie personnelle"}], "name_en": "General Opinions", "name_fr": "opinions générales", "name_zh": "一般观点", "original_name": "general_opinions"}, "admiration_opinions": {"subtopic_id": 4, "task_count": 6, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c7_t2", "janvier-2025_c3_t2", "janvier-2025_c15_t2"], "representative_id": "novembre-2024_c7_t2", "task_content": "Exprimez votre admiration pour une personnalité, célèbre ou non, en vous appuyant sur ses actions spécifiques. Rédigez un article de blog en détaillant les actions remarquables de cette personne et expliquez pourquoi vous l’aimez. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "janvier-2025", "janvier-2025"], "combination_numbers": ["7", "3", "15"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "exprimez votre admiration pour une personnalité, célèbre ou non, en vous appuyant sur ses actions spécifiques. rédigez un article de blog en détaillant les actions remarquables de cette personne et expliquez pourquoi vous l’aimez"}, {"task_ids": ["fevrier-2025_c11_t2", "mars-2025_c12_t2"], "representative_id": "fevrier-2025_c11_t2", "task_content": "Rédigez un article de blog pour exprimer votre admiration envers une personnalité, qu’elle soit célèbre ou non. Mettez en avant ses actions spécifiques et expliquez pourquoi vous l’appréciez tant . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["11", "12"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un article de blog pour exprimer votre admiration envers une personnalité, qu’elle soit célèbre ou non. mettez en avant ses actions spécifiques et expliquez pourquoi vous l’appréciez tant"}, {"task_ids": ["juillet-2024_c11_t2"], "representative_id": "juillet-2024_c11_t2", "task_content": "Vous aimez une personne célèbre ou non par ses actions. Écrivez dans votre blog personnel en précisant l’action que cette personne a faite et dites pourquoi vous l’adorez.", "month_years": ["juillet-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous aimez une personne célèbre ou non par ses actions. écrivez dans votre blog personnel en précisant l’action que cette personne a faite et dites pourquoi vous l’adorez"}], "name_en": "Admiration Opinions", "name_fr": "opinions d'admiration", "name_zh": "钦佩观点", "original_name": "admiration_opinions"}, "online_trainning_opinions": {"subtopic_id": 6, "task_count": 4, "unique_task_count": 2, "task_ids": ["juillet-2025_c3_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c15_t2"], "representative_id": "novembre-2024_c15_t2", "task_content": "Vous avez lu sur un forum un débat concernant les formations en ligne. Écrivez un message en décrivant votre expérience (cours de langue, formation professionnelle, etc.). Donnez votre avis sur ce que vous avez aimé et ce que vous n’en avez pas. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez lu sur un forum un débat concernant les formations en ligne. écrivez un message en décrivant votre expérience (cours de langue, formation professionnelle, etc.). donnez votre avis sur ce que vous avez aimé et ce que vous n’en avez pas"}, {"task_ids": ["fevrier-2025_c30_t2", "mars-2025_c7_t2", "avril-2025_c5_t2"], "representative_id": "fevrier-2025_c30_t2", "task_content": "Rédiger un message décrivant une expérience avec les formations en ligne, en mettant en avant les points positifs et les éventuels défis rencontrés. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "mars-2025", "avril-2025"], "combination_numbers": ["30", "7", "5"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "rédiger un message décrivant une expérience avec les formations en ligne, en mettant en avant les points positifs et les éventuels défis rencontrés"}], "tasks": [{"id": "juillet-2025_c3_t2", "task_content": "Rédiger un message décrivant une expérience avec les formations en ligne, en mettant en avant les points positifs et les éventuels défis rencontrés.\n(120 mots minimum/150 mots maximum)", "month_year": "juillet-2025", "combination_number": "3"}], "name_en": "Online Training Opinions", "name_fr": "opinions sur la formation en ligne", "name_zh": "在线培训意见", "original_name": "online_trainning_opinions"}, "colocation_opinions": {"subtopic_id": 7, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c6_t2"], "representative_id": "avril-2025_c6_t2", "task_content": "Vous avez déjà vécu en colocation avec des amis. Le site « colocation.com » cherche des témoignages. Raconter votre expérience de colocation et dire ce que vous en pensez . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez déjà vécu en colocation avec des amis. le site « colocation.com » cherche des témoignages. raconter votre expérience de colocation et dire ce que vous en pensez"}, {"task_ids": ["septembre-2024_c7_t2", "octobre-2024_c3_t2"], "representative_id": "septembre-2024_c7_t2", "task_content": "<< Le site «colocation.com» recherche des témoignes sur vos expériences de colocation. Écrivez-nous ! >> Vous avez déjà habité en colocation avec des amis. Vous racontez votre expérience aux membres du site internet. Vous donnez votre opinion sur ce mode de logement.", "month_years": ["septembre-2024", "octobre-2024"], "combination_numbers": ["7", "3"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "<< le site «colocation.com» recherche des témoignes sur vos expériences de colocation. écrivez-nous ! >> vous avez déjà habité en colocation avec des amis. vous racontez votre expérience aux membres du site internet. vous donnez votre opinion sur ce mode de logement"}], "name_en": "Opinions on colocating", "name_fr": "opinions sur la colocation", "name_zh": "共现观点", "original_name": "colocation_opinions"}, "travel_complaint_opinions": {"subtopic_id": 8, "task_count": 4, "unique_task_count": 3, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c22_t2", "avril-2025_c18_t2"], "representative_id": "fevrier-2025_c22_t2", "task_content": "Rédigez une lettre de réclamation pour exprimer votre insatisfaction à l’égard des services fournis par une agence de voyage suite à une organisation défaillante de votre séjour. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "avril-2025"], "combination_numbers": ["22", "18"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez une lettre de réclamation pour exprimer votre insatisfaction à l’égard des services fournis par une agence de voyage suite à une organisation défaillante de votre séjour"}, {"task_ids": ["juillet-2024_c29_t2"], "representative_id": "juillet-2024_c29_t2", "task_content": "Vous faites une réclamation par rapport aux mauvaises prestations d’une agence de voyages suite à un voyage mal organisé. Exprimez votre mécontentement.", "month_years": ["juillet-2024"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous faites une réclamation par rapport aux mauvaises prestations d’une agence de voyages suite à un voyage mal organisé. exprimez votre mécontentement"}, {"task_ids": ["octobre-2024_c13_t2"], "representative_id": "octobre-2024_c13_t2", "task_content": "Envoyez une réclamation par courrier à une agence de voyage pour exprimer votre mécontentement suite à un voyage mal organisé", "month_years": ["octobre-2024"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "envoyez une réclamation par courrier à une agence de voyage pour exprimer votre mécontentement suite à un voyage mal organisé"}], "name_en": "Opinions on Travel Complaints", "name_fr": "Opinions de plaintes de voyage", "name_zh": "旅行投诉意见", "original_name": "travel_complaint_opinions"}, "study_aborad_opinions": {"subtopic_id": 9, "task_count": 9, "unique_task_count": 8, "task_ids": ["juin-2025_c1_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c12_t2"], "representative_id": "novembre-2024_c12_t2", "task_content": "Vous avez lu ce message sur un site internet : Je suis étudiante et je vais partir étudier un an à l’étranger. Mai<PERSON>, j’ai un peu peur. Qui a déjà fait cela ? Justine. Vous répondez à Justine : – Racontez un séjour d’étude que vous avez fait dans un pays étranger. – Expliquez pourquoi cette expérience a été positive ou négative pour vous (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez lu ce message sur un site internet : je suis étudiante et je vais partir étudier un an à l’étranger. mais, j’ai un peu peur. qui a déjà fait cela ? justine. vous répondez à justine : – racontez un séjour d’étude que vous avez fait dans un pays étranger. – expliquez pourquoi cette expérience a été positive ou négative pour vous"}, {"task_ids": ["juillet-2024_c36_t2"], "representative_id": "juillet-2024_c36_t2", "task_content": "Un internaute a publié le message suivant : « Je vais partir étudier un an à l’étranger et j’ai peur ». Rédigez une réponse pour partager votre expérience personnelle. Parlez des défis que vous avez rencontrés, des solutions que vous as trouvées, et des bénéfices que vous avez tirés de cette expérience.", "month_years": ["juillet-2024"], "combination_numbers": ["36"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un internaute a publié le message suivant : « je vais partir étudier un an à l’étranger et j’ai peur ». rédigez une réponse pour partager votre expérience personnelle. parlez des défis que vous avez rencontrés, des solutions que vous as trouvées, et des bénéfices que vous avez tirés de cette expérience"}, {"task_ids": ["avril-2025_c7_t2"], "representative_id": "avril-2025_c7_t2", "task_content": "Une personne a publié sur Facebook un message au sujet des études à l’étranger. Rédigez un commentaire pour partager votre opinion en donnant les points positifs et négatifs de cette expérience . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "une personne a publié sur facebook un message au sujet des études à l’étranger. rédigez un commentaire pour partager votre opinion en donnant les points positifs et négatifs de cette expérience"}, {"task_ids": ["avril-2025_c8_t2"], "representative_id": "avril-2025_c8_t2", "task_content": "Un internaute exprime des inquiétudes concernant son départ pour étudier à l’étranger. Partagez votre expérience personnelle sur les défis rencontrés, les solutions trouvées et les bénéfices tirés de cette expérience . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un internaute exprime des inquiétudes concernant son départ pour étudier à l’étranger. partagez votre expérience personnelle sur les défis rencontrés, les solutions trouvées et les bénéfices tirés de cette expérience"}, {"task_ids": ["septembre-2024_c9_t2"], "representative_id": "septembre-2024_c9_t2", "task_content": "Répondez en commentaire à une publication sur Facebook au sujet des études à l’étranger en citant les avantages et les inconvénients de cette expérience", "month_years": ["septembre-2024"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "répondez en commentaire à une publication sur facebook au sujet des études à l’étranger en citant les avantages et les inconvénients de cette expérience"}, {"task_ids": ["mars-2025_c11_t2", "juin-2024_c1_t2"], "representative_id": "mars-2025_c11_t2", "task_content": "Une étudiante de 19 ans souhaite partir à l’étranger pour ses études et demande des témoignages sur un forum. Vous répondez en partageant votre propre expérience d’un séjour à l’étranger, en expliquant les défis et les bénéfices que vous en avez tirés . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025", "juin-2024"], "combination_numbers": ["11", "1"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "une étudiante de 19 ans souhaite partir à l’étranger pour ses études et demande des témoignages sur un forum. vous répondez en partageant votre propre expérience d’un séjour à l’étranger, en expliquant les défis et les bénéfices que vous en avez tirés"}, {"task_ids": ["janvier-2025_c4_t2"], "representative_id": "janvier-2025_c4_t2", "task_content": "Une étudiante qui à 19 ans veut aller à l’étranger pour les études. Elle demande aux internautes du forum de partager leurs expériences et vous avez fait un an à l’étranger. Parlez-lui de votre expérience (décrire le séjour à l’étranger, les activités…). (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "une étudiante qui à 19 ans veut aller à l’étranger pour les études. elle demande aux internautes du forum de partager leurs expériences et vous avez fait un an à l’étranger. parlez-lui de votre expérience (déc<PERSON><PERSON> le séjour à l’étranger, les activités…)"}, {"task_ids": ["janvier-2025_c16_t2"], "representative_id": "janvier-2025_c16_t2", "task_content": "“Chers internautes, J’ai 19 ans, je vais bientôt partir à l’étranger pour continuer mes études. J’aimerais bien lire les témoignages et avis des étudiants qui ont déjà fait des études loin de chez eux. Merci de me répondre. <PERSON>“ Vous avez fait des études à l’étranger pendant un an. Vous écrivez une réponse sur le forum. Vous racontez votre séjour à l’étranger, vous dites si vous avez aimé ou non cette expérience et vous dites pourquoi . (120 mots minimum/150 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "“chers internautes, j’ai 19 ans, je vais bientôt partir à l’étranger pour continuer mes études. j’aimerais bien lire les témoignages et avis des étudiants qui ont déjà fait des études loin de chez eux. merci de me répondre. julie“ vous avez fait des études à l’étranger pendant un an. vous écrivez une réponse sur le forum. vous racontez votre séjour à l’étranger, vous dites si vous avez aimé ou non cette expérience et vous dites pourquoi"}], "tasks": [{"id": "juin-2025_c1_t2", "task_content": "Une étudiante de 19 ans souhaite partir à l’étranger pour ses études et demande des témoignages sur un forum.Vous répondez en partageant votre propre expérience d’un séjour à l’étranger, en expliquant les défis et les bénéfices que vous en avez tirés.\n(120 mots minimum/150 mots maximum)", "month_year": "juin-2025", "combination_number": "1"}], "name_en": "Study Abroad Opinions", "name_fr": "opinions sur les études à l'étranger", "name_zh": "留学观点", "original_name": "study_aborad_opinions"}, "living_with_elderly/children_opinions": {"subtopic_id": 1, "task_count": 13, "unique_task_count": 6, "task_ids": [], "keywords": ["vie", "vivre", "personnes âgées", "lifestyle"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c5_t2", "fevrier-2025_c21_t2"], "representative_id": "fevrier-2025_c5_t2", "task_content": "Vivre avec une personne âgée : Comment faire ? Un site internet cherche des témoignages sur cette expérience. Tu racontes comment tu as vécu cette situation et ce que tu en as retenu. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["5", "21"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vivre avec une personne âgée : comment faire ? un site internet cherche des témoignages sur cette expérience. tu racontes comment tu as vécu cette situation et ce que tu en as retenu"}, {"task_ids": ["juillet-2024_c20_t2"], "representative_id": "juillet-2024_c20_t2", "task_content": "Vivre avec les personnes âgées, est-ce facile ? Un site internet recherche des gens pour faire des témoignages sur la vie avec les personnes âgées. Rédigez votre expérience pour faire part de comment vous avez pu vous en sortir (vis-à-vis des personnes âgées).", "month_years": ["juillet-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vivre avec les personnes âgées, est-ce facile ? un site internet recherche des gens pour faire des témoignages sur la vie avec les personnes âgées. rédigez votre expérience pour faire part de comment vous avez pu vous en sortir (vis-à-vis des personnes âgées)"}, {"task_ids": ["novembre-2024_c19_t2", "decembre-2024_c13_t2"], "representative_id": "novembre-2024_c19_t2", "task_content": "Vous travaillez dans une association qui aidents les personnes âgées. Rédigez un un article de blog pour raconter vos expériences et convaincre d’autres personnes de rejoindre l’association. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "decembre-2024"], "combination_numbers": ["19", "13"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous travaillez dans une association qui aidents les personnes âgées. rédigez un un article de blog pour raconter vos expériences et convaincre d’autres personnes de rejoindre l’association"}, {"task_ids": ["mars-2025_c2_t2"], "representative_id": "mars-2025_c2_t2", "task_content": "Rédiger un article de blog partageant votre expérience au sein d’une association d’aide aux personnes âgées, en mettant en avant son impact et en encourageant d’autres personnes à s’engager . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un article de blog partageant votre expérience au sein d’une association d’aide aux personnes âgées, en mettant en avant son impact et en encourageant d’autres personnes à s’engager"}, {"task_ids": ["novembre-2024_c2_t2", "juillet-2024_c19_t2", "janvier-2025_c5_t2", "janvier-2025_c11_t2"], "representative_id": "novembre-2024_c2_t2", "task_content": "Vous faites partie d’une association de quartier qui propose des activités aux enfants (aide aux devoirs, sorties, jeux, etc.) Sur votre site internet, vous racontez votre expérience et vous expliquez pourquoi ce type d’association est utile. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025", "janvier-2025"], "combination_numbers": ["2", "19", "5", "11"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous faites partie d’une association de quartier qui propose des activités aux enfants (aide aux devoirs, sorties, jeux, etc.) sur votre site internet, vous racontez votre expérience et vous expliquez pourquoi ce type d’association est utile"}, {"task_ids": ["fevrier-2025_c1_t2", "fevrier-2025_c7_t2", "fevrier-2025_c16_t2"], "representative_id": "fevrier-2025_c1_t2", "task_content": "Tu fais partie d’une association de quartier qui organise des activités pour les enfants (soutien scolaire, sorties, jeux, etc.). Sur le site internet de l’association, tu partages ton expérience et expliques en quoi ce type d’initiative est bénéfique. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025", "fevrier-2025"], "combination_numbers": ["1", "7", "16"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "tu fais partie d’une association de quartier qui organise des activités pour les enfants (soutien scolaire, sorties, jeux, etc.). sur le site internet de l’association, tu partages ton expérience et expliques en quoi ce type d’initiative est bénéfique"}], "name_en": "Opinions on Living with Elderly and Children", "name_fr": "Vivre avec des opinions de personnes âgées/enfants", "name_zh": "与老年人/儿童共同生活观点", "original_name": "living_with_elderly/children_opinions"}, "leave_city_opinions": {"subtopic_id": 7, "task_count": 4, "unique_task_count": 2, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c13_t2", "juillet-2024_c15_t2", "decembre-2024_c17_t2"], "representative_id": "novembre-2024_c13_t2", "task_content": "Vous avez quitté la ville afin de vous installer à la campagne. Sur votre blog, vous expliquez pourquoi vous avez fait ce choix et vous présentez les avantages de votre nouvelle vie. (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "decembre-2024"], "combination_numbers": ["13", "15", "17"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous avez quitté la ville afin de vous installer à la campagne. sur votre blog, vous expliquez pourquoi vous avez fait ce choix et vous présentez les avantages de votre nouvelle vie"}, {"task_ids": ["fevrier-2025_c28_t2"], "representative_id": "fevrier-2025_c28_t2", "task_content": "Après avoir quitté la ville, vous vous êtes installé à la campagne. Sur votre blog, vous expliquez les raisons de ce choix et mettez en avant les bienfaits de votre nouvelle vie . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["28"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "après avoir quitté la ville, vous vous êtes installé à la campagne. sur votre blog, vous expliquez les raisons de ce choix et mettez en avant les bienfaits de votre nouvelle vie"}], "name_en": "Opinions about leaving the city", "name_fr": "laisser des opinions sur la ville", "name_zh": "离开城市观点", "original_name": "leave_city_opinions"}, "social_media_opinions": {"subtopic_id": 8, "task_count": 3, "unique_task_count": 3, "task_ids": ["juin-2025_c2_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["mars-2025_c13_t2"], "representative_id": "mars-2025_c13_t2", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau social préféré (comme Instagram, Facebook, etc.). Dans un texte adressé à vos amis ou vos lecteurs, vous racontez cette expérience . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez décidé d’arrêter d’utiliser votre réseau social préféré (comme instagram, facebook, etc.). dans un texte adressé à vos amis ou vos lecteurs, vous racontez cette expérience"}, {"task_ids": ["aout-2024_c1_t2"], "representative_id": "aout-2024_c1_t2", "task_content": "Vous avez décidé de ne plus utiliser les réseaux sociaux ( twitter Instagram Facebook et autres). Écrivez un message à vos amis en citant les raisons derrière cette décision.", "month_years": ["aout-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez décidé de ne plus utiliser les réseaux sociaux ( twitter instagram facebook et autres). écrivez un message à vos amis en citant les raisons derrière cette décision"}, {"task_ids": ["juin-2024_c2_t2"], "representative_id": "juin-2024_c2_t2", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau social préféré (comme Instagram, Facebook, etc.).Dans un texte adressé à vos amis ou vos lecteurs, vous racontez cette expérience.\n(120 mots minimum/150 mots maximum)", "month_years": ["juin-2024"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez décidé d’arrêter d’utiliser votre réseau social préféré (comme instagram, facebook, etc.).dans un texte adressé à vos amis ou vos lecteurs, vous racontez cette expérience"}], "tasks": [{"id": "juin-2025_c2_t2", "task_content": "Vous avez décidé d’arrêter d’utiliser votre réseau social préféré (comme Instagram, Facebook, etc.).Dans un texte adressé à vos amis ou vos lecteurs, vous racontez cette expérience.\n(120 mots minimum/150 mots maximum)", "month_year": "juin-2025", "combination_number": "2"}], "name_en": "Social Media Opinions", "name_fr": "opinions sur les médias sociaux", "name_zh": "社交媒体观点", "original_name": "social_media_opinions"}, "change_diet_opinions": {"subtopic_id": 9, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2024_c27_t2"], "representative_id": "juillet-2024_c27_t2", "task_content": "Écrivez un article sur votre blog pour raconter pourquoi vous avez décidé de changer votre alimentation (vos habitudes alimentaires).", "month_years": ["juillet-2024"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un article sur votre blog pour raconter pourquoi vous avez décidé de changer votre alimentation (vos habitudes alimentaires)"}], "name_en": "Change Diet Opinions", "name_fr": "changer les opinions sur l'alimentation", "name_zh": "改变饮食观念", "original_name": "change_diet_opinions"}}, "name_en": "Forum Opinions", "name_fr": "opinions du forum", "name_zh": "论坛意见", "original_name": "forum_opinions"}, "Other": {"topic_id": 1, "keywords": ["<PERSON><PERSON><PERSON>", "mail", "écrivez un", "envoyez", "message", "lettre", "rédigez un courriel", "envoyez un message"], "total_tasks": 18, "unique_tasks": 14, "subtopics": {"business_correspondence": {"subtopic_id": 3, "task_count": 7, "unique_task_count": 5, "task_ids": [], "keywords": ["direction", "école", "recherche", "lieu", "organiser"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c18_t2", "juillet-2024_c33_t2"], "representative_id": "novembre-2024_c18_t2", "task_content": "Votre direction est à la recherche d’une salle pour la fête de fin d’année, capable d’accueillir 100 invités. Rédigez un message à la direction pour leur dire que vous avez trouvé un local idéal. (lieu, tarifs, services, etc.). (120 mots minimum/150 mots maximum)", "month_years": ["novembre-2024", "juillet-2024"], "combination_numbers": ["18", "33"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "votre direction est à la recherche d’une salle pour la fête de fin d’année, capable d’accueillir 100 invités. rédigez un message à la direction pour leur dire que vous avez trouvé un local idéal. (lieu, tarifs, services, etc.)"}, {"task_ids": ["fevrier-2025_c3_t2"], "representative_id": "fevrier-2025_c3_t2", "task_content": "La direction d’une école de musique recherche un lieu pouvant accueillir une fête rassemblant 100 personnes. Tu écris un courriel pour leur signaler que tu as trouvé un endroit adapté, en précisant le lieu, les tarifs et les services proposés. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "la direction d’une école de musique recherche un lieu pouvant accueillir une fête rassemblant 100 personnes. tu écris un courriel pour leur signaler que tu as trouvé un endroit adapté, en précisant le lieu, les tarifs et les services proposés"}, {"task_ids": ["fevrier-2025_c33_t2", "mars-2025_c4_t2"], "representative_id": "fevrier-2025_c33_t2", "task_content": "Rédiger un message à la direction pour informer qu’un lieu a été trouvé pour la fête de fin d’année, en précisant le lieu, les tarifs, les services et autres informations pertinentes. (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["33", "4"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédiger un message à la direction pour informer qu’un lieu a été trouvé pour la fête de fin d’année, en précisant le lieu, les tarifs, les services et autres informations pertinentes"}, {"task_ids": ["juillet-2024_c18_t2"], "representative_id": "juillet-2024_c18_t2", "task_content": "La direction d’une école de musique cherche un endroit où organiser une fête pour 100 personnes. Écrivez un mail pour les informer que vous avez trouvé un local (lieu, tarifs, etc…).", "month_years": ["juillet-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "la direction d’une école de musique cherche un endroit où organiser une fête pour 100 personnes. écrivez un mail pour les informer que vous avez trouvé un local (lieu, tarifs, etc…)"}, {"task_ids": ["decembre-2024_c5_t2"], "representative_id": "decembre-2024_c5_t2", "task_content": "Votre direction est à la recherche d’une salle pour la fête de fin d’année, capable d’accueillir 100 invités. Rédigez un message à la direction pour leur dire que vous avez trouvé un local idéal. (Lieu, tarifs et services etc.…) (120 mots minimum/150 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre direction est à la recherche d’une salle pour la fête de fin d’année, capable d’accueillir 100 invités. rédigez un message à la direction pour leur dire que vous avez trouvé un local idéal. (lieu, tarifs et services etc.…)"}], "name_en": "Business Correspondence", "name_fr": "correspondance commerciale", "name_zh": "商务信函", "original_name": "business_correspondence"}, "eating_habits": {"subtopic_id": 0, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": ["amis", "accompagner", "voyage", "visiter", "congé"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c18_t2", "juillet-2024_c6_t2"], "representative_id": "octobre-2024_c18_t2", "task_content": "www.manger-international.com Ce mois-ci, nous nous intéressons aux habitudes alimentaires dans le monde. Racontez-nous comment mangent les habitants de votre pays ! Les 10 premiers témoignages seront publiés sur notre site ! Vous avez lu cette annonce, vous écrivez un article pour les lecteurs du site “www.manger-international.com”. Vous expliquez comment mangent les habitants de votre pays et vous indiquez quelles habitudes vous plaisent ou vous déplaisent, et pourquoi.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["18", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "www.manger-international.com ce mois-ci, nous nous intéressons aux habitudes alimentaires dans le monde. racontez-nous comment mangent les habitants de votre pays ! les 10 premiers témoignages seront publiés sur notre site ! vous avez lu cette annonce, vous écrivez un article pour les lecteurs du site “www.manger-international.com”. vous expliquez comment mangent les habitants de votre pays et vous indiquez quelles habitudes vous plaisent ou vous déplaisent, et pourquoi"}, {"task_ids": ["Mai-2025_c1_t2"], "representative_id": "Mai-2025_c1_t2", "task_content": "Le site www.manger-international.com lance un appel à témoignages. Ce mois-ci, il veut découvrir les habitudes alimentaires dans différents pays. Vous écrivez un article pour parler de celles de votre pays. Dites ce que vous appréciez ou non dans ces habitudes, et expliquez pourquoi . (120 mots minimum/150 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "le site www.manger-international.com lance un appel à témoignages. ce mois-ci, il veut découvrir les habitudes alimentaires dans différents pays. vous écrivez un article pour parler de celles de votre pays. dites ce que vous appréciez ou non dans ces habitudes, et expliquez pourquoi"}], "name_en": "Eating Habits", "name_fr": "habitudes alimentaires", "name_zh": "饮食习惯", "original_name": "eating_habits"}, "countryside_trip": {"subtopic_id": 1, "task_count": 3, "unique_task_count": 2, "task_ids": [], "keywords": ["inviter", "invitation", "accompagner", "concert", "fête"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["mars-2025_c8_t2"], "representative_id": "mars-2025_c8_t2", "task_content": "Rédigez un message sur un forum pour partager votre expérience d’une journée à la campagne avec vos amis. Décrivez les moments marquants, les activités réalisées et ce que vous avez particulièrement apprécié . (120 mots minimum/150 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message sur un forum pour partager votre expérience d’une journée à la campagne avec vos amis. décrivez les moments marquants, les activités réalisées et ce que vous avez particulièrement apprécié"}, {"task_ids": ["aout-2024_c4_t2", "decembre-2024_c3_t2"], "representative_id": "aout-2024_c4_t2", "task_content": "Vous avez passé une journée à la campagne avec vos amis. À votre retour, vous écrivez un message sur votre forum pour raconter à vos amis comment cette journée s’est passée. Vous expliquez ce que vous avez aimé (activités, lieu, animaux, etc…).", "month_years": ["aout-2024", "decembre-2024"], "combination_numbers": ["4", "3"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez passé une journée à la campagne avec vos amis. à votre retour, vous écrivez un message sur votre forum pour raconter à vos amis comment cette journée s’est passée. vous expliquez ce que vous avez aimé (activités, lieu, animaux, etc…)"}], "name_en": "Countryside Trip", "name_fr": "voyage à la campagne", "name_zh": "乡村之旅", "original_name": "countryside_trip"}, "picnic_with_colleagues": {"subtopic_id": 5, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c16_t2"], "representative_id": "avril-2025_c16_t2", "task_content": "Rédiger un article pour le journal de l’entreprise sur un pique-nique entre collègues. (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un article pour le journal de l’entreprise sur un pique-nique entre collègues"}], "name_en": "Picnic with Colleagues", "name_fr": "pique-nique avec des collègues", "name_zh": "与同事野餐", "original_name": "picnic_with_colleagues"}, "introduce_careers": {"subtopic_id": 6, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c6_t2"], "representative_id": "septembre-2024_c6_t2", "task_content": "Les professeurs de l’école de votre quartier souhaitent présenter différents métiers aux élèves. Vous voulez participer à ce projet pour parler de votre expérience. Vous leur écrivez. Vous décrivez votre profession (activités, collègues, etc.). Vous expliquez pourquoi vous trouvez votre métier intéressant.", "month_years": ["septembre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "les professeurs de l’école de votre quartier souhaitent présenter différents métiers aux élèves. vous voulez participer à ce projet pour parler de votre expérience. vous leur écrivez. vous décrivez votre profession (activités, collègues, etc.). vous expliquez pourquoi vous trouvez votre métier intéressant"}], "name_en": "Introduce Careers", "name_fr": "présenter les carrières", "name_zh": "介绍职业", "original_name": "introduce_careers"}, "trainning_day_post": {"subtopic_id": 7, "task_count": 2, "unique_task_count": 2, "task_ids": ["juin-2025_c3_t2"], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["avril-2025_c17_t2"], "representative_id": "avril-2025_c17_t2", "task_content": "Après avoir participé à une journée de formation au sein de votre entreprise, rédigez un message à publier sur le forum interne. Partagez votre expérience avec vos collègues . (120 mots minimum/150 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "après avoir participé à une journée de formation au sein de votre entreprise, rédigez un message à publier sur le forum interne. partagez votre expérience avec vos collègues"}, {"task_ids": ["juin-2024_c3_t2"], "representative_id": "juin-2024_c3_t2", "task_content": "Après avoir participé à une journée de formation au sein de votre entreprise,rédigez un message à publier sur le forum interne. Partagez votre expérience avec vos collègues.\n(120 mots minimum/150 mots maximum)", "month_years": ["juin-2024"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "après avoir participé à une journée de formation au sein de votre entreprise,rédigez un message à publier sur le forum interne. partagez votre expérience avec vos collègues"}], "tasks": [{"id": "juin-2025_c3_t2", "task_content": "Après avoir participé à une journée de formation au sein de votre entreprise,rédigez un message à publier sur le forum interne. Partagez votre expérience avec vos collègues.\n(120 mots minimum/150 mots maximum)", "month_year": "juin-2025", "combination_number": "3"}], "name_en": "Training Day Post", "name_fr": "Journée de formation après", "name_zh": "培训日岗位", "original_name": "trainning_day_post"}, "rent_apartment": {"subtopic_id": 8, "task_count": 1, "unique_task_count": 1, "task_ids": [], "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c17_t2"], "representative_id": "fevrier-2025_c17_t2", "task_content": "Vous avez publié une annonce en ligne pour louer votre appartement. Rédigez un courriel afin de fournir des informations sur le logement et le quartier . (120 mots minimum/150 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez publié une annonce en ligne pour louer votre appartement. rédigez un courriel afin de fournir des informations sur le logement et le quartier"}], "name_en": "Rent Apartment", "name_fr": "Louer un appartement", "name_zh": "租房公寓", "original_name": "rent_apartment"}}, "name_en": "Alternative", "name_fr": "<PERSON><PERSON>", "name_zh": "其他", "original_name": "Other"}}}}