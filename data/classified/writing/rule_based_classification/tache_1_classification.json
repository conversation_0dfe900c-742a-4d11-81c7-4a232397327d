{"task_number": 1, "method": "predefined_classification_with_deduplication", "total_tasks": 206, "unique_tasks": 138, "duplicate_count": 68, "n_main_topics": 5, "main_topics": {"recommendation": {"topic_id": 0, "keywords": ["recommand", "conseil", "<PERSON>gg<PERSON><PERSON>", "propose", "avis", "choix", "meilleur", "excellent", "bon", "bonne", "qualité", "fortement"], "total_tasks": 21, "unique_tasks": 14, "subtopics": {"recommendation_marketplace": {"subtopic_id": 0, "task_count": 5, "unique_task_count": 3, "keywords": ["acheter", "produit", "magasin", "marché", "commerce", "vêtements", "sélection", "prix", "compé<PERSON><PERSON><PERSON>"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c5_t1", "decembre-2024_c3_t1", "decembre-2024_c10_t1"], "representative_id": "septembre-2024_c5_t1", "task_content": "Je cherche un vélo en bon état et bon marché. Contactez-moi par courriel\n<EMAIL>\nVous avez un vélo à vendre.Vous écrivez un courriel pour décrire votre vélo et proposer un prix. Vous lui donnez un rendez-vous pour essayer le vélo.", "month_years": ["septembre-2024", "decembre-2024", "decembre-2024"], "combination_numbers": ["5", "3", "10"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "je cherche un vélo en bon état et bon marché. contactez-moi <NAME_EMAIL> vous avez un vélo à vendre.vous écrivez un courriel pour décrire votre vélo et proposer un prix. vous lui donnez un rendez-vous pour essayer le vélo"}, {"task_ids": ["aout-2024_c4_t1"], "representative_id": "aout-2024_c4_t1", "task_content": "« Je cherche un vélo en bon état et bon marché. Contactez-\nmoi par courriel : <EMAIL>\nVous avez un vélo à vendre. Vous écrivez un courriel pour\ndécrire votre vélo et proposer un prix. Vous lui Donnez un RDV\npour essayer le vélo.", "month_years": ["aout-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« je cherche un vélo en bon état et bon marché. contactez- moi par courriel : <EMAIL> vous avez un vélo à vendre. vous écrivez un courriel pour décrire votre vélo et proposer un prix. vous lui donnez un rdv pour essayer le vélo"}, {"task_ids": ["fevrier-2025_c3_t1"], "representative_id": "fevrier-2025_c3_t1", "task_content": "Ton ami(e) va bientôt célébrer son anniversaire.Tu écris un message à tes amis pour leur proposer d’acheter un cadeau commun.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ton ami(e) va bientôt célébrer son anniversaire.tu écris un message à tes amis pour leur proposer d’acheter un cadeau commun"}]}, "recommendation_travel": {"subtopic_id": 1, "task_count": 6, "unique_task_count": 3, "keywords": ["voyage", "destination", "vacances", "<PERSON><PERSON><PERSON>", "partir", "nice", "paris", "ville", "confortable"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c7_t1", "janvier-2025_c3_t1", "janvier-2025_c15_t1"], "representative_id": "novembre-2024_c7_t1", "task_content": "Votre ami <PERSON> vient de d’emménager dans votre ville et cherche des renseignements sur les moyens de transports.Écrivez un message en lui donnant les informations nécessaires (types de transport, abonnement, tarif, etc.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "janvier-2025", "janvier-2025"], "combination_numbers": ["7", "3", "15"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "votre ami mehdi vient de d’emménager dans votre ville et cherche des renseignements sur les moyens de transports.écrivez un message en lui donnant les informations nécessaires (types de transport, abonnement, tarif, etc"}, {"task_ids": ["fevrier-2025_c11_t1", "mars-2025_c12_t1"], "representative_id": "fevrier-2025_c11_t1", "task_content": "Votre ami <PERSON> vient d’arriver dans votre ville et cherche des informations sur les moyens de transport.Écrivez-lui un message pour lui fournir toutes les informations utiles, telles que les options de transport disponibles, les tarifs, les abonnements, etc.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["11", "12"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "votre ami mehdi vient d’arriver dans votre ville et cherche des informations sur les moyens de transport.écrivez-lui un message pour lui fournir toutes les informations utiles, telles que les options de transport disponibles, les tarifs, les abonnements, etc"}, {"task_ids": ["avril-2025_c1_t1"], "representative_id": "avril-2025_c1_t1", "task_content": "<PERSON><PERSON><PERSON>,\nJe viens bientôt en vacances dans ta région. Est-ce que tu peux me conseiller une visite à faire pendant mon voyage ?\nMerc<PERSON> beaucoup.\n<PERSON><PERSON>\nVous répondez à votre ami <PERSON>. Vous décrivez un lieu à visiter dans votre région.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "bonjour, je viens bientôt en vacances dans ta région. est-ce que tu peux me conseiller une visite à faire pendant mon voyage ? merci beaucoup. mathieu vous répondez à votre ami mathieu. vous décrivez un lieu à visiter dans votre région"}]}, "recommendation_general": {"subtopic_id": 2, "task_count": 9, "unique_task_count": 7, "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c10_t1", "juillet-2024_c26_t1", "janvier-2025_c8_t1"], "representative_id": "novembre-2024_c10_t1", "task_content": "Votre ami(e) veut découvrir la région dans laquelle vous habitez.Écrivez lui un message pour lui proposer des sites à visiter.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025"], "combination_numbers": ["10", "26", "8"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "votre ami(e) veut découvrir la région dans laquelle vous habitez.écrivez lui un message pour lui proposer des sites à visiter"}, {"task_ids": ["fevrier-2025_c13_t1"], "representative_id": "fevrier-2025_c13_t1", "task_content": "Votre ami(e) souhaite découvrir votre région.Rédigez un message pour lui recommander des sites intéressants à visiter.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami(e) souhaite découvrir votre région.rédigez un message pour lui recommander des sites intéressants à visiter"}, {"task_ids": ["fevrier-2025_c29_t1"], "representative_id": "fevrier-2025_c29_t1", "task_content": "Les enseignants de votre quartier projettent d’organiser une rencontre pour guider leurs élèves dans le choix de leur futur métier.Rédigez un courriel pour leur exposer les atouts de votre profession.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "les enseignants de votre quartier projettent d’organiser une rencontre pour guider leurs élèves dans le choix de leur futur métier.rédigez un courriel pour leur exposer les atouts de votre profession"}, {"task_ids": ["juillet-2024_c2_t1"], "representative_id": "juillet-2024_c2_t1", "task_content": "Vous souhaitez faire du sport et vous voulez que votre ami vous accompagne.Écrivez lui un message pour lui proposer de pratiquer ensemble.", "month_years": ["juillet-2024"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez faire du sport et vous voulez que votre ami vous accompagne.écrivez lui un message pour lui proposer de pratiquer ensemble"}, {"task_ids": ["juillet-2024_c27_t1"], "representative_id": "juillet-2024_c27_t1", "task_content": "Vous voulez passer un week-end avec vos amis et faire du sport.Vous leur écrivez une lettre pour proposer ça en citant (date, lieu, programme.).", "month_years": ["juillet-2024"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez passer un week-end avec vos amis et faire du sport.vous leur écrivez une lettre pour proposer ça en citant (date, lieu, programme.)"}, {"task_ids": ["decembre-2024_c16_t1"], "representative_id": "decembre-2024_c16_t1", "task_content": "Les professeurs de votre quartier veulent organiser une rencontre avec leurs élèves pour les orienter dans le choix de leurs futures professions, vous leur écrivez un mail pour leur donner les avantages de votre profession.\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "les professeurs de votre quartier veulent organiser une rencontre avec leurs élèves pour les orienter dans le choix de leurs futures professions, vous leur écrivez un mail pour leur donner les avantages de votre profession"}, {"task_ids": ["avril-2025_c19_t1"], "representative_id": "avril-2025_c19_t1", "task_content": "Rédigez un message destiné à un ami pour lui proposer de faire du sport ensemble.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message destiné à un ami pour lui proposer de faire du sport ensemble"}]}, "recommendation_entertainment": {"subtopic_id": 3, "task_count": 1, "unique_task_count": 1, "keywords": ["sortir", "activité", "spectacle", "concert", "cinéma", "divertissement"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["decembre-2024_c5_t1"], "representative_id": "decembre-2024_c5_t1", "task_content": "Écrivez un courriel à vos amis pour les inviter à un anniversaire surpris pour votre meilleure ami(e), et proposez de lui offrir un cadeau commun(lieu cadeau, activités, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un courriel à vos amis pour les inviter à un anniversaire surpris pour votre meilleure ami(e), et proposez de lui offrir un cadeau commun(lieu cadeau, activités, etc.)"}]}}}, "sharing_information": {"topic_id": 1, "keywords": ["nouvelles", "racont", "partag", "expérience", "inform", "nouvelle", "savoir", "voulais", "commencé"], "total_tasks": 52, "unique_tasks": 34, "subtopics": {"sharing_work_news": {"subtopic_id": 0, "task_count": 6, "unique_task_count": 3, "keywords": ["travail", "emploi", "bureau", "coll<PERSON><PERSON>", "entreprise", "technologie"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juin-2025_c1_t1", "mars-2025_c11_t1"], "representative_id": "juin-2025_c1_t1", "task_content": "Vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone.Décrivez votre travail, vos collègues et votre environnement professionnel.\n(60 mots minimum/120 mots maximum)", "month_years": ["juin-2025", "mars-2025"], "combination_numbers": ["1", "11"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous venez d’obtenir un nouveau poste et souhaitez annoncer la nouvelle à votre amie francophone.décrivez votre travail, vos collègues et votre environnement professionnel"}, {"task_ids": ["fevrier-2025_c5_t1", "fevrier-2025_c21_t1"], "representative_id": "fevrier-2025_c5_t1", "task_content": "Ali t’a envoyé un message pour savoir comment se passe ton nouveau travail et si tu en es satisfait(e).Tu lui réponds en décrivant ton emploi (lieu, ambiance, collègues, etc.) et en partageant tes impressions.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["5", "21"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "ali t’a envoyé un message pour savoir comment se passe ton nouveau travail et si tu en es satisfait(e).tu lui réponds en décrivant ton emploi (lieu, ambiance, collègues, etc.) et en partageant tes impressions"}, {"task_ids": ["fevrier-2025_c17_t1", "fevrier-2025_c18_t1"], "representative_id": "fevrier-2025_c17_t1", "task_content": "Envoyez un message à votre ami(e) pour lui partager les détails de votre entretien d’embauche, comme l’entreprise, le salaire proposé, le type de poste, etc.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["17", "18"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "envoyez un message à votre ami(e) pour lui partager les détails de votre entretien d’embauche, comme l’entreprise, le salaire proposé, le type de poste, etc"}]}, "sharing_study_info": {"subtopic_id": 1, "task_count": 26, "unique_task_count": 16, "keywords": ["université", "cours", "études", "formation", "école"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juin-2025_c2_t1", "mars-2025_c13_t1"], "representative_id": "juin-2025_c2_t1", "task_content": "Vous avez reçu un message d’un ami Alex qui vous demande des nouvelles de votre nouvelle université.Vous lui répondre en décrivant brièvement votre environnement universitaire.\n(60 mots minimum/120 mots maximum)", "month_years": ["juin-2025", "mars-2025"], "combination_numbers": ["2", "13"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez reçu un message d’un ami alex qui vous demande des nouvelles de votre nouvelle université.vous lui répondre en décrivant brièvement votre environnement universitaire"}, {"task_ids": ["juin-2025_c3_t1", "avril-2025_c17_t1"], "representative_id": "juin-2025_c3_t1", "task_content": "Rédigez un email pour inviter un ami à passer une journée avec vous, en incluant les informations essentielles telles que le lieu, la date, l’heure, et les activités prévues pour la journée.\n(60 mots minimum/120 mots maximum)", "month_years": ["juin-2025", "avril-2025"], "combination_numbers": ["3", "17"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un email pour inviter un ami à passer une journée avec vous, en incluant les informations essentielles telles que le lieu, la date, l’heure, et les activités prévues pour la journée"}, {"task_ids": ["septembre-2024_c1_t1", "aout-2024_c2_t1"], "representative_id": "septembre-2024_c1_t1", "task_content": "Vous avez publié une annonce pour la location de votre appartement.Rédigez un courriel à une personne intéressée en lui fournissant des informations sur l’appartement ainsi que sur le quartier.", "month_years": ["septembre-2024", "aout-2024"], "combination_numbers": ["1", "2"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez publié une annonce pour la location de votre appartement.rédigez un courriel à une personne intéressée en lui fournissant des informations sur l’appartement ainsi que sur le quartier"}, {"task_ids": ["septembre-2024_c6_t1"], "representative_id": "septembre-2024_c6_t1", "task_content": "Vous souhaitez aller au concert de votre artiste préféré(e). Vous proposez à votre ami(e) de vous accompagner.Vous lui écrivez un courriel avec les informations utiles (date, prix, lieu, artiste, durée).", "month_years": ["septembre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez aller au concert de votre artiste préféré(e). vous proposez à votre ami(e) de vous accompagner.vous lui écrivez un courriel avec les informations utiles (date, prix, lieu, artiste, durée)"}, {"task_ids": ["octobre-2024_c4_t1"], "representative_id": "octobre-2024_c4_t1", "task_content": "vous avez mis en ligne une annonce pour la location de votre appartement.Écrivez un courriel à une personne intéressée pour lui donner les informations sur l’appartement et le quartier", "month_years": ["octobre-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez mis en ligne une annonce pour la location de votre appartement.écrivez un courriel à une personne intéressée pour lui donner les informations sur l’appartement et le quartier"}, {"task_ids": ["juillet-2025_c3_t1", "fevrier-2025_c30_t1", "mars-2025_c7_t1", "avril-2025_c5_t1"], "representative_id": "juillet-2025_c3_t1", "task_content": "Rédiger un message en réponse à un ami qui demande des informations sur une salle de sport réputée pour sa qualité.\n(60 mots minimum/120 mots maximum)", "month_years": ["juillet-2025", "fevrier-2025", "mars-2025", "avril-2025"], "combination_numbers": ["3", "30", "7", "5"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "rédiger un message en réponse à un ami qui demande des informations sur une salle de sport réputée pour sa qualité"}, {"task_ids": ["fevrier-2025_c1_t1", "fevrier-2025_c7_t1", "fevrier-2025_c16_t1"], "representative_id": "fevrier-2025_c1_t1", "task_content": "<PERSON> t’informe qu’il va bientôt habiter dans ton quartier et qu’il cherche un bon marché pour faire ses courses.Tu lui réponds en lui décrivant un marché que tu apprécies particulièrement, en précisant son emplacement, ses horaires et les produits qu’on peut y trouver.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025", "fevrier-2025"], "combination_numbers": ["1", "7", "16"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "be<PERSON>rd t’informe qu’il va bientôt habiter dans ton quartier et qu’il cherche un bon marché pour faire ses courses.tu lui réponds en lui décrivant un marché que tu apprécies particulièrement, en précisant son emplacement, ses horaires et les produits qu’on peut y trouver"}, {"task_ids": ["fevrier-2025_c4_t1"], "representative_id": "fevrier-2025_c4_t1", "task_content": "Tu aimerais assister à un festival de cinéma organisé dans ta ville. Tu écris un message à ton ami(e) pour lui proposer de t’accompagner.Tu lui donnes toutes les informations nécessaires sur l’événement, notamment les films projetés, les dates et horaires, ainsi que les tarifs d’entrée.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu aimerais assister à un festival de cinéma organisé dans ta ville. tu écris un message à ton ami(e) pour lui proposer de t’accompagner.tu lui donnes toutes les informations nécessaires sur l’événement, notamment les films projetés, les dates et horaires, ainsi que les tarifs d’entrée"}, {"task_ids": ["fevrier-2025_c9_t1"], "representative_id": "fevrier-2025_c9_t1", "task_content": "« Bonjour, bonne nouvelle ! J’ai enfin obtenu mon visa pour le Canada. Mon arrivée est prévue pour le 3 mars. Pourrais-tu m’aider à trouver un hôtel pour ma première semaine sur place ? Merci beaucoup pour ton aide ! »\n<PERSON>.\nVous avez réservé un hôtel pour <PERSON> et lui envoyez un courriel contenant une description détaillée de l’établissement ainsi que toutes les informations essentielles, telles que son emplacement, le prix et les services proposés.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« bonjour, bonne nouvelle ! j’ai enfin obtenu mon visa pour le canada. mon arrivée est prévue pour le 3 mars. pourrais-tu m’aider à trouver un hôtel pour ma première semaine sur place ? merci beaucoup pour ton aide ! » matthias. vous avez réservé un hôtel pour matthias et lui envoyez un courriel contenant une description détaillée de l’établissement ainsi que toutes les informations essentielles, telles que son emplacement, le prix et les services proposés"}, {"task_ids": ["fevrier-2025_c12_t1"], "representative_id": "fevrier-2025_c12_t1", "task_content": "Vous avez invité votre ami Cédric à votre mariage au Château de Chombony, mais il vous a répondu qu’il ne connaît pas ce château.Décrivez-lui le lieu en précisant son emplacement, les transports à prendre, et d’autres informations utiles.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez invité votre ami cédric à votre mariage au château de chombony, mais il vous a répondu qu’il ne connaît pas ce château.décrivez-lui le lieu en précisant son emplacement, les transports à prendre, et d’autres informations utiles"}, {"task_ids": ["mars-2025_c2_t1"], "representative_id": "mars-2025_c2_t1", "task_content": "Rédiger un message à un ami intéressé par des cours de langue dans votre école,en lui fournissant des informations utiles sur les programmes proposés, les tarifs et le lieu.\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un message à un ami intéressé par des cours de langue dans votre école,en lui fournissant des informations utiles sur les programmes proposés, les tarifs et le lieu"}, {"task_ids": ["juillet-2024_c1_t1", "juillet-2024_c35_t1"], "representative_id": "juillet-2024_c1_t1", "task_content": "Vous souhaitez assister à un festival de cinéma dans votre ville.Vous écrivez un message à votre ami(e) pour lui proposer de venir avec vous. Vous lui donnez toutes les informations nécessaires sur l’événement (films, dates et horaires, tarifs, etc.).", "month_years": ["juillet-2024", "juillet-2024"], "combination_numbers": ["1", "35"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous souhaitez assister à un festival de cinéma dans votre ville.vous écrivez un message à votre ami(e) pour lui proposer de venir avec vous. vous lui donnez toutes les informations nécessaires sur l’événement (films, dates et horaires, tarifs, etc.)"}, {"task_ids": ["juillet-2024_c3_t1"], "representative_id": "juillet-2024_c3_t1", "task_content": "« Salut ! Je sais que tu fais du sport depuis le mois dernier. Ça m’intéresse beaucoup et j’aimerais bien venir avec toi. Tu peux m’en dire plus ? A bientôt ! Camille »\nVous répondez à votre ami Camille. Dans votre message, vous décrivez votre activité sportive et vous donnez des informations utiles (lieu, durée, prix, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« salut ! je sais que tu fais du sport depuis le mois dernier. ça m’intéresse beaucoup et j’aimerais bien venir avec toi. tu peux m’en dire plus ? a bientôt ! camille » vous répondez à votre ami camille. dans votre message, vous décrivez votre activité sportive et vous donnez des informations utiles (lieu, durée, prix, etc.)"}, {"task_ids": ["juillet-2024_c13_t1", "janvier-2025_c1_t1"], "representative_id": "juillet-2024_c13_t1", "task_content": "Je suis votre amie Anna et je compte passer un weekend dans ta ville.Donnez-moi des informations sur les moyens de transport pour explorer la ville. Répondez à Anna dans un message.", "month_years": ["juillet-2024", "janvier-2025"], "combination_numbers": ["13", "1"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "je suis votre amie anna et je compte passer un weekend dans ta ville.donnez-moi des informations sur les moyens de transport pour explorer la ville. répondez à anna dans un message"}, {"task_ids": ["avril-2025_c10_t1"], "representative_id": "avril-2025_c10_t1", "task_content": "Envoyez un courriel à vos amis pour les inviter à fêter votre anniversaire dans un restaurant. Incluez toutes les informations nécessaires commele lieu, la date, le menu, les prix, et demandez-leur de confirmer leur présence.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "envoyez un courriel à vos amis pour les inviter à fêter votre anniversaire dans un restaurant. incluez toutes les informations nécessaires commele lieu, la date, le menu, les prix, et demandez-leur de confirmer leur présence"}, {"task_ids": ["avril-2025_c16_t1"], "representative_id": "avril-2025_c16_t1", "task_content": "Votre ami vous demande des informations sur votre école de langue.Vous devez lui répondre, en détaillant votre expérience dans cette école.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami vous demande des informations sur votre école de langue.vous devez lui répondre, en détaillant votre expérience dans cette école"}]}, "sharing_information_general": {"subtopic_id": 2, "task_count": 9, "unique_task_count": 6, "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c7_t1", "octobre-2024_c3_t1"], "representative_id": "septembre-2024_c7_t1", "task_content": "Vous faites du sport dans un club. Vous venez de remporter une compétition Vous écrivez un courriel à vos amis pour leur raconter cet évènement sportif et annoncer votre réussite sportive.", "month_years": ["septembre-2024", "octobre-2024"], "combination_numbers": ["7", "3"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous faites du sport dans un club. vous venez de remporter une compétition vous écrivez un courriel à vos amis pour leur raconter cet évènement sportif et annoncer votre réussite sportive"}, {"task_ids": ["septembre-2024_c9_t1"], "representative_id": "septembre-2024_c9_t1", "task_content": "Ecrivez un message à vos amis pour les inviter à votre anniversaire et leur raconter comment va se dérouler votre fête.", "month_years": ["septembre-2024"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ecrivez un message à vos amis pour les inviter à votre anniversaire et leur raconter comment va se dérouler votre fête"}, {"task_ids": ["octobre-2024_c6_t1"], "representative_id": "octobre-2024_c6_t1", "task_content": "écrivez un message à votre ami pour lui raconter votre entretien d’embauche", "month_years": ["octobre-2024"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour lui raconter votre entretien d’embauche"}, {"task_ids": ["fevrier-2025_c6_t1", "fevrier-2025_c15_t1"], "representative_id": "fevrier-2025_c6_t1", "task_content": "<PERSON> t’a envoyé un message pour savoir où elle pourrait déjeuner en plein air ce week-end.Tu lui réponds en décrivant un lieu agréable (parc, jardin, terrasse, etc.) et ses caractéristiques.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "fevrier-2025"], "combination_numbers": ["6", "15"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "barbara t’a envoyé un message pour savoir où elle pourrait déjeuner en plein air ce week-end.tu lui réponds en décrivant un lieu agréable (parc, jardin, terrasse, etc.) et ses caractéristiques"}, {"task_ids": ["fevrier-2025_c31_t1", "mars-2025_c6_t1"], "representative_id": "fevrier-2025_c31_t1", "task_content": "France Télévisions réalise un reportage sur le sport amateur et invite les passionnésà partager leur expérience en tant que sportifs sur francetélévision.fr.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["31", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "france télévisions réalise un reportage sur le sport amateur et invite les passionnésà partager leur expérience en tant que sportifs sur francetélévision.fr"}, {"task_ids": ["avril-2025_c6_t1"], "representative_id": "avril-2025_c6_t1", "task_content": "Vous pratiquez un sport dans un club et vous venez de gagner une compétition.Écrivez un courriel à vos amis pour leur raconter ce qui s’est passé et partager votre joie.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["6"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous pratiquez un sport dans un club et vous venez de gagner une compétition.écrivez un courriel à vos amis pour leur raconter ce qui s’est passé et partager votre joie"}]}, "sharing_general_info": {"subtopic_id": 3, "task_count": 3, "unique_task_count": 2, "keywords": ["équipements", "nouveaux", "piscine", "fitness", "quartier"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c19_t1", "juillet-2024_c8_t1"], "representative_id": "octobre-2024_c19_t1", "task_content": "Vous avez déménagé dans une nouvelle ville il y a un mois. Rédigez un courriel à votre ami Léo pour lui donner de vos nouvelles.Dans votre message, décrivez votre appartement, le quartier, la ville, ainsi que les activités que vous avez découvertes.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["19", "8"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez déménagé dans une nouvelle ville il y a un mois. rédigez un courriel à votre ami léo pour lui donner de vos nouvelles.dans votre message, décrivez votre appartement, le quartier, la ville, ainsi que les activités que vous avez découvertes"}, {"task_ids": ["mars-2025_c3_t1"], "representative_id": "mars-2025_c3_t1", "task_content": "Rédiger une réponse à un courriel d’un ami demandant des informations sur les nouveaux locaux de votre entreprise,en décrivant leur emplacement, l’aménagement des espaces et les équipements disponibles.\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger une réponse à un courriel d’un ami demandant des informations sur les nouveaux locaux de votre entreprise,en décrivant leur emplacement, l’aménagement des espaces et les équipements disponibles"}]}, "sharing_travel_experience": {"subtopic_id": 4, "task_count": 3, "unique_task_count": 3, "keywords": ["voyage", "vacances", "destination", "découverte", "paris", "visité", "magnifique"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c11_t1"], "representative_id": "novembre-2024_c11_t1", "task_content": "Vous avez récemment déménagé dans une nouvelle ville.Invitez votre amie à venir passer les vacances chez vous.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez récemment déménagé dans une nouvelle ville.invitez votre amie à venir passer les vacances chez vous"}, {"task_ids": ["juillet-2024_c36_t1"], "representative_id": "juillet-2024_c36_t1", "task_content": "L’anniversaire de votre ami approche à grands pas et vous avez décidé de lui offrir un voyage comme cadeau.Rédigez un message pour l’informer des préparations que vous avez faites (destination, dates, préparatifs, etc).", "month_years": ["juillet-2024"], "combination_numbers": ["36"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "l’anniversaire de votre ami approche à grands pas et vous avez décidé de lui offrir un voyage comme cadeau.rédigez un message pour l’informer des préparations que vous avez faites (destination, dates, préparatifs, etc)"}, {"task_ids": ["avril-2025_c14_t1"], "representative_id": "avril-2025_c14_t1", "task_content": "Vous venez d’emménager dans une nouvelle ville.Rédigez un message pour inviter une amie à venir passer les vacances chez vous.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez d’emménager dans une nouvelle ville.rédigez un message pour inviter une amie à venir passer les vacances chez vous"}]}, "sharing_personal_news": {"subtopic_id": 5, "task_count": 2, "unique_task_count": 2, "keywords": ["famille", "personnel", "vie", "nouveau", "changement"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["Mai-2025_c2_t1"], "representative_id": "Mai-2025_c2_t1", "task_content": "Vous habitez dans une nouvelle ville depuis un mois.Écrivez un message à votre ami Léo pour lui raconter comment se passe votre nouvelle vie : votre appartement, votre quartier, la ville en général, et ce que vous aimez y faire.\n(60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous habitez dans une nouvelle ville depuis un mois.écrivez un message à votre ami léo pour lui raconter comment se passe votre nouvelle vie : votre appartement, votre quartier, la ville en général, et ce que vous aimez y faire"}, {"task_ids": ["avril-2025_c11_t1"], "representative_id": "avril-2025_c11_t1", "task_content": "Rédigez un courriel à vos amis pour les convier à une fête d’anniversaire surprise organisée en l’honneur de votre meilleur ami.Mentionnez les détails essentiels : lieu, date, heure et toute autre information importante.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel à vos amis pour les convier à une fête d’anniversaire surprise organisée en l’honneur de votre meilleur ami.mentionnez les détails essentiels : lieu, date, heure et toute autre information importante"}]}, "sharing_event_experience": {"subtopic_id": 6, "task_count": 3, "unique_task_count": 2, "keywords": ["événement", "spectacle", "concert", "visite", "sortie", "louvre", "tour", "eiffel"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["fevrier-2025_c33_t1", "mars-2025_c4_t1"], "representative_id": "fevrier-2025_c33_t1", "task_content": "Écrire un message à un(e) ami(e) pour raconter son week-end à la campagne en détaillant les événements qui se sont déroulés.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["33", "4"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "écrire un message à un(e) ami(e) pour raconter son week-end à la campagne en détaillant les événements qui se sont déroulés"}, {"task_ids": ["mars-2025_c14_t1"], "representative_id": "mars-2025_c14_t1", "task_content": "Votre amie Anna prévoit de passer un week-end dans votre ville et souhaite en savoir plus sur les moyens de transport disponibles pour visiter les environs.Rédigez-lui un message en détaillant les différentes options (bus, métro, vélo, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre amie anna prévoit de passer un week-end dans votre ville et souhaite en savoir plus sur les moyens de transport disponibles pour visiter les environs.rédigez-lui un message en détaillant les différentes options (bus, métro, vélo, etc.)"}]}}}, "looking_for_service": {"topic_id": 2, "keywords": ["aide", "demande", "service", "assistance", "cherche", "besoin", "problème", "j'ai besoin", "pouvez-vous"], "total_tasks": 39, "unique_tasks": 24, "subtopics": {"looking_for_language_help": {"subtopic_id": 0, "task_count": 10, "unique_task_count": 4, "keywords": ["français", "langue", "pratiquer", "apprendre", "conversation", "partenaires", "linguistiques"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c4_t1", "octobre-2024_c14_t1", "juillet-2024_c5_t1", "decembre-2024_c9_t1"], "representative_id": "septembre-2024_c4_t1", "task_content": "Vous avez lu une annonce sur un site internet qui propose l’aide aux personnes qui souhaitent apprendre le français et les aider à trouver des personnes avec lesquelles elles peuvent pratiquer le français et qui leur permettront d’améliorer leur niveau.Envoyez un courriel pour répondre à cette annonce tout en vous présentant et en expliquant pourquoi vouloir pratiquer cette langue.", "month_years": ["septembre-2024", "octobre-2024", "juillet-2024", "decembre-2024"], "combination_numbers": ["4", "14", "5", "9"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous avez lu une annonce sur un site internet qui propose l’aide aux personnes qui souhaitent apprendre le français et les aider à trouver des personnes avec lesquelles elles peuvent pratiquer le français et qui leur permettront d’améliorer leur niveau.envoyez un courriel pour répondre à cette annonce tout en vous présentant et en expliquant pourquoi vouloir pratiquer cette langue"}, {"task_ids": ["novembre-2024_c9_t1", "juillet-2024_c25_t1", "janvier-2025_c7_t1", "decembre-2024_c15_t1"], "representative_id": "novembre-2024_c9_t1", "task_content": "Écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025", "decembre-2024"], "combination_numbers": ["9", "25", "7", "15"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "écrivez un message dans le journal de votre université pour rechercher un partenaire avec qui faire du sport"}, {"task_ids": ["fevrier-2025_c14_t1"], "representative_id": "fevrier-2025_c14_t1", "task_content": "Rédigez un message à publier dans le journal de votre université afin de rechercher un partenaire avec qui pratiquer une activité sportive.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à publier dans le journal de votre université afin de rechercher un partenaire avec qui pratiquer une activité sportive"}, {"task_ids": ["janvier-2025_c17_t1"], "representative_id": "janvier-2025_c17_t1", "task_content": "Vous avez lu une annonce sur un site internet qui propose d’aider ceux qui souhaitent apprendre le français en les mettant en contact avec des partenaires linguistiques.Envoyez un courriel en vous présentant et en expliquant pourquoi vous souhaitez pratiquer le français.\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez lu une annonce sur un site internet qui propose d’aider ceux qui souhaitent apprendre le français en les mettant en contact avec des partenaires linguistiques.envoyez un courriel en vous présentant et en expliquant pourquoi vous souhaitez pratiquer le français"}]}, "looking_for_help": {"subtopic_id": 1, "task_count": 23, "unique_task_count": 15, "keywords": ["aide", "assistance", "problème", "difficile", "besoin", "pouvez-vous"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["aout-2024_c5_t1", "decembre-2024_c2_t1"], "representative_id": "aout-2024_c5_t1", "task_content": "Vous voulez changer la décoration de votre appartement (meubles, peinture, objets, etc.).Vous écrivez un message à un(e) ami(e). Vous lui décrivez votre projet et vous lui demandez de vous aider.", "month_years": ["aout-2024", "decembre-2024"], "combination_numbers": ["5", "2"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous voulez changer la décoration de votre appartement (meubles, peinture, objets, etc.).vous écrivez un message à un(e) ami(e). vous lui décrivez votre projet et vous lui demandez de vous aider"}, {"task_ids": ["octobre-2024_c15_t1", "juillet-2024_c17_t1"], "representative_id": "octobre-2024_c15_t1", "task_content": "Vous organisez un événement. Écrivez une lettre à vos amis pour les inviter à cet événement, et pour leur demander de vous aider dans l’organisation(en précisant la date, le lieu, l’horaire…).", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["15", "17"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous organisez un événement. écrivez une lettre à vos amis pour les inviter à cet événement, et pour leur demander de vous aider dans l’organisation(en précisant la date, le lieu, l’horaire…)"}, {"task_ids": ["octobre-2024_c18_t1", "juillet-2024_c6_t1"], "representative_id": "octobre-2024_c18_t1", "task_content": "Vous allez fêter votre anniversaire. Vos amis vous demandent ce que vous souhaitez comme cadeaux.Vous voulez des vêtements, vous leur écrivez un message pour leur décrire les vêtements que vous aimeriez recevoir.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["18", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous allez fêter votre anniversaire. vos amis vous demandent ce que vous souhaitez comme cadeaux.vous voulez des vêtements, vous leur écrivez un message pour leur décrire les vêtements que vous aimeriez recevoir"}, {"task_ids": ["novembre-2024_c5_t1", "juillet-2024_c23_t1"], "representative_id": "novembre-2024_c5_t1", "task_content": "“Bon<PERSON><PERSON>, ça y est, j’ai obtenu mon visa pour le Canada. Je vais arriver le 3 mars. Est-ce que tu pourras m’aider à trouver un hôtel pour la première semaine ? Merci d’avance pour ton aide.<PERSON>\n<PERSON>.\nVous avez trouvé un hôtel pour Matthias. Vous lui écrivez un courriel. Dans ce message vous décrivez l’hôtel et vous lui donnez toutes les informations utiles (situation, tarif…).\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024"], "combination_numbers": ["5", "23"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "“bonjour, ça y est, j’ai obtenu mon visa pour le canada. je vais arriver le 3 mars. est-ce que tu pourras m’aider à trouver un hôtel pour la première semaine ? merci d’avance pour ton aide.” matthias. vous avez trouvé un hôtel pour matthias. vous lui écrivez un courriel. dans ce message vous décrivez l’hôtel et vous lui donnez toutes les informations utiles (situation, tarif…)"}, {"task_ids": ["novembre-2024_c13_t1", "juillet-2024_c15_t1", "decembre-2024_c17_t1"], "representative_id": "novembre-2024_c13_t1", "task_content": "Vous souhaitez fêter votre anniversaire dans un restaurant. Vous invitez vos amis.Vous leur écrivez un courriel pour leur donner toutes les informations nécessaires (lieu, date, menu, prix…) et vous leur demandez une réponse.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "decembre-2024"], "combination_numbers": ["13", "15", "17"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous souhaitez fêter votre anniversaire dans un restaurant. vous invitez vos amis.vous leur écrivez un courriel pour leur donner toutes les informations nécessaires (lieu, date, menu, prix…) et vous leur demandez une réponse"}, {"task_ids": ["novembre-2024_c17_t1"], "representative_id": "novembre-2024_c17_t1", "task_content": "Vous avez commandé un objet sur Internet et après réception du colis, vous constatez que l’objet est cassé.Rédigez un e-mail au service clientèle pour signaler le problème, décrivez le dommage de l’objet et précisez ce que vous attendez comme solution.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["17"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez commandé un objet sur internet et après réception du colis, vous constatez que l’objet est cassé.rédigez un e-mail au service clientèle pour signaler le problème, décrivez le dommage de l’objet et précisez ce que vous attendez comme solution"}, {"task_ids": ["novembre-2024_c19_t1", "decembre-2024_c13_t1"], "representative_id": "novembre-2024_c19_t1", "task_content": "Ecrivez un message à votre ami(e) qui souhaite suivre des cours de langue dans votre école. Donnez les détails spécifiques pour aider votre ami(e) à faire son choix.(lieu, tarifs, types de cours disponible, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "decembre-2024"], "combination_numbers": ["19", "13"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "ecrivez un message à votre ami(e) qui souhaite suivre des cours de langue dans votre école. donnez les détails spécifiques pour aider votre ami(e) à faire son choix.(lieu, tarifs, types de cours disponible, etc.)"}, {"task_ids": ["fevrier-2025_c24_t1"], "representative_id": "fevrier-2025_c24_t1", "task_content": "Rédigez un message à un ami pour l’informer de votre déménagement et lui demander son aide, en précisant la date, le lieu et le déroulement du programme.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un ami pour l’informer de votre déménagement et lui demander son aide, en précisant la date, le lieu et le déroulement du programme"}, {"task_ids": ["fevrier-2025_c28_t1"], "representative_id": "fevrier-2025_c28_t1", "task_content": "Vous souhaitez célébrer votre anniversaire dans un restaurant et avez invité vos amis.Vous leur écrivez un courriel pour leur fournir toutes les informations nécessaires telles que le lieu, la date, le menu, les prix, et vous leur demandez de confirmer leur présence.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["28"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez célébrer votre anniversaire dans un restaurant et avez invité vos amis.vous leur écrivez un courriel pour leur fournir toutes les informations nécessaires telles que le lieu, la date, le menu, les prix, et vous leur demandez de confirmer leur présence"}, {"task_ids": ["fevrier-2025_c32_t1", "mars-2025_c5_t1"], "representative_id": "fevrier-2025_c32_t1", "task_content": "Rédiger un e-mail au service client pour signaler la réception d’un objet endommagé après une commande en ligne, en décrivant le problème et en précisant la solution souhaitée.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "mars-2025"], "combination_numbers": ["32", "5"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédiger un e-mail au service client pour signaler la réception d’un objet endommagé après une commande en ligne, en décrivant le problème et en précisant la solution souhaitée"}, {"task_ids": ["mars-2025_c1_t1"], "representative_id": "mars-2025_c1_t1", "task_content": "Rédiger un message en réponse à un ami qui souhaite commencer le sport et cherche une salle adaptée à ses besoins.\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédiger un message en réponse à un ami qui souhaite commencer le sport et cherche une salle adaptée à ses besoins"}, {"task_ids": ["mars-2025_c9_t1"], "representative_id": "mars-2025_c9_t1", "task_content": "Rédigez un message à un(e) ami(e) pour lui parler de votre projet de rénovation de votre appartement.Décrivez les changements que vous souhaitez faire et demandez-lui son aide ou ses conseils.\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un(e) ami(e) pour lui parler de votre projet de rénovation de votre appartement.décrivez les changements que vous souhaitez faire et demandez-lui son aide ou ses conseils"}, {"task_ids": ["juillet-2024_c11_t1"], "representative_id": "juillet-2024_c11_t1", "task_content": "Votre ami Mehdi vient de s’installer dans votre ville et il a besoin d’aide concernant les transports.Répondez-lui en lui donnant les informations (types de transport, abonnement, prix…)", "month_years": ["juillet-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami mehdi vient de s’installer dans votre ville et il a besoin d’aide concernant les transports.répondez-lui en lui donnant les informations (types de transport, abonnement, prix…)"}, {"task_ids": ["juillet-2024_c30_t1"], "representative_id": "juillet-2024_c30_t1", "task_content": "Vous allez déménager. Des amis ont accepté de vous aider.Vous leur écrivez un message collectif pour leur expliquer comment le déménagement va se passer (lieux, horaires, durée, trajet, tâches à faire, etc.)", "month_years": ["juillet-2024"], "combination_numbers": ["30"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous allez déménager. des amis ont accepté de vous aider.vous leur écrivez un message collectif pour leur expliquer comment le déménagement va se passer (lieux, horaires, durée, trajet, tâches à faire, etc.)"}, {"task_ids": ["janvier-2025_c18_t1"], "representative_id": "janvier-2025_c18_t1", "task_content": "Vous voulez organiser une fête.Écrivez un message à vos amis pour les inviter et de vous aider à l’organiser (lieu, date, thème, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez organiser une fête.écrivez un message à vos amis pour les inviter et de vous aider à l’organiser (lieu, date, thème, etc.)"}]}, "looking_for_housing": {"subtopic_id": 2, "task_count": 5, "unique_task_count": 4, "keywords": ["appartement", "logement", "chambre", "colocataire", "louer", "centre-ville", "immobilières"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["juillet-2025_c1_t1", "avril-2025_c2_t1"], "representative_id": "juillet-2025_c1_t1", "task_content": "Vous avez trouvé une annonce en ligne pour louer un appartement.Écrivez un courriel pour demander des informations sur l’appartement (logement et quartier).\n(60 mots minimum/120 mots maximum)", "month_years": ["juillet-2025", "avril-2025"], "combination_numbers": ["1", "2"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez trouvé une annonce en ligne pour louer un appartement.écrivez un courriel pour demander des informations sur l’appartement (logement et quartier)"}, {"task_ids": ["juillet-2024_c16_t1"], "representative_id": "juillet-2024_c16_t1", "task_content": "Vous êtes locataire d’un appartement trop grand pour vous.Écrivez une annonce dans un journal pour chercher un colocataire (superficie de l’appartement, caractère du colocataire, prix, etc…).", "month_years": ["juillet-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement trop grand pour vous.écrivez une annonce dans un journal pour chercher un colocataire (superficie de l’appartement, caractère du colocataire, prix, etc…)"}, {"task_ids": ["juillet-2024_c28_t1"], "representative_id": "juillet-2024_c28_t1", "task_content": "Vous êtes locataire d’un appartement trop grand pour vous.Écrivez une annonce dans un journal pour chercher un colocataire. Il faut mentionner : la superficie, le caractère du colocataire, le prix, etc.", "month_years": ["juillet-2024"], "combination_numbers": ["28"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement trop grand pour vous.écrivez une annonce dans un journal pour chercher un colocataire. il faut mentionner : la superficie, le caractère du colocataire, le prix, etc"}, {"task_ids": ["janvier-2025_c14_t1"], "representative_id": "janvier-2025_c14_t1", "task_content": "Vous allez déménager à Nice, en France. Vous écrivez un message sur le site d’une agence immobilière.Vous donnez les informations nécessaires pour votre recherche d’appartement (date, personnes, loyer…).\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous allez déménager à nice, en france. vous écrivez un message sur le site d’une agence immobilière.vous donnez les informations nécessaires pour votre recherche d’appartement (date, personnes, loyer…)"}]}, "looking_for_other_service": {"subtopic_id": 3, "task_count": 1, "unique_task_count": 1, "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["mars-2025_c8_t1"], "representative_id": "mars-2025_c8_t1", "task_content": "Rédigez un courriel en réponse à une annonce de recherche de vélo.Présentez votre vélo, décrivez son état, proposez un prix et fixez un rendez-vous pour un essai.\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel en réponse à une annonce de recherche de vélo.présentez votre vélo, décrivez son état, proposez un prix et fixez un rendez-vous pour un essai"}]}}}, "description_places": {"topic_id": 3, "keywords": ["ville", "endroit", "lieu", "quartier", "bureau", "local", "adresse", "situé", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nouveau"], "total_tasks": 72, "unique_tasks": 46, "subtopics": {"description_venue": {"subtopic_id": 0, "task_count": 41, "unique_task_count": 22, "keywords": ["lieu", "endroit", "salle", "espace", "bâtiment"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c2_t1", "aout-2024_c3_t1", "novembre-2024_c14_t1", "juillet-2024_c37_t1", "decembre-2024_c4_t1", "decembre-2024_c19_t1"], "representative_id": "septembre-2024_c2_t1", "task_content": "Répondez au courriel de votre ami Lucas pour lui donner des informations sur les nouveaux locaux de votre entreprise(lieu, disposition des pièces, équipements, etc.)", "month_years": ["septembre-2024", "aout-2024", "novembre-2024", "juillet-2024", "decembre-2024", "decembre-2024"], "combination_numbers": ["2", "3", "14", "37", "4", "19"], "is_duplicate_group": true, "duplicate_count": 6, "clean_content": "répondez au courriel de votre ami lucas pour lui donner des informations sur les nouveaux locaux de votre entreprise(lieu, disposition des pièces, équipements, etc.)"}, {"task_ids": ["septembre-2024_c8_t1"], "representative_id": "septembre-2024_c8_t1", "task_content": "Votre ami veut se mettre au sport. Vous lui envoyez un message pour lui conseiller une salle de sport situé dans votre quartier( localisation, prix, type d’activités, etc…)", "month_years": ["septembre-2024"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami veut se mettre au sport. vous lui envoyez un message pour lui conseiller une salle de sport situé dans votre quartier( localisation, prix, type d’activités, etc…)"}, {"task_ids": ["aout-2024_c6_t1", "octobre-2024_c16_t1", "juillet-2024_c4_t1", "janvier-2025_c19_t1", "decembre-2024_c1_t1"], "representative_id": "aout-2024_c6_t1", "task_content": "Écrivez un courriel à vos amis pour les inviter à un anniversaire surpris de votre meilleur ami.(Lieu, date, horaire, etc.).", "month_years": ["aout-2024", "octobre-2024", "juillet-2024", "janvier-2025", "decembre-2024"], "combination_numbers": ["6", "16", "4", "19", "1"], "is_duplicate_group": true, "duplicate_count": 5, "clean_content": "écrivez un courriel à vos amis pour les inviter à un anniversaire surpris de votre meilleur ami.(lieu, date, horaire, etc.)"}, {"task_ids": ["octobre-2024_c1_t1"], "representative_id": "octobre-2024_c1_t1", "task_content": "Ecrivez un message à votre ami(e) pour lui faire part de votre programme de déménagement vers votre nouveau logement, en demandant son aide( date, lieu, programme)", "month_years": ["octobre-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "ecrivez un message à votre ami(e) pour lui faire part de votre programme de déménagement vers votre nouveau logement, en demandant son aide( date, lieu, programme)"}, {"task_ids": ["octobre-2024_c9_t1", "octobre-2024_c17_t1", "novembre-2024_c6_t1", "juillet-2024_c9_t1"], "representative_id": "octobre-2024_c9_t1", "task_content": "Vous voulez partir en week-end avec vos amis le mois prochain.Vous leur écrivez un message pour décrire votre projet (lieu, transport, activités, etc.)", "month_years": ["octobre-2024", "octobre-2024", "novembre-2024", "juillet-2024"], "combination_numbers": ["9", "17", "6", "9"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "vous voulez partir en week-end avec vos amis le mois prochain.vous leur écrivez un message pour décrire votre projet (lieu, transport, activités, etc.)"}, {"task_ids": ["juillet-2025_c2_t1"], "representative_id": "juillet-2025_c2_t1", "task_content": "Votre ami(e) souhaite visiter votre région.Rédigez-lui un message pour lui recommander quelques lieux intéressants à découvrir.\n(60 mots minimum/120 mots maximum)", "month_years": ["juillet-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami(e) souhaite visiter votre région.rédigez-lui un message pour lui recommander quelques lieux intéressants à découvrir"}, {"task_ids": ["novembre-2024_c1_t1"], "representative_id": "novembre-2024_c1_t1", "task_content": "Écrire un message à votre amie pour lui proposer un lieu de camping(lieu, date, activités…)\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrire un message à votre amie pour lui proposer un lieu de camping(lieu, date, activités…)"}, {"task_ids": ["novembre-2024_c2_t1", "juillet-2024_c19_t1", "janvier-2025_c5_t1", "janvier-2025_c11_t1"], "representative_id": "novembre-2024_c2_t1", "task_content": "Je vais bientôt vivre dans ton quartier. Je cherche un endroit sympathique pour faire mes courses. Est-ce que tu connais un marché intéressant ?\nMerci d’avance et à bientôt !\n<PERSON>\nVous répondez à votre ami <PERSON>. Dans votre message, vous décrivez un marché de votre quartier que vous aimez bien (lieu, horaires, produits, etc.)\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025", "janvier-2025"], "combination_numbers": ["2", "19", "5", "11"], "is_duplicate_group": true, "duplicate_count": 4, "clean_content": "je vais bientôt vivre dans ton quartier. je cherche un endroit sympathique pour faire mes courses. est-ce que tu connais un marché intéressant ? merci d’avance et à bientôt ! bernard vous répondez à votre ami bernard. dans votre message, vous décrivez un marché de votre quartier que vous aimez bien (lieu, horaires, produits, etc.)"}, {"task_ids": ["novembre-2024_c8_t1", "juillet-2024_c14_t1", "janvier-2025_c2_t1"], "representative_id": "novembre-2024_c8_t1", "task_content": "Vous avez invité votre ami Cédric à votre mariage au Château de Chombony et il vous a répondu qu’il ne connait pas ce château.Décrivez à votre ami (lieu, localisation, transports, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024", "janvier-2025"], "combination_numbers": ["8", "14", "2"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "vous avez invité votre ami cédric à votre mariage au château de chombony et il vous a répondu qu’il ne connait pas ce château.décrivez à votre ami (lieu, localisation, transports, etc.)"}, {"task_ids": ["Mai-2025_c4_t1"], "representative_id": "Mai-2025_c4_t1", "task_content": "Vous envisagez de partir en week-end avec vos amis le mois prochain.Rédigez un message pour leur présenter votre projet, incluant le lieu, les moyens de transport et les activités prévues.\n(60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous envisagez de partir en week-end avec vos amis le mois prochain.rédigez un message pour leur présenter votre projet, incluant le lieu, les moyens de transport et les activités prévues"}, {"task_ids": ["fevrier-2025_c8_t1"], "representative_id": "fevrier-2025_c8_t1", "task_content": "Léa t’a envoyé un message pour te proposer un pique-nique samedi prochain et te demander si tu connais un endroit agréable pour les enfants et les adultes.Tu réponds en acceptant son invitation et en lui suggérant un lieu adapté pour le pique-nique, en décrivant les caractéristiques de l’endroit et en expliquant les activités possibles sur place.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "léa t’a envoyé un message pour te proposer un pique-nique samedi prochain et te demander si tu connais un endroit agréable pour les enfants et les adultes.tu réponds en acceptant son invitation et en lui suggérant un lieu adapté pour le pique-nique, en décrivant les caractéristiques de l’endroit et en expliquant les activités possibles sur place"}, {"task_ids": ["fevrier-2025_c10_t1"], "representative_id": "fevrier-2025_c10_t1", "task_content": "Vous avez l’intention de partir en week-end avec vos amis le mois prochain.Vous leur envoyez un message pour leur présenter votre projet, en détaillant le lieu, les moyens de transport et les activités prévues.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez l’intention de partir en week-end avec vos amis le mois prochain.vous leur envoyez un message pour leur présenter votre projet, en détaillant le lieu, les moyens de transport et les activités prévues"}, {"task_ids": ["mars-2025_c10_t1"], "representative_id": "mars-2025_c10_t1", "task_content": "Rédigez un courriel à vos amis pour les inviter à une fête d’anniversaire surprise en précisant les détails de l’événement(lieu, date, heure, et organisation).\n(60 mots minimum/120 mots maximum)", "month_years": ["mars-2025"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un courriel à vos amis pour les inviter à une fête d’anniversaire surprise en précisant les détails de l’événement(lieu, date, heure, et organisation)"}, {"task_ids": ["juillet-2024_c20_t1"], "representative_id": "juillet-2024_c20_t1", "task_content": "<PERSON><PERSON>,\n<PERSON> as commencé ton nouveau travail ! C’est comment ? Tu es content(e) ?\n<PERSON>\nVous répondez à votre ami Ali. Dans votre message, vous décrivez votre nouveau travail (lieu, collègues, etc.) et vous donnez vos impressions.", "month_years": ["juillet-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "salut, tu as commencé ton nouveau travail ! c’est comment ? tu es content(e) ? ali vous répondez à votre ami ali. dans votre message, vous décrivez votre nouveau travail (lieu, collègues, etc.) et vous donnez vos impressions"}, {"task_ids": ["juillet-2024_c21_t1"], "representative_id": "juillet-2024_c21_t1", "task_content": "Votre ami vous propose de faire du camping.Écrivez un message à votre ami pour lui suggérer un endroit où camper en précisant le lieu, la date et les activités prévues.", "month_years": ["juillet-2024"], "combination_numbers": ["21"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami vous propose de faire du camping.écrivez un message à votre ami pour lui suggérer un endroit où camper en précisant le lieu, la date et les activités prévues"}, {"task_ids": ["juillet-2024_c22_t1", "janvier-2025_c6_t1"], "representative_id": "juillet-2024_c22_t1", "task_content": "Je cherche un endroit pour déjeuner en plein air ce week-end. Qu’est-ce que tu me proposes ?\n<PERSON> <PERSON><PERSON><PERSON><PERSON>,\n<PERSON> répondez à votre amie Barbara en décrivant le lieu (parc, jardin, terrasse, etc.).", "month_years": ["juillet-2024", "janvier-2025"], "combination_numbers": ["22", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "je cherche un endroit pour déjeuner en plein air ce week-end. qu’est-ce que tu me proposes ? à bient<PERSON><PERSON>, barbara vous répondez à votre amie barbara en décrivant le lieu (parc, jardin, terrasse, etc.)"}, {"task_ids": ["janvier-2025_c4_t1", "janvier-2025_c16_t1"], "representative_id": "janvier-2025_c4_t1", "task_content": "Vous avez trouvé un nouveau travail. Vous écrivez à votre amie Francophone pour lui annoncer la bonne nouvelle.Vous décrivez votre poste, vos collègues et votre lieu de travail.\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025", "janvier-2025"], "combination_numbers": ["4", "16"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez trouvé un nouveau travail. vous écrivez à votre amie francophone pour lui annoncer la bonne nouvelle.vous décrivez votre poste, vos collègues et votre lieu de travail"}, {"task_ids": ["janvier-2025_c12_t1"], "representative_id": "janvier-2025_c12_t1", "task_content": "L’été est arrivé ! Je vous propose de faire un pique-nique samedi prochain. Connaissez-vous un endroit sympa pour les enfants et les adultes où nous pouvons tous nous retrouver ? Bisous, à bientôt.\nLéa”\nVous acceptez l’invitation de Léa et vous lui proposez un endroit pour organiser le pique-nique. Vous décrivez le lieu et expliquez quelles sont les activités possibles.\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "l’été est arrivé ! je vous propose de faire un pique-nique samedi prochain. connaissez-vous un endroit sympa pour les enfants et les adultes où nous pouvons tous nous retrouver ? bisous, à bientôt. léa” vous acceptez l’invitation de léa et vous lui proposez un endroit pour organiser le pique-nique. vous décrivez le lieu et expliquez quelles sont les activités possibles"}, {"task_ids": ["janvier-2025_c13_t1"], "representative_id": "janvier-2025_c13_t1", "task_content": "Salut je vais venir en vacances dans ton pays tu peux me conseiller un endroit à visiter ?\nMerci!\nVous répondez à votre ami dans votre message ,vous décrivez un lieu intéressant à visiter ( ville,monuments site naturel etc).\n(60 mots minimum/120 mots maximum)", "month_years": ["janvier-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "salut je vais venir en vacances dans ton pays tu peux me conseiller un endroit à visiter ? merci! vous répondez à votre ami dans votre message ,vous décrivez un lieu intéressant à visiter ( ville,monuments site naturel etc)"}, {"task_ids": ["avril-2025_c3_t1"], "representative_id": "avril-2025_c3_t1", "task_content": "Votre ami(e) désire explorer la région dans laquelle vous vivez.Rédigez-lui un message en lui suggérant des lieux intéressants à découvrir.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami(e) désire explorer la région dans laquelle vous vivez.rédigez-lui un message en lui suggérant des lieux intéressants à découvrir"}, {"task_ids": ["avril-2025_c7_t1"], "representative_id": "avril-2025_c7_t1", "task_content": "Vous organisez bientôt votre fête d’anniversaire.Écrivez un message à vos amis pour les inviter et leur expliquer ce que vous avez prévu (lieu, activités, ambiance, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous organisez bientôt votre fête d’anniversaire.écrivez un message à vos amis pour les inviter et leur expliquer ce que vous avez prévu (lieu, activités, ambiance, etc.)"}, {"task_ids": ["avril-2025_c12_t1"], "representative_id": "avril-2025_c12_t1", "task_content": "Vous souhaitez organiser une fête et souhaitez inviter vos amis tout en leur demandant un coup de main pour la préparation (lieu, date, thème, etc.).Rédigez un message en ce sens.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous souhaitez organiser une fête et souhaitez inviter vos amis tout en leur demandant un coup de main pour la préparation (lieu, date, thème, etc.).rédigez un message en ce sens"}]}, "description_city": {"subtopic_id": 1, "task_count": 15, "unique_task_count": 11, "keywords": ["ville", "centre", "municipal", "urbain", "métropole"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c3_t1", "decembre-2024_c8_t1", "decembre-2024_c11_t1"], "representative_id": "septembre-2024_c3_t1", "task_content": "le journal ” Bienvenue” compte publier un article qui parle des habitants de notre ville. Écrivez nous un message qui sera diffusé dans ce numéro. Vous êtes récemment installé dans cette ville, et il vous est demandé de vous présenter et décrire ensuite tous vos lieux préférés au sein de cette ville.", "month_years": ["septembre-2024", "decembre-2024", "decembre-2024"], "combination_numbers": ["3", "8", "11"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "le journal ” bienvenue” compte publier un article qui parle des habitants de notre ville. écrivez nous un message qui sera diffusé dans ce numéro. vous êtes récemment installé dans cette ville, et il vous est demandé de vous présenter et décrire ensuite tous vos lieux préférés au sein de cette ville"}, {"task_ids": ["octobre-2024_c2_t1", "decembre-2024_c6_t1"], "representative_id": "octobre-2024_c2_t1", "task_content": "Vous voulez organiser une visite culturelle dans votre ville.Vous envoyez un message pour inviter vos amis. Vous leur donner toutes les informations nécessaires (activités, date, lieu, etc.).", "month_years": ["octobre-2024", "decembre-2024"], "combination_numbers": ["2", "6"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous voulez organiser une visite culturelle dans votre ville.vous envoyez un message pour inviter vos amis. vous leur donner toutes les informations nécessaires (activités, date, lieu, etc.)"}, {"task_ids": ["juillet-2025_c4_t1", "avril-2025_c4_t1"], "representative_id": "juillet-2025_c4_t1", "task_content": "Invitez votre ami(e) à venir passer les vacances chez vous.Dans votre message, décrivez les sites touristiques et les endroits intéressants à découvrir dans votre ville.\n(60 mots minimum/120 mots maximum)", "month_years": ["juillet-2025", "avril-2025"], "combination_numbers": ["4", "4"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "invitez votre ami(e) à venir passer les vacances chez vous.dans votre message, décrivez les sites touristiques et les endroits intéressants à découvrir dans votre ville"}, {"task_ids": ["novembre-2024_c4_t1"], "representative_id": "novembre-2024_c4_t1", "task_content": "Vous voulez communiquer avec quelqu’un en français en toute convivialité.Vous écrivez un courriel à cette adresse(…) pour qu’on vous propose quelqu’un (centre d’in<PERSON><PERSON><PERSON><PERSON>, présentez vous…)\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["4"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous voulez communiquer avec quelqu’un en français en toute convivialité.vous écrivez un courriel à cette adresse(…) pour qu’on vous propose quelqu’un (centre d’in<PERSON>r<PERSON><PERSON>, présentez vous…)"}, {"task_ids": ["Mai-2025_c3_t1"], "representative_id": "Mai-2025_c3_t1", "task_content": "Le journal « Bienvenue » souhaite publier un article sur les nouveaux habitants de la ville.Rédigez un message dans lequel vous vous présentez et décrivez vos endroits favoris dans la ville.\n(60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["3"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "le journal « bienvenue » souhaite publier un article sur les nouveaux habitants de la ville.rédigez un message dans lequel vous vous présentez et décrivez vos endroits favoris dans la ville"}, {"task_ids": ["fevrier-2025_c25_t1"], "representative_id": "fevrier-2025_c25_t1", "task_content": "Rédigez un message à un ami pour l’inviter à une rencontre avec un écrivain organisée par la bibliothèque de votre ville.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["25"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à un ami pour l’inviter à une rencontre avec un écrivain organisée par la bibliothèque de votre ville"}, {"task_ids": ["juillet-2024_c7_t1"], "representative_id": "juillet-2024_c7_t1", "task_content": "Vous venez de vous installer dans une nouvelle ville.Vous écrivez un message à un(e) ami(e) pour décrire votre nouvel environnement (quartier, voisins, magasins, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous venez de vous installer dans une nouvelle ville.vous écrivez un message à un(e) ami(e) pour décrire votre nouvel environnement (quartier, voisins, magasins, etc.)"}, {"task_ids": ["juillet-2024_c10_t1"], "representative_id": "juillet-2024_c10_t1", "task_content": "Un évènement sportif aura lieu dans votre ville bientôt.Faites parvenir un message à vos amis pour les inviter (date, lieu, inscription, etc.).", "month_years": ["juillet-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un évènement sportif aura lieu dans votre ville bientôt.faites parvenir un message à vos amis pour les inviter (date, lieu, inscription, etc.)"}, {"task_ids": ["juillet-2024_c31_t1"], "representative_id": "juillet-2024_c31_t1", "task_content": "La bibliothèque de votre ville organise une rencontre avec un écrivain.Écrivez un message à votre ami(e) pour l’inviter à cet événement.", "month_years": ["juillet-2024"], "combination_numbers": ["31"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "la bibliothèque de votre ville organise une rencontre avec un écrivain.écrivez un message à votre ami(e) pour l’inviter à cet événement"}, {"task_ids": ["juillet-2024_c32_t1"], "representative_id": "juillet-2024_c32_t1", "task_content": "Rédigez un message pour inviter votre ami à passer ses vacances dans votre ville.", "month_years": ["juillet-2024"], "combination_numbers": ["32"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message pour inviter votre ami à passer ses vacances dans votre ville"}, {"task_ids": ["decembre-2024_c7_t1"], "representative_id": "decembre-2024_c7_t1", "task_content": "écrivez un email pour répondre à votre ami(e) qui va passer le Week-end dans votre ville.Il faut décrire les moyens de transports.\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["7"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un email pour répondre à votre ami(e) qui va passer le week-end dans votre ville.il faut décrire les moyens de transports"}]}, "description_general_place": {"subtopic_id": 2, "task_count": 8, "unique_task_count": 7, "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["septembre-2024_c10_t1"], "representative_id": "septembre-2024_c10_t1", "task_content": "Vous partez en voyage et vous laissez votre appartement à un ami qui veut venir rester chez-vous pendant vos vacances.Vous lui envoyez un message pour décrire votre appartement (immeuble, logement, accès…).", "month_years": ["septembre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en voyage et vous laissez votre appartement à un ami qui veut venir rester chez-vous pendant vos vacances.vous lui envoyez un message pour décrire votre appartement (immeuble, logement, accès…)"}, {"task_ids": ["aout-2024_c1_t1"], "representative_id": "aout-2024_c1_t1", "task_content": "Vous êtes nouveaux dans une université.Décrivez à vos amis comment cela se passe avec ( les prof, autres étudiants, les activités et autres ).", "month_years": ["aout-2024"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes nouveaux dans une université.décrivez à vos amis comment cela se passe avec ( les prof, autres étudiants, les activités et autres )"}, {"task_ids": ["octobre-2024_c5_t1"], "representative_id": "octobre-2024_c5_t1", "task_content": "écrivez un message à votre ami pour lui décrire le programme de vos prochaines vacances", "month_years": ["octobre-2024"], "combination_numbers": ["5"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour lui décrire le programme de vos prochaines vacances"}, {"task_ids": ["novembre-2024_c18_t1", "juillet-2024_c33_t1"], "representative_id": "novembre-2024_c18_t1", "task_content": "Vous avez passé un week-end à la campagne.Écrivez un message à votre ami(e) pour lui décrire ce qui s’est passé.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024", "juillet-2024"], "combination_numbers": ["18", "33"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "vous avez passé un week-end à la campagne.écrivez un message à votre ami(e) pour lui décrire ce qui s’est passé"}, {"task_ids": ["decembre-2024_c14_t1"], "representative_id": "decembre-2024_c14_t1", "task_content": "Un nouveau restaurant vient d’ouvrir près de chez vous. Vous écrivez à un(e) ami(e) pour lui proposer d’y aller avec vous.Vous décrivez le restaurant (cuisine, prix, décoration, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["14"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "un nouveau restaurant vient d’ouvrir près de chez vous. vous écrivez à un(e) ami(e) pour lui proposer d’y aller avec vous.vous décrivez le restaurant (cuisine, prix, décoration, etc.)"}, {"task_ids": ["avril-2025_c15_t1"], "representative_id": "avril-2025_c15_t1", "task_content": "Vous avez découvert un nouveau restaurant.Écrivez un message à votre ami pour décrire l’ambiance, la décoration, les plats et le service.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez découvert un nouveau restaurant.écrivez un message à votre ami pour décrire l’ambiance, la décoration, les plats et le service"}, {"task_ids": ["avril-2025_c20_t1"], "representative_id": "avril-2025_c20_t1", "task_content": "Vous partez en vacances avec des amis et avez trouvé un hôtel. Écrivez-leur pour décrire l’hôtel :son emplacement, son tarif, ses équipements, et suggérez-leur de réserver cet hôtel.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en vacances avec des amis et avez trouvé un hôtel. écrivez-leur pour décrire l’hôtel :son emplacement, son tarif, ses équipements, et suggérez-leur de réserver cet hôtel"}]}, "description_tourist_place": {"subtopic_id": 3, "task_count": 4, "unique_task_count": 2, "keywords": ["château", "monument", "musée", "touristique", "historique"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c7_t1", "novembre-2024_c3_t1", "juillet-2024_c34_t1"], "representative_id": "octobre-2024_c7_t1", "task_content": "Parc de loisi<PERSON> : “J’ai hâte de passer la journée avec toi demain. S’il te plaît, dis-moi, quelle activité nous pourrons faire ?”\nRépondez à votre ami pour lui décrire la sortie (horaires,\ntransport, billet, activités)", "month_years": ["octobre-2024", "novembre-2024", "juillet-2024"], "combination_numbers": ["7", "3", "34"], "is_duplicate_group": true, "duplicate_count": 3, "clean_content": "parc de loisirs : “j’ai hâte de passer la journée avec toi demain. s’il te plaît, dis-moi, quelle activité nous pourrons faire ?” répondez à votre ami pour lui décrire la sortie (horaires, transport, billet, activités)"}, {"task_ids": ["octobre-2024_c10_t1"], "representative_id": "octobre-2024_c10_t1", "task_content": "« Salut,\nJe suis vraiment intéressé à l’idée de voyager et de découvrir un autre pays. Peux-tu me parler un peu de ton pays et de sa culture ?\nMarc. »\nÉcrivez un message à votre ami Marc, qui veut voyager et découvrir un autre pays, pour lui parler de votre pays et de sa culture (lieux, sites touristiques, monuments, etc). »", "month_years": ["octobre-2024"], "combination_numbers": ["10"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "« salut, je suis vraiment intéressé à l’idée de voyager et de découvrir un autre pays. peux-tu me parler un peu de ton pays et de sa culture ? marc. » écrivez un message à votre ami marc, qui veut voyager et découvrir un autre pays, pour lui parler de votre pays et de sa culture (lieux, sites touristiques, monuments, etc). »"}]}, "description_office": {"subtopic_id": 4, "task_count": 4, "unique_task_count": 4, "keywords": ["bureau", "entreprise", "local", "travail", "professionnel", "affaires"], "template_similarity": 1.0, "task_entries": [{"task_ids": ["novembre-2024_c20_t1"], "representative_id": "novembre-2024_c20_t1", "task_content": "Votre ami souhaite commencer à faire du sport.Rédigez un message pour lui recommander une salle de sport située dans votre quartier (localisation, tarifs, types d’activités, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami souhaite commencer à faire du sport.rédigez un message pour lui recommander une salle de sport située dans votre quartier (localisation, tarifs, types d’activités, etc.)"}, {"task_ids": ["fevrier-2025_c26_t1"], "representative_id": "fevrier-2025_c26_t1", "task_content": "Envoyez un courriel à votre ami Lucas pour lui présenter les nouveaux locaux de votre entreprise en précisant leur localisation, l’organisation des espaces et les équipements mis à disposition.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["26"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "envoyez un courriel à votre ami lucas pour lui présenter les nouveaux locaux de votre entreprise en précisant leur localisation, l’organisation des espaces et les équipements mis à disposition"}, {"task_ids": ["juillet-2024_c24_t1"], "representative_id": "juillet-2024_c24_t1", "task_content": "Vous partez en vacances avec vos amis, vous avez trouvé un hôtel.Vous écrivez un message à vos amis pour décrire cet hôtel (localisation, prix, équipements, etc.) et vous leur proposez de réserver cet hôtel.", "month_years": ["juillet-2024"], "combination_numbers": ["24"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous partez en vacances avec vos amis, vous avez trouvé un hôtel.vous écrivez un message à vos amis pour décrire cet hôtel (localisation, prix, équipements, etc.) et vous leur proposez de réserver cet hôtel"}, {"task_ids": ["avril-2025_c9_t1"], "representative_id": "avril-2025_c9_t1", "task_content": "Répondez au courriel de votre ami Lucas en lui fournissant des détails sur les nouveaux locaux de votre entreprise: emplacement, aménagement des pièces, équipements disponibles, etc.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["9"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "répondez au courriel de votre ami lucas en lui fournissant des détails sur les nouveaux locaux de votre entreprise: emplacement, aménagement des pièces, équipements disponibles, etc"}]}}}, "manual_review": {"topic_id": 4, "keywords": ["unclassified"], "total_tasks": 22, "unique_tasks": 20, "subtopics": {"manual_review_general": {"subtopic_id": 0, "task_count": 22, "unique_task_count": 20, "keywords": [], "template_similarity": 1.0, "task_entries": [{"task_ids": ["octobre-2024_c8_t1", "juillet-2024_c12_t1"], "representative_id": "octobre-2024_c8_t1", "task_content": "Invitez vos amis à célébrer votre anniversaire tout en sollicitant leur soutien pour organiser la fête.", "month_years": ["octobre-2024", "juillet-2024"], "combination_numbers": ["8", "12"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "invitez vos amis à célébrer votre anniversaire tout en sollicitant leur soutien pour organiser la fête"}, {"task_ids": ["octobre-2024_c11_t1"], "representative_id": "octobre-2024_c11_t1", "task_content": "écrivez un message à vos amis pour leur dire que vous avez trouvé un hôtel pour aller en vacances", "month_years": ["octobre-2024"], "combination_numbers": ["11"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à vos amis pour leur dire que vous avez trouvé un hôtel pour aller en vacances"}, {"task_ids": ["octobre-2024_c12_t1"], "representative_id": "octobre-2024_c12_t1", "task_content": "écrivez un message à votre ami pour l’inviter à pratiquer un sport avec vous", "month_years": ["octobre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour l’inviter à pratiquer un sport avec vous"}, {"task_ids": ["octobre-2024_c13_t1"], "representative_id": "octobre-2024_c13_t1", "task_content": "écrivez un message à votre ami qui a accepté de s’occuper de votre maison et de votre jardin pendant votre absence pour lui dire ce qu’il doit faire.", "month_years": ["octobre-2024"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami qui a accepté de s’occuper de votre maison et de votre jardin pendant votre absence pour lui dire ce qu’il doit faire"}, {"task_ids": ["novembre-2024_c12_t1"], "representative_id": "novembre-2024_c12_t1", "task_content": "C’est bientôt l’anniversaire de votre amie Flavie. Vous voulez lui offrir un voyage et écrivez à votre amie Flavie.\n–Expliquez votre projet.\n–Décrivez le programme du voyage (activités, destination, logement, etc.).\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "c’est bientôt l’anniversaire de votre amie flavie. vous voulez lui offrir un voyage et écrivez à votre amie flavie. –expliquez votre projet. –décrivez le programme du voyage (activités, destination, logement, etc.)"}, {"task_ids": ["novembre-2024_c15_t1"], "representative_id": "novembre-2024_c15_t1", "task_content": "“ Sal<PERSON>, j’ai appris que tu vas à une salle de sport et qu’elle est magnifique. Peux-tu m’en dire plus “.Écrivez un message pour répondre à votre ami concernant ce sujet.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["15"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "“ salut, j’ai appris que tu vas à une salle de sport et qu’elle est magnifique. peux-tu m’en dire plus “.écrivez un message pour répondre à votre ami concernant ce sujet"}, {"task_ids": ["novembre-2024_c16_t1"], "representative_id": "novembre-2024_c16_t1", "task_content": "France Télévision prépare un reportage sur le sport amateur.\nEt vous, quel sportif êtes-vous ? Envoyez-nous vos témoignages surfrancetélévision.fr.\n(60 mots minimum/120 mots maximum)", "month_years": ["novembre-2024"], "combination_numbers": ["16"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "france télévision prépare un reportage sur le sport amateur. et vous, quel sportif êtes-vous ? envoyez-nous vos témoignages surfrancetélévision.fr"}, {"task_ids": ["Mai-2025_c1_t1"], "representative_id": "Mai-2025_c1_t1", "task_content": "Votre anniversaire approche. Vos amis vous posent des questions sur les cadeaux que vous voulez.Vous répondez par un message en expliquant que vous aimeriez recevoir des vêtements, et vous donnez les détails.\n(60 mots minimum/120 mots maximum)", "month_years": ["Mai-2025"], "combination_numbers": ["1"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre anniversaire approche. vos amis vous posent des questions sur les cadeaux que vous voulez.vous répondez par un message en expliquant que vous aimeriez recevoir des vêtements, et vous donnez les détails"}, {"task_ids": ["fevrier-2025_c2_t1"], "representative_id": "fevrier-2025_c2_t1", "task_content": "Tu loues un appartement qui est trop grand pour toi.Tu rédiges une annonce dans un journal pour trouver un colocataire en mentionnant la superficie du logement, le type de colocataire recherché et le prix du loyer.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["2"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "tu loues un appartement qui est trop grand pour toi.tu rédiges une annonce dans un journal pour trouver un colocataire en mentionnant la superficie du logement, le type de colocataire recherché et le prix du loyer"}, {"task_ids": ["fevrier-2025_c19_t1"], "representative_id": "fevrier-2025_c19_t1", "task_content": "Rédigez un message à votre ami(e) dans lequel vous détaillez le programme de vos prochaines vacances, en précisant la destination, les dates et les activités prévues.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["19"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "rédigez un message à votre ami(e) dans lequel vous détaillez le programme de vos prochaines vacances, en précisant la destination, les dates et les activités prévues"}, {"task_ids": ["fevrier-2025_c20_t1"], "representative_id": "fevrier-2025_c20_t1", "task_content": "Vous avez mis en ligne une offre de location pour votre appartement montréalais.Rédigez un email à l’attention d’un candidat locataire potentiel en y intégrant les détails essentiels : spécificités du logement, prix de la location, avantages du secteur géographique, etc.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["20"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez mis en ligne une offre de location pour votre appartement montréalais.rédigez un email à l’attention d’un candidat locataire potentiel en y intégrant les détails essentiels : spécificités du logement, prix de la location, avantages du secteur géographique, etc"}, {"task_ids": ["fevrier-2025_c22_t1", "avril-2025_c18_t1"], "representative_id": "fevrier-2025_c22_t1", "task_content": "Rédigez un message à votre ami Cédric pour lui fournir des instructions concernant l’entretien de votre maison et de votre jardin pendant votre absence.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025", "avril-2025"], "combination_numbers": ["22", "18"], "is_duplicate_group": true, "duplicate_count": 2, "clean_content": "rédigez un message à votre ami cédric pour lui fournir des instructions concernant l’entretien de votre maison et de votre jardin pendant votre absence"}, {"task_ids": ["fevrier-2025_c23_t1"], "representative_id": "fevrier-2025_c23_t1", "task_content": "Vous êtes locataire d’un appartement qui vous semble trop grand. Rédigez une annonce dans un journal pour trouver un colocataire, en indiquant la superficie, le profil recherché et le montant du loyer, entre autres détails.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["23"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous êtes locataire d’un appartement qui vous semble trop grand. rédigez une annonce dans un journal pour trouver un colocataire, en indiquant la superficie, le profil recherché et le montant du loyer, entre autres détails"}, {"task_ids": ["fevrier-2025_c27_t1"], "representative_id": "fevrier-2025_c27_t1", "task_content": "Écrivez un message à votre ami pour encourager à intégrer le sport dans son quotidien.\n(60 mots minimum/120 mots maximum)", "month_years": ["fevrier-2025"], "combination_numbers": ["27"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour encourager à intégrer le sport dans son quotidien"}, {"task_ids": ["juillet-2024_c18_t1"], "representative_id": "juillet-2024_c18_t1", "task_content": "Votre ami va fêter son anniversaire.Écrivez un message à vos amis pour lui acheter un cadeau commun.", "month_years": ["juillet-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami va fêter son anniversaire.écrivez un message à vos amis pour lui acheter un cadeau commun"}, {"task_ids": ["juillet-2024_c29_t1"], "representative_id": "juillet-2024_c29_t1", "task_content": "Votre ami Cédric a accepté de garder votre maison et jardin pendant vos vacances.Écrivez un message pour lui dire ce qu’il doit faire.", "month_years": ["juillet-2024"], "combination_numbers": ["29"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "votre ami cédric a accepté de garder votre maison et jardin pendant vos vacances.écrivez un message pour lui dire ce qu’il doit faire"}, {"task_ids": ["decembre-2024_c12_t1"], "representative_id": "decembre-2024_c12_t1", "task_content": "Écrivez un message pour inviter vos amis à une fête de fin d’année.\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["12"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message pour inviter vos amis à une fête de fin d’année"}, {"task_ids": ["decembre-2024_c18_t1"], "representative_id": "decembre-2024_c18_t1", "task_content": "Écrivez un message à votre ami pour le convaincre à pratiquer une activité physique.\n(60 mots minimum/120 mots maximum)", "month_years": ["decembre-2024"], "combination_numbers": ["18"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "écrivez un message à votre ami pour le convaincre à pratiquer une activité physique"}, {"task_ids": ["avril-2025_c8_t1"], "representative_id": "avril-2025_c8_t1", "task_content": "Vous avez décidé d’offrir un voyage à votre ami pour son anniversaire.Écrivez un message pour lui expliquer ce que vous avez préparé : la destination, les dates et les autres détails du voyage.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["8"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez décidé d’offrir un voyage à votre ami pour son anniversaire.écrivez un message pour lui expliquer ce que vous avez préparé : la destination, les dates et les autres détails du voyage"}, {"task_ids": ["avril-2025_c13_t1"], "representative_id": "avril-2025_c13_t1", "task_content": "Vous avez découvert une plateforme en ligne mettant en relation des personnes souhaitant apprendre le français avec des partenaires linguistiques.Rédigez un courriel pour vous présenter et expliquer votre motivation à pratiquer cette langue.\n(60 mots minimum/120 mots maximum)", "month_years": ["avril-2025"], "combination_numbers": ["13"], "is_duplicate_group": false, "duplicate_count": 1, "clean_content": "vous avez découvert une plateforme en ligne mettant en relation des personnes souhaitant apprendre le français avec des partenaires linguistiques.rédigez un courriel pour vous présenter et expliquer votre motivation à pratiquer cette langue"}]}}}}, "deduplication_stats": {"total_tasks": 206, "unique_tasks": 138, "duplicate_groups": 43, "largest_group_size": 6, "total_duplicates": 68, "average_group_size": 0, "similarity_threshold": 0.95}}