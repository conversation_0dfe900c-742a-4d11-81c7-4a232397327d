[{"image_path": null, "audio_path": "listening_asset_free/media_test1/Q5.mp3", "chunks": [{"text": "Je t'invite chez moi ce soir. J'organise une fête pour mon anniversaire.", "start": 0.0, "end": 4.02}, {"text": "<PERSON><PERSON> merci, je veux juste du gâteau.", "start": 5.640000000000003, "end": 8.54}, {"text": "B. D'accord, je peux venir avec une amie ?", "start": 9.780000000000001, "end": 13.72}, {"text": "<PERSON><PERSON>, je souhaite connaître l'heure.", "start": 13.72, "end": 17.08}, {"text": "<PERSON><PERSON>, je préfère partir tout de suite.", "start": 18.340000000000003, "end": 21.26}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q5"], "correct_answer": "B", "source_location": "test1_gr Q5"}, {"image_path": null, "audio_path": "listening_asset_free/media_test1/Q6.mp3", "chunks": [{"text": "Salut Léa, ça va ?", "start": 0.0, "end": 1.62}, {"text": "<PERSON><PERSON>, oui ça va et toi ?", "start": 1.62, "end": 3.88}, {"text": "<PERSON>ien merci.", "start": 3.88, "end": 4.44}, {"text": "Di<PERSON><PERSON>moi, j'ai oublié quand est notre examen d'anglais la semaine prochaine ?", "start": 4.88, "end": 8.46}, {"text": "A. C'est à 9h.", "start": 9.200000000000001, "end": 10.9}, {"text": "B. C'est au lycée.", "start": 12.36, "end": 14.6}, {"text": "C. C'est en avril.", "start": 16.02, "end": 18.24}, {"text": "D. C'est mercredi.", "start": 19.64, "end": 21.78}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q6"], "correct_answer": "D", "source_location": "test1_gr Q6"}, {"image_path": null, "audio_path": "listening_asset_free/media_test1/Q7.mp3", "chunks": [{"text": "Voulez-vous dîner à la maison ce soir ?", "start": 0.0, "end": 2.84}, {"text": "<PERSON><PERSON> plaisir.", "start": 4.0, "end": 6.34}, {"text": "B. Ce n'est rien.", "start": 7.24, "end": 10.3}, {"text": "<PERSON><PERSON> Je vous en prie.", "start": 11.96, "end": 14.6}, {"text": "<PERSON><PERSON>.", "start": 15.86, "end": 18.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q7"], "correct_answer": "A", "source_location": "test1_gr Q7"}, {"image_path": null, "audio_path": "listening_asset_free/media_test1/Q8.mp3", "chunks": [{"text": "Allô ? Je voudrais parler à <PERSON> Bernard, s'il vous plaît.", "start": 0.0, "end": 3.52}, {"text": "<PERSON><PERSON> va partir en voyage.", "start": 5.76, "end": 8.74}, {"text": "<PERSON><PERSON> a préparé son dossier.", "start": 9.82, "end": 13.44}, {"text": "<PERSON><PERSON> doit vous inviter à dîner.", "start": 14.780000000000001, "end": 18.84}, {"text": "<PERSON><PERSON> est absente pour le moment.", "start": 19.58, "end": 23.1}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q8"], "correct_answer": "D", "source_location": "test1_gr Q8"}, {"image_path": null, "audio_path": "listening_asset_free/media_test1/Q9.mp3", "chunks": [{"text": "Pendant la nuit, avez-vous entendu quelque chose ?", "start": 0.0, "end": 3.26}, {"text": "<PERSON><PERSON>, aucun.", "start": 4.68, "end": 6.42}, {"text": "B<PERSON>, rien.", "start": 7.1800000000000015, "end": 9.4}, {"text": "<PERSON><PERSON>, personne.", "start": 10.18, "end": 12.6}, {"text": "<PERSON><PERSON>, tout le monde.", "start": 13.98, "end": 16.02}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q9"], "correct_answer": "B", "source_location": "test1_gr Q9"}, {"image_path": null, "audio_path": "listening_asset_free/media_test1/Q10.mp3", "chunks": [{"text": "Excusez-moi, madame. Est-ce que vous avez l'heure ?", "start": 0.0, "end": 3.24}, {"text": "A. D'accord. À plus tard.", "start": 3.24, "end": 6.16}, {"text": "B. Dan<PERSON> peu de temps.", "start": 7.28, "end": 9.38}, {"text": "C. Il y a quelques heures.", "start": 10.52, "end": 13.04}, {"text": "D. <PERSON> est 9 heures.", "start": 14.0, "end": 16.08}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q10"], "correct_answer": "D", "source_location": "test1_gr Q10"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, nous vous informons qu'un bar est situé dans la voiture numéro 14.", "start": 0.8799999999999994, "end": 5.28}, {"text": "Vous y trouverez un grand choix de boissons chaudes et froides et des pâtisseries.", "start": 5.6, "end": 9.18}, {"text": "Cette annonce a été passée.", "start": 10.48, "end": 12.42}, {"text": "<PERSON><PERSON> un train", "start": 12.7, "end": 14.44}, {"text": "B. Dans un café", "start": 15.28, "end": 17.22}, {"text": "<PERSON><PERSON> un magasin", "start": 17.939999999999998, "end": 20.14}, {"text": "<PERSON><PERSON> une boulangerie", "start": 20.880000000000003, "end": 23.08}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q5"], "correct_answer": "A", "source_location": "test2_gr Q5"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q6.mp3", "chunks": [{"text": "Et qu'est-ce que tu fais dans la vie en plus de la musique ?", "start": 0.0, "end": 3.48}, {"text": "<PERSON><PERSON>ai beaucoup d'amis.", "start": 4.039999999999999, "end": 5.96}, {"text": "<PERSON><PERSON> Je pars en voyage.", "start": 8.18, "end": 10.32}, {"text": "C. Je suis informaticienne.", "start": 12.48, "end": 14.94}, {"text": "D. Je viens du Sénégal.", "start": 16.84, "end": 19.32}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q6"], "correct_answer": "C", "source_location": "test2_gr Q6"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q7.mp3", "chunks": [{"text": "Je joue de la guitare. Et toi, tu joues d'un instrument ?", "start": 0.0, "end": 4.34}, {"text": "<PERSON><PERSON>, j'aime la musique.", "start": 4.34, "end": 6.76}, {"text": "<PERSON><PERSON>, j'écoute la radio.", "start": 8.18, "end": 10.86}, {"text": "<PERSON><PERSON>, je joue du piano.", "start": 12.32, "end": 14.7}, {"text": "<PERSON><PERSON>, je suis au concert.", "start": 16.26, "end": 18.8}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q7"], "correct_answer": "C", "source_location": "test2_gr Q7"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q8.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, nous avons réservé une table pour 4 au nom de Durand.", "start": 0.0, "end": 3.86}, {"text": "<PERSON><PERSON> mes remerciements.", "start": 5.48, "end": 8.46}, {"text": "<PERSON><PERSON> <PERSON><PERSON>, suivez-moi.", "start": 10.200000000000003, "end": 12.68}, {"text": "<PERSON><PERSON> de vous connaître.", "start": 13.68, "end": 16.52}, {"text": "<PERSON><PERSON> n'est pas grave.", "start": 17.88, "end": 20.04}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q8"], "correct_answer": "B", "source_location": "test2_gr Q8"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q9.mp3", "chunks": [{"text": "Quelle est votre date de naissance ?", "start": 0.0, "end": 3.06}, {"text": "<PERSON><PERSON> 12 mai prochain.", "start": 3.06, "end": 5.58}, {"text": "<PERSON><PERSON> 22 juin 1976.", "start": 6.94, "end": 10.16}, {"text": "C<PERSON> 1990.", "start": 11.8, "end": 14.16}, {"text": "<PERSON><PERSON>.", "start": 15.54, "end": 17.5}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q9"], "correct_answer": "B", "source_location": "test2_gr Q9"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q10.mp3", "chunks": [{"text": "Où habites-tu ?", "start": 0.0, "end": 1.62}, {"text": "<PERSON><PERSON> le troisième arrondissement.", "start": 2.3000000000000003, "end": 5.02}, {"text": "B. De Nice.", "start": 5.9, "end": 8.08}, {"text": "<PERSON><PERSON>.", "start": 8.9, "end": 11.6}, {"text": "<PERSON><PERSON> trois ans.", "start": 12.56, "end": 15.04}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q10"], "correct_answer": "A", "source_location": "test2_gr Q10"}, {"image_path": null, "audio_path": "listening_asset_free/media_test2/Q17.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, <PERSON> de la société Telcom.", "start": 0.0, "end": 3.18}, {"text": "Nous avons bien reçu votre candidature et souhaiterions vous rencontrer pour un entretien.", "start": 3.36, "end": 7.88}, {"text": "Pourriez-vous me rappeler afin de convenir d'une date ?", "start": 8.56, "end": 11.16}, {"text": "Merci beaucoup. Au revoir.", "start": 11.16, "end": 12.98}, {"text": "Que doit faire la personne qui reçoit ce message ?", "start": 15.24, "end": 18.38}], "choices": {"A": "Comp<PERSON>ter son dossier.", "B": "Retourner l’appel.", "C": "Écrire un message.", "D": "Envoyer son <PERSON><PERSON>."}, "test_id": "test2_gr", "locations": ["test2_gr Q17", "test14 Q10", "test22 Q9", "test37 Q9"], "correct_answer": "B", "source_location": "test2_gr Q17"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q5.mp3", "chunks": [{"text": "<PERSON>tte année, c'est décidé. Nous allons faire du ski pendant les vacances de Noël. Tu nous accompagnes ?", "start": 0.0, "end": 8.88}, {"text": "<PERSON><PERSON> plaisir. Je suis libre demain.", "start": 9.76, "end": 13.66}, {"text": "B. Bonne idée. J'adore la neige.", "start": 15.34, "end": 19.4}, {"text": "C. D'accord. Je pars tout seul.", "start": 21.04, "end": 24.94}, {"text": "D. Super. Je connais bien cette ville.", "start": 26.52, "end": 31.26}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q5", "test17 Q7", "test33 Q7"], "correct_answer": "B", "source_location": "test1 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q6.mp3", "chunks": [{"text": "<PERSON>ut <PERSON>, ça va ? T'as l'air fatiguée. Quand est -ce que tu pars en vacances ?", "start": 0.0, "end": 7.0}, {"text": "A. J'ai mon vol mardi prochain.", "start": 8.46, "end": 11.78}, {"text": "<PERSON>. Je reste cinq jours sur place.", "start": 13.36, "end": 16.96}, {"text": "<PERSON><PERSON> vais visiter l'Italie.", "start": 18.54, "end": 21.62}, {"text": "D. Je voyage avec mes collègues.", "start": 23.16, "end": 32.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q6", "test6 Q9", "test35 Q8"], "correct_answer": "A", "source_location": "test1 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q7.mp3", "chunks": [{"text": "Qui a écrit ce roman policier?", "start": 0.0, "end": 2.18}, {"text": "A. C'est un jeune auteur.", "start": 3.66, "end": 7.08}, {"text": "B. Il est en solde aujourd'hui.", "start": 8.62, "end": 12.44}, {"text": "<PERSON><PERSON> le trouve intéressant.", "start": 14.06, "end": 17.32}, {"text": "<PERSON><PERSON> <PERSON> sœur me l'a offert.", "start": 18.92, "end": 22.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q7"], "correct_answer": "A", "source_location": "test1 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q8.mp3", "chunks": [{"text": "Pour le souper de ce soir avec tes parents, qu'est -ce que tu penses d'un poulet aux champignons?", "start": 0.0, "end": 6.12}, {"text": "A. <PERSON> sera parfait avec du riz.", "start": 7.86, "end": 10.76}, {"text": "B. Ils sont contents de venir.", "start": 12.48, "end": 15.06}, {"text": "C. Je m'occupe du dessert.", "start": 16.86, "end": 19.18}, {"text": "<PERSON><PERSON> On peut manger au salon?", "start": 20.92, "end": 23.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q8"], "correct_answer": "A", "source_location": "test1 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q9.mp3", "chunks": [{"text": "<PERSON><PERSON>r, monsieur. <PERSON><PERSON> pouvez m'aider, s'il vous plaît ?", "start": 0.0, "end": 3.48}, {"text": "Je cherche une boîte pour poster ce courrier.", "start": 3.48, "end": 6.14}, {"text": "Ah, allez à la poste ? C'est à gauche, juste après le cinéma.", "start": 6.8, "end": 11.86}, {"text": "<PERSON>u al<PERSON>, il y en a une de l'autre côté, près du métro, en face de la pharmacie.", "start": 12.14, "end": 17.84}, {"text": "Ah, parfait. Merci.", "start": 18.32, "end": 20}, {"text": "Que veut faire cette femme ?", "start": 20, "end": 25.48}], "choices": {"A": "Acheter des médicaments.", "B": "Envoyer une lettre.", "C": "Prendre un transport.", "D": "Voir un film."}, "test_id": "test1", "locations": ["test1 Q9"], "correct_answer": "B", "source_location": "test1 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q10.mp3", "chunks": [{"text": "Salut Laura. Tu fais une drôle de tête.", "start": 0.0, "end": 3.18}, {"text": "Tu ne te sens pas bien ? Non, je pense que je n'ai pas réussi mon examen d'histoire.", "start": 3.64, "end": 8.94}, {"text": "Je n'ai pas pu réviser. <PERSON><PERSON>, je suis allée au cinéma et dimanche, j'ai joué au tennis.", "start": 9.5, "end": 14.94}, {"text": "Tu pouvais travailler le soir ?", "start": 15.38, "end": 17.36}, {"text": "<PERSON>s j'étais beaucoup trop fatiguée.", "start": 17.36, "end": 19.48}, {"text": "De quoi Laura a -t -elle peur ?", "start": 21.78, "end": 24.66}], "choices": {"A": "D’arriver en retard.", "B": "D’avoir une mauvaise note.", "C": "De manquer de temps.", "D": "De tomber malade."}, "test_id": "test1", "locations": ["test1 Q10"], "correct_answer": "B", "source_location": "test1 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q6.mp3", "chunks": [{"text": "J'ai trouvé un nouveau restaurant. On réserve une table pour deux à midi. Tu es d'accord ?", "start": 0.0, "end": 7.0}, {"text": "<PERSON><PERSON>, je mange avec mes collègues.", "start": 8.32, "end": 12.02}, {"text": "<PERSON><PERSON>, je ne peux pas t'aider.", "start": 13.66, "end": 17.2}, {"text": "<PERSON><PERSON>, je préfère le menu à 18 euros.", "start": 18.94, "end": 23.64}, {"text": "<PERSON><PERSON>, je te réponds dans la soirée. Désolé, je préfère le menu à 18 euros.", "start": 26.0, "end": 33.74}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q6", "test6 Q6", "test13 Q7", "test25 Q6"], "correct_answer": "A", "source_location": "test2 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON>, je prends l'avion jeudi à 4h du matin. À cette heure -là, il n'y a pas de transport en commun. Tu pourrais m'emmener à l'aéroport en voiture ?", "start": 0.6, "end": 10.38}, {"text": "<PERSON><PERSON>, j'adore les voyages.", "start": 11.5, "end": 14.74}, {"text": "<PERSON><PERSON> <PERSON><PERSON> sûr, je passe te prendre chez toi.", "start": 16.6, "end": 19.62}, {"text": "C. D'accord, je te prêterai ma valise.", "start": 21.54, "end": 24.88}, {"text": "<PERSON><PERSON>, je viendrai te chercher à la gare.", "start": 27.32, "end": 30.3}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q7", "test14 Q9", "test20 Q6"], "correct_answer": "B", "source_location": "test2 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q8.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, il fera beau. J'aimerais bien faire un peu de sport. Tu veux faire du vélo avec moi ?", "start": 0.0, "end": 9.34}, {"text": "<PERSON><PERSON>, je prends mes lunettes de soleil.", "start": 9.96, "end": 13.64}, {"text": "<PERSON><PERSON> idée. On pourra aller en forêt.", "start": 15.38, "end": 19.4}, {"text": "C. C'est d'accord. J'arrive tout de suite.", "start": 21.16, "end": 24.74}, {"text": "<PERSON><PERSON> dommage. Demain c'est fermé.", "start": 26.2, "end": 30.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q8", "test6 Q8"], "correct_answer": "B", "source_location": "test2 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q9.mp3", "chunks": [{"text": "<PERSON><PERSON>, je fais une fête samedi, tu viens ? <PERSON><PERSON>, super, avec plaisir. Comment on fait pour aller chez toi ?", "start": 0.0, "end": 7.4}, {"text": "<PERSON><PERSON> apportes quelque chose à boire.", "start": 7.94, "end": 10.5}, {"text": "<PERSON><PERSON> arrives vers 20h30.", "start": 12.48, "end": 15.16}, {"text": "<PERSON><PERSON> descends au dernier arrêt du bus 78.", "start": 17.22, "end": 20.46}, {"text": "<PERSON><PERSON> Tu peux venir avec ta sœur si tu veux.", "start": 23.26, "end": 30.78}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q9", "test33 Q10"], "correct_answer": "C", "source_location": "test2 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q10.mp3", "chunks": [{"text": "À 20h50 sur notre chaîne, participez à notre émission.", "start": 0.0, "end": 5.5}, {"text": "Composez le 01 40 00 01 01 pour choisir la meilleure chanson francophone de l'année.", "start": 5.92, "end": 16.84}, {"text": "Que doit -on faire pour participer ?", "start": 19.02, "end": 30.4}], "choices": {"A": "Écrire.", "B": "Téléphoner.", "C": "Lire.", "D": "Écouter."}, "test_id": "test2", "locations": ["test2 Q10", "test25 Q10"], "correct_answer": "B", "source_location": "test2 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q12.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON> monsieur, je suis étudiante, je cherche un logement.", "start": 0.0, "end": 5.0}, {"text": "Bienvenue chez nous. Est -ce que vous savez comment fonctionne notre association ?", "start": 5.66, "end": 10.0}, {"text": "Non, pas très bien. Eh bien, vous habitez chez une personne âgée et en échange vous lui rendez quelques petits services, faire les courses, préparer", "start": 10.6, "end": 21.38}, {"text": "le repas et surtout lui tenir compagnie. Et combien ça coûte ?", "start": 21.38, "end": 25.56}, {"text": "Ça dépend du temps passé auprès de la personne et de ce que vous faites.", "start": 25.56, "end": 29.3}, {"text": "Vous payerez un petit loyer si vous assurez seulement une présence certains soirs.", "start": 30.0, "end": 34.48}, {"text": "Que permet cette association ?", "start": 36.72, "end": 38.88}], "choices": {"A": "De faire des rencontres.", "B": "De manger gratuitement.", "C": "De trouver un travail.", "D": "De vivre chez quelqu’un."}, "test_id": "test2", "locations": ["test2 Q12", "test7 Q10", "test27 Q9"], "correct_answer": "D", "source_location": "test2 Q12"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q4.mp3", "chunks": [{"text": "Combien coûte un ticket de bus ?", "start": 0.0, "end": 3.18}, {"text": "A. 2 km.", "start": 4.23, "end": 6.08}, {"text": "B. La ligne 4.", "start": 7.9, "end": 10.36}, {"text": "C. 3 heures.", "start": 12.04, "end": 14.38}, {"text": "D. 1,20 €.", "start": 15.94, "end": 18.18}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q4", "test22 Q5"], "correct_answer": "D", "source_location": "test3 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q5.mp3", "chunks": [{"text": "Tu voudrais bien t'inscrire à ce cours de danse avec moi ?", "start": 0.0, "end": 4.12}, {"text": "A. C'est sûr. Elle va aimer la salsa.", "start": 4.98, "end": 8.56}, {"text": "B. D'accord. Si ça te fait plaisir.", "start": 10.36, "end": 14.12}, {"text": "C. J'ai étudié le piano l'an dernier.", "start": 16.0, "end": 19.22}, {"text": "<PERSON><PERSON> professeur t'a appelé hier.", "start": 20.94, "end": 24.14}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q5"], "correct_answer": "B", "source_location": "test3 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q6.mp3", "chunks": [{"text": "Qu'est -ce que tu prends pour aller à la plage ?", "start": 0.0, "end": 3.74}, {"text": "<PERSON><PERSON> quelques amis.", "start": 3.74, "end": 6.62}, {"text": "<PERSON>. Juste mon maillot de bain.", "start": 8.34, "end": 11.04}, {"text": "C. Nous y allons à pied.", "start": 12.76, "end": 15.14}, {"text": "<PERSON><PERSON>, c'est un bel endroit.", "start": 16.94, "end": 19.78}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q6", "test7 Q6", "test9 Q9", "test19 Q7"], "correct_answer": "B", "source_location": "test3 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, je fais une enquête sur les Français et leur temps libre. Pourriez -vous me parler de vos loisirs préférés ?", "start": 0.0, "end": 9.76}, {"text": "<PERSON><PERSON> <PERSON>adore jouer au tennis de table.", "start": 10.72, "end": 14.14}, {"text": "B. Je préfère faire les courses en semaine.", "start": 15.76, "end": 19.8}, {"text": "C<PERSON> souhaite travailler en équipe.", "start": 21.5, "end": 25.46}, {"text": "<PERSON><PERSON> <PERSON> voudrais acheter une maison.", "start": 26.88, "end": 30.56}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q7", "test24 Q7"], "correct_answer": "A", "source_location": "test3 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q8.mp3", "chunks": [{"text": "On pourrait aller voir un film cet après -midi.", "start": 0.0, "end": 3.24}, {"text": "Ça te dit ? Tu sais, je dois faire des courses.", "start": 3.58, "end": 7.0}, {"text": "Le frigidaire est vide. Et je dois chercher un paquet et des timbres.", "start": 7.36, "end": 12.2}, {"text": "<PERSON>t puis, je préf<PERSON><PERSON>is aller nager. Pourquoi pas ?", "start": 12.68, "end": 17.96}, {"text": "Je prépare les maillots et les serviettes et on se retrouve à 16h.", "start": 17.96, "end": 23.18}, {"text": "Où est -ce que ces personnes vont se retrouver à 16h ?", "start": 25.58, "end": 29.7}], "choices": {"A": "À la piscine.", "B": "À la poste.", "C": "Au cinéma.", "D": "Au marché."}, "test_id": "test3", "locations": ["test3 Q8", "test24 Q8"], "correct_answer": "A", "source_location": "test3 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q9.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, madame. J'habite à Québec depuis deux mois.", "start": 0.0, "end": 4.3}, {"text": "Je cherche une activité sportive à faire dans le quartier Montcalm.", "start": 4.84, "end": 9.0}, {"text": "<PERSON><PERSON><PERSON> -vous m'aider? » « <PERSON><PERSON>, monsieur.", "start": 9.36, "end": 12.06}, {"text": "Voici un catalogue avec de nombreuses informations utiles.", "start": 12.5, "end": 16.82}, {"text": "Qu'est -ce que cet homme veut faire?", "start": 18.22, "end": 21.36}], "choices": {"A": "Acheter un magazine.", "B": "Choisir un sport.", "C": "Trouver un logement.", "D": "Visiter un quartier."}, "test_id": "test3", "locations": ["test3 Q9", "test24 Q9", "test39 Q10"], "correct_answer": "B", "source_location": "test3 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON>, tu as des projets pour ce week -end ?", "start": 0.0, "end": 3.14}, {"text": "Non, pas vraiment. Et toi ?", "start": 3.14, "end": 5.16}, {"text": "<PERSON> oui, j'ai bien envie d'aller à la piscine samedi après -midi.", "start": 5.16, "end": 9.42}, {"text": "Ça te dit de venir avec moi ?", "start": 9.74, "end": 11.56}, {"text": "Pourquoi pas. J'ai besoin de me détendre après mes examens.", "start": 11.56, "end": 14.78}, {"text": "On se retrouve devant l'université avant.", "start": 15.26, "end": 17.54}, {"text": "Et on fait le trajet ensemble. On dit 14h ?", "start": 17.78, "end": 20.82}, {"text": "»<PERSON><PERSON>, d'accord. Que fait <PERSON> ?", "start": 20.82, "end": 22.99}, {"text": "<PERSON> fait Nicolas", "start": 23, "end": 26.64}], "choices": {"A": "Il accepte la proposition de son ami.", "B": "Il offre son aide à son ami.", "C": "Il propose une sortie à son ami.", "D": "Il refuse l’invitation de son ami."}, "test_id": "test3", "locations": ["test3 Q10", "test24 Q10"], "correct_answer": "A", "source_location": "test3 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON>r madame, je voudrais des informations sur cette machine à laver. Est -ce qu'elle fait beaucoup de bruit ?", "start": 0.0, "end": 6.86}, {"text": "<PERSON><PERSON>, elle a un programme silencieux.", "start": 6.86, "end": 10.38}, {"text": "<PERSON><PERSON>, elle consomme peu d'énergie.", "start": 12.26, "end": 15.32}, {"text": "<PERSON><PERSON>, elle est facile à installer.", "start": 17.36, "end": 20.4}, {"text": "<PERSON><PERSON>, elle peut contenir 8 kilos. ", "start": 22.46, "end": 31.68}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q5"], "correct_answer": "A", "source_location": "test4 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q6.mp3", "chunks": [{"text": "<PERSON><PERSON>, dis -moi pourquoi tu es toujours aussi en forme.", "start": 0.0, "end": 3.78}, {"text": "<PERSON><PERSON> Je fais souvent de l'exercice.", "start": 5.44, "end": 8.76}, {"text": "B. Je lis des histoires drôles.", "start": 10.32, "end": 13.54}, {"text": "<PERSON><PERSON> regarde le sport à la télé.", "start": 15.22, "end": 18.42}, {"text": "<PERSON><PERSON> vais au cinéma.", "start": 20.08, "end": 22.72}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q6"], "correct_answer": "A", "source_location": "test4 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON>, la réception ? <PERSON>, je suis dans la chambre 405. Pouvez -vous me faire monter mon repas ?", "start": 0.0, "end": 8.92}, {"text": "<PERSON><PERSON> ou sans sucre, monsieur ?", "start": 9.52, "end": 13.6}, {"text": "B. C'est à quel sujet, je vous prie ?", "start": 13.6, "end": 17.38}, {"text": "C. C'est un appel pour l'étranger ?", "start": 18.58, "end": 21.8}, {"text": "<PERSON><PERSON> avez déjà choisi votre menu ?", "start": 23.02, "end": 26.52}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q7"], "correct_answer": "D", "source_location": "test4 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q8.mp3", "chunks": [{"text": "Est -ce que vos amis sud -américains parlent français ?", "start": 0.0, "end": 4.1}, {"text": "<PERSON><PERSON>, ils ont pris des cours.", "start": 5.0, "end": 8.52}, {"text": "<PERSON><PERSON>, je parle espagnol avec eux.", "start": 10.0, "end": 14.52}, {"text": "<PERSON><PERSON>, nous discutons ensemble.", "start": 15.94, "end": 20.16}, {"text": "<PERSON><PERSON>, on se téléphone souvent.", "start": 21.6, "end": 25.8}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q8"], "correct_answer": "B", "source_location": "test4 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q9.mp3", "chunks": [{"text": "Quand commence le Festival de jazz de Montréal?", "start": 0.0, "end": 3.62}, {"text": "<PERSON><PERSON> juin.", "start": 4.86, "end": 7.44}, {"text": "<PERSON><PERSON> hier.", "start": 8.58, "end": 11.36}, {"text": "<PERSON><PERSON>à minuit.", "start": 12.64, "end": 15.36}, {"text": "D. <PERSON>dant dix jours.", "start": 17.18, "end": 19.54}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q9", "test12 Q10"], "correct_answer": "A", "source_location": "test4 Q9"}, {"image_path": "listening_asset/media_test5/Q5.webp", "audio_path": "listening_asset/media_test5/Q5.mp3", "chunks": [{"text": "Regardez l'image 5.", "start": 0.0, "end": 2.7}, {"text": "<PERSON><PERSON>oi<PERSON> le résultat de vos analyses médicales.", "start": 4.5, "end": 8.26}, {"text": "B. Temps chaud et sec au sud de l'Europe.", "start": 9.58, "end": 13.02}, {"text": "<PERSON><PERSON> une peinture moderne.", "start": 14.66, "end": 17.4}, {"text": "D. Les indices économiques sont bons.", "start": 18.94, "end": 21.94}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q5"], "correct_answer": "D", "source_location": "test5 Q5"}, {"image_path": "listening_asset/media_test5/Q8.webp", "audio_path": "listening_asset/media_test5/Q8.mp3", "chunks": [{"text": "Regardez l'image 8.", "start": 0.0, "end": 2.34}, {"text": "A. C'est un acteur formidable.", "start": 3.9, "end": 7.14}, {"text": "B<PERSON> Je déteste la publicité.", "start": 8.98, "end": 12.54}, {"text": "C. Tu vas payer nos billets.", "start": 14.1, "end": 16.76}, {"text": "<PERSON><PERSON>, on va s'asseoir là.", "start": 18.3, "end": 21.5}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q8", "test17 Q4"], "correct_answer": "D", "source_location": "test5 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test5/Q9.mp3", "chunks": [{"text": "Bon<PERSON>r, madame. Je souhaiterais réserver dans votre hôtel pour le week -end prochain. Vous avez de la place disponible ?", "start": 0.0, "end": 9.5}, {"text": "<PERSON><PERSON>, il y a des chambres libres.", "start": 10.52, "end": 14.34}, {"text": "<PERSON><PERSON>, le centre -ville est très proche.", "start": 15.78, "end": 20.28}, {"text": "<PERSON><PERSON>, le petit -déjeuner est compris.", "start": 21.84, "end": 26.4}, {"text": "<PERSON><PERSON>, nous avons un parking privé.", "start": 27.98, "end": 32.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q9"], "correct_answer": "A", "source_location": "test5 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test5/Q10.mp3", "chunks": [{"text": "Si tu veux, je t'emmène à la piscine cet après -midi. Qu'est -ce que tu en penses?", "start": 0.0, "end": 6.28}, {"text": "<PERSON><PERSON>'ai cassé ma raquette.", "start": 7.92, "end": 10.86}, {"text": "B<PERSON> J'ai oublié mon ballon.", "start": 12.66, "end": 15.48}, {"text": "C. J'ai perdu mon maillot.", "start": 17.2, "end": 19.88}, {"text": "D. J'ai trouvé mes chaussures. S. J'ai perdu mon maillot.", "start": 21.54, "end": 30.66}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q10", "test21 Q7"], "correct_answer": "C", "source_location": "test5 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test6/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, docteur. <PERSON><PERSON><PERSON>, <PERSON><PERSON>. As<PERSON>ez -vous. Quel est le problème?", "start": 0.0, "end": 6.82}, {"text": "<PERSON><PERSON> fait très chaud.", "start": 8.52, "end": 11.36}, {"text": "B. J'ai mal au ventre.", "start": 13.0, "end": 16.1}, {"text": "<PERSON><PERSON> Je cherche mes clés.", "start": 17.66, "end": 20.98}, {"text": "<PERSON><PERSON> Mon téléphone est cassé.", "start": 22.56, "end": 26.1}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test6", "locations": ["test6 Q5", "test24 Q5"], "correct_answer": "B", "source_location": "test6 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test6/Q7.mp3", "chunks": [{"text": "Mon neveu vient d'avoir cinq ans. Quel cadeau est -ce que je peux lui offrir ?", "start": 0.0, "end": 6.3}, {"text": "<PERSON><PERSON> jouet est beaucoup trop cher.", "start": 6.87, "end": 9.64}, {"text": "B. C'est un très joli prénom.", "start": 11.48, "end": 14.28}, {"text": "<PERSON><PERSON> la question à ses parents.", "start": 16.14, "end": 18.76}, {"text": "<PERSON><PERSON> pas, c'est une bonne idée.", "start": 20.54, "end": 23.58}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test6", "locations": ["test6 Q7", "test34 Q7"], "correct_answer": "C", "source_location": "test6 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test6/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, tu manges au lycée aujourd'hui ?", "start": 0.0, "end": 4.12}, {"text": "<PERSON><PERSON>, j'adore les légumes.", "start": 5.04, "end": 8.36}, {"text": "<PERSON><PERSON>, je peux dîner avec toi.", "start": 9.98, "end": 13.36}, {"text": "<PERSON><PERSON>, je préfère manger chez moi.", "start": 15.04, "end": 18.88}, {"text": "<PERSON><PERSON>, je vais à la cantine.", "start": 20.4, "end": 23.82}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test6", "locations": ["test6 Q10"], "correct_answer": "D", "source_location": "test6 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test7/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, c'est la fête d'anniversaire de Léa. On pourrait lui offrir une belle robe. Qu'en penses -tu ?", "start": 0.0, "end": 8.86}, {"text": "<PERSON><PERSON>, ce sont ses fleurs préférées.", "start": 8.86, "end": 13.12}, {"text": "<PERSON><PERSON>, elle va beaucoup aimer.", "start": 14.68, "end": 18.32}, {"text": "<PERSON><PERSON>, il y aura toute sa famille.", "start": 19.98, "end": 23.92}, {"text": "<PERSON><PERSON>, j'ai acheté un gâteau.", "start": 25.6, "end": 29.56}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test7", "locations": ["test7 Q5"], "correct_answer": "B", "source_location": "test7 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test7/Q7.mp3", "chunks": [{"text": "Pourquoi étiez -vous absent hier après -midi?", "start": 0.0, "end": 1.76}, {"text": "<PERSON><PERSON>avais rendez -vous chez le médecin.", "start": 3.6, "end": 6.04}, {"text": "<PERSON><PERSON> Moi non plus. Je ne suis pas venu.", "start": 7.88, "end": 10.84}, {"text": "<PERSON><PERSON> Je ne sais pas. Je n'étais pas là.", "start": 12.74, "end": 15.56}, {"text": "<PERSON><PERSON>, mais je n'ai pas eu le temps.", "start": 17.38, "end": 20.16}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test7", "locations": ["test7 Q7"], "correct_answer": "A", "source_location": "test7 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test7/Q8.mp3", "chunks": [{"text": "Allô ? <PERSON><PERSON>, bonjour. Je vous appelle pour votre petite annonce.", "start": 0.0, "end": 5.44}, {"text": "Est -ce que votre voiture est toujours à vendre ?", "start": 5.9, "end": 8.3}, {"text": "Eh bien oui, mais vous avez compris qu'elle ne démarre pas.", "start": 8.3, "end": 11.82}, {"text": "<PERSON><PERSON>, ça j'ai compris. Vous voulez la vendre 800 euros ?", "start": 12.18, "end": 16.62}, {"text": "La réparation coûte 300 euros, alors j'ai décidé de la laisser à 500 euros.", "start": 16.62, "end": 21.72}, {"text": "À ce prix -là, c'est une bonne affaire.", "start": 21.92, "end": 23.5}, {"text": "Quel est le problème avec la voiture ?", "start": 25.66, "end": 28.66}], "choices": {"A": "Elle est déjà vendue.", "B": "Elle est en panne.", "C": "Elle est irréparable.", "D": "Elle est trop chère."}, "test_id": "test7", "locations": ["test7 Q8", "test10 Q10"], "correct_answer": "B", "source_location": "test7 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test7/Q9.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, je viens d'emménager à Marseille et je voudrais déposer un dossier pour m'inscrire à l'université.", "start": 0.0, "end": 6.96}, {"text": "Vous avez rempli le dossier ? <PERSON>ui, le voilà, avec les justificatifs.", "start": 7.38, "end": 12.26}, {"text": "Très bien, mais je ne vois pas la copie de votre passeport.", "start": 12.92, "end": 16.44}, {"text": "C'est indispensable.", "start": 16.8, "end": 18}, {"text": "Que souhaite la jeune femme ?", "start": 18.5, "end": 23.34}], "choices": {"A": "Chercher un travail en France.", "B": "Obtenir une bourse d’études.", "C": "Suivre des cours à l’université.", "D": "Trouver un appartement à Marseille."}, "test_id": "test7", "locations": ["test7 Q9", "test15 Q9"], "correct_answer": "C", "source_location": "test7 Q9"}, {"image_path": "listening_asset/media_test8/Q5.webp", "audio_path": "listening_asset/media_test8/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON> neige encore.", "start": 0.0, "end": 2.58}, {"text": "<PERSON><PERSON> <PERSON>attends le soleil.", "start": 4.06, "end": 7.06}, {"text": "<PERSON><PERSON> belle journée!", "start": 8.54, "end": 11.26}, {"text": "<PERSON><PERSON> Qu'est -ce qu'il pleut!", "start": 12.9, "end": 15.68}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q5", "test26 Q5"], "correct_answer": "C", "source_location": "test8 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test8/Q6.mp3", "chunks": [{"text": "Je joue souvent au football avec mes enfants. Et vous ? Vous faites du sport ?", "start": 0.0, "end": 6.5}, {"text": "<PERSON><PERSON>, j'admire les surfers.", "start": 7.24, "end": 10.54}, {"text": "<PERSON><PERSON>, j'ai un ballon.", "start": 12.24, "end": 15.1}, {"text": "<PERSON><PERSON>, je fais du vélo.", "start": 16.82, "end": 19.78}, {"text": "<PERSON><PERSON>, je regarde le match.", "start": 21.48, "end": 31.26}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q6"], "correct_answer": "C", "source_location": "test8 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test8/Q7.mp3", "chunks": [{"text": "Salut. On ne se connaît pas. Je m'appelle <PERSON>. Je suis à Québec depuis trois mois seulement. Je suis informaticien. Et toi, tu fais quoi?", "start": 0.0, "end": 12.5}, {"text": "<PERSON><PERSON> joue au tennis le dimanche.", "start": 14.1, "end": 17.74}, {"text": "B. Je suis toujours célibataire.", "start": 19.38, "end": 22.92}, {"text": "C. Je travaille dans une école.", "start": 24.58, "end": 27.72}, {"text": "D. Je vis à Montréal maintenant.", "start": 29.1, "end": 32.84}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q7", "test26 Q7", "test30 Q7"], "correct_answer": "C", "source_location": "test8 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test8/Q8.mp3", "chunks": [{"text": "Dis -moi, quand est -ce que tu dois aller à la préfecture ?", "start": 0.0, "end": 5.38}, {"text": "A. J'ai consulté mon calendrier hier soir.", "start": 5.98, "end": 9.7}, {"text": "<PERSON><PERSON> J'ai eu un rendez -vous pour demain matin.", "start": 11.34, "end": 15.14}, {"text": "C<PERSON> <PERSON>'ai répondu à son courrier aujourd'hui.", "start": 16.92, "end": 20.42}, {"text": "D. J'ai voyagé avec lui la semaine dernière.", "start": 22.02, "end": 25.86}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q8"], "correct_answer": "B", "source_location": "test8 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test8/Q9.mp3", "chunks": [{"text": "<PERSON><PERSON>, bonjour. Est -ce que je peux parler à Céline, s'il vous plaît ?", "start": 0.0, "end": 5.0}, {"text": "A. C'est de la part de qui ?", "start": 6.1, "end": 9.84}, {"text": "B<PERSON> Elle a un numéro de téléphone ?", "start": 10.58, "end": 14.34}, {"text": "C. Je suis bien au 01 42 35 80 96.", "start": 15.84, "end": 24.7}, {"text": "<PERSON><PERSON> voulez laisser un message ?", "start": 26.88, "end": 29.98}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q9", "test16 Q9", "test23 Q9"], "correct_answer": "A", "source_location": "test8 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test8/Q10.mp3", "chunks": [{"text": "Je serai votre guide pendant cette visite. Suivez -moi s'il vous plaît.", "start": 0.0, "end": 4.32}, {"text": "<PERSON><PERSON> vais vous faire découvrir ce musée.", "start": 5.9, "end": 9.04}, {"text": "<PERSON><PERSON> Je vais vous indiquer la salle d'attente.", "start": 10.74, "end": 14.0}, {"text": "<PERSON><PERSON> vais vous installer à une table.", "start": 15.76, "end": 18.7}, {"text": "<PERSON><PERSON> vais vous montrer votre chambre.", "start": 20.46, "end": 23.46}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q10"], "correct_answer": "A", "source_location": "test8 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q5.mp3", "chunks": [{"text": "Qu'est -ce que tu fais dans la vie ?", "start": 0.0, "end": 2.0}, {"text": "A. J'aime le sport.", "start": 3.0, "end": 5.16}, {"text": "B. Je suis célibataire.", "start": 6.9, "end": 9.2}, {"text": "C<PERSON> vis au Maroc.", "start": 11.48, "end": 13.68}, {"text": "D. J'étudie l'anglais.", "start": 15.54, "end": 17.86}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q5", "test23 Q5"], "correct_answer": "D", "source_location": "test9 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q6.mp3", "chunks": [{"text": "Et un jus d'orange! Un! Et pour vous, monsieur, qu'est -ce que ce sera?", "start": 0.0, "end": 7.0}, {"text": "<PERSON><PERSON>, attendez. J'ai la monnaie, je crois.", "start": 8.9, "end": 12.74}, {"text": "<PERSON><PERSON> Laisse. C'est moi qui t'invite aujourd'hui.", "start": 14.54, "end": 17.9}, {"text": "C<PERSON> Un grand verre de lait, s'il vous plaît.", "start": 19.76, "end": 22.5}, {"text": "<PERSON><PERSON>, il fait trop chaud pour boire ça.", "start": 24.24, "end": 32.52}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q6", "test23 Q6"], "correct_answer": "C", "source_location": "test9 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON>, madame, vous pouvez m'aider ? Je cherche les vêtements de sport.", "start": 0.0, "end": 5.08}, {"text": "A. C'est au deuxième étage.", "start": 6.6, "end": 9.86}, {"text": "B. Ils existent dans ces deux couleurs.", "start": 11.46, "end": 15.28}, {"text": "<PERSON><PERSON> Les soldes commencent demain.", "start": 16.84, "end": 20.06}, {"text": "<PERSON><PERSON> <PERSON> pouvez payer par chèque.", "start": 21.68, "end": 30.54}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q7", "test13 Q6", "test23 Q7", "test33 Q6"], "correct_answer": "A", "source_location": "test9 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q8.mp3", "chunks": [{"text": "Comment on va au spectacle du Cirque du Soleil demain soir?", "start": 0.0, "end": 3.66}, {"text": "<PERSON><PERSON> va commencer tard.", "start": 5.36, "end": 7.86}, {"text": "B. C'est un bel endroit.", "start": 9.66, "end": 11.98}, {"text": "<PERSON><PERSON> Je t'emmène en voiture.", "start": 13.76, "end": 16.14}, {"text": "<PERSON><PERSON> vient avec nous.", "start": 17.94, "end": 20.62}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q8", "test23 Q8"], "correct_answer": "C", "source_location": "test9 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q10.mp3", "chunks": [{"text": "Peux -tu me donner le numéro de téléphone de Gisèle, s'il te plaît ?", "start": 0.0, "end": 5.3}, {"text": "A. C'est beaucoup trop cher.", "start": 5.92, "end": 8.24}, {"text": "<PERSON><PERSON> Je lui envoie un courriel.", "start": 9.88, "end": 12.86}, {"text": "C. Reg<PERSON> dans mon carnet.", "start": 15.16, "end": 17.36}, {"text": "<PERSON><PERSON> peux en changer aussi ?", "start": 19.0, "end": 22.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q10"], "correct_answer": "C", "source_location": "test9 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test10/Q6.mp3", "chunks": [{"text": "Tu viens au cinéma avec moi ?", "start": 0.0, "end": 3.62}, {"text": "<PERSON><PERSON>, à ce soir.", "start": 4.28, "end": 7.06}, {"text": "B. C'est une bonne idée.", "start": 8.72, "end": 11.36}, {"text": "<PERSON><PERSON>e.", "start": 13.06, "end": 15.92}, {"text": "<PERSON><PERSON>, ça va bien.", "start": 17.58, "end": 20.32}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q6", "test24 Q6"], "correct_answer": "B", "source_location": "test10 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test10/Q7.mp3", "chunks": [{"text": "Salut Juliette ! <PERSON><PERSON><PERSON> soir, nous allons manger au restaurant avec des amis. Tu veux venir ?", "start": 0.0, "end": 7.0}, {"text": "<PERSON><PERSON>, je ne suis pas libre.", "start": 8.5, "end": 11.72}, {"text": "<PERSON><PERSON> Non merci, j'ai déj<PERSON> mangé.", "start": 13.52, "end": 16.84}, {"text": "<PERSON><PERSON>, j'ai un rendez -vous pour le déjeuner.", "start": 18.64, "end": 22.62}, {"text": "<PERSON><PERSON>, je n'ai pas très faim aujourd'hui. ", "start": 24.3, "end": 31.44}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q7"], "correct_answer": "A", "source_location": "test10 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test10/Q8.mp3", "chunks": [{"text": "<PERSON> va bientôt passer ses examens de médecine. Est -ce qu'il travaille plus que l'année dernière?", "start": 0.0, "end": 6.54}, {"text": "<PERSON><PERSON>, il a de mauvaises notes.", "start": 8.28, "end": 12.16}, {"text": "<PERSON><PERSON>, il refuse d'aller en cours.", "start": 13.74, "end": 18.12}, {"text": "<PERSON><PERSON>, il révise tous les jours.", "start": 19.82, "end": 23.88}, {"text": "<PERSON><PERSON>, il s'est inscrit à l'université.", "start": 25.6, "end": 30.16}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q8", "test19 Q8"], "correct_answer": "C", "source_location": "test10 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test10/Q9.mp3", "chunks": [{"text": "Je pars au stade faire un peu de sport. Et toi, qu'est -ce que tu fais cet après -midi ? J'emmène les enfants à une exposition sur les jouets. Tu crois qu'ils vont aimer ?", "start": 0.0, "end": 14.16}, {"text": "<PERSON><PERSON> <PERSON><PERSON> sûr. Voilà une bonne sortie culturelle.", "start": 14.89, "end": 19.72}, {"text": "B. Certainement. Ils adorent faire les magasins.", "start": 21.48, "end": 26.76}, {"text": "C. D'accord. N'oublie pas leur maillot de bain.", "start": 28.38, "end": 32.88}, {"text": "D. Probablement. Au parc, ils pourront courir un peu.", "start": 34.46, "end": 40.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q9"], "correct_answer": "A", "source_location": "test10 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q5.mp3", "chunks": [{"text": "Question 5. Pour les vacances d'été, je voudrais partir au Japon avec Céline. Qu'est -ce que t'en penses?", "start": 0.82, "end": 9.9}, {"text": "A. C'est vraiment dommage.", "start": 11.78, "end": 14.52}, {"text": "<PERSON><PERSON> <PERSON> aime beaucoup cette ville.", "start": 16.58, "end": 19.7}, {"text": "C. J'ai un peu de temps libre.", "start": 21.74, "end": 24.84}, {"text": "<PERSON><PERSON>lle bonne idée! C'est vraiment h reboté. aldRay aaron. ouëune Parce enxouté des ...", "start": 26.86, "end": 37.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test11", "locations": ["test11 Q5", "test13 Q5"], "correct_answer": "D", "source_location": "test11 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q6.mp3", "chunks": [{"start": 2.22, "end": 8.16, "text": "Question 6 <PERSON><PERSON><PERSON> madame, est-ce que je peux vous aider ?"}, {"start": 18.14, "end": 21.54, "text": "<PERSON><PERSON>, asseyez-vous."}, {"start": 23.66, "end": 26.84, "text": "<PERSON><PERSON>, allongez-vous."}, {"start": 28.12, "end": 32.0, "text": "<PERSON><PERSON>, taisez-vous."}, {"start": 33.9, "end": 37.3, "text": "<PERSON><PERSON>, levez-vous."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test11", "locations": ["test11 Q6", "test37 Q6", "test39 Q7"], "correct_answer": "A", "source_location": "test11 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q7.mp3", "chunks": [{"text": "Bon<PERSON>r madame, je voudrais réserver des places pour le spectacle de danse du 18 décembre.", "start": 0.9, "end": 6.74}, {"text": "A cette date, il reste des places à 30 euros.", "start": 7.02, "end": 10.5}, {"text": "Ah, je ne voudrais pas payer plus de 20 euros.", "start": 11.14, "end": 13.98}, {"text": "Et pour la représentation du 17 ? Désolée, c'est pareil.", "start": 14.18, "end": 17.78}, {"text": "Question 7 Pourquoi est -ce que le jeune homme repart sans billet ?", "start": 20.68, "end": 26.86}], "choices": {"A": "La représentation est annulée.", "B": "Le spectacle est complet.", "C": "Les places sont trop chères.", "D": "Les réservations sont terminées."}, "test_id": "test11", "locations": ["test11 Q7", "test20 Q8", "test27 Q8", "test28 Q9"], "correct_answer": "C", "source_location": "test11 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q8.mp3", "chunks": [{"text": "Hôtel du centre, bonjour. <PERSON><PERSON><PERSON>, j'ai réservé une chambre pour deux nuits le week -end prochain.", "start": 0.78, "end": 7.62}, {"text": "Je voudrais arriver plus tôt. Est -ce que la chambre est libre dès le jeudi soir à 20h ?", "start": 8.0, "end": 14.28}, {"text": "<PERSON><PERSON>ez, je vérifie. Non, elle est prise. Nous avons une autre chambre disponible, mais elle coûte 50 euros de plus.", "start": 14.28, "end": 21.86}, {"text": "Ah, d'accord. Je vais réfléchir. <PERSON><PERSON><PERSON>, madame.", "start": 22.36, "end": 27.06}, {"text": "Question 8 Pourquoi est -ce que cet homme téléphone à l'hôtel ?", "start": 29.7, "end": 35.66}], "choices": {"A": "Pour annuler sa réservation.", "B": "Pour changer sa date d’arrivée.", "C": "Pour connaître l’heure d’ouverture.", "D": "Pour savoir le prix d’une chambre."}, "test_id": "test11", "locations": ["test11 Q8", "test38 Q9"], "correct_answer": "B", "source_location": "test11 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q9.mp3", "chunks": [{"text": "La météo a annoncé de la neige sur Montréal.", "start": 0.96, "end": 3.88}, {"text": "Je me méfie des prévisions, tu sais. Ne", "start": 4.42, "end": 8.4}, {"text": "me reproche rien si tu attrapes froid. <PERSON>,", "start": 8.4, "end": 12.02}, {"text": "d'accord. Pour une fois, je vais t'écouter.", "start": 12.5, "end": 14.92}, {"text": "Je prends mon manteau. ", "start": 15.5, "end": 20}, {"text": "Question 9. Que décide de faire l'homme?", "start": 21.38, "end": 23.82}], "choices": {"A": "De consulter un médecin.", "B": "De regarder la météo.", "C": "De <PERSON>acheter des vêtements.", "D": "De <PERSON>habiller chaudement."}, "test_id": "test11", "locations": ["test11 Q9", "test38 Q10"], "correct_answer": "D", "source_location": "test11 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q5.mp3", "chunks": [{"text": "Question 5. <PERSON><PERSON> <PERSON><PERSON><PERSON>, monsieur, pourriez -vous nous dire où se trouve la poste, s'il vous plaît ?", "start": 0.0, "end": 11.0}, {"text": "<PERSON><PERSON>, achetez un timbre.", "start": 11.0, "end": 15.64}, {"text": "<PERSON><PERSON>, allez au guichet numéro 3.", "start": 17.91, "end": 22.22}, {"text": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>z votre adresse ici.", "start": 24.1, "end": 28.86}, {"text": "<PERSON><PERSON>, prenez la rue en face.", "start": 30.82, "end": 35.18}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q5"], "correct_answer": "D", "source_location": "test12 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q6.mp3", "chunks": [{"start": 0.88, "end": 11.86, "text": "Question 6. Je ne suis pas en forme. J'ai souvent mal au dos. Tu penses que je vais aller mieux si je fais du sport?"}, {"start": 14.78, "end": 18.48, "text": "<PERSON><PERSON>, ce serait bien que tu manges mieux."}, {"start": 21.26, "end": 25.0, "text": "<PERSON><PERSON>, il faut que tu te reposes plus."}, {"start": 26.0, "end": 32, "text": "<PERSON><PERSON> en rendez vous chez le médecin et oui tu devrais t'inscrire à la piscine"}, {"start": 33, "end": 38.74, "text": "<PERSON><PERSON> <PERSON>ui tu devrais t'inscrire à la piscine"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q6", "test35 Q6", "test36 Q6"], "correct_answer": "D", "source_location": "test12 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q7.mp3", "chunks": [{"start": 2.56, "end": 8, "text": "Question 7. <PERSON><PERSON><PERSON>, c'est ici pour visiter l'appartement?"}, {"start": 10.22, "end": 12.3, "text": "Non monsieur, pas ici."}, {"start": 14.18, "end": 18.38, "text": "<PERSON><PERSON> <PERSON>, pardon, j'ai d<PERSON>."}, {"start": 19.8, "end": 23.06, "text": "<PERSON><PERSON>, pardon, j'ai fait une erreur."}, {"start": 25.16, "end": 27.94, "text": "<PERSON><PERSON>, j'ai perdu les clés."}, {"start": 29.82, "end": 33.56, "text": "<PERSON><PERSON>, je vais revenir demain."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q7", "test16 Q8", "test26 Q8", "test32 Q7"], "correct_answer": "B", "source_location": "test12 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q8.mp3", "chunks": [{"start": 0.82, "end": 9.02, "text": "Question 8. C'est bientôt l'heure de monter dans le train, mais est-ce que tu penses que j'ai le temps d'acheter un journal ?"}, {"start": 9.02, "end": 15.06, "text": "<PERSON><PERSON>, le train est annulé."}, {"start": 18.42, "end": 20.66, "text": "<PERSON><PERSON>, le train est complet."}, {"start": 23.04, "end": 26.14, "text": "<PERSON><PERSON>, le train est déjà là."}, {"start": 27, "end": 31.66, "text": "<PERSON><PERSON>, le train est en retard."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q8", "test30 Q8"], "correct_answer": "C", "source_location": "test12 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q9.mp3", "chunks": [{"start": 0.8, "end": 9.88, "text": "Question 9. <PERSON><PERSON> propose de faire du camping dans le sud de la France pour les prochaines vacances. Est-ce que tu en penses ?"}, {"start": 0.88, "end": 16.16, "text": "<PERSON><PERSON>, je réserve l'hôtel."}, {"start": 18.1, "end": 22.26, "text": "B. D'accord, on reste à la maison."}, {"start": 24.22, "end": 28.32, "text": "<PERSON><PERSON>, l'appartement est trop cher."}, {"start": 30.48, "end": 34.78, "text": "<PERSON><PERSON> <PERSON>t pourquoi pas, les enfants vont adorer."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q9", "test30 Q9", "test33 Q9", "test38 Q6", "test39 Q9"], "correct_answer": "D", "source_location": "test12 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test13/Q8.mp3", "chunks": [{"start": 5.24, "end": 12.68, "text": "Ma nièce Camille va avoir dix ans vendredi prochain. J'aimerais l'emmener au cirque. Tu en penses quoi?"}, {"start": 14.46, "end": 19.24, "text": "<PERSON><PERSON>, je déteste les animaux."}, {"start": 20.96, "end": 25.22, "text": "B. D'accord, le spectacle est complet."}, {"start": 27.08, "end": 32.26, "text": "C<PERSON>, la réservation est obligatoire."}, {"start": 34.94, "end": 38.52, "text": "<PERSON><PERSON> pas, c'est un beau cadeau."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q8", "test25 Q9"], "correct_answer": "D", "source_location": "test13 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test13/Q9.mp3", "chunks": [{"start": 2.26, "end": 9.52, "text": "Question 9. Vous avez mal au ventre depuis combien de temps?"}, {"start": 11.6, "end": 14.52, "text": "<PERSON><PERSON> fois par mois."}, {"start": 16.64, "end": 19.74, "text": "B. La semaine prochaine."}, {"start": 21.58, "end": 24.56, "text": "<PERSON><PERSON> les matins."}, {"start": 27.44, "end": 31.52, "text": "<PERSON><PERSON> jours."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q9"], "correct_answer": "D", "source_location": "test13 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test13/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON>, maman?", "start": 1.78, "end": 6.44}, {"text": "Prête pour notre repas au restaurant près du Parc des Rosiers ce midi?", "start": 6.14, "end": 9.86}, {"text": "<PERSON><PERSON><PERSON><PERSON>, j 'ai un gros rhume, je préfère rester à la maison.", "start": 7.52, "end": 11.6}, {"text": "Question 10 Pourquoi est -ce qu 'Elias téléphone à sa mère?", "start": 17.48, "end": 25}], "choices": {"A": "Pour aller manger ensemble.", "B": "Pour annuler leur rendez-vous.", "C": "Pour faire une promenade.", "D": "Pour prendre des nouvelles."}, "test_id": "test13", "locations": ["test13 Q10"], "correct_answer": "A", "source_location": "test13 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test14/Q6.mp3", "chunks": [{"text": "Question 6. Quand est -ce que tu commences ton nouveau travail ?", "start": 1.48, "end": 8.98}, {"text": "<PERSON><PERSON> y a 15 jours.", "start": 10.01, "end": 12.66}, {"text": "B. La semaine prochaine.", "start": 14.08, "end": 16.9}, {"text": "<PERSON><PERSON> les accommodis.", "start": 18.93, "end": 21.1}, {"text": "<PERSON><PERSON>.", "start": 22.48, "end": 25.2}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q6"], "correct_answer": "B", "source_location": "test14 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test14/Q7.mp3", "chunks": [{"start": 1.18, "end": 5.94, "text": "Question 7 J'ai envie de me baigner. On se retrouve à la piscine ?"}, {"start": 5.94, "end": 8.74, "text": "<PERSON><PERSON>, mais tu ne viens pas."}, {"start": 9.38, "end": 12.18, "text": "<PERSON><PERSON>, moi j'aime bien nager."}, {"start": 12.84, "end": 15.58, "text": "<PERSON><PERSON>, j'ai envie de nager."}, {"start": 16.3, "end": 18.62, "text": "<PERSON><PERSON>, pourquoi pas."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q7", "test31 Q7"], "correct_answer": "D", "source_location": "test14 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test14/Q8.mp3", "chunks": [{"start": 0.94, "end": 5.06, "text": "Question 8. Vous avez reçu la commande de Mme Cordy ?"}, {"start": 5.06, "end": 7.68, "text": "<PERSON><PERSON>, bientôt."}, {"start": 7.98, "end": 10.6, "text": "<PERSON><PERSON>, ce matin."}, {"start": 10.88, "end": 13.48, "text": "<PERSON><PERSON>, d'accord."}, {"start": 13.98, "end": 16.72, "text": "<PERSON><PERSON>, pourquoi pas."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q8", "test28 Q7", "test33 Q8"], "correct_answer": "B", "source_location": "test14 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q5.mp3", "chunks": [{"start": 2.6, "end": 11.78, "text": "Question 5 <PERSON><PERSON>, je serai chez toi dans une demi-heure. Est-ce que j'apporte quelque chose à manger ?"}, {"start": 11.78, "end": 19.18, "text": "<PERSON><PERSON>, j'ai déjà préparé un bon repas."}, {"start": 20.96, "end": 25.42, "text": "<PERSON><PERSON>, j'ai vraiment très faim."}, {"start": 28.2, "end": 31.36, "text": "<PERSON><PERSON>, j'ai fait les courses hier."}, {"start": 33.64, "end": 37.86, "text": "<PERSON><PERSON>, on pourra aller au restaurant."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q5", "test29 Q7", "test39 Q6"], "correct_answer": "A", "source_location": "test15 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q6.mp3", "chunks": [{"start": 3.14, "end": 12.1, "text": "Question 6 Oh, des skis ! C'est un super cadeau ! Comment vous avez eu l'idée ?"}, {"start": 12.1, "end": 17.34, "text": "<PERSON><PERSON> On a demandé à ton frère."}, {"start": 19.22, "end": 22.74, "text": "B. On a fait ton gâteau préféré."}, {"start": 24.7, "end": 28.74, "text": "C<PERSON> On a invité tes amis et ta famille."}, {"start": 30.74, "end": 34.1, "text": "D<PERSON> On a réservé une salle."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q6", "test21 Q6", "test22 Q7"], "correct_answer": "A", "source_location": "test15 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q7.mp3", "chunks": [{"start": 3.02, "end": 11.92, "text": "Question 7 J'ai deux places pour aller voir Hamlet au théâtre la semaine prochaine. Ça t'intéresse de venir avec moi ?"}, {"start": 11.92, "end": 17.08, "text": "<PERSON><PERSON> oui, c'est une bonne idée."}, {"start": 19.18, "end": 23.1, "text": "<PERSON><PERSON>, j'ai aimé ce film."}, {"start": 25.38, "end": 28.56, "text": "<PERSON><PERSON>, on prend les billets."}, {"start": 29, "end": 34.04, "text": "<PERSON><PERSON>, regarde le programme."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q7", "test37 Q7"], "correct_answer": "A", "source_location": "test15 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q10.mp3", "chunks": [{"text": "<PERSON>, monsieur, avez -vous choisi? <PERSON><PERSON>, pour moi, une tarte au fromage.", "start": 2.56, "end": 8.98}, {"text": "Et pour madame, du poulet aux trois épices.", "start": 9.22, "end": 12.26}, {"text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, j'ai changé d'avis. Je n'ai pas très faim.", "start": 13.06, "end": 16.82}, {"text": "Je prendrai une salade de crudité. Excellent choix.", "start": 17.22, "end": 21.92}, {"text": "Question 10. Pourquoi est -ce que la cliente commande la salade?", "start": 24.32, "end": 31.12}], "choices": {"A": "Elle est pressée.", "B": "Elle est végétarienne.", "C": "Elle fait le régime.", "D": "Elle manque d’appétit."}, "test_id": "test15", "locations": ["test15 Q10", "test27 Q10"], "correct_answer": "D", "source_location": "test15 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q5.mp3", "chunks": [{"start": 3.64, "end": 9.84, "text": "Question 5 Ce film est vraiment ennuyeux. On s'en va?"}, {"start": 11.72, "end": 15.94, "text": "<PERSON><PERSON>, ça vient juste de commencer."}, {"start": 17.96, "end": 22.22, "text": "B. C'est vrai, j'aime bien cette salle."}, {"start": 24.22, "end": 28.4, "text": "C. D'accord, approchons-nous de l'écran."}, {"start": 30.1, "end": 34.52, "text": "<PERSON><PERSON>, les acteurs sont peu connus."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q5", "test27 Q5"], "correct_answer": "A", "source_location": "test16 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q6.mp3", "chunks": [{"start": 2.7, "end": 14.16, "text": "Question 6 <PERSON><PERSON> ? <PERSON><PERSON>, <PERSON> ? Ça te dirait d'aller au cinéma ? Les places sont à 4 euros tout le week-end."}, {"start": 16.08, "end": 20.32, "text": "<PERSON><PERSON>. Est-ce que ça coûte cher ?"}, {"start": 20.32, "end": 26.04, "text": "<PERSON><PERSON>. On part mercredi ?"}, {"start": 26.04, "end": 32.08, "text": "<PERSON><PERSON> <PERSON><PERSON>, tu as vu son dernier film?"}, {"start": 34.3, "end": 37.58, "text": "<PERSON><PERSON>, qu'est-ce qu'on va voir?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q6"], "correct_answer": "D", "source_location": "test16 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q7.mp3", "chunks": [{"start": 4.48, "end": 8.94, "text": "Question 7. A quelle heure as -tu rendez -vous?"}, {"start": 11.24, "end": 13.56, "text": "A. A tout de suite."}, {"start": 15.92, "end": 18.28, "text": "B. C 'est très rapide."}, {"start": 20.4, "end": 23.0, "text": "<PERSON><PERSON> une demi -heure."}, {"start": 25.06, "end": 27.56, "text": "D. Il est déjà tard."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q7", "test22 Q6", "test34 Q6"], "correct_answer": "C", "source_location": "test16 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q10.mp3", "chunks": [{"start": 2.96, "end": 12.34, "text": "Question 10 Ch<PERSON>, demain soir je vais chez le médecin à 18h30. Tu peux passer au collège et chercher Salva ?"}, {"start": 12.34, "end": 17.2, "text": "<PERSON><PERSON>, c'est juste."}, {"start": 19.42, "end": 22.42, "text": "<PERSON><PERSON>, elle va sortir."}, {"start": 24.64, "end": 27.8, "text": "<PERSON><PERSON>, j'y serai."}, {"start": 29.86, "end": 33.04, "text": "<PERSON><PERSON>, tu as raison."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q10", "test23 Q10", "test39 Q8"], "correct_answer": "C", "source_location": "test16 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test17/Q6.mp3", "chunks": [{"text": "Question 6 Après mon baccalauréat, je voudrais étudier la biologie comme Lucie. Qu'est -ce que tu en penses ?", "start": 2.96, "end": 15.2}, {"text": "<PERSON><PERSON> change parfois.", "start": 15.97, "end": 18.68}, {"text": "<PERSON><PERSON> <PERSON>attends sa réponse.", "start": 20.28, "end": 23.42}, {"text": "C. <PERSON> bien.", "start": 25.16, "end": 28.08}, {"text": "<PERSON><PERSON> dois écouter.", "start": 30.0, "end": 32.72}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test17", "locations": ["test17 Q6"], "correct_answer": "C", "source_location": "test17 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test17/Q8.mp3", "chunks": [{"start": 3.0, "end": 14.62, "text": "Question 8. <PERSON> <PERSON>'app<PERSON>, j'ai 36 ans, je viens de Versailles et je suis traducteur en anglais et chinois."}, {"start": 15.02, "end": 18.38, "text": "Et toi, qu'est-ce que tu viens étudier à Montréal?"}, {"start": 20.28, "end": 24.32, "text": "<PERSON><PERSON>'ai choisi les cours de littérature."}, {"start": 26.16, "end": 30.22, "text": "B. J'ai habité à Bruxelles et à Paris"}, {"start": 32.04, "end": 35.62, "text": "C. J'ai trouvé une chambre d'étudiant"}, {"start": 37.32, "end": 41.22, "text": "D. J'ai voyagé pendant deux ans"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test17", "locations": ["test17 Q8", "test20 Q5", "test25 Q8"], "correct_answer": "A", "source_location": "test17 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test17/Q9.mp3", "chunks": [{"text": "Écoutez le document sonore et la question. Choisissez", "start": 0.0, "end": 5.26}, {"text": "la bonne réponse. Je dois aller au supermarché avant la fermeture.", "start": 5.26, "end": 14.98}, {"text": "Qu'est -ce que tu veux manger ce soir ?", "start": 15.36, "end": 17.6}, {"text": "Je ne sais pas. Des pizzas, c'est possible ?", "start": 17.6, "end": 21.44}, {"text": "<PERSON><PERSON>, mais elles sont meilleures au restaurant italien.", "start": 21.44, "end": 24.1}, {"text": "Tu peux en acheter une avant de rentrer à la maison ?", "start": 24.54, "end": 27.7}, {"text": "<PERSON><PERSON> sûr.", "start": 28.24, "end": 30}, {"text": "Question 9 De quoi est -ce que ces deux personnes parlent ?", "start": 31, "end": 38.18}], "choices": {"A": "D’un atelier de cuisine.", "B": "D’une recette familiale.", "C": "Des courses pour un repas.", "D": "Des horaires d’un magasin."}, "test_id": "test17", "locations": ["test17 Q9", "test28 Q8"], "correct_answer": "C", "source_location": "test17 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q5.mp3", "chunks": [{"text": "Question 5 <PERSON>, je réserve vos billets. Comment voulez -vous voyager ?", "start": 1.88, "end": 11.0}, {"text": "<PERSON><PERSON> parle le 12 mars.", "start": 12.72, "end": 16.22}, {"text": "B. Je préfère l'avion.", "start": 17.7, "end": 21.34}, {"text": "C. Je reste deux jours.", "start": 22.66, "end": 26.18}, {"text": "D. Je vais à Paris.", "start": 27.78, "end": 31.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test18", "locations": ["test18 Q5"], "correct_answer": "B", "source_location": "test18 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q6.mp3", "chunks": [{"text": "Question 6 À quel moment vous parlez de prochain avion pour Rio de Janeiro, s'il vous plaît ?", "start": 2.44, "end": 10.78}, {"text": "<PERSON><PERSON> demain matin", "start": 11.7, "end": 13.38}, {"text": "<PERSON><PERSON> 22h37", "start": 15.06, "end": 17.9}, {"text": "<PERSON><PERSON>", "start": 19.72, "end": 21.94}, {"text": "<PERSON><PERSON> 17h", "start": 23.38, "end": 26.12}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test18", "locations": ["test18 Q6", "test35 Q7"], "correct_answer": "B", "source_location": "test18 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q7.mp3", "chunks": [{"start": 3.02, "end": 9.52, "text": "Question 7. Je peux revenir échanger ce but si ça ne va pas ?"}, {"start": 9.52, "end": 15.08, "text": "<PERSON><PERSON>, tout est à 50%."}, {"start": 16.84, "end": 21.22, "text": "<PERSON><PERSON> <PERSON><PERSON>, mais seulement avec le ticket d'achat."}, {"start": 22.88, "end": 26.5, "text": "<PERSON><PERSON>, je n'ai pas votre taille."}, {"start": 3.02, "end": 32.56, "text": "<PERSON><PERSON>, le rayon 1 est juste en face."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test18", "locations": ["test18 Q7", "test27 Q7"], "correct_answer": "B", "source_location": "test18 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q8.mp3", "chunks": [{"start": 2.18, "end": 10.8, "text": "Question 8 Bon<PERSON>r, j'ai très mal à la gorge. Est-ce que vous pouvez me donner quelque chose ?"}, {"start": 10.8, "end": 17.42, "text": "Je vous conseille ces comprimés qui sont très efficaces. Je dois les prendre pendant combien de temps ?"}, {"start": 17.42, "end": 21.72, "text": "A. <PERSON> milieu du repas"}, {"start": 23.3, "end": 25.84, "text": "<PERSON><PERSON> fois par jour"}, {"start": 25.84, "end": 30.1, "text": "<PERSON><PERSON> Seulement un matin"}, {"start": 30.1, "end": 34.12, "text": "D. Une semaine"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test18", "locations": ["test18 Q8"], "correct_answer": "D", "source_location": "test18 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q9.mp3", "chunks": [{"text": "Question 9 Pourquoi tu vois ça il y a un peu le dit ?", "start": 3.26, "end": 9.28}, {"text": "<PERSON><PERSON>'avais rendez -vous chez le médecin", "start": 10.32, "end": 12.36}, {"text": "<PERSON><PERSON> non plus, je ne suis pas voulu", "start": 14.2, "end": 17.22}, {"text": "<PERSON><PERSON> ne sais pas, je n'étais pas là", "start": 19.04, "end": 21.96}, {"text": "<PERSON><PERSON>, mais je n'ai pas eu le temps", "start": 23.68, "end": 26.48}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test18", "locations": ["test18 Q9", "test29 Q9"], "correct_answer": "A", "source_location": "test18 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test18/Q10.mp3", "chunks": [{"text": "Écoutez le document sonore et la question. Choisissez", "start": 0.62, "end": 5.54}, {"text": "la bonne réponse. Ça ne va pas ?", "start": 5.54, "end": 12.12}, {"text": "<PERSON>, <PERSON><PERSON><PERSON>, j'ai la grippe. Je n'ai pas eu le temps d'aller voir le docteur.", "start": 12.12, "end": 18.22}, {"text": "Il ne faut pas attendre, appelle -le.", "start": 18.36, "end": 22.08}, {"text": "Question 10. Quel conseil donne <PERSON> à son amie ?", "start": 22.08, "end": 28.24}], "choices": {"A": "<PERSON><PERSON>re des médicaments.", "B": "<PERSON><PERSON>r chez elle se coucher.", "C": "Se rendre à la pharmacie.", "D": "Téléphoner au médecin."}, "test_id": "test18", "locations": ["test18 Q10"], "correct_answer": "D", "source_location": "test18 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q5.mp3", "chunks": [{"start": 3.06, "end": 9.9, "text": "Question 5. <PERSON><PERSON>, tout va bien, il fait beau. Quel temps fait-il à Marseille ?"}, {"start": 9.9, "end": 14.44, "text": "A. C'est très très beau."}, {"start": 16.34, "end": 18.8, "text": "<PERSON><PERSON>eux, merci."}, {"start": 20.86, "end": 23.24, "text": "C. Encore trois jours."}, {"start": 25.24, "end": 28.16, "text": "<PERSON><PERSON> et ensoleillé."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q5"], "correct_answer": "D", "source_location": "test19 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q6.mp3", "chunks": [{"text": "Question 6 Tu es allé dans ce nouveau restaurant? Qu'est -ce que tu penses du menu?", "start": 2.62, "end": 10.64}, {"text": "<PERSON><PERSON> sent toujours bon.", "start": 12.46, "end": 15.32}, {"text": "B. C'est complet chaque soir.", "start": 17.08, "end": 20.3}, {"text": "C. Il est proche du métro.", "start": 22.16, "end": 25.1}, {"text": "<PERSON><PERSON> y a beaucoup de choix.", "start": 26.88, "end": 29.58}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q6"], "correct_answer": "D", "source_location": "test19 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q9.mp3", "chunks": [{"text": "Question 9 Pour notre voyage de juillet prochain, j'ai réservé l'hôtel et le train. Et toi, quelles informations est -ce que tu as eues sur les activités proposées ?", "start": 3.16, "end": 18.32}, {"text": "<PERSON><PERSON> déteste prendre le bus.", "start": 19.54, "end": 23.3}, {"text": "B. Je préfère partir plus tard.", "start": 25.12, "end": 28.5}, {"text": "C. On écrira des cartes postales.", "start": 30.16, "end": 34.0}, {"text": "<PERSON><PERSON> On pourra visiter un château.", "start": 35.64, "end": 39.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q9"], "correct_answer": "D", "source_location": "test19 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q10.mp3", "chunks": [{"text": "Écoutez le document sonore et la question. Choisissez", "start": 0.74, "end": 5.62}, {"text": "la bonne réponse. Salut, on va prendre un café ?", "start": 5.62, "end": 15.04}, {"text": "<PERSON>c plaisir. On peut aller au salon de thé à côté de chez moi.", "start": 15.04, "end": 19.12}, {"text": "Il est très agréable et ils servent des gâteaux comme ceux de ma grand -mère quand j'étais petite.", "start": 19.54, "end": 25.28}, {"text": "Tu le connais ? C'est là où il y a des expositions de peinture ?", "start": 25.56, "end": 29.88}, {"text": "Non, juste à côté.", "start": 30.0, "end": 31.8}, {"text": "Question 10 De quoi parlent les deux personnes?", "start": 34.22, "end": 39.34}], "choices": {"A": "D’un lieu de restauration.", "B": "D’un nouveau musée.", "C": "D’une école de pâtisserie.", "D": "D’une maison de famille."}, "test_id": "test19", "locations": ["test19 Q10"], "correct_answer": "A", "source_location": "test19 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test20/Q4.mp3", "chunks": [{"text": "Ah oui, franchement. Question 4. <PERSON><PERSON> <PERSON><PERSON><PERSON>, est -ce que ce bus va au centre commercial ?", "start": 0.0, "end": 9.72}, {"text": "<PERSON><PERSON>, c'est le dernier arrêt.", "start": 11.06, "end": 14.48}, {"text": "B. <PERSON>, il y a de bonnes promotions.", "start": 16.32, "end": 19.74}, {"text": "<PERSON><PERSON> oui, je préfère cette petite boutique.", "start": 21.72, "end": 25.48}, {"text": "<PERSON><PERSON>, nous voulons offrir une robe.", "start": 27.32, "end": 31.08}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test20", "locations": ["test20 Q4", "test29 Q6", "test39 Q5"], "correct_answer": "A", "source_location": "test20 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test20/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON>, c'est Léo. J'ai deux billets pour le concert de Stromae samedi soir.", "start": 3.12, "end": 9.18}, {"text": "Ça te dit de venir avec moi ?", "start": 9.46, "end": 11.26}, {"text": "<PERSON>rci pour la proposition, mais je ne peux pas, c'est l'anniversaire de ma sœur.", "start": 11.26, "end": 15.8}, {"text": "Oh, dommage. J'ai deux billets seulement. Ta sœur ne pourrait même pas venir.", "start": 16.2, "end": 20.78}, {"text": "Une autre fois. Tu sais, on reste en famille.", "start": 21.4, "end": 23.92}, {"text": "Question 7 Qu'est -ce que Léo propose à Samira ?", "start": 26.6, "end": 31.86}], "choices": {"A": "D’aller au concert tous les deux.", "B": "D’inviter sa sœur à une sortie.", "C": "De diner avec sa famille.", "D": "De fêter son anniversaire."}, "test_id": "test20", "locations": ["test20 Q7"], "correct_answer": "A", "source_location": "test20 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test20/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON>, ça va ? Ça va, mais je cherche toujours un job après les cours pour gagner un peu d'argent.", "start": 2.12, "end": 8.18}, {"text": "Ah mince ! Moi je livre des courses à domicile deux soirs par semaine.", "start": 8.56, "end": 12.78}, {"text": "Je pourrais parler avec mon chef, je crois qu'il veut recruter quelqu'un.", "start": 13.06, "end": 16.9}, {"text": "Viens avec moi ce soir et je te le présenterai.", "start": 17.46, "end": 19.58}, {"text": "<PERSON><PERSON>, me<PERSON><PERSON>, ce serait super. Et j'ai vu que la fac organisait un forum pour l'emploi cette semaine.", "start": 19.76, "end": 26.4}, {"text": "<PERSON>a peut être utile aussi. <PERSON><PERSON><PERSON>, je vais y aller.", "start": 26.7, "end": 29.48}, {"text": "Question 10 Que propose <PERSON><PERSON><PERSON> à Faustine ?", "start": 31.18, "end": 38.38}], "choices": {"A": "La publication de son CV en ligne.", "B": "Un remplacement dans son entreprise.", "C": "Une formation gratuite à l’université.", "D": "Une rencontre avec son patron."}, "test_id": "test20", "locations": ["test20 Q10", "test21 Q9", "test34 Q9"], "correct_answer": "D", "source_location": "test20 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test21/Q4.mp3", "chunks": [{"start": 3.52, "end": 10.32, "text": "Question 4. Hey <PERSON>, dis-moi pourquoi tu es toujours aussi en forme ?"}, {"start": 10.32, "end": 15.54, "text": "<PERSON><PERSON> Je fais souvent de l'exercice."}, {"start": 17.52, "end": 20, "text": "B. Je lis des histoires drôles."}, {"start": 22.38, "end": 24.94, "text": "<PERSON><PERSON> regarde le sport à la télé."}, {"start": 26.84, "end": 29.48, "text": "<PERSON><PERSON> vais au cinéma."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test21", "locations": ["test21 Q4", "test36 Q5"], "correct_answer": "A", "source_location": "test21 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test21/Q5.mp3", "chunks": [{"start": 1.96, "end": 7.12, "text": "Question 5 Il est 19 heures."}, {"start": 7.72, "end": 15.42, "text": "Si on veut arriver au cinéma avant le début du film et trouver une place dans la salle, il faut partir maintenant."}, {"start": 22.16, "end": 26.12, "text": "<PERSON><PERSON>, mais la salle est sombre."}, {"start": 27.8, "end": 32.22, "text": "<PERSON><PERSON>, mais le cinéma est près d'ici"}, {"start": 33.78, "end": 38.62, "text": "<PERSON><PERSON>, mais le film se passe en plein air"}, {"start": 40.82, "end": 44.4, "text": "<PERSON><PERSON>, mais les places sont chères"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test21", "locations": ["test21 Q5", "test25 Q5", "test34 Q5"], "correct_answer": "B", "source_location": "test21 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test21/Q8.mp3", "chunks": [{"text": "Bonjour madame. Bonjour. Je cherche un cadeau pour mon fils.", "start": 2.14, "end": 6.78}, {"text": "C'est son anniversaire. Très bien. Il a quel âge ?", "start": 7.12, "end": 11.44}, {"text": "Et qu'est -ce qu'il aime ?", "start": 11.44, "end": 12.88}, {"text": "Il va avoir 19 ans demain. Il est étudiant en histoire.", "start": 12.88, "end": 16.4}, {"text": "Il adore la poésie, les romans policiers et historiques, bien sûr.", "start": 17.02, "end": 21.84}, {"text": "Question 8. Qu'est -ce que le vendeur peut proposer à cette cliente ?", "start": 23.8, "end": 29.72}], "choices": {"A": "Un film.", "B": "Un livre.", "C": "Un vêtement.", "D": "Un voyage."}, "test_id": "test21", "locations": ["test21 Q8"], "correct_answer": "B", "source_location": "test21 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test22/Q8.mp3", "chunks": [{"text": "Question 8 Je vais faire les courses pour le dîner. Qu'est -ce que je prends ?", "start": 1.68, "end": 10.0}, {"text": "<PERSON><PERSON> Des glaces pour le détail.", "start": 11.12, "end": 14.14}, {"text": "<PERSON>. Des vêtements chauds.", "start": 15.58, "end": 18.86}, {"text": "<PERSON><PERSON> bouquet de fleurs.", "start": 20.6, "end": 23.1}, {"text": "D. Un magazine d'actualité.", "start": 24.52, "end": 33.9}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test22", "locations": ["test22 Q8"], "correct_answer": "A", "source_location": "test22 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test22/Q10.mp3", "chunks": [{"text": "Pour les pandas, il y a un niveau diplomatique très haut, ça veut dire que ce sont les prédidents de la République qui décident de l'arrivée des pandas.", "start": 2.14, "end": 9.4}, {"text": "En fait, c'est parce qu'en Chine, le panda est un animal bénigré par la population et par les Chinois.", "start": 9.6, "end": 15.7}, {"text": "C'est le trésor national chinois. Le président doit lui -même donner son accord pour que les pandas puissent sortir de Chine, parce que pour eux, c'est un animal extrêmement important.", "start": 15.94, "end": 25.2}, {"text": "Question 8 Que dit -on des pandas chinois dans ce reportage ?", "start": 27.48, "end": 32.98}], "choices": {"A": "Ils coûtent cher au pays.", "B": "Ils ont une santé fragile.", "C": "Ils sont difficiles à nourrir.", "D": "Ils sont protégés par l’État."}, "test_id": "test22", "locations": ["test22 Q10", "test36 Q9"], "correct_answer": "D", "source_location": "test22 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test25/Q7.mp3", "chunks": [{"start": 1.76, "end": 7.18, "text": "Question 7 Quand êtes-vous arrivés en France ?"}, {"start": 7.18, "end": 10.98, "text": "<PERSON><PERSON>"}, {"start": 12.98, "end": 15.06, "text": "<PERSON><PERSON> Il y a trois mois"}, {"start": 17.02, "end": 19.0, "text": "<PERSON><PERSON> tout l'été"}, {"start": 20.62, "end": 23.14, "text": "<PERSON><PERSON> quelques semaines"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test25", "locations": ["test25 Q7"], "correct_answer": "B", "source_location": "test25 Q7"}, {"image_path": null, "audio_path": "listening_asset/media_test25/Q11.mp3", "chunks": [{"text": "Salut Ayat ! Waouh ! Il est beau ton manteau !", "start": 1.92, "end": 6.12}, {"text": "<PERSON>rci ! Il est parfait pour l'automne et le froid qui arrive.", "start": 6.12, "end": 10.1}, {"text": "<PERSON><PERSON>, moi j'adore cette époque. Les couleurs des arbres sont magnifiques.", "start": 10.5, "end": 14.74}, {"text": "C'est super pour se promener en forêt.", "start": 15.12, "end": 17.24}, {"text": "Et on peut sortir son beau manteau, comme toi.", "start": 17.44, "end": 20.0}, {"text": "Mais moi en fait, je préfère l'été.", "start": 21.08, "end": 23.56}, {"text": "Les vêtements légers, être dehors... Eh bien, on n'a pas les mêmes goûts.", "start": 24.04, "end": 29.34}, {"text": "<PERSON><PERSON> bon, c'est vrai qu'il fait un peu froid dehors.", "start": 29.34, "end": 32.0}, {"text": "On va boire un café ? ", "start": 32.54, "end": 34.72}, {"text": "Question 11. Sur quoi est -ce que ces deux personnes ont des goûts différents ?", "start": 35.78, "end": 42.16}], "choices": {"A": "La mode.", "B": "La nature.", "C": "Les saisons.", "D": "Les vacances."}, "test_id": "test25", "locations": ["test25 Q11", "test28 Q10", "test30 Q11"], "correct_answer": "C", "source_location": "test25 Q11"}, {"image_path": null, "audio_path": "listening_asset/media_test26/Q6.mp3", "chunks": [{"start": 10.48, "end": 11.88, "text": "C'est combien pour les enfants?"}, {"start": 13.76, "end": 17.56, "text": "<PERSON><PERSON> <PERSON>'est passionnant. Il<PERSON> vont adorer."}, {"start": 19.32, "end": 23.84, "text": "B. C'est possible. La séance est à 16 heures."}, {"start": 26.18, "end": 29.08, "text": "C. C'est gratuit jusqu'à 8 ans."}, {"start": 31.12, "end": 35.04, "text": "D. C'est la première salle sur votre droite."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test26", "locations": ["test26 Q6", "test30 Q6"], "correct_answer": "C", "source_location": "test26 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test26/Q9.mp3", "chunks": [{"text": "Garage du centre, bonjour. Bonjour. Je voudrais remplacer les quatre pneus de ma voiture.", "start": 2.18, "end": 8.4}, {"text": "Quand est -ce que je pourrais venir ?", "start": 8.98, "end": 11.48}, {"text": "Ah, pas avant mardi prochain. Dans l'après -midi à 15h.", "start": 11.48, "end": 16.5}, {"text": "<PERSON><PERSON>, ce serait parfait. Je suis M. Cha<PERSON>ier.", "start": 16.84, "end": 20.28}, {"text": "Ah, <PERSON><PERSON>. Votre voiture n'a pas eu de problème depuis la dernière fois ?", "start": 20.7, "end": 26.0}, {"text": "<PERSON>, elle roule très bien, merci. Très bien.", "start": 26.0, "end": 29.68}, {"text": "À mardi alors. Oui, au revoir.", "start": 29.98, "end": 32.1}, {"text": "Question 9. Que souhaite ce client ?", "start": 33.82, "end": 39.44}], "choices": {"A": "Acheter un nouveau véhicule.", "B": "Connaître les horaires.", "C": "Obtenir un rendez-vous.", "D": "Se plaindre d’une réparation."}, "test_id": "test26", "locations": ["test26 Q9"], "correct_answer": "C", "source_location": "test26 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test26/Q10.mp3", "chunks": [{"text": "Tu as pris le métro ce matin ?", "start": 1.94, "end": 4.68}, {"text": "Je n'ai pas travaillé aujourd'hui. Je suis allé au cinéma.", "start": 4.68, "end": 8.26}, {"text": "<PERSON><PERSON> bien, tu as de la chance. La ligne 6 était arrêtée.", "start": 8.8, "end": 12.64}, {"text": "J'ai dû marcher pendant une heure. Question", "start": 12.92, "end": 15}, {"text": "Question 10 De quoi parle la femme ?", "start": 16, "end": 21}], "choices": {"A": "D’un nouveau film à voir.", "B": "D’un souci de transport.", "C": "D’une recherche d’emploi.", "D": "D’une séance de sport."}, "test_id": "test26", "locations": ["test26 Q10"], "correct_answer": "B", "source_location": "test26 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test27/Q6.mp3", "chunks": [{"text": "Question 6. <PERSON><PERSON>, <PERSON> ? Je t'attends à la sortie du métro.", "start": 3.48, "end": 11.3}, {"text": "Tu es en tant loin ?", "start": 11.88, "end": 16.37}, {"start": 15.36, "end": 17.5, "text": "<PERSON> <PERSON>'est qui ça?"}, {"start": 20.1, "end": 22.18, "text": "<PERSON>. Il est qu'à deux."}, {"start": 24.82, "end": 27.08, "text": "<PERSON><PERSON><PERSON> bientôt."}, {"start": 30.16, "end": 31.78, "text": "<PERSON><PERSON> On se voit demain."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test27", "locations": ["test27 Q6"], "correct_answer": "C", "source_location": "test27 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test28/Q5.mp3", "chunks": [{"text": "Question 5. J'ai rencontré mon mari quand je travaillais à l'étranger. On prenait des cours dans la même école de langue. Ah oui? Et vous vivez à Toulouse depuis longtemps?", "start": 3.18, "end": 17.68}, {"text": "<PERSON><PERSON>, ça fait deux ans.", "start": 19.64, "end": 23.2}, {"text": "<PERSON><PERSON>, j'ai changé de m<PERSON>.", "start": 25.04, "end": 28.94}, {"text": "<PERSON><PERSON>, je parle français.", "start": 30.4, "end": 34.46}, {"text": "<PERSON><PERSON>, nous voulons déménager.", "start": 36.16, "end": 40.28}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test28", "locations": ["test28 Q5", "test30 Q5"], "correct_answer": "A", "source_location": "test28 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test28/Q6.mp3", "chunks": [{"text": "Question 6. <PERSON><PERSON>? <PERSON><PERSON>? Di<PERSON> -moi, demain, tu arrives à quelle heure?", "start": 2.84, "end": 10.98}, {"text": "<PERSON><PERSON> deux heures, je pense.", "start": 13.09, "end": 16.3}, {"text": "<PERSON><PERSON> 8 heures, si tout va bien.", "start": 18.14, "end": 22.1}, {"text": "<PERSON><PERSON> plus tard que demain.", "start": 23.94, "end": 26.82}, {"text": "<PERSON><PERSON> peu plus tôt, je crois.", "start": 28.96, "end": 31.8}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test28", "locations": ["test28 Q6"], "correct_answer": "B", "source_location": "test28 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test29/Q5.mp3", "chunks": [{"start": 4.6, "end": 13.5, "text": "<PERSON><PERSON><PERSON> madame, je peux vous aider ? <PERSON><PERSON><PERSON>, je cherche d'autres langues, oui, comme celle-ci."}, {"start": 15.02, "end": 18.52, "text": "<PERSON><PERSON> bottes sont parfaites."}, {"start": 19.98, "end": 23.62, "text": "B. J'aime tant faire les soldes."}, {"start": 25.7, "end": 28.28, "text": "C. J'aimerais essayer la verte."}, {"start": 30.41, "end": 33.26, "text": "<PERSON><PERSON> ê<PERSON> ouvert le lundi?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test29", "locations": ["test29 Q5", "test39 Q4"], "correct_answer": "C", "source_location": "test29 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test29/Q8.mp3", "chunks": [{"start": 1.92, "end": 11.38, "text": "Question 8. Au concert de Zaz au Francophonie de Montréal, qu'est-ce que tu penses de cet artiste?"}, {"start": 14.31, "end": 16.54, "text": "<PERSON><PERSON>aime beaucoup ses chansons."}, {"start": 19.24, "end": 21.56, "text": "<PERSON><PERSON> <PERSON> préfère les petites salles."}, {"start": 24.48, "end": 26.68, "text": "<PERSON><PERSON> sais jouer de la guitare."}, {"start": 30.06, "end": 32.34, "text": "D. Je te remercie de l'invitation."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test29", "locations": ["test29 Q8", "test38 Q5"], "correct_answer": "A", "source_location": "test29 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test29/Q10.mp3", "chunks": [{"text": "Je vais aussi à l'anniversaire de Tom ce soir. Qu'est -ce qu'on peut lui offrir ?", "start": 2.14, "end": 11.96}, {"text": "A. C'est pour ses 18 ans.", "start": 12.64, "end": 15.02}, {"text": "<PERSON><PERSON> Il veut une écharpe en laine.", "start": 16.8, "end": 19.7}, {"text": "C<PERSON> le trouve sympa.", "start": 21.52, "end": 24.16}, {"text": "D<PERSON> On y va en voiture.", "start": 25.88, "end": 28.46}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test29", "locations": ["test29 Q10", "test31 Q8"], "correct_answer": "B", "source_location": "test29 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test31/Q5.mp3", "chunks": [{"start": 2.18, "end": 12.88, "text": "Question 5 Bon<PERSON><PERSON> monsieur, ce matin je me suis brûlé la main quand je cuisinais. J'ai vraiment mal."}, {"start": 13.12, "end": 15.8, "text": "Quel médicament est-ce que vous me conseillez ?"}, {"start": 15.8, "end": 21.48, "text": "A. Des antibiotiques contre la fièvre."}, {"start": 23.5, "end": 26.8, "text": "B. Des comprimés contre la grippe."}, {"start": 28.36, "end": 32, "text": "C'est un sirop contre la toux"}, {"start": 33, "end": 37, "text": "D. une crème contre la douleur"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test31", "locations": ["test31 Q5"], "correct_answer": "D", "source_location": "test31 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test31/Q6.mp3", "chunks": [{"text": "Question 6 Qu'est -ce qu'on mange ?", "start": 2.18, "end": 8.44}, {"text": "<PERSON><PERSON> restaurant, chez Pierrot.", "start": 9.02, "end": 11.64}, {"text": "B. C'est prêt dans une minute.", "start": 13.18, "end": 15.88}, {"text": "<PERSON><PERSON> tu veux, tu choisis.", "start": 17.5, "end": 20.2}, {"text": "D. Prends des assiettes en plastique.", "start": 21.74, "end": 30.82}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test31", "locations": ["test31 Q6"], "correct_answer": "C", "source_location": "test31 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test31/Q9.mp3", "chunks": [{"text": "Hey, ça va Jeanne ? Non Kamel, j'ai un problème avec ma nouvelle voiture.", "start": 2.24, "end": 6.34}, {"text": "Wow, elle est en panne ? Non, elle roule, mais avec un bruit bizarre.", "start": 6.78, "end": 10.14}, {"text": "C'est le moteur, je crois. Tu dois vite appeler le mécanicien du garage.", "start": 10.32, "end": 13.74}, {"text": "Il va la réparer. Ah oui, tu as raison.", "start": 13.96, "end": 15.84}, {"text": "Question 9. Quel est le problème de la voiture de Jeanne ?", "start": 18.42, "end": 23.7}], "choices": {"A": "Elle coûte très cher à réparer.", "B": "Elle est restée au garage.", "C": "Elle fait un bruit anormal.", "D": "Elle vient de tomber en panne."}, "test_id": "test31", "locations": ["test31 Q9"], "correct_answer": "C", "source_location": "test31 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test32/Q5.mp3", "chunks": [{"text": "Question 5 <PERSON>cia, est -ce que tu pars en vacances cet été ?", "start": 2.1, "end": 11.0}, {"text": "<PERSON><PERSON>, je peux te conseiller un e -mail.", "start": 11.0, "end": 14.54}, {"text": "<PERSON><PERSON>, je peux pas prendre le train.", "start": 16.46, "end": 19.7}, {"text": "<PERSON><PERSON>, je retourne au Maroc.", "start": 21.62, "end": 24.44}, {"text": "<PERSON><PERSON>, je vais rester chez moi.", "start": 26.4, "end": 29.5}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q5"], "correct_answer": "C", "source_location": "test32 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test32/Q6.mp3", "chunks": [{"start": 0.0, "end": 10.78, "text": "Bonjour ! Question 6. <PERSON><PERSON>, bonjour. Est -ce que je pourrais parler à <PERSON>, s 'il vous plaît ?"}, {"start": 10.78, "end": 15.38, "text": "<PERSON><PERSON>, c 'est à quelle adresse ?"}, {"start": 15.38, "end": 20.52, "text": "<PERSON><PERSON>, c 'est de la part de qui ?"}, {"start": 20.52, "end": 25.36, "text": "<PERSON><PERSON>, elle arrive à quelle heure ?"}, {"start": 26.5, "end": 30.02, "text": "<PERSON><PERSON>, c 'est vrai que c 'est"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q6"], "correct_answer": "B", "source_location": "test32 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test32/Q8.mp3", "chunks": [{"text": "Question 8. Mon neveu vient d'avoir 5 ans. Et quelqu'un d'autre est -ce que je peux lui appeler?", "start": 2.3, "end": 10.1}, {"text": "<PERSON><PERSON> jouer beaucoup trop cher.", "start": 12.22, "end": 15.2}, {"text": "B. C'est un très joli comment.", "start": 17.12, "end": 19.76}, {"text": "<PERSON><PERSON> la question à ses parents.", "start": 21.78, "end": 24.36}, {"text": "<PERSON><PERSON> pas? C'est une grande idée.", "start": 26.18, "end": 29.24}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q8"], "correct_answer": "C", "source_location": "test32 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test32/Q9.mp3", "chunks": [{"start": 2.28, "end": 3.7, "text": "Question 9."}, {"start": 5.94, "end": 10.62, "text": "<PERSON><PERSON><PERSON>, il sera bon. J'aimerais bien faire un télésport."}, {"start": 11.58, "end": 13.74, "text": "Tu veux faire du vélo avec moi ?"}, {"start": 13.74, "end": 19.36, "text": "<PERSON><PERSON> <PERSON> ! Attends, je prends mes lunettes de soleil."}, {"start": 21.14, "end": 25.06, "text": "<PERSON><PERSON>, on pourra aller dans comment ?"}, {"start": 25.06, "end": 30.52, "text": "<PERSON><PERSON>, c'est d'accord, j'en ai fait chute."}, {"start": 32.42, "end": 36.52, "text": "<PERSON><PERSON>, quel dommage, le mars est terminé."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q9"], "correct_answer": "B", "source_location": "test32 Q9"}, {"image_path": null, "audio_path": "listening_asset/media_test32/Q10.mp3", "chunks": [{"text": "Question 10 Salut mère, as -tu une tête en disant ? Tu viens ? Ouais super, plaisir. Comment on fait pour aller chez toi ?", "start": 2.56, "end": 12.0}, {"text": "<PERSON><PERSON> lui apportes quelque chose à voir.", "start": 13.3, "end": 16.0}, {"text": "<PERSON><PERSON> lui arrives dans ton compte.", "start": 18.1, "end": 20.78}, {"text": "<PERSON><PERSON> <PERSON> lui dessous en reviens avec une autre personne à la suite.", "start": 22.82, "end": 26.42}, {"text": "<PERSON><PERSON> Tu lui dis qu'il y a une autre personne si tu veux.", "start": 28.88, "end": 31.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q10"], "correct_answer": "C", "source_location": "test32 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test33/Q5.mp3", "chunks": [{"start": 3.04, "end": 9.68, "text": "Question 5. <PERSON><PERSON><PERSON><PERSON>vous garder ma valise, s'il vous plaît?"}, {"start": 11.74, "end": 15.7, "text": "<PERSON><PERSON>, sans problème."}, {"start": 18.62, "end": 21.96, "text": "B. D'accord, j'y vais."}, {"start": 24.84, "end": 28.5, "text": "<PERSON><PERSON>, il est absent."}, {"start": 32.9, "end": 37.32, "text": "<PERSON><PERSON>, ça va très bien"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test33", "locations": ["test33 Q5"], "correct_answer": "A", "source_location": "test33 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test35/Q5.mp3", "chunks": [{"start": 1.76, "end": 6.82, "text": "Question 5 Est-ce que c'est un bon restaurant ?"}, {"start": 6.82, "end": 11.42, "text": "<PERSON><PERSON> Il est très bien décoré."}, {"start": 12.68, "end": 15.6, "text": "<PERSON><PERSON> Je mange en terrasse."}, {"start": 16.98, "end": 20.52, "text": "C. La cuisine est excellente."}, {"start": 21.94, "end": 24.9, "text": "<PERSON><PERSON> Les serveurs sont gentils."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test35", "locations": ["test35 Q5"], "correct_answer": "C", "source_location": "test35 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test36/Q8.mp3", "chunks": [{"text": "<PERSON>, as -tu terminé ton travail ? Pas encore, maman.", "start": 2.42, "end": 6.26}, {"text": "Cet exercice de français est très difficile. Tu veux que je t'aide ?", "start": 6.54, "end": 10.38}, {"text": "Non, ça va, je réf<PERSON>chis encore un peu.", "start": 10.38, "end": 12.62}, {"text": "Tu m'appelles quand le dîner est prêt ?", "start": 12.94, "end": 14.78}, {"text": "D'accord. ", "start": 14.78, "end": 16}, {"text": "Question 8. Que veut Jean ?", "start": 16.5, "end": 20.94}], "choices": {"A": "Il veut de l’aide.", "B": "Il veut travailler seul.", "C": "Il veut manger.", "D": "Il veut reporter son travail."}, "test_id": "test36", "locations": ["test36 Q8"], "correct_answer": "B", "source_location": "test36 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test36/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON>, d<PERSON><PERSON><PERSON> pour le retard. <PERSON><PERSON>, al<PERSON>", "start": 2.64, "end": 7.36}, {"text": "tu n'as pas entendu le réveil ?", "start": 7.36, "end": 9.52}, {"text": "Il y avait une grève des bus ce matin, je n'ai pas de voiture, donc je suis venue à pied et j'habite une heure.", "start": 9.52, "end": 15.06}, {"text": "La prochaine fois, appelle un taxi. <PERSON><PERSON>, si tu me le payes.", "start": 15.66, "end": 19.22}, {"text": "Question 10. Pourquoi est -ce que Marie est en retard ?", "start": 21.46, "end": 25.78}], "choices": {"A": "Elle a raté le réveil.", "B": "Elle a raté le bus.", "C": "Elle n’a pas trouvé de taxi", "D": "Elle n’a pas de voiture."}, "test_id": "test36", "locations": ["test36 Q10"], "correct_answer": "B", "source_location": "test36 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test37/Q5.mp3", "chunks": [{"start": 3.32, "end": 11.68, "text": "Question 5. Tu dois choisir ton orientation ce mois-ci. Quel métier est-ce que tu veux faire plus tard ?"}, {"start": 11.68, "end": 17.38, "text": "<PERSON><PERSON> J'ai trouvé l'examen difficile."}, {"start": 19.5, "end": 22.4, "text": "B. J'aurai bientôt la réponse."}, {"start": 24.5, "end": 27.46, "text": "<PERSON><PERSON> Je prends des cours d'anglais."}, {"start": 30.38, "end": 33.22, "text": "<PERSON><PERSON> <PERSON> rêve d'être musicien."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test37", "locations": ["test37 Q5"], "correct_answer": "D", "source_location": "test37 Q5"}, {"image_path": null, "audio_path": "listening_asset/media_test37/Q8.mp3", "chunks": [{"text": "Question 8. C'est incroyable. Trois ministres vont quitter le gouvernement. Est -ce que tu as vu le journal télévisé ?", "start": 2.3, "end": 13.32}, {"text": "<PERSON><PERSON>, c'est impossible.", "start": 14.9, "end": 18.06}, {"text": "<PERSON><PERSON>, c'est trop tard.", "start": 19.94, "end": 23.52}, {"text": "<PERSON><PERSON>, je l'ai déjà regardé.", "start": 25.22, "end": 29.42}, {"text": "<PERSON><PERSON>, raconte -moi.", "start": 30.0, "end": 34.76}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test37", "locations": ["test37 Q8", "test38 Q8"], "correct_answer": "D", "source_location": "test37 Q8"}, {"image_path": null, "audio_path": "listening_asset/media_test37/Q10.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, j'ai acheté ce pantalon la semaine dernière, mais il a un défaut.", "start": 2.62, "end": 7.58}, {"text": "Dans ce cas, je peux vous l'échanger.", "start": 8.28, "end": 10.36}, {"text": "Je préférerais un remboursement. C'est impossible pendant les soldes, mais vous pouvez choisir un autre article, même dans la nouvelle collection.", "start": 10.7, "end": 18.44}, {"text": "Question 10. Que souhaite le client ?", "start": 21.18, "end": 26.34}], "choices": {"A": "Choisir un pantalon différent.", "B": "Découvrir la nouvelle collection.", "C": "Être remboursé de son achat.", "D": "Trouver un vêtement en solde."}, "test_id": "test37", "locations": ["test37 Q10"], "correct_answer": "C", "source_location": "test37 Q10"}, {"image_path": null, "audio_path": "listening_asset/media_test38/Q7.mp3", "chunks": [{"text": "<PERSON><PERSON>, c'est <PERSON>. Ton week-end à Nice s'est bien passé, j'espère.", "start": 4.38, "end": 9.28}, {"text": "<PERSON><PERSON>, je pars en déplacement pour voir des clients.", "start": 10.1, "end": 13.52}, {"text": "Tu pourrais me rendre ma valise, s'il te plaît?", "start": 13.9, "end": 16.54}, {"text": "Je peux passer demain soir chez toi, si tu es libre.", "start": 16.54, "end": 20.4}, {"text": "À plus!", "start": 20.9, "end": 21.58}, {"text": "Question 7 Qu'est-ce que Laura explique dans son message?", "start": 21.58, "end": 29.94}], "choices": {"A": "Elle a besoin de sa valise.", "B": "Elle invite <PERSON><PERSON>.", "C": "Elle part en vacances.", "D": "Elle travaille ce week-end."}, "test_id": "test38", "locations": ["test38 Q7"], "correct_answer": "A", "source_location": "test38 Q7"}]