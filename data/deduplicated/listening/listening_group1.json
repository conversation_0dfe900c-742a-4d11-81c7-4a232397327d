[{"image_path": "listening_asset_free/media_test1/Q1.webp", "audio_path": "listening_asset_free/media_test1/Q1.mp3", "chunks": [{"text": "1. Oh, regarde, il neige. N'oublie pas.", "start": 0.0, "end": 4.76}, {"text": "A. Ton sac.", "start": 6.08, "end": 7.9}, {"text": "<PERSON><PERSON> lunettes.", "start": 8.92, "end": 11.94}, {"text": "<PERSON><PERSON> c<PERSON>.", "start": 13.02, "end": 15.5}, {"text": "D. To<PERSON> manteau.", "start": 16.7, "end": 19.22}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q1"], "correct_answer": "D", "source_location": "test1_gr Q1"}, {"image_path": "listening_asset_free/media_test1/Q2.webp", "audio_path": "listening_asset_free/media_test1/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON> beau<PERSON>.", "start": 0.0, "end": 2.08}, {"text": "<PERSON><PERSON> <PERSON> m'excuse.", "start": 2.84, "end": 5.5}, {"text": "C. Ce n'est rien.", "start": 6.2, "end": 8.64}, {"text": "<PERSON><PERSON> plaisir.", "start": 9.44, "end": 11.8}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q2"], "correct_answer": "A", "source_location": "test1_gr Q2"}, {"image_path": "listening_asset_free/media_test1/Q3.webp", "audio_path": "listening_asset_free/media_test1/Q3.mp3", "chunks": [{"text": "<PERSON><PERSON> secours !", "start": 0.0, "end": 2.44}, {"text": "<PERSON><PERSON> Au lit !", "start": 2.44, "end": 4.94}, {"text": "<PERSON><PERSON> table !", "start": 4.94, "end": 7.64}, {"text": "<PERSON><PERSON> l'eau !", "start": 7.64, "end": 10.08}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q3"], "correct_answer": "C", "source_location": "test1_gr Q3"}, {"image_path": "listening_asset_free/media_test1/Q4.webp", "audio_path": "listening_asset_free/media_test1/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON> anniversaire.", "start": 0.0, "end": 2.96}, {"text": "<PERSON><PERSON>.", "start": 3.62, "end": 6.32}, {"text": "<PERSON><PERSON> fê<PERSON>.", "start": 7.16, "end": 9.42}, {"text": "<PERSON><PERSON> an<PERSON>.", "start": 10.58, "end": 12.14}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1_gr", "locations": ["test1_gr Q4"], "correct_answer": "A", "source_location": "test1_gr Q4"}, {"image_path": "listening_asset_free/media_test2/Q1.jpg", "audio_path": "listening_asset_free/media_test2/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON> Je suis en retard.", "start": 0.0, "end": 2.4}, {"text": "<PERSON><PERSON>lle heure est-il ?", "start": 3.2399999999999998, "end": 6.2}, {"text": "C. Je suis en avance.", "start": 6.78, "end": 8.92}, {"text": "D. <PERSON> est 15 heures.", "start": 9.76, "end": 12.34}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q1"], "correct_answer": "A", "source_location": "test2_gr Q1"}, {"image_path": "listening_asset_free/media_test2/Q2.jpg", "audio_path": "listening_asset_free/media_test2/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON> places, s'il vous plaît.", "start": 0.0, "end": 3.54}, {"text": "<PERSON><PERSON> Je vais prendre cette cravate.", "start": 4.660000000000002, "end": 8.74}, {"text": "C. C'est très difficile à lire.", "start": 11.240000000000004, "end": 13.92}, {"text": "D. Il est déjà l'heure de rentrer.", "start": 15.140000000000002, "end": 18.92}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q2"], "correct_answer": "B", "source_location": "test2_gr Q2"}, {"image_path": "listening_asset_free/media_test2/Q3.jpg", "audio_path": "listening_asset_free/media_test2/Q3.mp3", "chunks": [{"text": "<PERSON><PERSON> belle ville !", "start": 0.0, "end": 3.54}, {"text": "<PERSON><PERSON> <PERSON>'eau est froide, rentrons !", "start": 3.54, "end": 7.62}, {"text": "<PERSON><PERSON> ! Tu vas lui faire peur !", "start": 7.62, "end": 11.6}, {"text": "<PERSON><PERSON> Il pleut depuis trois jours sur cette plage !", "start": 12.18, "end": 15.92}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q3"], "correct_answer": "C", "source_location": "test2_gr Q3"}, {"image_path": "listening_asset_free/media_test2/Q4.jpg", "audio_path": "listening_asset_free/media_test2/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON> la lumière.", "start": 0.0, "end": 2.62}, {"text": "<PERSON><PERSON> allume la télé.", "start": 3.4, "end": 6.04}, {"text": "<PERSON><PERSON> ouvre la fenêtre.", "start": 7.06, "end": 9.94}, {"text": "<PERSON><PERSON> allume la lumière.", "start": 11.36, "end": 13.58}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2_gr", "locations": ["test2_gr Q4"], "correct_answer": "D", "source_location": "test2_gr Q4"}, {"image_path": "listening_asset/media_test1/Q1.webp", "audio_path": "listening_asset/media_test1/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON>, entrez.", "start": 0.0, "end": 3.08}, {"text": "<PERSON><PERSON>vous, je vous en prie.", "start": 5.16, "end": 8.42}, {"text": "<PERSON><PERSON> porte, s'il vous plaît.", "start": 10.44, "end": 13.44}, {"text": "<PERSON><PERSON> pour ce café.", "start": 15.42, "end": 18.18}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q1"], "correct_answer": "B", "source_location": "test1 Q1"}, {"image_path": "listening_asset/media_test1/Q2.webp", "audio_path": "listening_asset/media_test1/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON>, au plaisir de vous revoir.", "start": 0.0, "end": 3.78}, {"text": "B. Le programme est affiché sur la porte.", "start": 5.26, "end": 9.48}, {"text": "C. Voici la salle pour prendre le petit déjeuner.", "start": 11.0, "end": 15.52}, {"text": "<PERSON><PERSON> <PERSON><PERSON> pouvez laisser vos bagages là.", "start": 16.98, "end": 20.66}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q2"], "correct_answer": "D", "source_location": "test1 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q3.mp3", "chunks": [{"start": 0.0, "end": 1.94, "text": "Comment allez-vous à votre travail ?"}, {"start": 1.94, "end": 5.5, "text": "A. À l'heure"}, {"start": 7.14, "end": 9.24, "text": "<PERSON><PERSON>"}, {"start": 10.76, "end": 12.7, "text": "C. À pied"}, {"start": 14.22, "end": 16.74, "text": "D. <PERSON> 8 heures"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q3"], "correct_answer": "C", "source_location": "test1 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test1/Q4.mp3", "chunks": [{"text": "Je ne sais pas utiliser la photocopieuse. Tu peux m'aider?", "start": 0.0, "end": 4.6}, {"text": "<PERSON><PERSON>, je vais y aller.", "start": 6.74, "end": 9.52}, {"text": "<PERSON><PERSON>, je vais la réparer.", "start": 11.28, "end": 14.94}, {"text": "<PERSON><PERSON>, je vais te montrer.", "start": 16.64, "end": 20.24}, {"text": "<PERSON><PERSON>, je vais lui demander.", "start": 21.94, "end": 25.36}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test1", "locations": ["test1 Q4"], "correct_answer": "C", "source_location": "test1 Q4"}, {"image_path": "listening_asset/media_test2/Q1.webp", "audio_path": "listening_asset/media_test2/Q1.mp3", "chunks": [{"text": "A. J'aimerais ouvrir ce colis. C'est un cadeau.", "start": 0.0, "end": 4.44}, {"text": "<PERSON><PERSON> Je n'arrive pas à écrire le courrier pour le directeur.", "start": 6.08, "end": 11.1}, {"text": "<PERSON><PERSON> v<PERSON>rais envoyer cette lettre, s'il vous plaît.", "start": 12.84, "end": 17.28}, {"text": "<PERSON><PERSON> Le facteur est passé. Tu vas chercher le courrier?", "start": 18.86, "end": 23.28}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q1", "test11 Q1", "test22 Q1", "test24 Q1", "test25 Q1"], "correct_answer": "C", "source_location": "test2 Q1"}, {"image_path": "listening_asset/media_test2/Q2.webp", "audio_path": "listening_asset/media_test2/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON>er avec le ballon.", "start": 0.0, "end": 3.06}, {"text": "<PERSON>. Fais attention avant de traverser.", "start": 4.6, "end": 8.12}, {"text": "C. Mets ton bonnet tout de suite.", "start": 9.66, "end": 12.52}, {"text": "D. Regarde les enfants.", "start": 14.04, "end": 16.76}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q2", "test26 Q3"], "correct_answer": "C", "source_location": "test2 Q2"}, {"image_path": "listening_asset/media_test2/Q3.webp", "audio_path": "listening_asset/media_test2/Q3.mp3", "chunks": [{"start": 0.0, "end": 4.18, "text": "<PERSON><PERSON> au cuisinier. C'est délicieux."}, {"start": 6.18, "end": 10.28, "text": "<PERSON><PERSON> <PERSON> voudrais une table près de la fenêtre."}, {"start": 12.14, "end": 15.52, "text": "C. Un autre café, s'il vous plaît."}, {"start": 17.36, "end": 20.34, "text": "<PERSON><PERSON> <PERSON> pouvez garder la monnaie."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q3", "test5 Q7", "test6 Q2", "test29 Q2"], "correct_answer": "B", "source_location": "test2 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON>, <PERSON>, je suis libre demain. Nous déjeunons ensemble ?", "start": 0.0, "end": 5.4}, {"text": "<PERSON><PERSON> plaisir.", "start": 6.09, "end": 7.86}, {"text": "<PERSON><PERSON> va, merci.", "start": 9.74, "end": 12.36}, {"text": "<PERSON><PERSON> Je t'en prie.", "start": 14.16, "end": 16.4}, {"text": "<PERSON><PERSON> quoi.", "start": 18.41, "end": 20.26}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q4", "test29 Q4"], "correct_answer": "A", "source_location": "test2 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test2/Q5.mp3", "chunks": [{"text": "<PERSON><PERSON> -vous entendu quelque chose ?", "start": 0.0, "end": 3.0}, {"text": "<PERSON><PERSON>, je ne veux rien.", "start": 4.16, "end": 7.3}, {"text": "<PERSON><PERSON> Non, c'est par là.", "start": 8.76, "end": 12.2}, {"text": "<PERSON><PERSON>, je crois.", "start": 14.18, "end": 16.92}, {"text": "<PERSON><PERSON><PERSON><PERSON> demain ?", "start": 18.82, "end": 21.64}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test2", "locations": ["test2 Q5", "test7 Q4"], "correct_answer": "C", "source_location": "test2 Q5"}, {"image_path": "listening_asset/media_test3/Q1.webp", "audio_path": "listening_asset/media_test3/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON> séance débute tout de suite, salle 3. Bon film.", "start": 0.0, "end": 5.12}, {"text": "<PERSON><PERSON> votre carte d'embarquement. Bon voyage.", "start": 6.74, "end": 11.26}, {"text": "C<PERSON> Votre table est juste devant vous. Bon appétit.", "start": 13.0, "end": 17.9}, {"text": "<PERSON>. <PERSON> train part dans 30 minutes. Bon après -midi.", "start": 19.4, "end": 24.36}, {"text": "D. <PERSON> train part dans 30 minutes.", "start": 24.36, "end": 29.22}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q1", "test17 Q1"], "correct_answer": "B", "source_location": "test3 Q1"}, {"image_path": "listening_asset/media_test3/Q2.webp", "audio_path": "listening_asset/media_test3/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON>, les cabines se trouvent à droite.", "start": 0.0, "end": 3.92}, {"text": "<PERSON><PERSON>, ça vous fait 49 $.", "start": 5.58, "end": 9.88}, {"text": "<PERSON><PERSON>, je vous apporte la taille en dessous.", "start": 11.84, "end": 16.4}, {"text": "<PERSON><PERSON>, cette robe vous va très bien.", "start": 17.98, "end": 22.34}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q2"], "correct_answer": "C", "source_location": "test3 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test3/Q3.mp3", "chunks": [{"text": "Je vous sers un café.", "start": 0.0, "end": 1.62}, {"text": "<PERSON><PERSON>.", "start": 3.46, "end": 5.1}, {"text": "<PERSON><PERSON> plaisir.", "start": 6.58, "end": 9.14}, {"text": "C. C'est joli.", "start": 10.86, "end": 13.14}, {"text": "<PERSON><PERSON>.", "start": 15.06, "end": 17.18}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test3", "locations": ["test3 Q3", "test34 Q3"], "correct_answer": "B", "source_location": "test3 Q3"}, {"image_path": "listening_asset/media_test4/Q1.webp", "audio_path": "listening_asset/media_test4/Q1.mp3", "chunks": [{"text": "A. C'est une bonne idée de peindre des enfants.", "start": 0.0, "end": 4.02}, {"text": "<PERSON><PERSON> sculpture est très intéressante.", "start": 5.66, "end": 9.66}, {"text": "<PERSON><PERSON> y a beaucoup de visiteurs aujourd'hui.", "start": 11.42, "end": 15.06}, {"text": "D<PERSON> <PERSON>aime les couleurs de ce tableau.", "start": 16.64, "end": 20.18}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q1", "test7 Q1", "test35 Q1"], "correct_answer": "B", "source_location": "test4 Q1"}, {"image_path": "listening_asset/media_test4/Q2.webp", "audio_path": "listening_asset/media_test4/Q2.mp3", "chunks": [{"text": "A. Il est interdit de courir.", "start": 0.0, "end": 2.74}, {"text": "B. Le toboggan est fermé.", "start": 4.2, "end": 7.14}, {"text": "<PERSON><PERSON> votre bonnet de bain.", "start": 8.64, "end": 11.62}, {"text": "<PERSON><PERSON>vez sortir de l'eau.", "start": 13.06, "end": 16.1}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q2", "test24 Q4"], "correct_answer": "A", "source_location": "test4 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q3.mp3", "chunks": [{"text": "Quelle est votre profession ?", "start": 0.0, "end": 2.56}, {"text": "<PERSON><PERSON> <PERSON>'appelle <PERSON>.", "start": 3.5, "end": 6.74}, {"text": "B. <PERSON>re de Bruxelles.", "start": 8.06, "end": 11.28}, {"text": "C. Je suis avocat à Paris.", "start": 12.74, "end": 16.48}, {"text": "D. Je viens d'avoir 36 ans.", "start": 17.82, "end": 21.62}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q3"], "correct_answer": "C", "source_location": "test4 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test4/Q4.mp3", "chunks": [{"text": "C'est votre premier séjour en France ?", "start": 0.0, "end": 3.42}, {"text": "<PERSON><PERSON>, j'ai fait plusieurs voyages en Asie et en Afrique.", "start": 4.4, "end": 9.6}, {"text": "<PERSON><PERSON>, j'ai gardé de très bons souvenirs de ce voyage.", "start": 11.34, "end": 16.06}, {"text": "<PERSON><PERSON>, je suis déjà venue en vacances avec mes parents.", "start": 17.98, "end": 22.6}, {"text": "<PERSON><PERSON>, je suis partie pour deux semaines avec mes amis.", "start": 24.32, "end": 29.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test4", "locations": ["test4 Q4"], "correct_answer": "C", "source_location": "test4 Q4"}, {"image_path": "listening_asset/media_test5/Q1.webp", "audio_path": "listening_asset/media_test5/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON>moi ça, maman.", "start": 0.0, "end": 2.24}, {"text": "<PERSON><PERSON>, c'est très chaud.", "start": 3.76, "end": 6.78}, {"text": "<PERSON><PERSON> anniversaire, maman.", "start": 8.34, "end": 11.16}, {"text": "<PERSON><PERSON>, je n'ai pas envie.", "start": 12.68, "end": 16.12}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q1", "test20 Q1", "test39 Q1"], "correct_answer": "C", "source_location": "test5 Q1"}, {"image_path": "listening_asset/media_test5/Q2.webp", "audio_path": "listening_asset/media_test5/Q2.mp3", "chunks": [{"text": "A. Bienvenue à Paris Agora.", "start": 0.0, "end": 3.3}, {"text": "B. 200 euros, s'il vous plaît.", "start": 4.98, "end": 8.14}, {"text": "C. Il est 20h30.", "start": 9.68, "end": 12.66}, {"text": "D. Il vient du Japon.", "start": 14.2, "end": 16.84}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q2", "test10 Q1"], "correct_answer": "A", "source_location": "test5 Q2"}, {"image_path": "listening_asset/media_test5/Q3.webp", "audio_path": "listening_asset/media_test5/Q3.mp3", "chunks": [{"text": "Regardez l'image 3.", "start": 0.0, "end": 2.42}, {"text": "<PERSON><PERSON>eux m'asseoir ici.", "start": 4.18, "end": 7.36}, {"text": "<PERSON><PERSON> Mon chien peut venir avec moi.", "start": 8.96, "end": 12.76}, {"text": "<PERSON><PERSON> Quand passe le prochain bus?", "start": 14.42, "end": 18.08}, {"text": "<PERSON><PERSON> Vous me donnez un ticket?", "start": 19.68, "end": 22.74}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q3", "test6 Q1", "test14 Q2"], "correct_answer": "A", "source_location": "test5 Q3"}, {"image_path": "listening_asset/media_test5/Q4.webp", "audio_path": "listening_asset/media_test5/Q4.mp3", "chunks": [{"text": "Regardez l'image 4.", "start": 0.0, "end": 2.66}, {"text": "<PERSON><PERSON>'espère qu'on va pouvoir entrer.", "start": 4.46, "end": 7.48}, {"text": "<PERSON><PERSON>, c'est mieux.", "start": 8.96, "end": 12.2}, {"text": "<PERSON><PERSON> <PERSON> glace est très bonne.", "start": 13.84, "end": 16.9}, {"text": "<PERSON><PERSON>. <PERSON><PERSON> places, s'il vous plaît.", "start": 18.54, "end": 22.74}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q4"], "correct_answer": "A", "source_location": "test5 Q4"}, {"image_path": "listening_asset/media_test5/Q6.webp", "audio_path": "listening_asset/media_test5/Q6.mp3", "chunks": [{"text": "Regardez l'image 6.", "start": 0.0, "end": 2.44}, {"text": "<PERSON><PERSON> les bras.", "start": 4.52, "end": 6.54}, {"text": "<PERSON><PERSON> votre pull.", "start": 8.4, "end": 10.72}, {"text": "<PERSON><PERSON>vous.", "start": 12.28, "end": 14.6}, {"text": "D. <PERSON> bien fort.", "start": 15.82, "end": 19.34}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test5", "locations": ["test5 Q6", "test12 Q2"], "correct_answer": "D", "source_location": "test5 Q6"}, {"image_path": null, "audio_path": "listening_asset/media_test6/Q3.mp3", "chunks": [{"text": "Vous pouvez fermer la porte, s'il vous plaît.", "start": 0.0, "end": 2.92}, {"text": "<PERSON><PERSON> s<PERSON>r.", "start": 4.36, "end": 6.84}, {"text": "B. C'est vrai.", "start": 8.64, "end": 10.94}, {"text": "C. Très souvent.", "start": 12.46, "end": 14.74}, {"text": "D. Merci bien.", "start": 16.3, "end": 18.8}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test6", "locations": ["test6 Q3", "test10 Q5", "test21 Q2", "test24 Q3", "test34 Q2"], "correct_answer": "A", "source_location": "test6 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test6/Q4.mp3", "chunks": [{"start": 0.0, "end": 5.22, "text": "<PERSON><PERSON> -moi, je suis perdue. Je veux aller à la mairie."}, {"start": 7.2, "end": 11.48, "text": "<PERSON><PERSON>, allez -y. C 'est ouvert maintenant."}, {"start": 13.22, "end": 17.76, "text": "<PERSON><PERSON>, attendez. Elle va bientôt arriver."}, {"start": 19.54, "end": 23.5, "text": "<PERSON><PERSON>, cherchez dans la bibliothèque."}, {"start": 25.04, "end": 29.5, "text": "<PERSON><PERSON>, prenez la première rue à droite."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test6", "locations": ["test6 Q4"], "correct_answer": "D", "source_location": "test6 Q4"}, {"image_path": "listening_asset/media_test7/Q2.webp", "audio_path": "listening_asset/media_test7/Q2.mp3", "chunks": [{"text": "A. M. Tremblay est devant sa femme.", "start": 0.0, "end": 2.62}, {"text": "B. M. Tremblay suit avec les achats.", "start": 4.18, "end": 7.78}, {"text": "<PERSON><PERSON> Trem<PERSON>y sort d'une parfumerie.", "start": 9.28, "end": 12.94}, {"text": "<PERSON><PERSON> Tremblay porte tous les paquets.", "start": 14.38, "end": 18.02}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test7", "locations": ["test7 Q2"], "correct_answer": "B", "source_location": "test7 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test7/Q3.mp3", "chunks": [{"text": "Bonjour Madame. J'aime beaucoup ces chaussures en cuir dans la vitrine. Elles existent en 38 ?", "start": 0.0, "end": 9.22}, {"text": "<PERSON><PERSON>, nous n'acceptons pas l'échec.", "start": 10.08, "end": 14.38}, {"text": "<PERSON><PERSON> <PERSON><PERSON> sûr, la caisse est à l'entrée du magasin.", "start": 16.14, "end": 21.4}, {"text": "<PERSON><PERSON>, nous n'avons plus cette pointure.", "start": 23.18, "end": 27.74}, {"text": "<PERSON><PERSON> oui, les soldes commencent demain.", "start": 30.0, "end": 34.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test7", "locations": ["test7 Q3"], "correct_answer": "C", "source_location": "test7 Q3"}, {"image_path": "listening_asset/media_test8/Q1.webp", "audio_path": "listening_asset/media_test8/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON>, a<PERSON><PERSON><PERSON> -moi un jouet.", "start": 0.0, "end": 3.4}, {"text": "<PERSON><PERSON>, regarde le joli chien.", "start": 5.02, "end": 9.04}, {"text": "<PERSON><PERSON>, tu me lis une histoire.", "start": 10.66, "end": 14.6}, {"text": "<PERSON><PERSON>, je veux jouer avec eux.", "start": 16.88, "end": 20.62}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q1"], "correct_answer": "D", "source_location": "test8 Q1"}, {"image_path": "listening_asset/media_test8/Q2.webp", "audio_path": "listening_asset/media_test8/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON>, tu viens t'asseoir pour dîner ?", "start": 0.0, "end": 4.68}, {"text": "B. Ça y est, tu es prêt à mettre la table ?", "start": 4.68, "end": 10.2}, {"text": "<PERSON><PERSON> tu ne veux pas manger ?", "start": 11.42, "end": 15.18}, {"text": "<PERSON><PERSON> <PERSON> peux m'aider à préparer le repas ?", "start": 16.02, "end": 19.64}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q2"], "correct_answer": "C", "source_location": "test8 Q2"}, {"image_path": "listening_asset/media_test8/Q3.webp", "audio_path": "listening_asset/media_test8/Q3.mp3", "chunks": [{"start": 0.0, "end": 3.22, "text": "A. C'est un bon marchand de légumes."}, {"start": 5.92, "end": 8.12, "text": "<PERSON><PERSON> un plan de la ville."}, {"start": 10.48, "end": 13.28, "text": "C. L'entrée de la préfecture est là-bas."}, {"start": 15.82, "end": 17.72, "text": "D. Ma voiture est au parking."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q3", "test28 Q2", "test33 Q2"], "correct_answer": "C", "source_location": "test8 Q3"}, {"image_path": "listening_asset/media_test8/Q4.webp", "audio_path": "listening_asset/media_test8/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON> chaleur me fatigue.", "start": 0.0, "end": 3.44}, {"text": "B. Il va bientôt faire nuit.", "start": 5.24, "end": 8.66}, {"text": "<PERSON><PERSON> commence à avoir froid.", "start": 10.16, "end": 13.82}, {"text": "<PERSON><PERSON> Le temps est très nuageux.", "start": 15.24, "end": 19.04}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test8", "locations": ["test8 Q4", "test26 Q4", "test36 Q3"], "correct_answer": "A", "source_location": "test8 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q1.mp3", "chunks": [{"start": 2.88, "end": 10.68, "text": "Oh là là ! Cette valise est lourde et le train va partir. Est-ce que tu peux m'aider ?"}, {"start": 10.68, "end": 19.68, "text": "<PERSON><PERSON>, à 16h."}, {"start": 25.92, "end": 28.36, "text": "<PERSON><PERSON>, bien sûr."}, {"start": 34.66, "end": 37.12, "text": "<PERSON><PERSON>, il arrive."}, {"start": 43.58, "end": 46.1, "text": "<PERSON><PERSON>, j'ai mal."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q1", "test23 Q1", "test33 Q3"], "correct_answer": "B", "source_location": "test9 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON>, je voudrais un rendez -vous avec le docteur <PERSON>, s'il vous plaît.", "start": 0.0, "end": 5.68}, {"text": "<PERSON><PERSON>, à 10 heures aujourd'hui.", "start": 7.26, "end": 11.76}, {"text": "<PERSON><PERSON>, c'est à quelle adresse ?", "start": 13.16, "end": 18.32}, {"text": "<PERSON><PERSON>, pour combien de personnes ?", "start": 18.32, "end": 23.8}, {"text": "<PERSON><PERSON>, qu'est -ce que vous cherchez ?", "start": 25.0, "end": 32.86}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q2", "test20 Q2", "test23 Q2", "test30 Q2"], "correct_answer": "A", "source_location": "test9 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q3.mp3", "chunks": [{"start": 0.0, "end": 7.08, "text": "Nous organisons une petite fête pour l 'anniversaire de Clémence samedi soir. Tu peux venir?"}, {"start": 9.06, "end": 12.6, "text": "<PERSON><PERSON>, avec plaisir. Je serai là."}, {"start": 14.56, "end": 18.36, "text": "<PERSON><PERSON>, ce matin, je serai disponible."}, {"start": 20.36, "end": 24.02, "text": "<PERSON><PERSON>, je serai absent aujourd 'hui."}, {"start": 26.0, "end": 29.48, "text": "<PERSON><PERSON>, je serai libre mardi midi."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q3", "test20 Q3", "test23 Q3"], "correct_answer": "A", "source_location": "test9 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test9/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et bienvenue à la conférence <PERSON> -Europe. D'où venez -vous ?", "start": 0.0, "end": 8.0}, {"text": "A. Je viens de Chine.", "start": 9.26, "end": 11.7}, {"text": "<PERSON><PERSON> <PERSON> m'appelle <PERSON>.", "start": 13.18, "end": 16.28}, {"text": "C. J'aime beaucoup la France.", "start": 17.78, "end": 20.72}, {"text": "<PERSON><PERSON> <PERSON> vous remercie beaucoup.", "start": 22.42, "end": 31.02}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test9", "locations": ["test9 Q4", "test23 Q4"], "correct_answer": "A", "source_location": "test9 Q4"}, {"image_path": "listening_asset/media_test10/Q2.webp", "audio_path": "listening_asset/media_test10/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON> aimes le bœuf à la québécoise.", "start": 0.0, "end": 4.82}, {"text": "<PERSON><PERSON> <PERSON> connais l'heure du prochain film.", "start": 8.08, "end": 12.46}, {"text": "C. Tu sais où est la station de métro?", "start": 15.84, "end": 20.22}, {"text": "<PERSON><PERSON> vois où j'habite sur ce plan?", "start": 22.76, "end": 27.6}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q2", "test18 Q2"], "correct_answer": "A", "source_location": "test10 Q2"}, {"image_path": "listening_asset/media_test10/Q3.webp", "audio_path": "listening_asset/media_test10/Q3.mp3", "chunks": [{"text": "A. C'est au troisième étage.", "start": 0.0, "end": 2.92}, {"text": "<PERSON><PERSON> Entreprise Lapex, bonjour.", "start": 4.38, "end": 7.74}, {"text": "C. La cafétéria est derrière vous.", "start": 9.2, "end": 12.94}, {"text": "D. Voici votre badge, monsieur.", "start": 14.34, "end": 17.42}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q3"], "correct_answer": "A", "source_location": "test10 Q3"}, {"image_path": "listening_asset/media_test10/Q4.webp", "audio_path": "listening_asset/media_test10/Q4.mp3", "chunks": [{"text": "<PERSON><PERSON> bague<PERSON>, s'il vous plaît.", "start": 0.0, "end": 2.34}, {"text": "B. Cette boutique est magnifique.", "start": 3.68, "end": 6.84}, {"text": "C<PERSON> <PERSON>'adore toutes ces chaussures.", "start": 8.32, "end": 11.6}, {"text": "<PERSON><PERSON><PERSON> ! J'achète sept vestes.", "start": 12.92, "end": 16.76}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test10", "locations": ["test10 Q4"], "correct_answer": "B", "source_location": "test10 Q4"}, {"image_path": "listening_asset/media_test11/Q2.webp", "audio_path": "listening_asset/media_test11/Q2.mp3", "chunks": [{"start": 0.0, "end": 3.52, "text": "Regardez l'image 2"}, {"start": 3.52, "end": 8.18, "text": "<PERSON><PERSON> bonjour à ta sœur."}, {"start": 9.6, "end": 12.1, "text": "<PERSON><PERSON>-moi mon sac."}, {"start": 14.1, "end": 16.66, "text": "C. Prends des gâteaux."}, {"start": 18.76, "end": 21.06, "text": "D. Viens boire ton thé."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test11", "locations": ["test11 Q2"], "correct_answer": "A", "source_location": "test11 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q3.mp3", "chunks": [{"text": "Écoutez l'extrait sonore et les quatre propositions. Choisissez la bonne réponse. Question 3 Quel est le prix de ce kilo de pommes, s'il vous plaît ?", "start": 0.0, "end": 19.9}, {"text": "<PERSON><PERSON> Le deuxième est gratuit.", "start": 20.54, "end": 23.4}, {"text": "<PERSON><PERSON> sont très sucrées.", "start": 25.32, "end": 28.44}, {"text": "C. C'est au rayon des fruits.", "start": 30.06, "end": 33.28}, {"text": "D. Il coûte 2,10 euros.", "start": 34.88, "end": 38.46}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test11", "locations": ["test11 Q3"], "correct_answer": "D", "source_location": "test11 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test11/Q4.mp3", "chunks": [{"text": "Question 4 Mon ordinateur ne fonctionne plus. Je ne sais pas comment faire pour mes devoirs. Tu peux m'aider, s'il te plaît ?", "start": 0.88, "end": 11.96}, {"text": "<PERSON><PERSON>, c'est un bon conseil.", "start": 12.6, "end": 15.02}, {"text": "<PERSON><PERSON>, il coûte trop cher.", "start": 17.28, "end": 20.04}, {"text": "<PERSON><PERSON>, j'arrive tout de suite.", "start": 22.28, "end": 24.88}, {"text": "<PERSON><PERSON>, tu as bien travaillé.", "start": 26.56, "end": 30.16}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test11", "locations": ["test11 Q4"], "correct_answer": "C", "source_location": "test11 Q4"}, {"image_path": "listening_asset/media_test12/Q1.webp", "audio_path": "listening_asset/media_test12/Q1.mp3", "chunks": [{"text": "Écoutez les quatre propositions et choisissez celle qui correspond à l'image. Regardez l'image 1.", "start": 0.0, "end": 7.96}, {"text": "A. L'acémarque à la montagne.", "start": 8.42, "end": 11.24}, {"text": "B. L'acémarque avec ses voisins.", "start": 11.66, "end": 14.76}, {"text": "C. L'acémarque dans sa voiture.", "start": 15.22, "end": 18.02}, {"text": "<PERSON><PERSON> L'acémar<PERSON> de<PERSON> le musée.", "start": 18.78, "end": 21.72}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q1"], "correct_answer": "A", "source_location": "test12 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q3.mp3", "chunks": [{"text": "Écoutez l'extrait sonore et les quatre propositions. Choisissez la bonne réponse. Question 3 <PERSON>n, le professeur de mathématiques nous a donné un problème très difficile pour demain. Je ne comprends rien et tout seul, je n'y arrive pas. Tu veux bien m'expliquer, s'il te plaît ?", "start": 0.6, "end": 32.18}, {"text": "A. C'est gentil.", "start": 32.55, "end": 34.92}, {"text": "<PERSON><PERSON> bien sûr.", "start": 36.58, "end": 39.36}, {"text": "<PERSON><PERSON> beauco<PERSON>.", "start": 41.57, "end": 43.8}, {"text": "<PERSON><PERSON> as raison.", "start": 45.58, "end": 48.28}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q3"], "correct_answer": "B", "source_location": "test12 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test12/Q4.mp3", "chunks": [{"start": 0.76, "end": 11.2, "text": "Question 4. Je ne sais pas quel dessert préparer pour le dîner avec Marc ce soir. Qu'est-ce que tu penses d'une salade de fruits ?"}, {"start": 11.2, "end": 17.36, "text": "<PERSON><PERSON>, bonne idée. C'est facile à préparer."}, {"start": 19.34, "end": 22.52, "text": "<PERSON><PERSON>, fais-nous une tarte au citron."}, {"start": 24.48, "end": 27.94, "text": "<PERSON><PERSON>, il aime bien le chocolat chaud."}, {"start": 0.76, "end": 33.06, "text": "<PERSON><PERSON> <PERSON>t oui, on peut manger au restaurant."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test12", "locations": ["test12 Q4"], "correct_answer": "A", "source_location": "test12 Q4"}, {"image_path": "listening_asset/media_test13/Q1.webp", "audio_path": "listening_asset/media_test13/Q1.mp3", "chunks": [{"text": "Regardez l'image 1.", "start": 1.1, "end": 3.16}, {"text": "<PERSON><PERSON> vous, je vous en prie.", "start": 4.94, "end": 8.2}, {"text": "<PERSON><PERSON>, je suis <PERSON>.", "start": 10.38, "end": 13.78}, {"text": "<PERSON><PERSON> Je suis désolée, c'est complet.", "start": 15.56, "end": 19.52}, {"text": "<PERSON><PERSON> Un instant, ne quittez pas.", "start": 21.3, "end": 25.32}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q1"], "correct_answer": "B", "source_location": "test13 Q1"}, {"image_path": "listening_asset/media_test13/Q2.webp", "audio_path": "listening_asset/media_test13/Q2.mp3", "chunks": [{"start": 3.24, "end": 5.02, "text": "Regardez l 'image 2."}, {"start": 6.9, "end": 10.12, "text": "<PERSON><PERSON> là, c 'est ta maison?"}, {"start": 12.14, "end": 15.88, "text": "B. C 'est toi, le joli bé<PERSON>?"}, {"start": 17.72, "end": 21.58, "text": "C<PERSON> Il est gentil, ce gros chien?"}, {"start": 23.5, "end": 27.32, "text": "<PERSON><PERSON> sœur, elle est sur le vélo?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q2"], "correct_answer": "B", "source_location": "test13 Q2"}, {"image_path": "listening_asset/media_test13/Q3.webp", "audio_path": "listening_asset/media_test13/Q3.mp3", "chunks": [{"text": "Regardez l'image 3.", "start": 3.0, "end": 5.08}, {"text": "<PERSON><PERSON> une taille plus grande.", "start": 7.05, "end": 10.56}, {"text": "<PERSON><PERSON> La couleur de ta robe est très jolie.", "start": 12.87, "end": 16.6}, {"text": "C. Mets tes chaussures.", "start": 18.81, "end": 21.66}, {"text": "<PERSON><PERSON>utes les cabines sont occupées.", "start": 23.46, "end": 27.4}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q3", "test35 Q3"], "correct_answer": "A", "source_location": "test13 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test13/Q4.mp3", "chunks": [{"text": "Question 4 Qu'est -ce que tu veux manger ce soir?", "start": 2.32, "end": 7.74}, {"text": "A. Au restaurant grec.", "start": 9.88, "end": 12.46}, {"text": "B. Il n'y a pas de beurre.", "start": 14.24, "end": 17.2}, {"text": "C. Juste une salade.", "start": 18.76, "end": 21.64}, {"text": "<PERSON><PERSON> 20h chez moi.", "start": 23.22, "end": 26.68}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test13", "locations": ["test13 Q4", "test17 Q5"], "correct_answer": "C", "source_location": "test13 Q4"}, {"image_path": "listening_asset/media_test14/Q1.webp", "audio_path": "listening_asset/media_test14/Q1.mp3", "chunks": [{"start": 0.0, "end": 5.06, "text": "Écoutez les quatre propositions. Choisissez celle qui correspond à l'image."}, {"start": 9.06, "end": 11.62, "text": "<PERSON><PERSON> est beau ces danseurs."}, {"start": 11.94, "end": 14.64, "text": "B<PERSON> J'adore cette chanson."}, {"start": 15.06, "end": 17.98, "text": "C. Ma radio est trop forte."}, {"start": 18.46, "end": 20.88, "text": "<PERSON><PERSON> joues bien du piano."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q1", "test18 Q1", "test26 Q1"], "correct_answer": "B", "source_location": "test14 Q1"}, {"image_path": "listening_asset/media_test14/Q3.webp", "audio_path": "listening_asset/media_test14/Q3.mp3", "chunks": [{"start": 1.96, "end": 6, "text": "Regardez l'image 3."}, {"start": 6.5, "end": 10.08, "text": "<PERSON><PERSON>-vous."}, {"start": 11.84, "end": 14.56, "text": "B, attention, souriez."}, {"start": 16.32, "end": 18.44, "text": "C, chantez ensemble."}, {"start": 20.48, "end": 23.38, "text": "<PERSON>, pr<PERSON><PERSON>, partez."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q3"], "correct_answer": "B", "source_location": "test14 Q3"}, {"image_path": "listening_asset/media_test14/Q4.webp", "audio_path": "listening_asset/media_test14/Q4.mp3", "chunks": [{"text": "Regardez image 4.", "start": 2.4, "end": 4.28}, {"text": "<PERSON><PERSON>, sans nous marcher, mais lui.", "start": 6.32, "end": 8.92}, {"text": "<PERSON><PERSON> De l'eau est là en plein de la ville.", "start": 10.82, "end": 13.86}, {"text": "C. L'entrée de la préfecture est là.", "start": 15.8, "end": 18.92}, {"text": "D. La voiture est au parking.", "start": 20.9, "end": 37.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q4"], "correct_answer": "C", "source_location": "test14 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test14/Q5.mp3", "chunks": [{"text": "Question 5 <PERSON><PERSON><PERSON> madame, qu'est -ce qui vous arrive ?", "start": 1.7, "end": 8.66}, {"text": "Bon<PERSON>r, j'ai très mal à la gorge, est-ce que vous avez quelque chose pour moi ?", "start": 8.66, "end": 16.12}, {"text": "<PERSON><PERSON>, c'est plus difficile F", "start": 16.68, "end": 21}, {"text": "<PERSON><PERSON>, je suis malade aussi F", "start": 22, "end": 27.4}, {"text": "<PERSON><PERSON>, prenez ce médicament ", "start": 27.4, "end": 31}, {"text": "<PERSON><PERSON>, voici votre lecture", "start": 31.5, "end": 35.62}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test14", "locations": ["test14 Q5", "test18 Q3"], "correct_answer": "C", "source_location": "test14 Q5"}, {"image_path": "listening_asset/media_test15/Q1.webp", "audio_path": "listening_asset/media_test15/Q1.mp3", "chunks": [{"text": "Regardez l'image 1.", "start": 1.26, "end": 2.64}, {"text": "<PERSON><PERSON> dans la salle suivante.", "start": 2.86, "end": 5.44}, {"text": "B. Posez vos manteaux ici.", "start": 5.8, "end": 8.3}, {"text": "<PERSON><PERSON>z un billet à la caisse.", "start": 8.78, "end": 11.5}, {"text": "D. <PERSON>ez bien ce paysage.", "start": 12.04, "end": 14.82}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q1"], "correct_answer": "D", "source_location": "test15 Q1"}, {"image_path": "listening_asset/media_test15/Q2.webp", "audio_path": "listening_asset/media_test15/Q2.mp3", "chunks": [{"text": "Regardez l'image 2.", "start": 1.32, "end": 2.6}, {"text": "<PERSON><PERSON> -toi. Le train va bientôt partir.", "start": 2.88, "end": 6.78}, {"text": "B. Fais un beau sourire pour la photo.", "start": 7.32, "end": 10.36}, {"text": "<PERSON><PERSON> une bonne journée et travaille bien.", "start": 10.74, "end": 13.9}, {"text": "<PERSON><PERSON> ton parapluie. Il va bientôt pleuvoir.", "start": 14.38, "end": 18.1}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q2", "test31 Q2"], "correct_answer": "C", "source_location": "test15 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q3.mp3", "chunks": [{"start": 3.34, "end": 9.4, "text": "Question 3. Qu'est-ce que tu as pensé du match de football hier ?"}, {"start": 9.4, "end": 15.32, "text": "A<PERSON> Il préfère faire de la natation."}, {"start": 17.4, "end": 21.62, "text": "<PERSON><PERSON> Je ne peux pas, j'ai perdu mon ballon."}, {"start": 23.7, "end": 27.28, "text": "C. Les deux équipes ont très bien joué."}, {"start": 28, "end": 32.64, "text": "D<PERSON> On peut le regarder ensemble?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q3", "test30 Q3"], "correct_answer": "C", "source_location": "test15 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test15/Q4.mp3", "chunks": [{"start": 2.8, "end": 8.88, "text": "Question 4 Où est-ce que je peux prendre un café pendant la pause?"}, {"start": 10.8, "end": 13.34, "text": "<PERSON><PERSON> euros"}, {"start": 15.18, "end": 17.64, "text": "B<PERSON> 4"}, {"start": 19.78, "end": 22.04, "text": "<PERSON><PERSON> minutes"}, {"start": 24.2, "end": 26.62, "text": "<PERSON><PERSON> sucre"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test15", "locations": ["test15 Q4", "test18 Q4"], "correct_answer": "B", "source_location": "test15 Q4"}, {"image_path": "listening_asset/media_test16/Q1.webp", "audio_path": "listening_asset/media_test16/Q1.mp3", "chunks": [{"text": "Écoutez les quatre propositions. Choisissez celle qui correspond à l'image. Regardez l'image 1.", "start": 2.44, "end": 16.44}, {"text": "<PERSON><PERSON> pantalon me va.", "start": 18.22, "end": 20.74}, {"text": "<PERSON><PERSON> Je mets quelle cravate ?", "start": 22.92, "end": 25.8}, {"text": "<PERSON><PERSON> est ma chemise?", "start": 28.16, "end": 30.12}, {"text": "<PERSON><PERSON> aimes ce pull?", "start": 32.08, "end": 34.72}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q1"], "correct_answer": "B", "source_location": "test16 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q2.mp3", "chunks": [{"start": 0.9, "end": 7.66, "text": "Écoutez l'extrait sonore et les quatre propositions. Choisissez la bonne réponse."}, {"start": 19.88, "end": 25.18, "text": "A. D'accord, bonne soirée."}, {"start": 27.04, "end": 30.42, "text": "B. Merci. Au revoir."}, {"start": 32.68, "end": 35.58, "text": "<PERSON><PERSON>, avec plaisir."}, {"start": 37.68, "end": 40.74, "text": "D. Très bien. À demain"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q2"], "correct_answer": "C", "source_location": "test16 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q3.mp3", "chunks": [{"start": 2.1, "end": 12.74, "text": "Question 3. <PERSON><PERSON><PERSON>, monsieur. Je suis perdu. Où est le restaurant des Trois Bornes ?"}, {"start": 12.74, "end": 20, "text": "<PERSON><PERSON>, oui, je vais bien."}, {"start": 20.54, "end": 23.76, "text": "B. C'est très facile. C'est à côté."}, {"start": 25, "end": 33, "text": "<PERSON><PERSON>moi le plat du jour."}, {"start": 34.0, "end": 38.28, "text": "<PERSON><PERSON>, c'est très bon."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q3"], "correct_answer": "B", "source_location": "test16 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test16/Q4.mp3", "chunks": [{"start": 2.72, "end": 10.72, "text": "Question 4 Vous avez choisi ? Non. Qu'est-ce que vous me conseillez ?"}, {"start": 10.72, "end": 15.96, "text": "A. <PERSON>'est au fond, à droite."}, {"start": 18.0, "end": 21.3, "text": "<PERSON><PERSON> poisson est très bon."}, {"start": 23.2, "end": 26.06, "text": "<PERSON><PERSON> Nous allons fermer."}, {"start": 28.06, "end": 31.12, "text": "<PERSON><PERSON> <PERSON> r<PERSON>."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test16", "locations": ["test16 Q4", "test22 Q4", "test34 Q4"], "correct_answer": "B", "source_location": "test16 Q4"}, {"image_path": "listening_asset/media_test17/Q2.webp", "audio_path": "listening_asset/media_test17/Q2.mp3", "chunks": [{"start": 3.42, "end": 6.16, "text": "Regardez l 'image de..."}, {"start": 7.0, "end": 9.64, "text": "<PERSON><PERSON> temps fait -il ?"}, {"start": 9.64, "end": 14.18, "text": "<PERSON><PERSON> Qu 'est -ce que tu lis ?"}, {"start": 14.18, "end": 19.02, "text": "<PERSON><PERSON> Qui est au téléphone ?"}, {"start": 19.02, "end": 24.28, "text": "<PERSON><PERSON> -tu aller dehors ?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test17", "locations": ["test17 Q2"], "correct_answer": "B", "source_location": "test17 Q2"}, {"image_path": "listening_asset/media_test17/Q3.webp", "audio_path": "listening_asset/media_test17/Q3.mp3", "chunks": [{"start": 2.98, "end": 5.66, "text": "Regardez l'image 3,"}, {"start": 8.14, "end": 9.58, "text": "<PERSON><PERSON> lumière,"}, {"start": 12.12, "end": 14.74, "text": "<PERSON><PERSON> fer<PERSON> la fenêtre,"}, {"start": 17.04, "end": 19.67, "text": "C. lève-toi c'est l'heure,"}, {"start": 19.67, "end": 23.75, "text": "<PERSON><PERSON>se ton livre"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test17", "locations": ["test17 Q3"], "correct_answer": "C", "source_location": "test17 Q3"}, {"image_path": "listening_asset/media_test19/Q1.webp", "audio_path": "listening_asset/media_test19/Q1.mp3", "chunks": [{"text": "Écoutez les quatdasdare propositions. Choisissez celle qui commence pour un orage.", "start": 0.0, "end": 7.0}, {"start": 17.0, "end": 20.54, "text": "A, là, c'est marque à l'avantage."}, {"text": "B, là, c'est marque avec les voisins.", "start": 22.46, "end": 26.7}, {"text": "C. C'est la, c'est marque à la montagne.", "start": 28.82, "end": 32.28}, {"text": "<PERSON><PERSON> la, c'est marque à la montagne.", "start": 35.18, "end": 38.06}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q1"], "correct_answer": "A", "source_location": "test19 Q1"}, {"image_path": "listening_asset/media_test19/Q2.webp", "audio_path": "listening_asset/media_test19/Q2.mp3", "chunks": [{"text": "Regardez l'image 2.", "start": 3.12, "end": 5.48}, {"text": "A. J'aime prendre l'avion.", "start": 7.2, "end": 10.3}, {"text": "B. Le train est arrivé.", "start": 11.68, "end": 15.08}, {"text": "<PERSON><PERSON> belle voiture!", "start": 16.6, "end": 20.14}, {"text": "D. Voilà enfin l'autobus!", "start": 21.98, "end": 33.98}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q2"], "correct_answer": "D", "source_location": "test19 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q3.mp3", "chunks": [{"start": 0.0, "end": 7.3, "text": "Écoutez l'extrait sonore et les quatre propositions. Choisissez la bonne réponse."}, {"start": 12.38, "end": 21.94, "text": "Question 3. <PERSON>, il faut qu'on achète un nouveau frigo. Quel modèle tu préfères ?"}, {"start": 21.94, "end": 27.52, "text": "<PERSON><PERSON>, il est très grand."}, {"start": 29.8, "end": 33.96, "text": "<PERSON><PERSON> Je ne sais pas. Tu peux choisir."}, {"start": 36.7, "end": 40.2, "text": "<PERSON><PERSON>, la cuisinière est chère."}, {"start": 42.9, "end": 46.06, "text": "<PERSON><PERSON> as raison. C'est le gris."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q3"], "correct_answer": "B", "source_location": "test19 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test19/Q4.mp3", "chunks": [{"start": 3.86, "end": 9.36, "text": "Question 4. Quand est-ce que la conférence commence ?"}, {"start": 9.36, "end": 14.54, "text": "<PERSON><PERSON> 9 heures."}, {"start": 16.16, "end": 18.58, "text": "<PERSON><PERSON> deux jours."}, {"start": 20.84, "end": 22.86, "text": "C. Il y a dix minutes."}, {"start": 25.12, "end": 27.54, "text": "<PERSON><PERSON>"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test19", "locations": ["test19 Q4"], "correct_answer": "A", "source_location": "test19 Q4"}, {"image_path": "listening_asset/media_test21/Q1.webp", "audio_path": "listening_asset/media_test21/Q1.mp3", "chunks": [{"start": 3.22, "end": 5.26, "text": "Regardez l'image 1."}, {"start": 6.96, "end": 10.74, "text": "<PERSON><PERSON> vite, le train arrive."}, {"start": 12.46, "end": 15.72, "text": "<PERSON><PERSON> Descends vite de ton vélo."}, {"start": 17.38, "end": 20.38, "text": "C. Entre vite dans la voiture."}, {"start": 21.22, "end": 25.54, "text": "D, monte vite dans le bus."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test21", "locations": ["test21 Q1"], "correct_answer": "C", "source_location": "test21 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test21/Q3.mp3", "chunks": [{"start": 2.1, "end": 14, "text": "Question 3. Qu'est-ce que tu fais cet après-midi ? Je m'occupe de ma nièce. Super ! Est-ce que tu vas la chercher à l'école ?"}, {"start": 12.76, "end": 18.08, "text": "<PERSON><PERSON>, c'est la fille de mon frère."}, {"start": 19.88, "end": 23.52, "text": "<PERSON><PERSON> <PERSON><PERSON> sûr, elle est en vacances."}, {"start": 26.4, "end": 29.02, "text": "<PERSON><PERSON>, et après nous irons au parc."}, {"start": 30.14, "end": 34, "text": "<PERSON>. Et ? Peut-être. Elle sait lire et écrire."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test21", "locations": ["test21 Q3"], "correct_answer": "C", "source_location": "test21 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test22/Q2.mp3", "chunks": [{"start": 1.88, "end": 7.0, "text": "Question 2. Tu aimes visiter les musées ?"}, {"start": 7.0, "end": 12.42, "text": "<PERSON><PERSON>, j'y habite depuis un mois."}, {"start": 14.24, "end": 18.28, "text": "<PERSON><PERSON>, j'y fais mes courses le dimanche."}, {"start": 19.78, "end": 24.76, "text": "<PERSON><PERSON>, j'y suis né en 1992."}, {"start": 25, "end": 30.36, "text": "<PERSON><PERSON>, j'y vais très souvent."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test22", "locations": ["test22 Q2"], "correct_answer": "D", "source_location": "test22 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test22/Q3.mp3", "chunks": [{"text": "Question 3 Qu'est -ce que tu veux faire ce soir ?", "start": 2.08, "end": 8.0}, {"text": "<PERSON><PERSON> l'hôtel du parc", "start": 9.5, "end": 12.12}, {"text": "B. À voir de nos maisons", "start": 13.74, "end": 16.8}, {"text": "C. Un pain au chocolat", "start": 18.22, "end": 21.22}, {"text": "<PERSON><PERSON> au cinéma", "start": 22.92, "end": 26.02}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test22", "locations": ["test22 Q3"], "correct_answer": "D", "source_location": "test22 Q3"}, {"image_path": "listening_asset/media_test24/Q2.webp", "audio_path": "listening_asset/media_test24/Q2.mp3", "chunks": [{"start": 2.6, "end": 4.64, "text": "Regardez l'image 2."}, {"start": 6.36, "end": 9.78, "text": "<PERSON><PERSON>, c'est libre ici ?"}, {"start": 9.78, "end": 14.74, "text": "<PERSON><PERSON>, je peux m'asseoir ?"}, {"start": 14.74, "end": 19.66, "text": "<PERSON><PERSON>, où est votre billet ?"}, {"start": 19.66, "end": 25.02, "text": "<PERSON><PERSON>, vous voulez ma place ?"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test24", "locations": ["test24 Q2", "test26 Q2"], "correct_answer": "D", "source_location": "test24 Q2"}, {"image_path": "listening_asset/media_test25/Q2.webp", "audio_path": "listening_asset/media_test25/Q2.mp3", "chunks": [{"text": "Regardez l'image 2.", "start": 2.42, "end": 4.46}, {"text": "<PERSON><PERSON> petits boissons.", "start": 6.18, "end": 9.84}, {"text": "<PERSON><PERSON>, je nage.", "start": 11.32, "end": 14.36}, {"text": "<PERSON><PERSON> pleut, vite.", "start": 16.37, "end": 18.68}, {"text": "D. C'est mon papa.", "start": 20.26, "end": 22.82}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test25", "locations": ["test25 Q2"], "correct_answer": "D", "source_location": "test25 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test25/Q3.mp3", "chunks": [{"text": "Question 3 Comment tu trouves les photos de <PERSON> ?", "start": 0.0, "end": 9.26}, {"text": "<PERSON><PERSON>", "start": 9.52, "end": 11.36}, {"text": "<PERSON><PERSON>", "start": 11.36, "end": 15.42}, {"text": "C. <PERSON>licitations", "start": 15.42, "end": 19.18}, {"text": "<PERSON><PERSON>", "start": 20.5, "end": 23.36}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test25", "locations": ["test25 Q3"], "correct_answer": "D", "source_location": "test25 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test25/Q4.mp3", "chunks": [{"start": 3.64, "end": 9.1, "text": "Question 4. <PERSON>, est -ce que tu pars en vacances cet été ?"}, {"start": 9.1, "end": 15.18, "text": "<PERSON><PERSON>, je peux te conseiller un hôtel."}, {"start": 16.88, "end": 19.96, "text": "<PERSON><PERSON>, je préfère prendre le train."}, {"start": 22.02, "end": 24.82, "text": "<PERSON><PERSON>, je retourne au Maroc."}, {"start": 26.82, "end": 29.86, "text": "<PERSON><PERSON>, je vais rester chez moi."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test25", "locations": ["test25 Q4"], "correct_answer": "C", "source_location": "test25 Q4"}, {"image_path": "listening_asset/media_test27/Q1.webp", "audio_path": "listening_asset/media_test27/Q1.mp3", "chunks": [{"text": "Regardez l'image 1.", "start": 0.0, "end": 2.38}, {"text": "<PERSON><PERSON>vre ton livre.", "start": 4.1, "end": 6.2}, {"text": "<PERSON><PERSON> à ta place.", "start": 7.3, "end": 10.82}, {"text": "<PERSON><PERSON>rs tes affaires.", "start": 13.0, "end": 15.48}, {"text": "D. Viens répondre.", "start": 17.02, "end": 19.7}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test27", "locations": ["test27 Q1"], "correct_answer": "D", "source_location": "test27 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test27/Q2.mp3", "chunks": [{"text": "Question 2. A quelle heure arrivez -vous en travail?", "start": 2.24, "end": 8.32}, {"text": "<PERSON><PERSON> quoi?", "start": 10.42, "end": 12.12}, {"text": "<PERSON>. À nuit?", "start": 13.8, "end": 16.28}, {"text": "C. <PERSON>?", "start": 17.88, "end": 20.44}, {"text": "<PERSON><PERSON> vélo?", "start": 21.88, "end": 24.44}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test27", "locations": ["test27 Q2"], "correct_answer": "B", "source_location": "test27 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test27/Q3.mp3", "chunks": [{"text": "Question 3 <PERSON>, j'ai deux places pour le concert de Céline Dion samedi prochain. Tu veux venir avec moi?", "start": 2.92, "end": 12.86}, {"text": "A. D'accord. J'adore cette chanteuse.", "start": 15.12, "end": 18.94}, {"text": "B. D'accord. J'apporte le dessert.", "start": 21.06, "end": 24.58}, {"text": "C. D'accord. J'ai un téléphone demain.", "start": 26.6, "end": 30.48}, {"text": "D. D'accord. Je réserve les billets.", "start": 32.68, "end": 36.26}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test27", "locations": ["test27 Q3"], "correct_answer": "A", "source_location": "test27 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test27/Q4.mp3", "chunks": [{"text": "Question 4 Tu peux me dire quel moyen de transport je dois prendre pour aller à l'aéroport?", "start": 2.7, "end": 11.66}, {"text": "A. Arrive bien en avance.", "start": 13.98, "end": 17.2}, {"text": "B. Garde ta valise avec toi.", "start": 19.34, "end": 22.5}, {"text": "C. <PERSON> le bus 78.", "start": 24.62, "end": 27.66}, {"text": "<PERSON><PERSON> une bouteille d'eau.", "start": 30.0, "end": 33.08}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test27", "locations": ["test27 Q4"], "correct_answer": "C", "source_location": "test27 Q4"}, {"image_path": "listening_asset/media_test28/Q1.webp", "audio_path": "listening_asset/media_test28/Q1.mp3", "chunks": [{"text": "<PERSON><PERSON> l'image 1.", "start": 0.0, "end": 5.6}, {"text": "<PERSON><PERSON>, vous allez vous blesser.", "start": 6.92, "end": 11.26}, {"text": "B. Le cours d'anglais commence à 8 heures.", "start": 13.04, "end": 17.16}, {"text": "<PERSON><PERSON>, tu es libre pour un tennis?", "start": 18.92, "end": 22.92}, {"text": "<PERSON><PERSON> Voici le dossier d'inscription à compléter.", "start": 24.56, "end": 29.66}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test28", "locations": ["test28 Q1", "test38 Q1"], "correct_answer": "D", "source_location": "test28 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test28/Q3.mp3", "chunks": [{"text": "Question 3. Je viens d'acheter mes billets d'avion pour Dakar. Je pars le 2 juillet. Et toi, qu'est -ce que tu fais pendant les vacances ?", "start": 3.08, "end": 14.0}, {"text": "A. C'est une chouette idée.", "start": 15.52, "end": 18.04}, {"text": "B. Je pars avec des amis.", "start": 20.1, "end": 23.28}, {"text": "C. Ma mère vit là -bas.", "start": 25.36, "end": 28.0}, {"text": "D<PERSON> On revient dans 3 jours.", "start": 30.0, "end": 32.98}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test28", "locations": ["test28 Q3"], "correct_answer": "B", "source_location": "test28 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test28/Q4.mp3", "chunks": [{"text": "Question 4. Où habite ta famille?", "start": 2.44, "end": 7.36}, {"text": "<PERSON><PERSON>lier.", "start": 9.34, "end": 11.66}, {"text": "B. En vacances.", "start": 12.8, "end": 15.74}, {"text": "<PERSON><PERSON> <PERSON> grande sœur.", "start": 17.62, "end": 20.22}, {"text": "<PERSON><PERSON> prochain.", "start": 21.68, "end": 24.5}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test28", "locations": ["test28 Q4", "test30 Q4"], "correct_answer": "A", "source_location": "test28 Q4"}, {"image_path": "listening_asset/media_test29/Q1.webp", "audio_path": "listening_asset/media_test29/Q1.mp3", "chunks": [{"start": 2.66, "end": 10.22, "text": "Regardez l'image de <PERSON><PERSON> votre téléphone madame."}, {"start": 11.72, "end": 16.38, "text": "<PERSON><PERSON> votre manteau, il fait froid ici."}, {"start": 18.12, "end": 22.22, "text": "<PERSON><PERSON> couché, j'appelle un médecin."}, {"start": 23.92, "end": 27.92, "text": "<PERSON><PERSON>, je vais regarder votre bras."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test29", "locations": ["test29 Q1", "test36 Q2"], "correct_answer": "D", "source_location": "test29 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test29/Q3.mp3", "chunks": [{"start": 2.14, "end": 7.98, "text": "Donc toi, à quelle heure arrivez-vous au travail ?"}, {"start": 7.98, "end": 11.8, "text": "<PERSON><PERSON> d<PERSON>,"}, {"start": 2.14, "end": 24.44, "text": "<PERSON><PERSON> ou à midi,"}, {"start": 2.14, "end": 24.44, "text": "<PERSON><PERSON>,"}, {"start": 2.14, "end": 24.44, "text": "D. et à vélo."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test29", "locations": ["test29 Q3", "test37 Q2"], "correct_answer": "B", "source_location": "test29 Q3"}, {"image_path": "listening_asset/media_test30/Q1.webp", "audio_path": "listening_asset/media_test30/Q1.mp3", "chunks": [{"text": "Regardez l'image 2.", "start": 0.0, "end": 1.86}, {"text": "<PERSON><PERSON>moi un verre de lait.", "start": 3.98, "end": 8.26}, {"text": "B. Elle est belle, votre viande.", "start": 11.54, "end": 16.52}, {"text": "<PERSON><PERSON> voudrais un kilo de tomates.", "start": 19.76, "end": 26.22}, {"text": "<PERSON><PERSON>, je cherche le marché.", "start": 30.0, "end": 36.58}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test30", "locations": ["test30 Q1", "test35 Q2"], "correct_answer": "C", "source_location": "test30 Q1"}, {"image_path": "listening_asset/media_test31/Q1.webp", "audio_path": "listening_asset/media_test31/Q1.mp3", "chunks": [{"text": "Regardez l'image 1.", "start": 3.08, "end": 5.24}, {"text": "<PERSON><PERSON>, c'est fermé ce soir.", "start": 6.78, "end": 10.98}, {"text": "<PERSON><PERSON>, il manque un document.", "start": 12.62, "end": 16.64}, {"text": "<PERSON><PERSON>, il s'est cassé la jambe.", "start": 18.84, "end": 22.72}, {"text": "<PERSON><PERSON>, la fumée vient de là.", "start": 24.38, "end": 33.14}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test31", "locations": ["test31 Q1"], "correct_answer": "D", "source_location": "test31 Q1"}, {"image_path": "listening_asset/media_test31/Q3.webp", "audio_path": "listening_asset/media_test31/Q3.mp3", "chunks": [{"text": "Regardez l'image 3.", "start": 2.84, "end": 4.82}, {"text": "A. Circulation tout à fait normale sur l'autoroute 440.", "start": 6.62, "end": 11.56}, {"text": "B. Aucune circulation dans les deux sens sur l'autoroute.", "start": 13.18, "end": 17.58}, {"text": "C. Un terrible accident sur la route des vacances.", "start": 19.3, "end": 23.42}, {"text": "D. Attention gros embouteillages sur l'autoroute 440.", "start": 23.42, "end": 30.44}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test31", "locations": ["test31 Q3"], "correct_answer": "A", "source_location": "test31 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test31/Q4.mp3", "chunks": [{"text": "Question 4 <PERSON><PERSON> <PERSON>moi, madame, je cherche le restaurant les 4 saisons. Est -ce que vous savez où il se trouve ?", "start": 2.44, "end": 13.84}, {"text": "A. C'est la formule avec le plat du jour.", "start": 14.6, "end": 18.4}, {"text": "B. C'est mieux de réserver avant.", "start": 20.24, "end": 23.44}, {"text": "C. C'est tout droit, après la pharmacie.", "start": 25.5, "end": 29.1}, {"text": "D. Et c'est un menu à 25 euros.", "start": 30.94, "end": 34.12}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test31", "locations": ["test31 Q4"], "correct_answer": "C", "source_location": "test31 Q4"}, {"image_path": "listening_asset/media_test32/Q1.webp", "audio_path": "listening_asset/media_test32/Q1.mp3", "chunks": [{"start": 0.0, "end": 2.36, "text": "Regardez l'image 1."}, {"start": 3.9, "end": 9.6, "text": "<PERSON><PERSON>, votre valise est ouverte."}, {"start": 12.46, "end": 19.12, "text": "B. Ce modèle de valise coûte 69 euros."}, {"start": 22.44, "end": 28.36, "text": "<PERSON><PERSON> votre valise ici, s'il vous plaît."}, {"start": 31, "end": 37.24, "text": "<PERSON><PERSON>, votre valise est prête."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q1"], "correct_answer": "C", "source_location": "test32 Q1"}, {"image_path": "listening_asset/media_test32/Q2.webp", "audio_path": "listening_asset/media_test32/Q2.mp3", "chunks": [{"text": "Bonjour ! Regardez l'image de...", "start": 0.0, "end": 4.86}, {"text": "<PERSON><PERSON>, on s'est frites, s'il vous plaît.", "start": 6.02, "end": 10.66}, {"text": "<PERSON><PERSON>, une table pour toute personne.", "start": 12.02, "end": 16.68}, {"text": "<PERSON><PERSON>, un petit dos de tomate, je vous prie.", "start": 18.3, "end": 23.16}, {"text": "<PERSON><PERSON>, vous pouvez garder la main en air.", "start": 24.68, "end": 28.98}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q2"], "correct_answer": "A", "source_location": "test32 Q2"}, {"image_path": "listening_asset/media_test32/Q3.webp", "audio_path": "listening_asset/media_test32/Q3.mp3", "chunks": [{"text": "Nous, regardez l'image forte.", "start": 0.0, "end": 4.6}, {"text": "<PERSON><PERSON> modèle vous modèlez ?", "start": 6.46, "end": 10.0}, {"text": "<PERSON><PERSON>l gout vous coisserez ?", "start": 11.08, "end": 14.4}, {"text": "<PERSON><PERSON>lle est votre pointure ?", "start": 15.06, "end": 19.86}, {"text": "<PERSON><PERSON> taille vous faites ?", "start": 20.34, "end": 23.32}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q3"], "correct_answer": "A", "source_location": "test32 Q3"}, {"image_path": "listening_asset/media_test32/Q4.webp", "audio_path": "listening_asset/media_test32/Q4.mp3", "chunks": [{"text": "Deuxièmement. Regardez image 4.", "start": 0.0, "end": 4.78}, {"text": "<PERSON><PERSON>, elle est très lourde.", "start": 6.42, "end": 10.48}, {"text": "<PERSON><PERSON> <PERSON> voudrais un billet pour Strasbourg.", "start": 11.9, "end": 16.18}, {"text": "<PERSON><PERSON> plein de diesel, s'il vous plaît.", "start": 17.62, "end": 21.86}, {"text": "<PERSON><PERSON> votre ceinture, monsieur.", "start": 23.06, "end": 26.92}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test32", "locations": ["test32 Q4"], "correct_answer": "A", "source_location": "test32 Q4"}, {"image_path": "listening_asset/media_test33/Q1.webp", "audio_path": "listening_asset/media_test33/Q1.mp3", "chunks": [{"start": 4.0, "end": 6.22, "text": "Regardez l'image 1."}, {"start": 7.54, "end": 10.98, "text": "<PERSON><PERSON> vous disputer."}, {"start": 12.76, "end": 17.02, "text": "B. Finissez votre goûter les enfants."}, {"start": 18.78, "end": 22.9, "text": "<PERSON><PERSON>, vous allez attraper froid."}, {"start": 24, "end": 28.28, "text": "<PERSON><PERSON> de dessous vos lits."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test33", "locations": ["test33 Q1"], "correct_answer": "A", "source_location": "test33 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test33/Q4.mp3", "chunks": [{"start": 3.72, "end": 10.96, "text": "Question 4. Je ne trouve pas la clé de la voiture. Tu l'as vu quelque part ?"}, {"start": 10.96, "end": 17.48, "text": "<PERSON><PERSON>, tu l'as garée devant la maison."}, {"start": 19.62, "end": 24.12, "text": "<PERSON><PERSON>, tu l'as posée sur la table."}, {"start": 26.16, "end": 30.98, "text": "<PERSON><PERSON>, tu peux en racheter une au marché"}, {"start": 32.9, "end": 37.32, "text": "<PERSON><PERSON>, tu peux sûrement la prendre"}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test33", "locations": ["test33 Q4"], "correct_answer": "B", "source_location": "test33 Q4"}, {"image_path": "listening_asset/media_test34/Q1.webp", "audio_path": "listening_asset/media_test34/Q1.mp3", "chunks": [{"text": "Regardez l'image 1.", "start": 0.0, "end": 2.8}, {"text": "<PERSON><PERSON> le Chef, il décore le restaurant.", "start": 4.36, "end": 8.52}, {"text": "<PERSON><PERSON> le Chef, il lave les assiettes.", "start": 10.24, "end": 14.64}, {"text": "<PERSON><PERSON> le Chef, il prépare les plats.", "start": 16.8, "end": 20.38}, {"text": "<PERSON><PERSON> le Chef, il prend les commandes.", "start": 22.22, "end": 26.1}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test34", "locations": ["test34 Q1"], "correct_answer": "B", "source_location": "test34 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test35/Q4.mp3", "chunks": [{"start": 2.24, "end": 7.52, "text": "Question 4. Où se passe l'examen d'anglais?"}, {"start": 9.42, "end": 11.28, "text": "A. C'est difficile."}, {"start": 13.14, "end": 15.32, "text": "B. C'est en joint."}, {"start": 17.06, "end": 19.46, "text": "C. C'est en salle 8."}, {"start": 21.38, "end": 23.64, "text": "D. C'est un oral."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test35", "locations": ["test35 Q4"], "correct_answer": "C", "source_location": "test35 Q4"}, {"image_path": "listening_asset/media_test36/Q1.webp", "audio_path": "listening_asset/media_test36/Q1.mp3", "chunks": [{"text": "Pum, pum, pum, pum, regardez l'image 1.", "start": 0.0, "end": 4.86}, {"text": "<PERSON><PERSON>, un aller -retour pour Paris, s'il vous plaît.", "start": 6.6, "end": 12.16}, {"text": "<PERSON><PERSON>, un pain au chocolat, s'il vous plaît.", "start": 13.72, "end": 18.5}, {"text": "<PERSON><PERSON>, une boîte d'aspirine, s'il vous plaît.", "start": 20.06, "end": 24.82}, {"text": "<PERSON><PERSON>, une enveloppe avec un timbre, s'il vous plaît.", "start": 26.4, "end": 32.3}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test36", "locations": ["test36 Q1"], "correct_answer": "A", "source_location": "test36 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test36/Q4.mp3", "chunks": [{"start": 2.7, "end": 9.12, "text": "Question 4. <PERSON><PERSON>, tu sais s'il va faire beau dimanche?"}, {"start": 11.84, "end": 14.66, "text": "<PERSON><PERSON>, il va toujours pleuvoir."}, {"start": 17.22, "end": 20.06, "text": "<PERSON><PERSON>, il va peut-être neiger."}, {"start": 22.52, "end": 25.48, "text": "<PERSON><PERSON>, il va y avoir de l'orage."}, {"start": 26, "end": 31.8, "text": "<PERSON><PERSON>t oui, il va y avoir du soleil."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test36", "locations": ["test36 Q4"], "correct_answer": "D", "source_location": "test36 Q4"}, {"image_path": "listening_asset/media_test37/Q1.png", "audio_path": "listening_asset/media_test37/Q1.mp3", "chunks": [{"text": "Question 1. <PERSON><PERSON><PERSON><PERSON> les quatre propositions, choisissez celle qui correspond à l'image.", "start": 0.0, "end": 5.86}, {"text": "<PERSON>. <PERSON> spectacle est vraiment intéressant.", "start": 8.12, "end": 11.22}, {"text": "<PERSON><PERSON> Oh là là, je m'ennuie vraiment.", "start": 12.33, "end": 15.24}, {"text": "C. J'adore le football.", "start": 16.06, "end": 18.5}, {"text": "<PERSON><PERSON> Je préfère le menu à 50 dollars.", "start": 19.77, "end": 22.98}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test37", "locations": ["test37 Q1"], "correct_answer": "A", "source_location": "test37 Q1"}, {"image_path": null, "audio_path": "listening_asset/media_test37/Q3.mp3", "chunks": [{"text": "Question 3. <PERSON><PERSON><PERSON> Madame la Directrice, vous avez passé un bon week -end ?", "start": 0.0, "end": 7.06}, {"text": "<PERSON><PERSON> plaisir.", "start": 7.06, "end": 8.96}, {"text": "B. C'est d'accord.", "start": 9.52, "end": 11.52}, {"text": "<PERSON><PERSON>.", "start": 11.98, "end": 13.98}, {"text": "<PERSON><PERSON>, et vous ?", "start": 14.7, "end": 16.62}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test37", "locations": ["test37 Q3"], "correct_answer": "D", "source_location": "test37 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test37/Q4.mp3", "chunks": [{"start": 2.14, "end": 13.04, "text": "Question 4. On se rappelle pour déjeuner ensemble? O<PERSON>, ça me ferait plaisir. Tu es libre quand?"}, {"start": 14.7, "end": 17.64, "text": "<PERSON><PERSON> la cafétéria."}, {"start": 19.22, "end": 22.02, "text": "B. Au centre-ville."}, {"start": 23.8, "end": 26.66, "text": "<PERSON>. <PERSON>c ma sœur."}, {"start": 28.38, "end": 31.42, "text": "<PERSON><PERSON> début de sema<PERSON>."}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test37", "locations": ["test37 Q4"], "correct_answer": "D", "source_location": "test37 Q4"}, {"image_path": "listening_asset/media_test38/Q2.png", "audio_path": "listening_asset/media_test38/Q2.mp3", "chunks": [{"text": "<PERSON><PERSON> yeux", "start": 0.0, "end": 2.28}, {"text": "B. <PERSON>", "start": 2.28, "end": 6.62}, {"text": "<PERSON><PERSON>-toi les dents", "start": 6.62, "end": 11.18}, {"text": "<PERSON><PERSON> la bouche", "start": 11.18, "end": 15.86}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test38", "locations": ["test38 Q2"], "correct_answer": "D", "source_location": "test38 Q2"}, {"image_path": "listening_asset/media_test38/Q3.png", "audio_path": "listening_asset/media_test38/Q3.mp3", "chunks": [{"text": "<PERSON><PERSON> Je t'apporte un verre d'eau.", "start": 1.02, "end": 5.06}, {"text": "<PERSON><PERSON> je te dépose un roman policier.", "start": 6.68, "end": 10.66}, {"text": "C<PERSON> je te donne le courrier urgent.", "start": 12.5, "end": 15.6}, {"text": "D<PERSON> Je te montre le programme télé.", "start": 17, "end": 20.92}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test38", "locations": ["test38 Q3"], "correct_answer": "A", "source_location": "test38 Q3"}, {"image_path": null, "audio_path": "listening_asset/media_test38/Q4.mp3", "chunks": [{"text": "Question 4.", "start": 2.9, "end": 4.18}, {"text": "<PERSON><PERSON>, c'est <PERSON>. Et toi?", "start": 5.72, "end": 7.88}, {"text": "<PERSON><PERSON>, c'est <PERSON>.", "start": 9.86, "end": 12.46}, {"text": "<PERSON><PERSON>, j'aime le cinéma.", "start": 14.58, "end": 17.3}, {"text": "<PERSON><PERSON>, j'ai 20 ans.", "start": 19.32, "end": 21.82}, {"text": "<PERSON><PERSON>, j'habite à Paris", "start": 23.76, "end": 26.92}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test38", "locations": ["test38 Q4"], "correct_answer": "A", "source_location": "test38 Q4"}, {"image_path": null, "audio_path": "listening_asset/media_test39/Q2.mp3", "chunks": [{"text": "Question 2 A. quelle heure finit le cours de français ?", "start": 2.28, "end": 7.78}, {"text": "<PERSON><PERSON> et demi", "start": 7.78, "end": 12.06}, {"text": "B. Ave<PERSON> le professeur", "start": 13.9, "end": 16.74}, {"text": "C. C'est le jeudi", "start": 18.4, "end": 21.1}, {"text": "D. Dan<PERSON> la salle B10", "start": 22.96, "end": 25.82}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test39", "locations": ["test39 Q2"], "correct_answer": "A", "source_location": "test39 Q2"}, {"image_path": null, "audio_path": "listening_asset/media_test39/Q3.mp3", "chunks": [{"text": "Question 3. <PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON>. Je cherche la gare, s'il vous plaît.", "start": 2.78, "end": 10.14}, {"text": "A. C'est droit devant vous.", "start": 11.9, "end": 14.58}, {"text": "B. Elle ferme à 18 heures.", "start": 16.74, "end": 19.12}, {"text": "C. Le train va bientôt partir.", "start": 20.94, "end": 23.72}, {"text": "<PERSON><PERSON> vos billets, <PERSON>.", "start": 25.68, "end": 28.38}], "choices": {"A": "", "B": "", "C": "", "D": ""}, "test_id": "test39", "locations": ["test39 Q3"], "correct_answer": "A", "source_location": "test39 Q3"}]