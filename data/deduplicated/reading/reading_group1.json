[{"extracted_text": "Il est 17 heures. Je suis chez\nPaul. Je rentre à la maison\nà 19 heures. On mange à\n20 heures ou plus tard ?\nBises\nJulie", "question_text": "À quelle heure va arriver <PERSON> ?", "choices": {"A": "À 19 heures.", "B": "À 17 heures.", "C": "À 21 heures.", "D": "À 20 heures."}, "locations": ["test1_gr Q1"], "correct_answer": "A", "source_location": "test1_gr Q1"}, {"extracted_text": "Nous allons au parc avec Stéphanie.\nEst-ce que tu veux venir avec nous ?\nOn se retrouve devant l’entrée\ndans un quart d’heure ?\n\nÀ tout de suite !\n\nAnne", "question_text": "Que propose Anne dans ce message ?", "choices": {"A": "Un emploi.", "B": "Une aide.", "C": "Un service.", "D": "Un rendez-vous."}, "locations": ["test1_gr Q2"], "correct_answer": "D", "source_location": "test1_gr Q2"}, {"extracted_text": "FESTIVAL INTERNATIONAL DU CINÉMA\n\nCe Pass donne accès à tous les films pour 1 € seulement,\ndu 16 mars 2007, 21 heures au 17 mars 2007, minuit.", "question_text": "Ce Pass est valable :", "choices": {"A": "Une semaine.", "B": "Un an.", "C": "Un jour.", "D": "Un mois."}, "locations": ["test1_gr Q3"], "correct_answer": "C", "source_location": "test1_gr Q3"}, {"extracted_text": "De...    <EMAIL>\nA...     <EMAIL>\nObjet : <PERSON><PERSON><PERSON>, tu viens toujours me chercher demain ?\n\nMon train arrive à 11h.\n\n<PERSON><PERSON><PERSON>,\n\n-- \n<PERSON>", "question_text": "Que doit faire Loïc ?", "choices": {"A": "Réserver une place.", "B": "Prendre un train.", "C": "Acheter un billet.", "D": "Allez à la gare."}, "locations": ["test1_gr Q4"], "correct_answer": "D", "source_location": "test1_gr Q4"}, {"extracted_text": "<PERSON><PERSON>, je t<PERSON>attends au\ncoin de la rue, devant la\npharmacie. Fais vite, le film\ncommence dans 10 minutes !", "question_text": "Où va Delphine ?", "choices": {"A": "Chez le docteur.", "B": "En ville.", "C": "À la maison.", "D": "Au cinéma."}, "locations": ["test2_gr Q1"], "correct_answer": "D", "source_location": "test2_gr Q1"}, {"extracted_text": "Information aux élèves de 3eA\nLe cours de biologie aura exceptionnellement \nlieu au 4e étage en salle 406.\nL'horaire reste le même.\n\nVotre professeur", "question_text": "Pourquoi le professeur laisse-t-il ce message ?", "choices": {"A": "Pour préciser les devoirs à faire.", "B": "Pour indiquer un changement de salle.", "C": "Pour donner la date de l’examen.", "D": "Pour annuler la classe du jour."}, "locations": ["test2_gr Q2"], "correct_answer": "B", "source_location": "test2_gr Q2"}, {"extracted_text": "Salon Tiff - Tiff\nCoiffeur visagiste\n\nOuvert du mardi au samedi.\nDe 10h à 13h et de 14h à 19h du mardi au vendredi.\nSans interruption le samedi.\n<PERSON><PERSON><PERSON> le lundi et le jeudi matin.\nSur rendez-vous uniquement.", "question_text": "Quand le client peut-il aller dans ce salon", "choices": {"A": "Le lundi à 14h.", "B": "Le mercredi à 13h.", "C": "Le vendredi à 14h.", "D": "Le jeudi à 10h."}, "locations": ["test2_gr Q3"], "correct_answer": "C", "source_location": "test2_gr Q3"}, {"extracted_text": "De : <EMAIL>\n  \n  À : <copains de fac>\n  \n  Objet : <PERSON> à tous,\n  \n  <PERSON> dé<PERSON>nage la semaine prochaine à Bruxelles.\n  Elle prépare une fête pour son départ. Vous êtes \n  d’accord lui offrir un petit souvenir ?\n  \n  Répondez-moi vite !\n  \n  Sami", "question_text": "<PERSON> propose Sami ?", "choices": {"A": "<PERSON>’acheter un cadeau.", "B": "<PERSON>’aller à Bruxelles.", "C": "D’écrire à Mina.", "D": "D’organiser une fête."}, "locations": ["test2_gr Q4"], "correct_answer": "A", "source_location": "test2_gr Q4"}, {"extracted_text": "Maman.\nJe fais mes devoirs de\nmathématiques chez Louise.\nJe rentre à 21 heures. Bises\net à ce soir.\nPatrick", "question_text": "Qu'est-ce que <PERSON> fait chez <PERSON> ?", "choices": {"A": "Il dort", "B": "Il travaille", "C": "Il joue", "D": "Il mange"}, "locations": ["test1 Q1", "test4 Q1"], "correct_answer": "B", "source_location": "test1 Q1"}, {"extracted_text": "Attention\nCe<PERSON> se<PERSON>, l'accueil de l'université est fermé lundi\ntoute la journée et vendredi après-midi.", "question_text": "À quoi sert cette affiche ?", "choices": {"A": "<PERSON><PERSON><PERSON> un changement", "B": "Décrire un endroit", "C": "Donner un rendez-vous", "D": "Organiser une réunion"}, "locations": ["test1 Q2"], "correct_answer": "A", "source_location": "test1 Q2"}, {"extracted_text": "<PERSON><PERSON>, bon<PERSON><PERSON>,\nC'est d'accord, RV avec les \ncopines chez-moi le 30 avril \npour déjeuner\nBisous\nYvette", "question_text": "Quelles sont les relations entre Aline et Yvette ?", "choices": {"A": "Professionnelles", "B": "Familiales", "C": "Amicales", "D": "Commerciales"}, "locations": ["test1 Q3"], "correct_answer": "C", "source_location": "test1 Q3"}, {"extracted_text": "Nous informons notre aimable clientèle\nque nous sommes actuellement en\nvacances jusqu'au dimanche 1er\nseptembre. Nous aurons le plaisir de vous\nretrouver à partir du 2. Le magasin ouvrira\ncomme tous les lundis à 11 h.\nBonnes vacances à tous", "question_text": "Qu'apprend-on sur le magasin ?", "choices": {"A": "Il va avoir de nouveaux horaires", "B": "Il va changer de propriétaire", "C": "Il va déménager en septembre", "D": "Il va fermer pendant l’été"}, "locations": ["test1 Q4", "test9 Q3"], "correct_answer": "D", "source_location": "test1 Q4"}, {"extracted_text": "Vous voulez découvrir d’autres\ncultures ? vous voulez parler anglais,\nfrançais, espagnol ?\nLeçons à la maison ou au centre\nBemardisilmo.\nInformation au 01 03 02 06 65", "question_text": "Qu’est ce que propose cette publicité ?", "choices": {"A": "Des cours", "B": "<PERSON>p<PERSON>", "C": "Des livres", "D": "Des voyages"}, "locations": ["test2 Q1"], "correct_answer": "A", "source_location": "test2 Q1"}, {"extracted_text": "Faites confiance à l’entreprise\npour tous vous envois urgents.\nNous prenons vos paquets à domicile\nou dans les bureaux de poste et nous\nles envoyons dans le monde entier.", "question_text": "Que propose cette entreprise ?", "choices": {"A": "De stocker des marchandises", "B": "De transporter des colis", "C": "De vendre des cartons", "D": "De voyager à l’étranger"}, "locations": ["test2 Q2"], "correct_answer": "B", "source_location": "test2 Q2"}, {"extracted_text": "Je suis bien arrivé chez moi. Je\nt’appelle demain pour aller voir\nle film. Je t’embrasse.\n\nCristian.", "question_text": "Où est <PERSON><PERSON>ian ?", "choices": {"A": "À la gare", "B": "À la maison", "C": "Au cinéma", "D": "Au travail"}, "locations": ["test2 Q3", "test4 Q3", "test11 Q3"], "correct_answer": "B", "source_location": "test2 Q3"}, {"extracted_text": "<PERSON>, n’oubliez pas notre\ndéjeuner au restaurant. <PERSON> vous\nattends devant l’ascenseur\n\nAlice", "question_text": "Pourquoi est-ce qu’<PERSON> attend <PERSON> ?", "choices": {"A": "Pour des courses", "B": "Pour un repas", "C": "Pour un travail", "D": "Pour une réunion"}, "locations": ["test2 Q4"], "correct_answer": "B", "source_location": "test2 Q4"}, {"extracted_text": "<PERSON><PERSON> <PERSON>,\n\nNous sommes heureux de vous \nannoncer l’arrivée d’un nouveau \nmembre dans notre famille. Il se nomme \nAdrien et a vu le jour le 1er septembre \nau matin.\nBien à vous.", "question_text": "De quel événement s’agit-il ?", "choices": {"A": "D’un anniversaire", "B": "D’un décès", "C": "D’un mariage", "D": "D’une naissance"}, "locations": ["test3 Q1", "test24 Q1"], "correct_answer": "D", "source_location": "test3 Q1"}, {"extracted_text": "<PERSON><PERSON><PERSON><PERSON>, je ne viens pas aux cours de français et je ne peux pas travailler à la\nbibliothèque avec toi aujourd’hui. Je suis malade et je reste à la maison.\n<PERSON><PERSON>,\n<PERSON>", "question_text": "Qu’est-ce que Sandra fait ?", "choices": {"A": "Elle étudie son français", "B": "Elle invite son ami <PERSON>", "C": "Elle reste chez elle", "D": "Elle va à la bibliothèque"}, "locations": ["test3 Q2"], "correct_answer": "C", "source_location": "test3 Q2"}, {"extracted_text": "J’ai une réunion jusqu’à 19 h 30. Tu\npeux réserver nos places au cinéma\npour un film après 20h ?\n<PERSON><PERSON>i\n<PERSON>", "question_text": "Que veut faire Lucie après 19 h 30 ?", "choices": {"A": "Aller voir un film", "B": "Diner avec Fred", "C": "Passer chez un ami", "D": "Rester au travail"}, "locations": ["test3 Q3"], "correct_answer": "A", "source_location": "test3 Q3"}, {"extracted_text": "<PERSON><PERSON> : 12h—13h : Cours de natation\n\nDjamel : 11h—13h : Révision avec Claire.\n\nDimanche\n\nMarthe : 8h—9h : Course avec Claire\n\nDjamel : 14h—18h : Répétition avec groupe", "question_text": "Que fait <PERSON> le samedi à 12 heures ?", "choices": {"A": "<PERSON> chante", "B": "Elle court", "C": "Elle étudie", "D": "Elle nage"}, "locations": ["test3 Q4"], "correct_answer": "D", "source_location": "test3 Q4"}, {"extracted_text": "France 5\n\n-19 h 55 SÉRIE DOCUMENTAIRE ➔ Bonjour la Bretagne\n-20 H 35 MAGAZINES ➔ La grande libraire\n  Présentation. <PERSON>\n-21 h 35 DOCUMENTAIRES ➔ <PERSON><PERSON>, portrait d’un\n  peintre et d’un sculpteur\n-23 h 55 SÉRIE DOCUMENTAIRE ➔ J’irai dormir chez\n  vous « Maroc »", "question_text": "À quelle heure est-ce qu’on peut voir une émission sur l’art ?", "choices": {"A": "•\tÀ 19 h 55", "B": "•\tÀ 20 h 55", "C": "•\tÀ 21 h 35", "D": "•\tÀ 23 h 55"}, "locations": ["test4 Q2"], "correct_answer": "C", "source_location": "test4 Q2"}, {"extracted_text": "LA MAIRIE VOUS INFORME\n\nPendant les travaux, il est interdit de stationner devant l’entrée du supermarché.\nMerci d’utiliser le parking derrière la poste.", "question_text": "<PERSON><PERSON>-vous laisser votre voiture pendant les travaux ?", "choices": {"A": "Devant la mairie", "B": "Près de l’entrée du supermarché", "C": "<PERSON><PERSON><PERSON> le bâtiment de la poste", "D": "En face du parc"}, "locations": ["test4 Q4"], "correct_answer": "C", "source_location": "test4 Q4"}, {"extracted_text": "Heures d'ouverture du service consulaire :\n\nLe service consulaire est ouvert au public les lundis, mercredis et jeudis de 8 h à 14 h et les mardis et vendredis de 14 h 30 à 18 h.", "question_text": "Quand le service est-il fermé au public ?", "choices": {"A": "Le lundi matin", "B": "Le mardi matin", "C": "Le mercredi matin", "D": "Le jeudi matin"}, "locations": ["test5 Q1", "test23 Q1"], "correct_answer": "B", "source_location": "test5 Q1"}, {"extracted_text": "La Table « votre restaurant d’entreprise »\n\nOUVERT DU LUNDI AU VENDREDI\n\nSERVICE DE 12 H À 14 H\n\nFERMETURE : SAMEDI/DIMANCHE", "question_text": "Que peut-on faire dans ce lieu ?", "choices": {"A": "<PERSON>ire un thé ou un café l’après midi", "B": "Inviter des enfants pour le goûter", "C": "Manger avec des collègues à midi", "D": "Partager un repas entre amis le soir"}, "locations": ["test5 Q2"], "correct_answer": "C", "source_location": "test5 Q2"}, {"extracted_text": "Madame,\n\nSuite à votre courrier du 17 juin dernier, je\nvous envoie le catalogue de nos produits.\n\nTrès cordialement\n\nSOPHIE LELOUX Service clientèle", "question_text": "Pourquoi est-ce que Sophie LELOUX écrit cette lettre ?", "choices": {"A": "Elle cherche une vendeuse", "B": "Elle demande un renseignement", "C": "Elle informe une cliente", "D": "Elle prend un rendez-vous"}, "locations": ["test5 Q3", "test27 Q3", "test30 Q3"], "correct_answer": "C", "source_location": "test5 Q3"}, {"extracted_text": "<PERSON><PERSON>, pense à appeler ta\nsœur pour avoir de ses\nnouvelles", "question_text": "Que doit faire Karim ?", "choices": {"A": "Aller chez sa sœur", "B": "Inviter sa sœur", "C": "Jouer avec sa sœur", "D": "Téléphoner à sa sœur"}, "locations": ["test5 Q4"], "correct_answer": "D", "source_location": "test5 Q4"}, {"extracted_text": "Je suis en consultation\nrevenez dans quelques\nminutes", "question_text": "Que faire pour rencontrer l’infirmière ?", "choices": {"A": "Frapper et entrer", "B": "<PERSON><PERSON><PERSON> rendez-vous", "C": "Revenir plus tard", "D": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "locations": ["test6 Q1", "test12 Q1"], "correct_answer": "C", "source_location": "test6 Q1"}, {"extracted_text": "Du 10 avril au 15 septembre, les\nascenseurs de votre station de métro\nsont en réparation. Pour faciliter vos\ndéplacements, les installations\nchangent. Pendant les travaux, prenez\nl’escalier à droite des guichets", "question_text": "Qu’est-ce qui est fermé du 10 avril au 15 septembre ?", "choices": {"A": "L’escalier", "B": "La station de métro", "C": "Les ascenseurs", "D": "Les guichets"}, "locations": ["test6 Q2", "test10 Q2"], "correct_answer": "C", "source_location": "test6 Q2"}, {"extracted_text": "Consommez cinq fruits et légumes par\njour. Limitez la quantité de viande\nrouge, de beurre de sucre et de sel et\nbuvez au moins huit verres d’eau par\njour ! Vous ferez le plein d’énergie !", "question_text": "Que permet le conseil de « Vie pratique » ?", "choices": {"A": "De faire des économies", "B": "De pratiquer un sport", "C": "De rester en bonne santé", "D": "De trouver des produits bios"}, "locations": ["test6 Q3", "test15 Q3", "test19 Q3"], "correct_answer": "C", "source_location": "test6 Q3"}, {"extracted_text": "Vous souhaitez améliorer votre niveau en langue étrangère ?\nLe Conseil de l’Europe offre à ses salariés des formations dans les langues des pays de l’Union Européenne. Inscrivez-vous dès maintenant pour les cours d’italien et d’allemand", "question_text": "Qu’est-ce que le Conseil de l’Europe propose à ses employés ?", "choices": {"A": "Des cours gratuits", "B": "Des postes à l’étranger", "C": "Des stages en Italie", "D": "Des voyages en Europe"}, "locations": ["test6 Q4", "test16 Q3", "test28 Q3", "test33 Q4"], "correct_answer": "A", "source_location": "test6 Q4"}, {"extracted_text": "Je suis dans le métro l’arrive dans\n20 minutes au bureau.\nCommencez la réunion de travail\nSans moi. <PERSON><PERSON><PERSON>", "question_text": "Pour qui est ce message de Jean ?", "choices": {"A": "Ses amis", "B": "<PERSON><PERSON> coll<PERSON>", "C": "<PERSON><PERSON> enfants", "D": "<PERSON>s voisins"}, "locations": ["test7 Q1"], "correct_answer": "B", "source_location": "test7 Q1"}, {"extracted_text": "Evangélina\n\n• Choux\n• Paris-brest\n• Eclairs au café\n• Tartes aux pommes\n\nSur place ou à emporter. Faites votre choix", "question_text": "Que peut-on acheter à Evangé­lina ?", "choices": {"A": "Des billets de train", "B": "Des légumes", "C": "Des pâtisseries", "D": "Des vêtements"}, "locations": ["test7 Q2", "test24 Q3"], "correct_answer": "C", "source_location": "test7 Q2"}, {"extracted_text": "De : satoltcourriel.fr\nAu : clients\nObjet : Nouvelles coordonnées\n\nMadame, <PERSON>,\nVoici les nouvelles coordonnées de l’entreprise SATO\nMerci d’écrire maintenant au :\n10, rue Lalou\n34110 Frontignan\nEntreprise Sato\nwww.sato.fr\nTel : 01 43 82 67 42", "question_text": "Quelle information a changé ?", "choices": {"A": "L’adresse postale", "B": "Le courrier électronique", "C": "Le numéro de téléphone", "D": "Le site internet"}, "locations": ["test7 Q3"], "correct_answer": "A", "source_location": "test7 Q3"}, {"extracted_text": "<PERSON><PERSON><PERSON>,\n\nLe train de ta collègue\nAllemande arrive à 15 heures.\nTu peux aller la chercher ?\nSMS 12 :14", "question_text": "Où doit aller Taméo ?", "choices": {"A": "La gare", "B": "Au travail", "C": "Chez une amie", "D": "En Allemagne"}, "locations": ["test8 Q1"], "correct_answer": "A", "source_location": "test8 Q1"}, {"extracted_text": "Vous êtes petit, blond, gros ?\nVous avez les cheveux longs ?\nVous voulez faire du cinéma ?\n\nContactez-nous au 05 45 87 86 68", "question_text": "Qu’est-ce que cette annonce propose ?", "choices": {"A": "De changer de coiffure", "B": "De devenir mannequin", "C": "De jouer un film", "D": "<PERSON>’essayer un régime"}, "locations": ["test8 Q2"], "correct_answer": "C", "source_location": "test8 Q2"}, {"extracted_text": "Visite d'un musée des beaux-arts\nvendredi prochain.\n<PERSON><PERSON><PERSON> de donner deux tickets de\nmétro à votre enfant.\n\nLe professeur.", "question_text": "Qu'est-ce que les parents payent pour cette activité ?", "choices": {"A": "L’entrée", "B": "Le guide", "C": "Le <PERSON><PERSON>", "D": "Le transport"}, "locations": ["test8 Q3", "test20 Q2"], "correct_answer": "D", "source_location": "test8 Q3"}, {"extracted_text": "Vend Renault grise 50 000 km.\nClimatiseur automatique airbag.\n4 vitres électriques, autoradio.\nBon état général, 2100 euros.\nContactez Pierre au 06 97 73 61 27.", "question_text": "Que cherche Pierre ?", "choices": {"A": "Un acheteur", "B": "Un conseil", "C": "Un garage", "D": "Une voiture"}, "locations": ["test8 Q4"], "correct_answer": "A", "source_location": "test8 Q4"}, {"extracted_text": "• 500 euros /mois :\nLocation deux pièces\nCuisine, vue sur mer.", "question_text": "Que présente cette annonce ?", "choices": {"A": "Un appartement", "B": "Un restaurant", "C": "Un voyage", "D": "Une voiture"}, "locations": ["test9 Q1", "test33 Q1"], "correct_answer": "A", "source_location": "test9 Q1"}, {"extracted_text": "ÉTUDIANTS INTERNATIONAUX : NE RESTEZ PAS SEULS !\n\nInscrivez-vous dans l’association de l’Université pour :\n• Discuter avec des étudiants français ;\n• Participer à des groupes de conversation ;\n• Faire des activités ensemble : sport, shopping, visites, sorties...\n\nET TOUT EST GRATUIT !", "question_text": "Que propose cette association ?", "choices": {"A": "Des cours particuliers", "B": "Des <PERSON> pendant le Week-end", "C": "Des rencontres entre jeunes", "D": "Des voyages à l’étranger"}, "locations": ["test9 Q2", "test12 Q2"], "correct_answer": "C", "source_location": "test9 Q2"}, {"extracted_text": "<PERSON>\nest arrivée avec un peu d’avance le 14\nmars, pour le plus grand bonheur de sa\nfamille.\nVous pouvez venir lui dire bonjour au\n3, rue des bleuets à Laval.\nChez Agnès et É<PERSON>.", "question_text": "Qu’est-ce que ce message annonce ?", "choices": {"A": "Une naissance", "B": "Une exposition", "C": "Un anniversaire", "D": "Un mariage"}, "locations": ["test9 Q4"], "correct_answer": "A", "source_location": "test9 Q4"}, {"extracted_text": "<PERSON>,\n\n<PERSON><PERSON> a téléphoné. Il\nNe vient pas aujourd’hui\nCar il a la grippe.", "question_text": "Pourquoi est-ce que <PERSON><PERSON> annule son rendez-vous ?", "choices": {"A": "Il a perdu ses papiers", "B": "Il a trop de travail", "C": "Il est très malade", "D": "Il part en vacances"}, "locations": ["test10 Q1"], "correct_answer": "C", "source_location": "test10 Q1"}, {"extracted_text": "AU CLUB DES ENFANTS\nCINÉ-GOÛTER\nSAMEDI 16 HEURES - SALLE DES JOUETS.\n« L’HISTOIRE SANS FIN »\n(DE W. PETERSEN) – 1 H 35", "question_text": "Que propose le club des enfants ce samedi ?", "choices": {"A": "D’écouter une histoire", "B": "De faire des gâteaux", "C": "De jouer aux cartes", "D": "De regarder un film"}, "locations": ["test10 Q3"], "correct_answer": "D", "source_location": "test10 Q3"}, {"extracted_text": "<PERSON>,\n\nnous sommes en réunion sur la publicité du jus de fruit Koj.\nC’est en salle 20.\n\n<PERSON>", "question_text": "Où est Jean ?", "choices": {"A": "Dans un café", "B": "Dans un cinéma", "C": "Dans une boutique", "D": "Dans une entreprise"}, "locations": ["test10 Q4", "test36 Q3"], "correct_answer": "D", "source_location": "test10 Q4"}, {"extracted_text": "De : Société BALY INFORMATIQUE\nÀ : Monsieur <PERSON>\nObjet : Printemps des conférences – Lyon\n\nMonsieur,\nUne voiture de notre société va nous attendre à votre\narrivée à l’aéroport (porte-F) pour vous conduire au Palais\ndes Congrès.\nCordialement,\nM<PERSON>", "question_text": "Comment est-ce que <PERSON><PERSON> va aller au palais des Congrès ?", "choices": {"A": "Avec M<PERSON>or", "B": "Avec un chauffeur", "C": "En autobus", "D": "En taxi"}, "locations": ["test10 Q5", "test23 Q3"], "correct_answer": "B", "source_location": "test10 Q5"}, {"extracted_text": "La bibliothèque ferme ses portes à 19 h 00.\n\n<PERSON><PERSON><PERSON> de quitter la salle de lecture avant 18 h 50", "question_text": "Quelle information est communiquée au public ?", "choices": {"A": "Un événement.", "B": "Un horaire.", "C": "Un plan.", "D": "Une adresse."}, "locations": ["test11 Q1"], "correct_answer": "B", "source_location": "test11 Q1"}, {"extracted_text": "L'association E comme Enfants recherche\n\ndes étudiants pour garder des enfants, le soir et le\n\nweek-end. 01 47 61 19 90 www.e-enfant.com", "question_text": "Que propose l’annonce ?", "choices": {"A": "Un cours.", "B": "Un spectacle.", "C": "Un travail.", "D": "Une visite."}, "locations": ["test11 Q2"], "correct_answer": "C", "source_location": "test11 Q2"}, {"extracted_text": "Tous les étés. Nous allons à la mer avec mon \npère et ma mère. Ma sœur et moi regardons les \ngens qui achètent des glaces sur la plage. Nos \nparents voudraient bien aussi manger des glaces \nmais ils payent déjà cher pour la location et on \nne peut plus dépenser beaucoup.", "question_text": "Que savons-nous sur les parents ?", "choices": {"A": "Ils achètent souvent des sucreries.", "B": "Ils font attention à l’argent.", "C": "Ils aiment préparer des desserts.", "D": "Ils ont une maison de vacances."}, "locations": ["test11 Q4"], "correct_answer": "B", "source_location": "test11 Q4"}, {"extracted_text": "Désolé de te le dire si tard !\nJe ne peux pas venir ce soir.\n<PERSON>", "question_text": "Pourquoi Pierre a-t-il écrit ce message ?", "choices": {"A": "Il annonce son absence.", "B": "Il donne un rendez-vous.", "C": "Il indique son arrivée.", "D": "Il signale son retard."}, "locations": ["test12 Q3", "test13 Q3", "test14 Q3"], "correct_answer": "A", "source_location": "test12 Q3"}, {"extracted_text": "Musique jazz et \nbrésilienne durant \ntout l’été dans le quartier \ndu Musée d’Art Moderne !\n\nChaque jeudi et vendredi de 17 h à \n19 h, profitez d’un été musical sur \nles nombreuses terrasses de notre \nbeau quartier.", "question_text": "Qu’est-ce qu’on peut faire dans ce quartier ?", "choices": {"A": "Apprendre à jouer d’un instrument.", "B": "Assister à des concerts.", "C": "Réserver des billets de spectacle.", "D": "S’inscrire à des animations."}, "locations": ["test12 Q4"], "correct_answer": "B", "source_location": "test12 Q4"}, {"extracted_text": "RENCONTRES INTERCULTURELLES\n\nASSOCIATION DEUX RIVES\n\n➔ ➔ ➔  27 – 28 novembre\n\nFaites connaissance avec des gens du monde entier et découvrez d’autres cultures !\n\nAu programme : musique, repas, expositions...\n\n15 € - 10 € (moins de 25 ans)\nInscrivez-vous avant le 15 novembre\n\nVenez nombreux !", "question_text": "Que propose cette association ?", "choices": {"A": "D’apprendre une nouvelle langue.", "B": "De participer à un concours.", "C": "De prendre des cours de cuisine.", "D": "De rencontrer des étrangers."}, "locations": ["test13 Q1", "test34 Q1"], "correct_answer": "D", "source_location": "test13 Q1"}, {"extracted_text": "<PERSON><PERSON>,\n\n<PERSON> <PERSON><PERSON>attends devant l’entrée\ndu stade rue Victor Hugo.\nFais vite, le match va\ncommencer !\n\nLaura", "question_text": "Pourquoi est-ce que <PERSON> attend <PERSON><PERSON> ?", "choices": {"A": "Pour aller visiter un musée.", "B": "Pour participer à un concours.", "C": "Pour prendre un café ensemble.", "D": "Pour voir un événement sportif."}, "locations": ["test13 Q2", "test37 Q2"], "correct_answer": "D", "source_location": "test13 Q2"}, {"extracted_text": "<PERSON>,\n\nJe ne peux pas te retrouver au concert ce soir, <PERSON><PERSON><PERSON><PERSON>. Mes enfants sont malades et je dois rester avec eux.\nViens déjeuner à la maison dimanche. Je pars bientôt en voyage. J’aimerais te voir avant mon départ.\n\nB<PERSON>,\nDriss", "question_text": "Qu'est-ce <PERSON><PERSON> propose à son amie ?", "choices": {"A": "D’aller à un concert.", "B": "D’organiser un voyage.", "C": "De déje<PERSON>r chez lui.", "D": "De garder ses enfants."}, "locations": ["test13 Q4", "test14 Q4", "test22 Q4", "test26 Q4"], "correct_answer": "C", "source_location": "test13 Q4"}, {"extracted_text": "ATTENTION\n\nTraversée du parc\ninterdite aux voitures,\naux vélomoteurs et aux\nbicyclettes.", "question_text": "Comment peut-on se promener ?", "choices": {"A": "À moto.", "B": "À pied.", "C": "À vélo.", "D": "En taxi."}, "locations": ["test14 Q1"], "correct_answer": "B", "source_location": "test14 Q1"}, {"extracted_text": "Apprenez une langue étrangère sans aller en classe !\n\nL'école Lingua Mobile vous offre plusieurs\nsolutions modernes : vous pouvez suivre\nvos cours par internet, depuis chez vous\nou de votre bureau.", "question_text": "Quelle est la spécialité de Lingua Mobile ?", "choices": {"A": "Ses leçons à distance.", "B": "Ses professeurs expérimentés.", "C": "Ses salles neuves connectées.", "D": "Ses tarifs bon marché."}, "locations": ["test14 Q2", "test34 Q2"], "correct_answer": "A", "source_location": "test14 Q2"}, {"extracted_text": "SERVICE DES VISAS\nOuvert du lundi au vendredi\nDe 8h à 13h", "question_text": "Quand pouvez-vous demander un visa ?", "choices": {"A": "Le jeudi soir.", "B": "Le mardi matin.", "C": "Le mercredi après-midi.", "D": "Le <PERSON>di midi."}, "locations": ["test15 Q1"], "correct_answer": "B", "source_location": "test15 Q1"}, {"extracted_text": "<PERSON>,\n\nEst-ce que tu viens voir avec moi le dernier film de\nDoillon, lundi à 20 h 00 ?\n\nLa place est à quatre euros seulement !\n\nJérémy", "question_text": "Quelle information est-ce que J<PERSON>rémy donne dans ce message ?", "choices": {"A": "La durée du spectacle.", "B": "Le nom du cinéma.", "C": "Le prix du billet.", "D": "Le titre du film."}, "locations": ["test15 Q2"], "correct_answer": "C", "source_location": "test15 Q2"}, {"extracted_text": "Prière de n’emporter ni les draps de bain, ni les objets\nappartenant au bar de la chambre.\n\nNous vous remercions de votre compréhension.\n\nLa direction de l’hôtel", "question_text": "À qui est destinée cette annonce ?", "choices": {"A": "Aux clients de l’hôtel.", "B": "Au service de nettoyage.", "C": "Au directeur de l’hôtel.", "D": "Aux serveurs du bar."}, "locations": ["test15 Q4", "test24 Q4", "test37 Q4"], "correct_answer": "A", "source_location": "test15 Q4"}, {"extracted_text": "Bonjour Anna.\n\nEst-ce que vous pouvez vous\noccuper de <PERSON><PERSON><PERSON> après l’école\nvendredi ? Pour le repas, j’ai\nfait les courses et un gâteau.\nIl doit se coucher avant 21h,\nJe rentrerai vers 23h. <PERSON><PERSON><PERSON>\nbeaucoup !", "question_text": "Que demande Agnès à Anna ?", "choices": {"A": "<PERSON>’aller au supermarché.", "B": "De garder son fils.", "C": "De préparer un dessert.", "D": "<PERSON><PERSON>e rester dormir."}, "locations": ["test16 Q1", "test28 Q1"], "correct_answer": "B", "source_location": "test16 Q1"}, {"extracted_text": "Restaurant\nLa bonne fourchette\n\nFormule légère\nSalade au choix 9€\n\nFormule express\nPlat du jour 12,50€\n\nFormule déjeuner 16€\n(1/2 eau minérale et café compris)\nEntrée + plat du jour\nPlat du jour + dessert\n\nFormule plaisir 20€\nEntrée + plat du jour + dessert", "question_text": "Combien coûte la formule avec boisson ?", "choices": {"A": "9 euros.", "B": "12,50 euros.", "C": "16 euros.", "D": "20 euros."}, "locations": ["test16 Q2", "test21 Q3", "test28 Q2", "test30 Q2"], "correct_answer": "C", "source_location": "test16 Q2"}, {"extracted_text": "La comédienne franco-suisse Pascale Rocard part tourner \nun film à Paris. Actrice et réalisatrice à succès, elle est aussi \npeintre et photographe. Mère de deux enfants, elle vit à \nVerbier en Suisse.", "question_text": "Que va faire Pascale Rocard ?", "choices": {"A": "Avoir un bébé.", "B": "Déménager en France.", "C": "<PERSON>uer au cinéma.", "D": "Organiser une exposition."}, "locations": ["test16 Q4", "test26 Q5", "test28 Q4"], "correct_answer": "C", "source_location": "test16 Q4"}, {"extracted_text": "Salut <PERSON>,\n\nEst-ce que tu es libre samedi ?\nJ’organise une soirée pour mes 25 ans.\nCe sera chez moi à partir de 20 h. Il y\naura de la musique et on pourra danser !\nBastien\n\nPS : Est-ce que tu pourrais apporter un\ngâteau ?", "question_text": "Où est invitée <PERSON> ?", "choices": {"A": "À un cours de pâtisserie.", "B": "À un déjeuner chez un ami.", "C": "À une fête d’anniversaire.", "D": "À une sortie en discothèque."}, "locations": ["test17 Q1", "test39 Q1"], "correct_answer": "C", "source_location": "test17 Q1"}, {"extracted_text": "<PERSON><PERSON><PERSON>,\n\nJe suis bien arrivée à Cannes et il fait très chaud ! <PERSON>ureusement, j’ai pris des vêtements légers. Avec mon ami Tom, nous avons trouvé un hôtel pas cher à côté de la plage. Hier soir, nous sommes allés au théâtre. Aujourd’hui, nous visitons le musée du cinéma.\n\nBisous,\n\nClémence.", "question_text": "Pourquoi est-ce que Clémence écrit à sa mère ?", "choices": {"A": "Pour lui conseiller un film.", "B": "Pour lui demander des nouvelles.", "C": "Pour lui proposer une sortie.", "D": "Pour lui raconter ses vacances."}, "locations": ["test17 Q2"], "correct_answer": "D", "source_location": "test17 Q2"}, {"extracted_text": "Chers <PERSON>,\n\nAttention ! À partir de la semaine prochaine, les cours d’anglais du mardi et du jeudi avec <PERSON><PERSON> ne <PERSON> plus à 10 h 30, mais à 12 h 30, <PERSON><PERSON> vous attendra dans la même salle.", "question_text": "Qu’est-ce qui est annoncé ?", "choices": {"A": "Un changement de lieu.", "B": "Un nouvel horaire.", "C": "Une absence de professeur.", "D": "Une rencontre entre étudiants."}, "locations": ["test17 Q3", "test26 Q3"], "correct_answer": "B", "source_location": "test17 Q3"}, {"extracted_text": "<PERSON><PERSON>,\n\n<PERSON> a télé<PERSON>é à midi. Rappelez-le aujourd’hui.\n\nIl part à 20 heures en Inde pour tout l’été.", "question_text": "Quand est-ce qu’<PERSON>rien doit rappeler <PERSON> ?", "choices": {"A": "Avant ce soir.", "B": "<PERSON><PERSON> se<PERSON>.", "C": "Dans trois mois.", "D": "Pendant le déjeuner."}, "locations": ["test17 Q4"], "correct_answer": "A", "source_location": "test17 Q4"}, {"extracted_text": "La bibliothèque de Chatou vous invite\nà passer un moment parent-enfant\nmercredi prochain.\n\nVenez avec votre enfant écrire une histoire\navec des dessins. À la fin de la journée,\nnous imprimerons ces histoires et vous\npourrez repartir avec un petit livre !", "question_text": "Qu’est-ce que la bibliothèque de Chatou organise ?", "choices": {"A": "La décoration de ses salles.", "B": "La lecture d’un roman jeunesse.", "C": "Une activité à faire en famille.", "D": "Une rencontre avec un auteur."}, "locations": ["test18 Q1"], "correct_answer": "C", "source_location": "test18 Q1"}, {"extracted_text": "La société des transports T2C offre plusieurs moyens de transport publics : trente-quatre stations de tramway, de train et de métro en journée, 23 lignes de bus, dont une de nuit.\nPlus de renseignements sur notre site www.t2c.fr !", "question_text": "Quel transport circule après 23h ?", "choices": {"A": "Le bus.", "B": "Le métro.", "C": "Le train.", "D": "Le tramway."}, "locations": ["test18 Q2", "test20 Q1", "test39 Q2"], "correct_answer": "A", "source_location": "test18 Q2"}, {"extracted_text": "<PERSON><PERSON>,\n\nJe t’annonce une bonne nouvelle : j’ai trouvé un poste de vendeur pour cet été ! \nC’est dans une boutique du centre-ville de Bordeaux. C’est un peu loin de la maison \nmais en voiture j’y serai en 20 minutes. \nOn fêtera ça samedi !\nSimon", "question_text": "Qu’est-ce que Simon va faire ?", "choices": {"A": "Acheter une voiture.", "B": "Aller chez <PERSON>.", "C": "Faire des courses samedi.", "D": "Travailler à Bordeaux."}, "locations": ["test18 Q3", "test39 Q3"], "correct_answer": "D", "source_location": "test18 Q3"}, {"extracted_text": "IMAGES\n\nVous êtes passionné de photographie et vous\nvoulez partager vos créations ?\nInscrivez-vous au prix du « Petit Reporter » :\nenvoyez une photo sur le thème de l’amitié au\nclub « Images » ! Un appareil photo à gagner !\n\nInscription :\nwww.clubImages.fr/PrixPetitReporter", "question_text": "Que propose le club « images » ?", "choices": {"A": "<PERSON>’acheter du matériel en soldes.", "B": "De participer à un concours.", "C": "De rencontre des professionnels.", "D": "De suivre des cours débutants."}, "locations": ["test18 Q4", "test36 Q5", "test39 Q4"], "correct_answer": "B", "source_location": "test18 Q4"}, {"extracted_text": "Perdu chat gris avec un collier jaune.\n\nAppelez Stéphanie       06 42 58 96 31", "question_text": "Qu’est-ce que St<PERSON>phanie cherche ?", "choices": {"A": "Un animal.", "B": "Un bijou.", "C": "Un sac.", "D": "Un téléphone."}, "locations": ["test19 Q1"], "correct_answer": "A", "source_location": "test19 Q1"}, {"extracted_text": "PIÈCE DE THÉÂTRE\n\nLe Rhinocéros\n\n19h 00, salle des fêtes de Versailles", "question_text": "Que présente l'affiche ?", "choices": {"A": "Un cours.", "B": "Une fête.", "C": "Un match.", "D": "Un spectacle."}, "locations": ["test19 Q2"], "correct_answer": "D", "source_location": "test19 Q2"}, {"extracted_text": "Bon<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>,\n\nC’est mon anniversaire samedi et j’ai encore\nbeaucoup de choses à faire.\n\nTu pourrais m’aider s’il te plaît ? Il faut décorer la\nsalle du restaurant et je sais que tu as toujours de\nbonnes idées !\n\nEn plus, c’est à cinq minutes de ton appartement.\n\nMerci d’avance.\nEmma", "question_text": "Qu’est-ce qu’Emma demande à Jean-Eudes ?", "choices": {"A": "De choisir une salle pour la fête.", "B": "De <PERSON>’occuper de la décoration.", "C": "De trouver un bon restaurant.", "D": "D’organiser son anniversaire chez lui."}, "locations": ["test19 Q4"], "correct_answer": "B", "source_location": "test19 Q4"}, {"extracted_text": "● À louer : logement très lumineux de trois \npièces avec une cuisine équipée, parfait pour \ndes parents avec enfants. Situé au deuxième \nétage d’un bel immeuble avec ascenseur, dans \nun quartier calme, proche des commerces. 10 \nminutes à pied de l’université.", "question_text": "Qu’est-ce qu’on peut louer ?", "choices": {"A": "Un appartement familial.", "B": "Un logement étudiant.", "C": "Une chambre en colocation.", "D": "Une maison de campagne."}, "locations": ["test20 Q3"], "correct_answer": "A", "source_location": "test20 Q3"}, {"extracted_text": "Collège S. Veil – Concert Chant et Musique pour tous\n\nLe 20 mai à 20h00, venez écouter vos enfants et leurs professeurs de musique chanter et jouer de leurs instruments.\n\nEntrée : minimum 5€.\n\nL’argent des entrées financera une visite de l’opéra pour les classes du collège.\n\nVenez nombreux.", "question_text": "Qui est invité ?", "choices": {"A": "Les artistes.", "B": "Les élèves.", "C": "Les enseignants.", "D": "Les familles."}, "locations": ["test20 Q4", "test36 Q4"], "correct_answer": "D", "source_location": "test20 Q4"}, {"extracted_text": "Chers parents,\n\nLes classes de notre école passeront la journée de \nvendredi à la mer. Le pique-nique de midi \n(sandwichs et boisson) sera fourni par la cantine. Il \nva faire très chaud, alors pensez aux casquettes, \nles manteaux et les pulls sont inutiles.\n\nLa directrice", "question_text": "Qu’est-ce que les parents doivent donner aux enfants ?", "choices": {"A": "<PERSON><PERSON><PERSON> chose à boire.", "B": "<PERSON><PERSON><PERSON> chose à manger.", "C": "<PERSON><PERSON><PERSON> chose pour la pluie.", "D": "<PERSON><PERSON><PERSON> chose pour le soleil."}, "locations": ["test21 Q2"], "correct_answer": "D", "source_location": "test21 Q2"}, {"extracted_text": "Vous souhaitez repeindre l’intérieur de votre appartement ?\n\nSuivez nos conseils pour réussir et avoir un résultat professionnel. Sur Créadéco.com, nous vous accompagnons aussi pour le choix de la couleur de la peinture de votre cuisine ou votre salon.", "question_text": "Qu'est-ce que ce site internet propose ?", "choices": {"A": "Des adresses d’artisans.", "B": "Des idées de décoration.", "C": "Des leçons de bricolage.", "D": "Des modèles de meubles."}, "locations": ["test21 Q4"], "correct_answer": "B", "source_location": "test21 Q4"}, {"extracted_text": "L’Association des étudiants de l’École des sciences et techniques\n\nVOUS INVITE A LA SOIRÉE DE FIN D’ANNÉE.\n\nSamedi 15 décembre à partir de 20h.\n\nVENEZ NOMBREUX !", "question_text": "Qu’est-ce que les étudiants organisent ?", "choices": {"A": "Un anniversaire.", "B": "Un concert.", "C": "Une exposition.", "D": "Une fête."}, "locations": ["test22 Q1"], "correct_answer": "D", "source_location": "test22 Q1"}, {"extracted_text": "Logement, études ou téléphone portable, tout coûte cher pour les jeunes à l’université. Alors 80% des parents aident leurs enfants. Le père d’Hamza, 18 ans, calcule combien ça lui coûtera : environ 8 % de son salaire.", "question_text": "Qu’est-ce que cet article dit sur les jeunes ?", "choices": {"A": "Ils ont besoin de l’aide de leur famille.", "B": "Ils travaillent pour payer leur logement.", "C": "Ils veulent finir leurs études rapidement.", "D": "Ils vont à l’université dans une autre ville."}, "locations": ["test22 Q2", "test26 Q2"], "correct_answer": "A", "source_location": "test22 Q2"}, {"extracted_text": "VOUS AUSSI, VOUS SOUHAITEZ DONNER DE\nVOTRE TEMPS À DES HOMMES ET DES\nFEMMES DANS LE BESOIN ?\n\nL’association « Les Restos du Cœur » de Nice vous\npropose de participer à la préparation des\ndéjeuners du lundi au samedi, le matin.\n\nAppelez-nous au 08 44 25 68 79 87", "question_text": "Qu’est-ce que cette annonce propose ?", "choices": {"A": "D’aider à faire des repas.", "B": "De caner de profession.", "C": "De servir dans une cantine.", "D": "De soigner des habitants."}, "locations": ["test22 Q3"], "correct_answer": "A", "source_location": "test22 Q3"}, {"extracted_text": "Urgent ! cherche maison à louer pour\nvacances d'été dans le centre-ville.\n\nLaetitia : 07 08 09 80 90", "question_text": "Qu’est-ce que Laetitia veut faire ?", "choices": {"A": "Habiter à la compagne.", "B": "Réserver un hôtel.", "C": "Trouver un logement.", "D": "Vendre un appartement."}, "locations": ["test23 Q2"], "correct_answer": "C", "source_location": "test23 Q2"}, {"extracted_text": "« DRÔLE ET TRÈS BEAU ! »\n\n« DES ACTEURS MAGNIFIQUES ! »\n\n« À VOIR ABSOLUMENT ! »", "question_text": "De quoi parle la critique ?", "choices": {"A": "D’un concert.", "B": "D’un film.", "C": "D’un livre.", "D": "D’un tableau."}, "locations": ["test24 Q2"], "correct_answer": "B", "source_location": "test24 Q2"}, {"extracted_text": "Bonjour Madame <PERSON>,\n\nJe vous confirme notre rendez-vous de demain. Je viendrai vous chercher à l'aéroport.\nNous irons ensuite manger au restaurant.\n<PERSON><PERSON>, nous rencontrerons les responsables de l'entreprise à 14h pour signer le contrat.\n\n<PERSON> demain,\n\nMadame <PERSON>", "question_text": "Pourquoi est-ce que Madame Dire écrit à Madame Grange ?", "choices": {"A": "Pour lui annoncer un projet.", "B": "Pour lui présenter un nouveau produit.", "C": "Pour organiser leur journ<PERSON> de travail.", "D": "Pour préparer son voyage."}, "locations": ["test25 Q1", "test27 Q9", "test30 Q9"], "correct_answer": "C", "source_location": "test25 Q1"}, {"extracted_text": "Production franco-canadienne.\nComédie.\n\nDurée : 1 h 56 min.\nAnnée de production : 2004. Sortie : mercredi 6 juillet.\nPublic : à partir de 8 ans. Résumé : Que peut-on offrir à \nun ami qui a tout ? Rien ! C'est l'histoire d'une amitié \nextraordinaire entre deux garçons : l'un, très pauvre, et \nl'autre, très riche. Une amitié racontée avec tendresse, \nhumour, et un brin de philosophie.", "question_text": "Que décrit-on dans ce document ?", "choices": {"A": "Un concert.", "B": "Une exposition.", "C": "Un film.", "D": "Un livre."}, "locations": ["test25 Q3"], "correct_answer": "C", "source_location": "test25 Q3"}, {"extracted_text": "La recette de mamie !\n\nSur la pâte, mettre des pommes coupées en morceaux avec du sucre\nAjouter un peu de beurre\nFaire cuire au four 30 minutes\nLa tarte est prête !", "question_text": "Qu’est-ce qu’on fait avec cette recette ?", "choices": {"A": "Un dessert.", "B": "Une entrée.", "C": "Une salade.", "D": "Une soupe."}, "locations": ["test26 Q1"], "correct_answer": "A", "source_location": "test26 Q1"}, {"extracted_text": "<PERSON><PERSON>,\n\n<PERSON>a y est ! J'ai commencé mon tour du monde ! Je viens d'arriver à Montréal, il fait très beau et l'hôtel est génial ! Je pars aux États-Unis jeudi ! J'espère que tout va bien au bureau.\n\nBises.\n\nAntoine", "question_text": "De quoi parle ce message ?", "choices": {"A": "D’un déménagement.", "B": "D’un grand voyage.", "C": "D’une réunion de travail.", "D": "D’une sortie culturelle."}, "locations": ["test27 Q1"], "correct_answer": "B", "source_location": "test27 Q1"}, {"extracted_text": "Le musée cherche un guide. Vous aimez la\npeinture, la sculpture et vous êtes disponible\nle week-end entre 14h et 20h. Pour plus\nd'informations, allez sur notre site Internet.\nSi vous êtes intéressé(e), envoyez votre CV à\n<EMAIL>.", "question_text": "Qu'est-ce que cette annonce propose ?", "choices": {"A": "Des informations culturelles.", "B": "Des visites en ligne gratuites.", "C": "Un cours d’initiation à l’art.", "D": "Un travail à temps partiel."}, "locations": ["test27 Q2"], "correct_answer": "D", "source_location": "test27 Q2"}, {"extracted_text": "PARTICIPEZ À NOTRE\nGRAND JEU !\n\nREM<PERSON>ISSEZ UN FORMULAIRE À\nL'ACCUEIL DU MAGASIN. 10 CLIENTS\nSERONT SÉLECTIONNÉS ILS\nGAGNERONT UN SUPERRE CADEAU\nPOUR LEUR CUISINE !\nLISTE DES CADEAUX DISPONIBLE\nSUR WWW.GRANDJEU.FR", "question_text": "Que faut-il faire pour participer à ce jeu ?", "choices": {"A": "Compléter une fiche.", "B": "Proposer une recette.", "C": "Répondre à une question.", "D": "S’inscrire sur Internet."}, "locations": ["test27 Q4"], "correct_answer": "A", "source_location": "test27 Q4"}, {"extracted_text": "Madame <PERSON> est malade aujourd'hui.\n\nSon cours est reporté à jeudi\n(même salle, même heure).", "question_text": "De quel changement informe ce message ?", "choices": {"A": "Un changement de jour.", "B": "Un changement de professeur.", "C": "Un changement de salle.", "D": "Un changement d’heure."}, "locations": ["test29 Q1"], "correct_answer": "A", "source_location": "test29 Q1"}, {"extracted_text": "<PERSON><PERSON> déje<PERSON>r\n\n16 euros Entrée du jour ou charcuterie.\nPlat du jour ou steak frites fromage ou dessert\n\n20 euros avec un verre de vin et un café.", "question_text": "Qu'avez-vous avec le menu à 16 euros ?", "choices": {"A": "Un plat, un dessert et un café.", "B": "Une entrée, un plat et une boisson.", "C": "Une entrée, un plat et un fromage.", "D": "Un plat, un fromage et une boisson."}, "locations": ["test29 Q2"], "correct_answer": "C", "source_location": "test29 Q2"}, {"extracted_text": "Initiation à l'informatique\n\n25, 26 et 27 mars de 9h à 14h\n\nSalle de réunion.", "question_text": "Pendant cette formation, qu'est-ce qu'on va apprendre à utiliser ?", "choices": {"A": "Un photocopieur.", "B": "Un climatiseur.", "C": "Un ordinateur.", "D": "Un télécopieur."}, "locations": ["test29 Q3"], "correct_answer": "C", "source_location": "test29 Q3"}, {"extracted_text": "<PERSON>, <PERSON>,\n\nVous êtes convoqués à l'école avec votre\nfils, <PERSON>, inscrit en 6°B, le jeudi 13\njanvier à 10 h, bureau 8.\n\n<PERSON><PERSON><PERSON> de vous présenter au secrétariat du\ndirecteur adjoint.", "question_text": "Qui est <PERSON> ?", "choices": {"A": "Un secrétaire.", "B": "Un parent.", "C": "Un élève.", "D": "Un professeur."}, "locations": ["test29 Q4", "test32 Q3"], "correct_answer": "C", "source_location": "test29 Q4"}, {"extracted_text": "<PERSON><PERSON>,\n\n<PERSON><PERSON><PERSON><PERSON>-tu m'emmener à la fête de <PERSON> dem<PERSON> soir ? Ma voiture est en panne et c'est moi qui ai son cadeau d'anniversaire.\n\nÀ demain\n\nElsa,", "question_text": "Que doit faire Alex ?", "choices": {"A": "Acheter un cadeau.", "B": "<PERSON><PERSON>er une amie.", "C": "Organiser une fête.", "D": "<PERSON><PERSON><PERSON><PERSON> une voiture."}, "locations": ["test29 Q5", "test32 Q4"], "correct_answer": "B", "source_location": "test29 Q5"}, {"extracted_text": "Pour l'été, voici quelques gestes simples s'il fait très chaud:\n\n- boire un litre et demi d'eau par jour;\n- limiter les efforts physiques ;\n- manger des fruits et des légumes ;\n- garder sa maison au frais.", "question_text": "Quel est le but de cette annonce ?", "choices": {"A": "Donner des conseils.", "B": "Informer sur la météo.", "C": "Présenter des activités.", "D": "Proposer un menu."}, "locations": ["test31 Q1"], "correct_answer": "A", "source_location": "test31 Q1"}, {"extracted_text": "Chers parents,\n\nLes inscriptions à l'école et au centre de loisirs\npour la rentrée prochaine sont ouvertes.\nRemplissez le dossier sur le site de la ville\nwww.ville-de-vernon.fr ou retirez-le à la mairie et\nrenvoyez-le avec toutes les informations avant\nle 17 mars.", "question_text": "Qu'est-ce que les parents doivent faire ?", "choices": {"A": "Compléter des documents.", "B": "Demander un programme.", "C": "Prendre un rendez-vous.", "D": "Visiter un établissement."}, "locations": ["test31 Q2"], "correct_answer": "A", "source_location": "test31 Q2"}, {"extracted_text": "À Chamonix, à 10 minutes à pied des pistes de ski, grand 3 pièces de 65 m²,\ntout équipé, confortable et calme.\nProche de nombreux commerces et d'une garderie. 620 euros la semaine en\nété, 1050 euros en hiver.", "question_text": "Qu'est-ce que cette annonce propose?", "choices": {"A": "Des ateliers pour enfants.", "B": "Du matériel de sport.", "C": "Un emploi à la montagne.", "D": "Une location de vacances."}, "locations": ["test31 Q3"], "correct_answer": "D", "source_location": "test31 Q3"}, {"extracted_text": "L'Association « Bon appétit ! » vous propose\nde mieux manger. Elle vient chez vous avec\ndes bons petits plats tous les jours de la\nsemaine. Tous nos menus sont préparés\navec des légumes de saison ! Choisissez\nvotre menu sur notre site :\nwww.bonappetit.fr !", "question_text": "Qu'est-ce que cette association propose?", "choices": {"A": "Des achats de produits frais.", "B": "Des conseils pour maigrir.", "C": "Des recettes de cuisine.", "D": "Des repas livrés à domicile."}, "locations": ["test31 Q4"], "correct_answer": "D", "source_location": "test31 Q4"}, {"extracted_text": "Vous partez en vacances et vous\ncherchez quelqu'un pour s'occuper de\nvotre chien ? Je suis la personne idéale\n! Je suis étudiant et j'habite dans une\nmaison avec un jardin. Je serai\nheureux de vous rencontrer pour en\nparler, prenez contact avec moi :\n<EMAIL>", "question_text": "Qu'est-ce que cette personne propose ?", "choices": {"A": "Une aide aux devoirs", "B": "Une chambre à louer", "C": "Une garde d’animaux", "D": "Une visite touristique"}, "locations": ["test32 Q1"], "correct_answer": "C", "source_location": "test32 Q1"}, {"extracted_text": "<PERSON><PERSON><PERSON>,\n\n<PERSON> mère et moi rentrons tard ce soir. Ton\nrepas est dans le réfrigérateur, je t'ai fait\ndu poulet avec des légumes. Avant de te\ncoucher, n'oublie pas de sortir le chien et\nde lui donner à manger !\n\n<PERSON><PERSON> soir<PERSON>,\n\n<PERSON>", "question_text": "Qu'est-ce que C<PERSON>dric doit faire ?", "choices": {"A": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "B": "Rentrer pour le diner", "C": "Se <PERSON>er tôt", "D": "<PERSON>’occuper du chien"}, "locations": ["test32 Q2"], "correct_answer": "D", "source_location": "test32 Q2"}, {"extracted_text": "<PERSON>,\n\n<PERSON><PERSON>ez l'agence de voyage et prenez une place pour le directeur sur le vol \nParis-Marseille de demain, SVP.\n\nLe n° de l'agence : 01 22 35 53 46\n\nMerci.\n\n<PERSON><PERSON>", "question_text": "Qu'est-ce que Marie doit faire ?", "choices": {"A": "Acheter un billet d’avion.", "B": "<PERSON><PERSON> à Marseille.", "C": "Prendre un rendez-vous.", "D": "Téléphoner à un collègue."}, "locations": ["test33 Q2"], "correct_answer": "A", "source_location": "test33 Q2"}, {"extracted_text": "VENEZ APPRENDRE LE FRANÇAIS À PARIS !\n\nClaude, votre professeur, vous accueille dans son\nconfortable appartement à Montmartre. Au programme :\ndes leçons de français, de bons repas et des activités pour\nmieux connaître la culture française.", "question_text": "Qu'est-ce que cette annonce propose ?", "choices": {"A": "Un cours de langue.", "B": "Un logement à louer.", "C": "Une sortie au restaurant.", "D": "Une visite touristique."}, "locations": ["test33 Q3"], "correct_answer": "A", "source_location": "test33 Q3"}, {"extracted_text": "Nouveau message\n\nMonsieur,\n\nLe docteur <PERSON> vous attend jeudi à 9 h. Merc<PERSON> d'arriver 15 minutes avant avec la liste des médicaments que vous prenez. Si vous ne pouvez pas venir, merci de prendre contact avec nous.\n\nLe secrétariat médical.", "question_text": "Quel est le but de ce message ?", "choices": {"A": "<PERSON><PERSON><PERSON>.", "B": "Prendre des nouvelles.", "C": "Présenter des produits.", "D": "<PERSON><PERSON>er un rendez-vous."}, "locations": ["test34 Q3"], "correct_answer": "D", "source_location": "test34 Q3"}, {"extracted_text": "<PERSON>,\n\n<PERSON> es jolie, intelligente, merveilleuse.\nJe t'aime ! <PERSON><PERSON>-nous !\n\n<PERSON><PERSON>", "question_text": "Pourquoi Robert écrit à Sophie ?", "choices": {"A": "Pour lui demander d’être sa femme", "B": "Pour lui donner du courage", "C": "Pour lui offrir un emploi", "D": "Pour lui proposer un voyage"}, "locations": ["test35 Q1"], "correct_answer": "A", "source_location": "test35 Q1"}, {"extracted_text": "Salut tout le monde,\n\n<PERSON><PERSON> est né. Le bébé et la maman vont bien. Ils vous attendent dans la chambre 335 au 3e étage.\n\nGabin", "question_text": "Où est Camilo ?", "choices": {"A": "À la maison", "B": "À l’hôpital", "C": "À l’hôtel", "D": "Au bureau"}, "locations": ["test35 Q2"], "correct_answer": "B", "source_location": "test35 Q2"}, {"extracted_text": "Ne jetez pas vos vieux jouets, livres, ou vêtements\nd'enfants!\n\nL'association « Un Noël pour tous » les offre en\ncadeau à des enfants pour les fêtes de fin d'année.\nVous pouvez les apporter tous les mercredis matin\nà la mairie.", "question_text": "Qu'est-ce que cette association propose ?", "choices": {"A": "D’échanger des livres", "B": "De donner des cadeaux", "C": "<PERSON> ré<PERSON> des jouets", "D": "De vendre des vêtements"}, "locations": ["test35 Q3"], "correct_answer": "B", "source_location": "test35 Q3"}, {"extracted_text": "Vous n'avez pas pu voyager pendant la grève ? Vous pouvez\ndemander le remboursement de votre billet de train sur notre\nsite Internet à l'adresse : «www.montrain.fr». Vous recevrez\nun virement sur votre compte en banque. Pour un\nremboursement en espèces, allez à un guichet dans une gare. Il\nn'y aura pas de remboursement par chèques ou par carte\nbancaire.", "question_text": "Qu’est-ce que cette annonce explique ?", "choices": {"A": "Comment envoyer un don aux conducteurs", "B": "Comment faire un paiement en ligne.", "C": "Comment modifier un forfait de transport", "D": "Comment récupérer de l’argent"}, "locations": ["test35 Q4"], "correct_answer": "D", "source_location": "test35 Q4"}, {"extracted_text": "Bonjour madame <PERSON>, <PERSON><PERSON>, nous n'avons pas reçu le certificat médical et la photocopie de votre carte d'identité pour compléter votre dossier d'inscription au club de judo.\n\nMerci de nous les envoyer rapidement, par courrier ou par mail.\n\nLa secrétaire", "question_text": "Qu'est-ce que madame <PERSON> doit faire ?", "choices": {"A": "<PERSON><PERSON><PERSON> la secrétaire.", "B": "Consulter un médecin.", "C": "Envoyer des documents.", "D": "Payer les cours de judo."}, "locations": ["test36 Q1"], "correct_answer": "C", "source_location": "test36 Q1"}, {"extracted_text": "Message reçu\n\nJ'ai faim. J'achète une pizza ou tu\nprépares quelque chose?", "question_text": "Que veut la personne?", "choices": {"A": "<PERSON><PERSON>.", "B": "<PERSON><PERSON><PERSON><PERSON>.", "C": "<PERSON><PERSON>.", "D": "Sortir."}, "locations": ["test36 Q2"], "correct_answer": "C", "source_location": "test36 Q2"}, {"extracted_text": "Vous êtes étudiant, vous cherchez un logement, et\nvous aimez aidez les autres ? Pourquoi ne pas\nvivre chez une personne âgée pendant vos études\n? Plus d'informations sur jeunes.gouv.fr.", "question_text": "Que propose le site jeunes.gouv.fr ?", "choices": {"A": "D’acheter un appartement.", "B": "D’habiter avec quelqu’un.", "C": "De rencontrer ses voisins.", "D": "De visiter son quartier."}, "locations": ["test37 Q1"], "correct_answer": "B", "source_location": "test37 Q1"}, {"extracted_text": "<PERSON> n’oublie pas d’aller chercher les enfants à l’école !\n\nJe suis en rendez-vous Jusqu’à 17h\n\nBisous  \nClaudia", "question_text": "Que doit faire Jean ?", "choices": {"A": "Aller à une réunion.", "B": "Déjeuner avec Claudia.", "C": "S’occuper des enfants.", "D": "Téléphoner à l’école."}, "locations": ["test37 Q3"], "correct_answer": "C", "source_location": "test37 Q3"}, {"extracted_text": "<PERSON>,\n\n<PERSON> veux passer à la maison ce\nsoir pour boire un verre?\nOn peut aussi dîner ensemble\net regarder un film\nBisous, <PERSON><PERSON><PERSON><PERSON>", "question_text": "O<PERSON> est-elle invitée ?", "choices": {"A": "Au cinéma.", "B": "Au restaurant.", "C": "Chez son ami.", "D": "Dans un bar."}, "locations": ["test38 Q1"], "correct_answer": "C", "source_location": "test38 Q1"}, {"extracted_text": "De: <EMAIL>\nÀ: <EMAIL>\nObjet: message urgent\n\nRappelez monsieur <PERSON> au\n01 47 38 91 88", "question_text": "Que doit faire la destinataire de ce message?", "choices": {"A": "Téléphoner au numéro donné.", "B": "Écrire un courriel de réponse.", "C": "Écouter le répondeur.", "D": "Contacter le standard."}, "locations": ["test38 Q2"], "correct_answer": "A", "source_location": "test38 Q2"}, {"extracted_text": "Cours de cuisine au\nrestaurant Chez nous !\nOuverts aux petits et\ngrands Pour réserver,\nappelez le 04 25 69 35\n14. <PERSON><PERSON><PERSON>vous tous\nles mercredis de 16h30\nà 18h, au 25 rue de la\nGrange, Bagneux.", "question_text": "Comment est-ce qu'on peut s'inscrire à cette activité?", "choices": {"A": "<PERSON>r courrier.", "B": "Par téléphone.", "C": "Sur Internet.", "D": "Sur place."}, "locations": ["test38 Q3"], "correct_answer": "B", "source_location": "test38 Q3"}, {"extracted_text": "Des élèves français ont envoyé un message\nsur internet afin de recevoir des cartes\npostales du monde entier. Ils ont reçu 8000\nréponses. L'instituteur explique : « En\nclasse, les élèves cherchent d'où viennent\nles cartes et ils découvrent des capitales et\ndes pays. »", "question_text": "Qu'est-ce que ces élèves font avec les cartes postales?", "choices": {"A": "Ils apprennent la géographie.", "B": "Ils invitent des amis à l’école.", "C": "Ils pratiquent des langues étrangères.", "D": "Ils préparent un voyage de classe."}, "locations": ["test38 Q4"], "correct_answer": "A", "source_location": "test38 Q4"}, {"extracted_text": "\"Sur le site Internet www.medico.fr, vous\npouvez prendre rendez-vous rapidement\navec un professionnel de santé.\nVous pouvez aussi télécharger\ngratuitement l'application sur votre\ntéléphone et avoir une consultation vidéo\nde chez vous.\"", "question_text": "Qu'est-ce qu'on peut faire sur ce site Internet ?", "choices": {"A": "Commander des médicaments.", "B": "Demander un remboursement.", "C": "Lire des résultats d’analyse.", "D": "Voir un docteur à distance."}, "locations": ["test40 Q2"], "correct_answer": "D", "source_location": "test40 Q2"}, {"extracted_text": "<PERSON><PERSON><PERSON>,\n\n<PERSON>eux-tu passer à la boulangerie et prendre du pain pour le dîner ?\nBises.\nMichèle.", "question_text": "Qu'est-ce que Michèle demande à Thierry ?", "choices": {"A": "De faire un achat.", "B": "De préparer un repas.", "C": "De réserver un restaurant.", "D": "De trouver une recette."}, "locations": ["test40 Q3"], "correct_answer": "A", "source_location": "test40 Q3"}, {"extracted_text": "Vous souhaitez participer au festival des\narts de la rue « Tours dans la rue » ?\nNous recherchons des personnes\nbénévoles pour s'occuper de\nl'organisation des concerts et spectacles\ndu 31 mai au 2 juin.\nRenseignez-vous à la mairie de Tours.\nfestival-arts-rue@mairie_tours.com", "question_text": "Qu'est-ce qu'on recherche ?", "choices": {"A": "De l’aide pour un événement.", "B": "De l’argent pour un concours.", "C": "Des artistes pour monter sur scène.", "D": "Des guides pour des visites de la ville."}, "locations": ["test40 Q4"], "correct_answer": "A", "source_location": "test40 Q4"}]