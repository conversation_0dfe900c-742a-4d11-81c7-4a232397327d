#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply answer modifications from modification logs
to the correct answer JSON files for both reading and listening tests.

This script reads modification logs and updates the corresponding answers
in both free and paid version answer files.

Usage:
    python apply_modifications.py --type listening
    python apply_modifications.py --type reading
    python apply_modifications.py --type both
"""

import json
import os
import argparse
from datetime import datetime

def load_json_file(filepath):
    """Load JSON file and return the data."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: File {filepath} not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in {filepath}: {e}")
        return None

def save_json_file(filepath, data):
    """Save data to JSON file with proper formatting."""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"Error saving {filepath}: {e}")
        return False

def create_backup(filepath):
    """Create a backup of the original file."""
    backup_path = f"{filepath}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        with open(filepath, 'r', encoding='utf-8') as original:
            with open(backup_path, 'w', encoding='utf-8') as backup:
                backup.write(original.read())
        print(f"Backup created: {backup_path}")
        return True
    except Exception as e:
        print(f"Error creating backup: {e}")
        return False

def get_file_paths(test_type):
    """Get file paths for the specified test type."""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    paths = {
        'log_file': os.path.join(script_dir, f'modified_answer_{test_type}_log.json'),
        'paid_answer_file': os.path.join(script_dir, f'{test_type}_correct_answer.json'),
        'free_answer_file': os.path.join(script_dir, f'{test_type}_correct_answer_free.json')
    }
    
    return paths

def apply_modifications_for_type(test_type):
    """Apply modifications for a specific test type (reading or listening)."""
    
    print(f"\n{'='*50}")
    print(f"PROCESSING {test_type.upper()} MODIFICATIONS")
    print(f"{'='*50}")
    
    # Get file paths
    paths = get_file_paths(test_type)
    log_file = paths['log_file']
    paid_answer_file = paths['paid_answer_file']
    free_answer_file = paths['free_answer_file']
    
    print(f"Log file: {log_file}")
    print(f"Paid answer file: {paid_answer_file}")
    print(f"Free answer file: {free_answer_file}")
    print()
    
    # Load modification log
    print("Loading modification log...")
    log_data = load_json_file(log_file)
    if not log_data:
        return False
    
    # Load current answers for both versions
    print("Loading current answers...")
    paid_answer_data = load_json_file(paid_answer_file)
    free_answer_data = load_json_file(free_answer_file)
    
    if not paid_answer_data:
        print(f"❌ Could not load paid version answers")
        return False
    
    # Free version might not exist for all test types
    if not free_answer_data:
        print(f"⚠️  Free version answers not found - will only process paid version")
    
    # Create backups
    print("Creating backups...")
    if not create_backup(paid_answer_file):
        print("Warning: Could not create backup for paid version. Continue? (y/n): ", end="")
        if input().lower() != 'y':
            return False
    
    if free_answer_data and not create_backup(free_answer_file):
        print("Warning: Could not create backup for free version. Continue? (y/n): ", end="")
        if input().lower() != 'y':
            return False
    
    # Separate modifications by version type
    paid_modifications = []
    free_modifications = []
    
    for mod in log_data['modifications']:
        if mod.get('version_type') == 'paid':
            paid_modifications.append(mod)
        elif mod.get('version_type') == 'free':
            free_modifications.append(mod)
        else:
            # For listening logs that don't have version_type, assume paid
            paid_modifications.append(mod)
    
    print(f"\nFound {len(paid_modifications)} paid modifications and {len(free_modifications)} free modifications")
    
    # Apply paid modifications
    print(f"\n🔄 Applying PAID version modifications...")
    paid_results = apply_modifications_to_file(paid_modifications, paid_answer_data, "PAID")
    
    # Apply free modifications
    free_results = {'applied': 0, 'skipped': 0, 'errors': []}
    if free_answer_data and free_modifications:
        print(f"\n🔄 Applying FREE version modifications...")
        free_results = apply_modifications_to_file(free_modifications, free_answer_data, "FREE")
    elif free_modifications:
        print(f"\n⚠️  Skipping {len(free_modifications)} free modifications (no free answer file)")
    
    # Save updated answers
    print(f"\nSaving updated answers...")
    
    if save_json_file(paid_answer_file, paid_answer_data):
        print(f"✅ Successfully saved updated PAID answers to {paid_answer_file}")
    else:
        print(f"❌ Failed to save PAID answers")
        return False
    
    if free_answer_data:
        if save_json_file(free_answer_file, free_answer_data):
            print(f"✅ Successfully saved updated FREE answers to {free_answer_file}")
        else:
            print(f"❌ Failed to save FREE answers")
            return False
    
    # Summary
    print(f"\n{'='*30} {test_type.upper()} SUMMARY {'='*30}")
    print(f"PAID VERSION:")
    print(f"  Modifications applied: {paid_results['applied']}")
    print(f"  Modifications skipped: {paid_results['skipped']}")
    print(f"  Errors encountered: {len(paid_results['errors'])}")
    
    print(f"FREE VERSION:")
    print(f"  Modifications applied: {free_results['applied']}")
    print(f"  Modifications skipped: {free_results['skipped']}")
    print(f"  Errors encountered: {len(free_results['errors'])}")
    
    all_errors = paid_results['errors'] + free_results['errors']
    if all_errors:
        print(f"\nAll errors:")
        for error in all_errors:
            print(f"  - {error}")
    
    return True

def apply_modifications_to_file(modifications, answer_data, version_name):
    """Apply modifications to a specific answer file."""
    results = {'applied': 0, 'skipped': 0, 'errors': []}
    
    for mod in modifications:
        test_id = mod['test_id']
        question = mod['question']
        question_num = question.replace('Q', '')  # Remove 'Q' prefix to get number
        original_answer = mod['original_answer']
        new_answer = mod['new_answer']
        
        # Check if test exists in answer data
        if test_id not in answer_data:
            error_msg = f"{version_name}: Test {test_id} not found in answer data"
            results['errors'].append(error_msg)
            print(f"  ❌ {error_msg}")
            continue
        
        # Check if question exists in test
        if question_num not in answer_data[test_id]:
            error_msg = f"{version_name}: Question {question} not found in {test_id}"
            results['errors'].append(error_msg)
            print(f"  ❌ {error_msg}")
            continue
        
        # Get current answer
        current_answer = answer_data[test_id][question_num]
        
        # Verify original answer matches current answer
        if current_answer != original_answer:
            if mod.get('note') == "No change - confirmed correct":
                print(f"  ✓ {version_name} {test_id} {question}: Confirmed correct ({current_answer})")
                results['skipped'] += 1
                continue
            else:
                warning_msg = f"Warning: {version_name} {test_id} {question} current answer '{current_answer}' doesn't match expected original '{original_answer}'"
                print(f"  ⚠️  {warning_msg}")
                print(f"     Proceeding with change to '{new_answer}'...")
        
        # Apply the change
        if new_answer != current_answer:
            answer_data[test_id][question_num] = new_answer
            print(f"  ✓ {version_name} {test_id} {question}: {current_answer} → {new_answer}")
            results['applied'] += 1
        else:
            print(f"  ✓ {version_name} {test_id} {question}: No change needed ({current_answer})")
            results['skipped'] += 1
    
    return results

def verify_changes_for_type(test_type):
    """Verify that changes were applied correctly for a specific test type."""
    
    print(f"\n{'='*20} VERIFYING {test_type.upper()} {'='*20}")
    
    paths = get_file_paths(test_type)
    log_data = load_json_file(paths['log_file'])
    paid_answer_data = load_json_file(paths['paid_answer_file'])
    free_answer_data = load_json_file(paths['free_answer_file'])
    
    if not log_data or not paid_answer_data:
        print(f"❌ Could not load required files for {test_type} verification")
        return False
    
    verification_passed = True
    paid_verified = 0
    free_verified = 0
    
    for mod in log_data['modifications']:
        test_id = mod['test_id']
        question = mod['question']
        question_num = question.replace('Q', '')
        expected_answer = mod['new_answer']
        version_type = mod.get('version_type', 'paid')  # Default to paid for old logs
        
        # Choose the appropriate answer data
        if version_type == 'free' and free_answer_data:
            answer_data = free_answer_data
            prefix = "FREE"
        else:
            answer_data = paid_answer_data
            prefix = "PAID"
        
        if test_id in answer_data and question_num in answer_data[test_id]:
            actual_answer = answer_data[test_id][question_num]
            if actual_answer == expected_answer:
                print(f"  ✓ {prefix} {test_id} {question}: {actual_answer} (correct)")
                if version_type == 'free':
                    free_verified += 1
                else:
                    paid_verified += 1
            else:
                print(f"  ❌ {prefix} {test_id} {question}: Expected {expected_answer}, got {actual_answer}")
                verification_passed = False
        else:
            print(f"  ❌ {prefix} {test_id} {question}: Not found in answer data")
            verification_passed = False
    
    print(f"\n{test_type.upper()} Verification Summary:")
    print(f"  PAID modifications verified: {paid_verified}")
    print(f"  FREE modifications verified: {free_verified}")
    
    if verification_passed:
        print(f"✅ All {test_type} modifications verified successfully!")
    else:
        print(f"❌ Some {test_type} modifications were not applied correctly!")
    
    return verification_passed

def main():
    """Main function with command line argument parsing."""
    parser = argparse.ArgumentParser(description="Apply answer modifications for reading and/or listening tests")
    parser.add_argument('--type', choices=['reading', 'listening', 'both'], default='both',
                       help='Type of modifications to apply (default: both)')
    
    args = parser.parse_args()
    
    print("="*60)
    print("ANSWER MODIFICATION SCRIPT")
    print("="*60)
    print(f"Processing: {args.type}")
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    
    if args.type in ['listening', 'both']:
        success &= apply_modifications_for_type('listening')
        
    if args.type in ['reading', 'both']:
        success &= apply_modifications_for_type('reading')
    
    if success:
        print(f"\n{'='*60}")
        print("VERIFICATION PHASE")
        print(f"{'='*60}")
        
        if args.type in ['listening', 'both']:
            verify_changes_for_type('listening')
            
        if args.type in ['reading', 'both']:
            verify_changes_for_type('reading')
        
        print(f"\n🎉 Process completed successfully!")
    else:
        print(f"\n❌ Process failed!")

if __name__ == "__main__":
    main() 