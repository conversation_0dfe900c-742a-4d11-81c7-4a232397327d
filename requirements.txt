# TCF Canada - Production Requirements
# This file contains all dependencies needed for production deployment

# ============================================================================
# BACKEND DEPENDENCIES (Python/Flask)
# ============================================================================

# Core Framework
Flask==3.0.0
Flask-CORS==4.0.0

# Database & ORM
supabase==2.15.2
postgrest==1.0.2

# Authentication & Security
Werkzeug==3.0.1
PyJWT==2.10.1
cryptography==41.0.7

# Email Services
sendgrid==6.11.0
Flask-Mail==0.9.1

# Payment Processing
stripe==7.8.0

# Environment & Configuration
python-dotenv==1.0.0

# HTTP Requests
requests==2.31.0
urllib3==2.1.0

# Date/Time Handling
python-dateutil==2.8.2

# JSON Processing
jsonschema==4.20.0

# Production Server
gunicorn==21.2.0
gevent==23.9.1

# ============================================================================
# SYSTEM REQUIREMENTS
# ============================================================================

# Node.js (for frontend build)
# Version: 18.x or higher
# Install via: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# Python
# Version: 3.9 or higher

# Git LFS (for audio assets)
# Install via: sudo apt-get install git-lfs

# ============================================================================
# FRONTEND DEPENDENCIES (Node.js/React)
# ============================================================================

# Frontend dependencies are managed via package.json in web_react/frontend/
# Key dependencies include:
# - React 18.x
# - TypeScript 5.x
# - Vite 5.x
# - Mantine UI 7.x
# - React Router 6.x
# - TanStack Query 5.x
# - i18next (internationalization)

# ============================================================================
# PRODUCTION NOTES
# ============================================================================

# 1. Install Python dependencies: pip install -r requirements.txt
# 2. Install Node.js dependencies: cd web_react/frontend && npm install
# 3. Build frontend: cd web_react/frontend && npm run build
# 4. Configure environment variables (see .env.example files)
# 5. Set up reverse proxy (Nginx recommended)
# 6. Configure SSL certificates
# 7. Set up process manager (PM2 or systemd)

# ============================================================================
# OPTIONAL DEPENDENCIES
# ============================================================================

# Development Tools (not needed in production)
# pytest==7.4.3
# black==23.11.0
# flake8==6.1.0

# Monitoring (optional)
# sentry-sdk[flask]==1.38.0

# Performance (optional)
# redis==5.0.1
# celery==5.3.4 

openai 