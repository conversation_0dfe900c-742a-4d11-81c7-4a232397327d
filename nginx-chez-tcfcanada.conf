# HTTP to HTTPS redirect for all domains
server {
    listen 80;
    server_name chez-tcfcanada.com www.chez-tcfcanada.com tcfcanada.site www.tcfcanada.site chez-tcfcanada.online www.chez-tcfcanada.online;
    return 301 https://chez-tcfcanada.com$request_uri;
}

# Redirect non-primary domains to main domain
server {
    listen 443 ssl http2;
    server_name www.chez-tcfcanada.com tcfcanada.site www.tcfcanada.site chez-tcfcanada.online www.chez-tcfcanada.online;
    
    ssl_certificate /etc/letsencrypt/live/chez-tcfcanada.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chez-tcfcanada.com/privkey.pem;
    
    return 301 https://chez-tcfcanada.com$request_uri;
}

# Main HTTPS server for chez-tcfcanada.com
server {
    listen 443 ssl http2;
    server_name chez-tcfcanada.com;
    
    ssl_certificate /etc/letsencrypt/live/chez-tcfcanada.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/chez-tcfcanada.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # CORS headers for API
    add_header Access-Control-Allow-Origin "https://chez-tcfcanada.com" always;
    add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Accept, Authorization, Cache-Control, Content-Type, DNT, If-Modified-Since, Keep-Alive, Origin, User-Agent, X-Requested-With" always;
    
    # Serve robots.txt directly (IMPORTANT FOR SEO)
    location = /robots.txt {
        alias /path/to/your/project/web_react/frontend/public/robots.txt;
        add_header Content-Type text/plain;
        access_log off;
    }
    
    # Serve sitemap.xml directly
    location = /sitemap.xml {
        alias /path/to/your/project/web_react/frontend/public/sitemap.xml;
        add_header Content-Type application/xml;
        access_log off;
    }
    
    # Serve favicon directly
    location = /favicon.ico {
        alias /path/to/your/project/web_react/frontend/public/favicon.ico;
        access_log off;
        expires 1y;
    }
    
    # Handle old URLs and redirects
    location ~ ^/old-page\.html$ {
        return 301 https://chez-tcfcanada.com/reading;
    }
    
    location ~ ^/test\.html$ {
        return 301 https://chez-tcfcanada.com/listening;
    }
    
    location ~ ^/practice\.html$ {
        return 301 https://chez-tcfcanada.com/writing;
    }
    
    location ~ ^/exam\.html$ {
        return 301 https://chez-tcfcanada.com/speaking;
    }
    
    # Catch-all for old file extensions
    location ~ \.(html|php)$ {
        return 301 https://chez-tcfcanada.com/;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:5001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
    
    # Frontend with proper routing
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Handle React Router
        try_files $uri $uri/ @fallback;
    }
    
    location @fallback {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml text/javascript application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
} 