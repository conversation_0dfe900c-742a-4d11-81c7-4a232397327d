#!/bin/bash

# TCF Canada Local Development Server Startup Script
echo "🚀 Starting TCF Canada Local Development Servers..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to cleanup ports
cleanup_ports() {
    echo -e "${YELLOW}Cleaning up existing processes...${NC}"
    lsof -ti:8000 | xargs kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs kill -9 2>/dev/null || true
    sleep 2
}

# Cleanup existing processes
cleanup_ports

# Check if we're in the web_react directory
if [[ ! -d "backend" ]] || [[ ! -d "frontend" ]]; then
    echo -e "${RED}Error: Please run this script from the web_react directory${NC}"
    echo -e "${YELLOW}Expected structure:${NC}"
    echo -e "  web_react/"
    echo -e "    ├── backend/"
    echo -e "    ├── frontend/"
    echo -e "    └── start_dev.sh (this script)"
    exit 1
fi

# Check if virtual environment exists
if [[ ! -d "backend/venv" ]]; then
    echo -e "${RED}Error: Virtual environment not found in backend/venv${NC}"
    echo -e "${YELLOW}Please run setup first from the backend directory${NC}"
    exit 1
fi

echo -e "${BLUE}Starting Backend Server...${NC}"
cd backend

# Activate virtual environment and start backend
source venv/bin/activate
export PORT=8000
export FLASK_ENV=development
export FLASK_DEBUG=1

# Start backend in background
python run.py &
BACKEND_PID=$!
echo -e "${GREEN}✅ Backend started on http://localhost:8000 (PID: $BACKEND_PID)${NC}"

# Wait a moment for backend to start
sleep 3

# Check if backend started successfully
if ! curl -s http://localhost:8000/api/health > /dev/null; then
    echo -e "${RED}❌ Backend failed to start${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo -e "${BLUE}Starting Frontend Server...${NC}"
cd ../frontend

# Ensure .env file exists
if [[ ! -f ".env" ]]; then
    echo "VITE_API_URL=http://localhost:8000/api" > .env
    echo -e "${GREEN}Created .env file${NC}"
fi

# Start frontend in background
npm run dev &
FRONTEND_PID=$!
echo -e "${GREEN}✅ Frontend started on http://localhost:5173 (PID: $FRONTEND_PID)${NC}"

# Wait for frontend to start
sleep 5

echo ""
echo -e "${GREEN}🎉 TCF Canada is now running locally!${NC}"
echo -e "${BLUE}Frontend: http://localhost:5173${NC}"
echo -e "${BLUE}Backend API: http://localhost:8000/api${NC}"
echo -e "${BLUE}API Health: http://localhost:8000/api/health${NC}"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop both servers${NC}"

# Function to cleanup on exit
cleanup() {
    echo -e "\n${YELLOW}Shutting down servers...${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}✅ Servers stopped${NC}"
    exit 0
}

# Trap Ctrl+C and call cleanup
trap cleanup INT

# Keep script running and show logs
echo -e "${BLUE}Monitoring servers... (Ctrl+C to stop)${NC}"
wait 