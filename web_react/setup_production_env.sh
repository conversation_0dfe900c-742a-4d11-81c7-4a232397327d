#!/bin/bash

# TCF Canada Production Environment Setup Script
# This script helps set up production environment files securely

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║              TCF Canada Production Environment               ║"
echo "║                        Setup Script                         ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# Function to generate a secure secret key
generate_secret_key() {
    python3 -c "import secrets; print(secrets.token_urlsafe(32))"
}

# Function to setup backend .env
setup_backend_env() {
    echo -e "${BLUE}Setting up Backend Environment...${NC}"
    
    if [[ -f "backend/.env" ]]; then
        echo -e "${YELLOW}Backend .env already exists. Backup will be created.${NC}"
        cp backend/.env backend/.env.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # Copy template
    cp backend/env.production.template backend/.env
    
    # Generate secret key
    SECRET_KEY=$(generate_secret_key)
    
    # Replace the secret key in the file
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/CHANGE_THIS_TO_A_STRONG_SECRET_KEY_IN_PRODUCTION_32_CHARS_MIN/$SECRET_KEY/" backend/.env
    else
        # Linux
        sed -i "s/CHANGE_THIS_TO_A_STRONG_SECRET_KEY_IN_PRODUCTION_32_CHARS_MIN/$SECRET_KEY/" backend/.env
    fi
    
    echo -e "${GREEN}✅ Backend .env created with secure secret key${NC}"
    echo -e "${YELLOW}⚠️  Please edit backend/.env and update the following:${NC}"
    echo "   - SUPABASE_URL, SUPABASE_KEY, SUPABASE_SERVICE_KEY"
    echo "   - STRIPE_PUBLISHABLE_KEY, STRIPE_SECRET_KEY, STRIPE_WEBHOOK_SECRET"
    echo "   - MAIL_USERNAME, MAIL_PASSWORD"
    echo "   - FRONTEND_URL (your production domain)"
    echo "   - CORS_ORIGINS (your production domain)"
}

# Function to setup frontend .env
setup_frontend_env() {
    echo -e "${BLUE}Setting up Frontend Environment...${NC}"
    
    if [[ -f "frontend/.env" ]]; then
        echo -e "${YELLOW}Frontend .env already exists. Backup will be created.${NC}"
        cp frontend/.env frontend/.env.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # Copy template
    cp frontend/env.production.template frontend/.env
    
    echo -e "${GREEN}✅ Frontend .env created${NC}"
    echo -e "${YELLOW}⚠️  Please edit frontend/.env and update the following:${NC}"
    echo "   - VITE_API_URL (your production API URL)"
    echo "   - VITE_STRIPE_PUBLISHABLE_KEY (production publishable key)"
    echo "   - VITE_SUPABASE_URL, VITE_SUPABASE_ANON_KEY"
}

# Function to validate environment files
validate_env_files() {
    echo -e "${BLUE}Validating Environment Files...${NC}"
    
    local errors=0
    
    # Check backend .env
    if [[ -f "backend/.env" ]]; then
        if grep -q "your-production-project" backend/.env; then
            echo -e "${RED}❌ Backend .env still contains template values${NC}"
            ((errors++))
        fi
        if grep -q "CHANGE_THIS_TO_A_STRONG_SECRET_KEY" backend/.env; then
            echo -e "${RED}❌ Backend .env still contains default secret key${NC}"
            ((errors++))
        fi
    else
        echo -e "${RED}❌ Backend .env file not found${NC}"
        ((errors++))
    fi
    
    # Check frontend .env
    if [[ -f "frontend/.env" ]]; then
        if grep -q "yourdomain.com" frontend/.env; then
            echo -e "${RED}❌ Frontend .env still contains template values${NC}"
            ((errors++))
        fi
    else
        echo -e "${RED}❌ Frontend .env file not found${NC}"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        echo -e "${GREEN}✅ Environment files validation passed${NC}"
        return 0
    else
        echo -e "${RED}❌ Environment files validation failed ($errors errors)${NC}"
        return 1
    fi
}

# Function to show security checklist
show_security_checklist() {
    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                    Security Checklist                       ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    echo -e "${BLUE}Before deploying to production, ensure:${NC}"
    echo "□ All template values replaced with real production values"
    echo "□ Using production Stripe keys (pk_live_*, sk_live_*)"
    echo "□ Using production Supabase project"
    echo "□ CORS_ORIGINS set to your production domain only"
    echo "□ FRONTEND_URL points to your production domain"
    echo "□ Email credentials are for production SMTP"
    echo "□ .env files are NOT committed to version control"
    echo "□ Server firewall configured properly"
    echo "□ SSL certificates installed and configured"
    echo ""
    echo -e "${RED}NEVER commit .env files with real credentials!${NC}"
}

# Main function
main() {
    local command=${1:-"setup"}
    
    case $command in
        "setup")
            setup_backend_env
            echo ""
            setup_frontend_env
            echo ""
            show_security_checklist
            ;;
        "validate")
            validate_env_files
            ;;
        "checklist")
            show_security_checklist
            ;;
        "help"|"-h"|"--help")
            echo -e "${BLUE}TCF Canada Production Environment Setup${NC}"
            echo ""
            echo -e "${YELLOW}Usage:${NC}"
            echo "  $0 [command]"
            echo ""
            echo -e "${YELLOW}Commands:${NC}"
            echo "  setup      Set up production environment files (default)"
            echo "  validate   Validate environment files"
            echo "  checklist  Show security checklist"
            echo "  help       Show this help message"
            echo ""
            echo -e "${YELLOW}Examples:${NC}"
            echo "  $0                    # Set up environment files"
            echo "  $0 setup              # Set up environment files"
            echo "  $0 validate           # Validate configuration"
            echo "  $0 checklist          # Show security checklist"
            ;;
        *)
            echo -e "${RED}Unknown command: $command${NC}"
            echo -e "${YELLOW}Use '$0 help' for usage information${NC}"
            exit 1
            ;;
    esac
}

# Run main function
main "$@" 