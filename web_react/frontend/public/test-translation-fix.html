<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Fix Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .test-result { margin: 10px 0; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🧪 Translation Fix Test</h1>
    <p>Testing the subtopic name normalization fix for classified writing translations.</p>

    <div class="test-section">
        <h2>🔍 Test Results</h2>
        <div id="test-results"></div>
        <button onclick="runTests()">Run Translation Tests</button>
        <button onclick="testLanguageSwitching()">Test Language Switching</button>
    </div>

    <div class="test-section">
        <h2>📊 API Test</h2>
        <div id="api-results"></div>
        <button onclick="testAPI()">Test API Endpoints</button>
    </div>

    <script>
        async function runTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 Running tests...</p>';

            const tests = [
                {
                    name: 'Subtopic Name Normalization',
                    test: () => {
                        // Test the normalization function
                        const normalize = (name) => name.toLowerCase().replace(/\s+/g, '_');
                        
                        const testCases = [
                            { input: 'description camping', expected: 'description_camping' },
                            { input: 'Description Apartment', expected: 'description_apartment' },
                            { input: 'DESCRIPTION CITY', expected: 'description_city' },
                            { input: 'description_hotel', expected: 'description_hotel' }
                        ];

                        return testCases.every(testCase => {
                            const result = normalize(testCase.input);
                            const passed = result === testCase.expected;
                            if (!passed) {
                                console.error(`Failed: ${testCase.input} -> ${result}, expected: ${testCase.expected}`);
                            }
                            return passed;
                        });
                    }
                },
                {
                    name: 'Translation API Response',
                    test: async () => {
                        try {
                            const response = await fetch('/api/classified-writing/translations/1');
                            const data = await response.json();
                            
                            // Check if key subtopics exist
                            const requiredSubtopics = ['description_camping', 'description_apartment', 'description_city'];
                            return requiredSubtopics.every(subtopic => {
                                const exists = data.translations.subtopics[subtopic];
                                if (!exists) {
                                    console.error(`Missing subtopic: ${subtopic}`);
                                }
                                return exists;
                            });
                        } catch (error) {
                            console.error('API test failed:', error);
                            return false;
                        }
                    }
                }
            ];

            let results = [];
            for (const test of tests) {
                try {
                    const passed = await test.test();
                    results.push({
                        name: test.name,
                        passed,
                        message: passed ? '✅ Passed' : '❌ Failed'
                    });
                } catch (error) {
                    results.push({
                        name: test.name,
                        passed: false,
                        message: `❌ Error: ${error.message}`
                    });
                }
            }

            resultsDiv.innerHTML = results.map(result => 
                `<div class="test-result ${result.passed ? 'success' : 'error'}">
                    <strong>${result.name}:</strong> ${result.message}
                </div>`
            ).join('');
        }

        async function testAPI() {
            const resultsDiv = document.getElementById('api-results');
            resultsDiv.innerHTML = '<p>🔄 Testing API endpoints...</p>';

            try {
                // Test translations endpoint
                const translationsResponse = await fetch('/api/classified-writing/translations/1');
                const translationsData = await translationsResponse.json();

                // Test subtopic endpoint
                const subtopicResponse = await fetch('/api/classified-writing/subtopic/description_camping/tasks?page=1&limit=5');
                const subtopicData = await subtopicResponse.json();

                const results = [
                    {
                        endpoint: 'Translations API',
                        status: translationsResponse.ok ? '✅ OK' : '❌ Failed',
                        data: `${Object.keys(translationsData.translations.subtopics).length} subtopics loaded`
                    },
                    {
                        endpoint: 'Subtopic API',
                        status: subtopicResponse.ok ? '✅ OK' : '❌ Failed',
                        data: `Subtopic: "${subtopicData.subtopic?.subtopic_name}" (${subtopicData.tasks?.length || 0} tasks)`
                    }
                ];

                resultsDiv.innerHTML = results.map(result => 
                    `<div class="test-result">
                        <strong>${result.endpoint}:</strong> ${result.status}<br>
                        <small>${result.data}</small>
                    </div>`
                ).join('');

            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ API Error: ${error.message}</div>`;
            }
        }

        async function testLanguageSwitching() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>🔄 Testing language switching...</p>';

            try {
                const response = await fetch('/api/classified-writing/translations/1');
                const data = await response.json();
                
                const testSubtopic = 'description_camping';
                const translations = data.translations.subtopics[testSubtopic];

                if (translations) {
                    resultsDiv.innerHTML = `
                        <div class="test-result success">
                            <strong>Language Test for "${testSubtopic}":</strong><br>
                            🇬🇧 English: "${translations.en}"<br>
                            🇫🇷 French: "${translations.fr}"<br>
                            🇨🇳 Chinese: "${translations.zh}"
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="test-result error">❌ No translations found for ${testSubtopic}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ Language test failed: ${error.message}</div>`;
            }
        }

        // Auto-run tests on page load
        window.onload = () => {
            runTests();
            testAPI();
        };
    </script>
</body>
</html>
