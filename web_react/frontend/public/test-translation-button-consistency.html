<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation Button Consistency Test</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            line-height: 1.6;
        }
        .test-section { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #ccc; 
            border-radius: 5px; 
            background-color: #f9f9f9;
        }
        .success { 
            background-color: #d4edda; 
            border-color: #c3e6cb; 
        }
        .error { 
            background-color: #f8d7da; 
            border-color: #f5c6cb; 
        }
        .test-result { 
            margin: 10px 0; 
            padding: 10px;
            border-radius: 4px;
        }
        .test-text {
            background-color: #fff;
            padding: 20px;
            border: 2px solid #007bff;
            border-radius: 8px;
            margin: 15px 0;
            user-select: text;
            cursor: text;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        button { 
            margin: 5px; 
            padding: 10px 15px; 
            cursor: pointer; 
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .highlight {
            background-color: yellow;
            padding: 2px;
        }
    </style>
</head>
<body>
    <h1>🧪 Translation Button Consistency Test</h1>
    <p>This test verifies that the translation button positioning is <strong>consistent</strong> regardless of whether a translation popup is already open.</p>

    <div class="instructions">
        <h3>📋 Test Instructions:</h3>
        <ol>
            <li><strong>Step 1:</strong> Select text in the passage below to open the translate button</li>
            <li><strong>Step 2:</strong> Note the button's position and click "Translate" to open the popup</li>
            <li><strong>Step 3:</strong> Select different text while the popup is open</li>
            <li><strong>Step 4:</strong> Verify the new translate button appears in the <em>same relative position</em> to the selected text</li>
            <li><strong>Expected Result:</strong> Button positioning should be identical in both scenarios</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 Test Passage</h2>
        <div class="test-text">
            <p>This is a test passage for translation button consistency. <span class="highlight">Select this highlighted text first</span> to see the initial button position.</p>
            
            <p>After opening the translation popup, <span class="highlight">select this second highlighted text</span> to verify the button appears in the same relative position.</p>
            
            <p>You can also test with this longer sentence to see how the button positioning works with different text lengths and positions on the screen.</p>
            
            <p>The key test is that the translate button should appear in exactly the same position relative to your text selection, whether or not a translation popup is already visible on the screen.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 Manual Test Checklist</h2>
        <div id="test-checklist">
            <div class="test-result">
                <label>
                    <input type="checkbox" id="test1"> 
                    ✅ Button appears when selecting text (no popup open)
                </label>
            </div>
            <div class="test-result">
                <label>
                    <input type="checkbox" id="test2"> 
                    ✅ Translation popup opens when clicking translate
                </label>
            </div>
            <div class="test-result">
                <label>
                    <input type="checkbox" id="test3"> 
                    ✅ Button appears when selecting new text (popup already open)
                </label>
            </div>
            <div class="test-result">
                <label>
                    <input type="checkbox" id="test4"> 
                    ✅ Button position is consistent in both scenarios
                </label>
            </div>
            <div class="test-result">
                <label>
                    <input type="checkbox" id="test5"> 
                    ✅ Button styling is identical in both scenarios
                </label>
            </div>
        </div>
        
        <button onclick="checkResults()">Check Test Results</button>
        <button onclick="resetTest()">Reset Test</button>
    </div>

    <div class="test-section">
        <h2>🎯 Expected Behavior</h2>
        <ul>
            <li><strong>Consistent Position:</strong> Button should appear at the same relative position to selected text</li>
            <li><strong>Consistent Styling:</strong> Button should look identical (same colors, shadows, size)</li>
            <li><strong>Consistent Z-Index:</strong> Button should always be clickable and visible</li>
            <li><strong>No Weird Positioning:</strong> Button should not jump to different locations</li>
        </ul>
    </div>

    <div id="results"></div>

    <script>
        function checkResults() {
            const checkboxes = document.querySelectorAll('#test-checklist input[type="checkbox"]');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            
            const resultsDiv = document.getElementById('results');
            
            if (checkedCount === totalCount) {
                resultsDiv.innerHTML = `
                    <div class="test-section success">
                        <h3>🎉 All Tests Passed!</h3>
                        <p>Translation button positioning is consistent! The fix is working correctly.</p>
                    </div>
                `;
            } else if (checkedCount > 0) {
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>⚠️ Partial Success</h3>
                        <p>Passed ${checkedCount}/${totalCount} tests. Some issues may still exist.</p>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h3>❌ Tests Not Completed</h3>
                        <p>Please complete the manual tests above.</p>
                    </div>
                `;
            }
        }
        
        function resetTest() {
            const checkboxes = document.querySelectorAll('#test-checklist input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            document.getElementById('results').innerHTML = '';
        }
        
        // Add some helpful tips
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Translation Button Consistency Test loaded');
            console.log('💡 Tip: Open browser dev tools to see any console messages from the translation components');
        });
    </script>
</body>
</html>
