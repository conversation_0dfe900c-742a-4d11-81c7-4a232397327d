<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tâche 3 Translations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .translation-item {
            margin: 5px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-left: 3px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Tâche 3 Translation Test</h1>
        <p>This page tests if the translation API is working correctly for Tâche 3 classified writing.</p>
        
        <div>
            <button onclick="testTache3Translations()">Test Tâche 3 Translations</button>
            <button onclick="testAllTranslations()">Test All Translations</button>
            <button onclick="testSpecificTopics()">Test Specific Topics</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testTache3Translations() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-result warning">🔄 Testing Tâche 3 translations...</div>';

            try {
                const response = await fetch('/api/classified-writing/translations/3');
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }

                const topicsCount = Object.keys(data.translations.topics).length;
                const subtopicsCount = Object.keys(data.translations.subtopics).length;

                let html = `
                    <div class="test-result success">
                        <strong>✅ Tâche 3 Translation API Success!</strong><br>
                        📊 Topics: ${topicsCount}<br>
                        📊 Subtopics: ${subtopicsCount}<br>
                        📊 Total translations: ${data.metadata.total_translations}
                    </div>
                `;

                // Show some sample translations
                if (topicsCount > 0) {
                    html += '<h3>📝 Sample Topic Translations:</h3>';
                    let count = 0;
                    for (const [topicKey, translations] of Object.entries(data.translations.topics)) {
                        if (count >= 3) break; // Show only first 3
                        html += `
                            <div class="translation-item">
                                <strong>${topicKey}:</strong><br>
                                🇬🇧 EN: ${translations.en || 'N/A'}<br>
                                🇫🇷 FR: ${translations.fr || 'N/A'}<br>
                                🇨🇳 ZH: ${translations.zh || 'N/A'}
                            </div>
                        `;
                        count++;
                    }
                }

                if (subtopicsCount > 0) {
                    html += '<h3>📝 Sample Subtopic Translations:</h3>';
                    let count = 0;
                    for (const [subtopicKey, translations] of Object.entries(data.translations.subtopics)) {
                        if (count >= 3) break; // Show only first 3
                        html += `
                            <div class="translation-item">
                                <strong>${subtopicKey}:</strong><br>
                                🇬🇧 EN: ${translations.en || 'N/A'}<br>
                                🇫🇷 FR: ${translations.fr || 'N/A'}<br>
                                🇨🇳 ZH: ${translations.zh || 'N/A'}
                            </div>
                        `;
                        count++;
                    }
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Tâche 3 Translation Test Failed:<br>
                        ${error.message}
                    </div>
                `;
            }
        }

        async function testAllTranslations() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-result warning">🔄 Testing all translations...</div>';

            try {
                const response = await fetch('/api/classified-writing/translations/all');
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }

                const topicsCount = Object.keys(data.translations.topics).length;
                const subtopicsCount = Object.keys(data.translations.subtopics).length;

                resultsDiv.innerHTML = `
                    <div class="test-result success">
                        <strong>✅ All Translations API Success!</strong><br>
                        📊 Total Topics: ${topicsCount}<br>
                        📊 Total Subtopics: ${subtopicsCount}<br>
                        📊 Total translations: ${data.metadata.total_translations}<br>
                        📊 Tâches included: ${data.metadata.taches_included.join(', ')}
                    </div>
                `;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ All Translations Test Failed:<br>
                        ${error.message}
                    </div>
                `;
            }
        }

        async function testSpecificTopics() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-result warning">🔄 Testing specific Tâche 3 topics...</div>';

            try {
                // First get the tâche 3 overview to see what topics exist
                const overviewResponse = await fetch('/api/classified-writing/tache/3/overview');
                const overviewData = await overviewResponse.json();
                
                if (!overviewResponse.ok) {
                    throw new Error(`Overview API failed: ${overviewData.message || 'Unknown error'}`);
                }

                // Get translations
                const translationsResponse = await fetch('/api/classified-writing/translations/3');
                const translationsData = await translationsResponse.json();
                
                if (!translationsResponse.ok) {
                    throw new Error(`Translations API failed: ${translationsData.message || 'Unknown error'}`);
                }

                let html = `
                    <div class="test-result success">
                        <strong>✅ Tâche 3 Topics and Translations Comparison</strong>
                    </div>
                    <h3>📊 Topics in Overview vs Translations:</h3>
                `;

                const overviewTopics = Object.keys(overviewData.topics || {});
                const translationTopics = Object.keys(translationsData.translations.topics || {});

                html += `
                    <div class="translation-item">
                        <strong>Topics in Overview:</strong> ${overviewTopics.length}<br>
                        ${overviewTopics.join(', ')}<br><br>
                        <strong>Topics with Translations:</strong> ${translationTopics.length}<br>
                        ${translationTopics.join(', ')}
                    </div>
                `;

                // Check for missing translations
                const missingTranslations = overviewTopics.filter(topic => !translationTopics.includes(topic));
                if (missingTranslations.length > 0) {
                    html += `
                        <div class="test-result warning">
                            ⚠️ Topics missing translations: ${missingTranslations.join(', ')}
                        </div>
                    `;
                }

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-result error">
                        ❌ Specific Topics Test Failed:<br>
                        ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
