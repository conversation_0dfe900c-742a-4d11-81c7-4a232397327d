# Redirect old URLs to new React app structure
/old-page.html /reading 301
/test.html /listening 301
/practice.html /writing 301
/exam.html /speaking 301

# Catch-all for old file extensions
/*.html / 301
/*.php / 301

# Current domain (tcf-canada.site) configuration
# Ensure all traffic goes to HTTPS
http://tcf-canada.site/* https://tcf-canada.site/:splat 301

# Handle www subdomain
https://www.tcf-canada.site/* https://tcf-canada.site/:splat 301

# New domain (chez-tcfcanada.com) configuration
# Ensure all traffic goes to HTTPS
http://chez-tcfcanada.com/* https://chez-tcfcanada.com/:splat 301

# Handle www subdomain
https://www.chez-tcfcanada.com/* https://chez-tcfcanada.com/:splat 301

# Domain migration redirects (commented out until ready)
https://tcf-canada.site/* https://chez-tcfcanada.com/:splat 301!
https://www.tcf-canada.site/* https://chez-tcfcanada.com/:splat 301!