{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/core": "^8.0.2", "@mantine/form": "^8.0.2", "@mantine/hooks": "^8.0.2", "@mantine/notifications": "^8.0.2", "@stripe/stripe-js": "^7.3.1", "@supabase/supabase-js": "^2.50.0", "@tabler/icons-react": "^3.34.0", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.79.2", "@types/react-helmet-async": "^1.0.1", "axios": "^1.9.0", "i18next": "^25.2.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.2", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "serve": "^14.2.4", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}