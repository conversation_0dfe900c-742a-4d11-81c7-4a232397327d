import js from '@eslint/js'
import globals from 'globals'
import reactHooks from 'eslint-plugin-react-hooks'
import reactRefresh from 'eslint-plugin-react-refresh'
import tseslint from 'typescript-eslint'

export default tseslint.config(
  { ignores: ['dist'] },
  {
    extends: [js.configs.recommended, ...tseslint.configs.recommended],
    files: ['**/*.{ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2020,
      globals: globals.browser,
    },
    plugins: {
      'react-hooks': reactHooks,
      'react-refresh': reactRefresh,
    },
    rules: {
      ...reactHooks.configs.recommended.rules,
      'react-refresh/only-export-components': [
        'warn',
        { allowConstantExport: true },
      ],
      // Warn about console statements in production
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      // Allow console.error and console.warn for legitimate error handling
      'no-restricted-syntax': [
        'warn',
        {
          selector: "CallExpression[callee.object.name='console'][callee.property.name!=/^(warn|error)$/]",
          message: 'Unexpected console statement. Use console.warn() or console.error() for legitimate error handling.',
        },
      ],
    },
  },
)
