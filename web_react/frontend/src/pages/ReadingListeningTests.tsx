import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { 
  Container, 
  Title, 
  SimpleGrid, 
  Card, 
  Text, 
  Button, 
  Group, 
  Badge, 
  Loader, 
  Alert, 
  Stack,
  Box,
  Breadcrumbs,
  Anchor,
  Accordion,
  Progress,
  Divider,
  Modal,
  ActionIcon,
  Menu,
  Center,
  Tooltip,
  Paper
} from '@mantine/core';
import { Link } from 'react-router-dom';
import { 
  IconHome, 
  IconEar, 
  IconBook, 
  IconPencil, 
  IconMicrophone, 
  IconLock, 
  IconPlayerPlay, 
  IconInfoCircle, 
  IconGift, 
  IconList, 
  IconStack, 
  IconPlant, 
  IconWalk, 
  IconRun, 
  IconMountain, 
  IconTrophy,
  IconProgress,
  IconCheck,
  IconX,
  IconDots,
  IconTrash,
  IconRefresh,
  IconPlus,
  IconFlask,
  IconClock,
  IconTarget,
  IconLeaf
} from '@tabler/icons-react';
import { testApi, api } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import type { Test, TestsResponse } from '../types';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';
import { ModificationNotification } from '../components/ModificationNotification';
import { useThemeColors } from '../store/useThemeStore';

// Section configurations for all four TCF test types
const getSectionConfig = (t: any) => ({
  listening: {
    title: t('readingListeningTests.sections.listening.title'),
    icon: IconEar,
    color: '#007bff',
    description: t('readingListeningTests.sections.listening.description')
  },
  reading: {
    title: t('readingListeningTests.sections.reading.title'), 
    icon: IconBook,
    color: '#28a745',
    description: t('readingListeningTests.sections.reading.description')
  },
  writing: {
    title: t('readingListeningTests.sections.writing.title'),
    icon: IconPencil,
    color: '#fd7e14',
    description: t('readingListeningTests.sections.writing.description')
  },
  speaking: {
    title: t('readingListeningTests.sections.speaking.title'),
    icon: IconMicrophone,
    color: '#e83e8c',
    description: t('readingListeningTests.sections.speaking.description')
  }
});

// TCF difficulty levels for practice by level section
const getLevelGroups = (t: any) => [
  { 
    id: 1, 
    title: 'A1', 
    description: t('readingListeningTests.levels.a1'), 
    icon: IconPlant, 
    color: '#17a2b8' 
  },
  { 
    id: 2, 
    title: 'A2', 
    description: t('readingListeningTests.levels.a2'), 
    icon: IconLeaf, 
    color: '#17a2b8' 
  },
  { 
    id: 3, 
    title: 'B1', 
    description: t('readingListeningTests.levels.b1'), 
    icon: IconWalk, 
    color: '#17a2b8' 
  },
  { 
    id: 4, 
    title: 'B2', 
    description: t('readingListeningTests.levels.b2'), 
    icon: IconRun, 
    color: '#17a2b8' 
  },
  { 
    id: 5, 
    title: 'C1', 
    description: t('readingListeningTests.levels.c1'), 
    icon: IconMountain, 
    color: '#17a2b8' 
  },
  { 
    id: 6, 
    title: 'C2', 
    description: t('readingListeningTests.levels.c2'), 
    icon: IconTrophy, 
    color: '#17a2b8' 
  }
];

// Add interface for test with progress
interface TestWithProgress {
  id: string;
  title: string;
  progress?: number;
  total?: number;
  grading?: {
    score?: number;
    max_score?: number;
    correct_count?: number;
    wrong_count?: number;
  };
}

// Mock Exam interface
interface MockExam {
  id: string;
  exam_name: string;
  created_at: string;
  is_completed: boolean;
  score?: number;
  max_score?: number;
  completion_time?: number;
}

// Helper function to clean up test ID display
const formatTestId = (testId: string): string => {
  // Remove "test" prefix if it exists, keeping only the number
  const match = testId.match(/^test(\d+)$/i);
  if (match) {
    return match[1]; // Return just the number
  }
  return testId;
};

interface TestCardProps {
  test: Test;
  section: string;
  accessible: boolean;
  color: string;
  type: 'free' | 'premium';
  progress?: number;
  total?: number;
  grading?: {
    score?: number;
    max_score?: number;
    correct_count?: number;
    wrong_count?: number;
  };
  isAuthenticated: boolean;
}

// MockExamCard component
const MockExamCard: React.FC<{
  exam: MockExam;
  testType: 'reading' | 'listening';
  onDelete: (examId: string) => void;
  onRecreate: (examId: string) => void;
}> = ({ exam, testType, onDelete, onRecreate }) => {
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const formatTime = (seconds?: number) => {
    if (!seconds) return 'N/A';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return 'green';
    if (percentage >= 60) return 'yellow';
    return 'red';
  };

  // Generate simple exam title
  const getSimpleExamTitle = (examId: string) => {
    // Extract number from exam ID or use a simple counter
    const match = examId.match(/(\d+)$/);
    const number = match ? match[1] : examId.slice(-1);
    return `Examen Blanc ${number}`;
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onDelete(exam.id);
  };

  const handleRecreate = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onRecreate(exam.id);
  };

  return (
    <Card
      withBorder
      shadow="sm"
      radius="md"
      component={Link}
      to={`/${testType}/mock/${exam.id}`}
      style={{
        textDecoration: 'none',
        color: 'inherit',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        position: 'relative',
        height: '100%'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12)';
      }}
    >
      {/* Recreate Button - Top Left */}
      <Tooltip label={t('readingListeningTests.mockExams.recreateTooltip')}>
        <ActionIcon
          variant="light"
          color="blue"
          size="lg"
          onClick={handleRecreate}
          style={{
            position: 'absolute',
            top: '8px',
            left: '8px',
            zIndex: 10
          }}
        >
          <IconRefresh size={16} />
        </ActionIcon>
      </Tooltip>

      {/* Delete Button - Top Right */}
      <Tooltip label={t('readingListeningTests.mockExams.deleteTooltip')}>
        <ActionIcon
          variant="light"
          color="red"
          size="lg"
          onClick={handleDelete}
          style={{
            position: 'absolute',
            top: '8px',
            right: '8px',
            zIndex: 10
          }}
        >
          <IconTrash size={16} />
        </ActionIcon>
      </Tooltip>

      <Stack gap="sm" p="sm">
        {/* Header - Centered Title */}
        <Box style={{ textAlign: 'center', paddingTop: '8px', paddingLeft: '8px', paddingRight: '8px' }}>
          <Text
            fw={600}
            size="sm"
            style={{
              lineHeight: 1.3,
              wordBreak: 'break-word',
              hyphens: 'auto'
            }}
          >
            {getSimpleExamTitle(exam.id)}
          </Text>
        </Box>

        {/* Status Badge */}
        <Group justify="center">
          {exam.is_completed ? (
            <Badge
              color="green"
              variant="light"
              leftSection={<IconCheck size={12} />}
              size="sm"
            >
              {t('readingListeningTests.mockExams.completed')}
            </Badge>
          ) : (
            <Badge
              color="blue"
              variant="light"
              leftSection={<IconClock size={12} />}
              size="sm"
            >
              {t('readingListeningTests.mockExams.completed')}
            </Badge>
          )}
        </Group>

        {/* Results Section */}
        {exam.is_completed && exam.score !== undefined && exam.max_score !== undefined ? (
          <Stack gap="xs">
            {/* Score */}
            <Group justify="space-between" align="center">
              <Group gap="xs">
                <IconTarget size={14} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">Score</Text>
              </Group>
              <Text
                size="sm"
                fw={600}
                c={getScoreColor(exam.score, exam.max_score)}
              >
                {exam.score}/{exam.max_score}
              </Text>
            </Group>

            {/* Progress Bar */}
            <Progress
              value={(exam.score / exam.max_score) * 100}
              color={getScoreColor(exam.score, exam.max_score)}
              size="sm"
              radius="xl"
            />

            {/* Completion Time */}
            {exam.completion_time ? (
              <Group justify="space-between" align="center">
                <Group gap="xs">
                  <IconClock size={14} color={themeColors.textSecondary} />
                  <Text size="xs" c="dimmed">Temps</Text>
                </Group>
                <Text size="xs" fw={500}>
                  {formatTime(exam.completion_time)}
                </Text>
              </Group>
            ) : null}
          </Stack>
        ) : (
          /* Not completed */
          <Stack gap="xs" align="center">
            <Text size="xs" c="dimmed" ta="center">
              {t('readingListeningTests.mockExams.randomQuestions')}
            </Text>
            <Text size="xs" c="dimmed" ta="center">
              {t('readingListeningTests.testCard.start')}
            </Text>
          </Stack>
        )}
      </Stack>
    </Card>
  );
};

export function ReadingListeningTests() {
  const location = useLocation();
  const { section } = useParams<{ section: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  // Get the translated configurations
  const sectionConfig = getSectionConfig(t);
  const levelGroups = getLevelGroups(t);

  // Mock exam state management
  const [mockExams, setMockExams] = useState<MockExam[]>([]);
  const [mockExamsLoading, setMockExamsLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [maxAllowed, setMaxAllowed] = useState(3);

  // Get section from current path as fallback
  const sectionFromPath = section || location.pathname.replace('/', '');

  // Check if section is valid and redirect if needed
  useEffect(() => {
    if (!sectionFromPath) {
      navigate('/');
      return;
    }

    const config = sectionConfig[sectionFromPath as keyof typeof sectionConfig];
    if (!config) {
      navigate('/');
      return;
    }
  }, [sectionFromPath, navigate, sectionConfig]);

  // Fetch mock exams when component mounts or section changes
  useEffect(() => {
    if (isAuthenticated && isMember() && !isMembershipExpired() && 
        (sectionFromPath === 'reading' || sectionFromPath === 'listening')) {
      fetchMockExams();
    }
  }, [sectionFromPath, isAuthenticated]);

  // Early return if no section or invalid section
  if (!sectionFromPath || !sectionConfig[sectionFromPath as keyof typeof sectionConfig]) {
    return null;
  }

  // At this point, sectionFromPath is guaranteed to be a valid section
  const validSection = sectionFromPath as keyof typeof sectionConfig;
  const config = sectionConfig[validSection];
  const Icon = config.icon;

  // For authenticated users, use two-phase loading for optimal performance
  // Phase 1: Fast basic test list (no progress data)
  const { data: basicTestsData, isLoading: basicLoading } = useQuery({
    queryKey: ['basic-tests', validSection],
    queryFn: () => {
      if (validSection === 'reading') {
        return testApi.getReadingTestsList();
      } else if (validSection === 'listening') {
        return testApi.getListeningTestsList();
      }
      return Promise.reject(new Error('Invalid section'));
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  // Phase 2: Detailed test cards with progress/history (slower)
  const { data: sectionTestsData, isLoading: sectionLoading, error: sectionError, refetch: refetchSectionTests } = useQuery({
    queryKey: ['section-tests', validSection],
    queryFn: () => {
      if (validSection === 'reading') {
        return testApi.getReadingTestCards();
      } else if (validSection === 'listening') {
        return testApi.getListeningTestCards();
      }
      return Promise.reject(new Error('Invalid section'));
    },
    enabled: isAuthenticated,
    retry: false,
    staleTime: 0, // Always fetch fresh progress data
    gcTime: 0     // Don't cache the progress data
  });

  // For non-authenticated users, use general tests API
  const { data: testsData, isLoading, error } = useQuery({
    queryKey: ['tests'],
    queryFn: testApi.getTests,
    enabled: !isAuthenticated
  });

  // Mock exam functions with retry logic
  const fetchMockExams = async (retryCount = 0) => {
    if (!isAuthenticated || !isMember() || isMembershipExpired()) return;
    if (validSection !== 'reading' && validSection !== 'listening') return;

    setMockExamsLoading(true);
    try {
      const response = await api.get(`/mock-exams/${validSection}`);
      setMockExams(response.data.mock_exams);
      setMaxAllowed(response.data.max_allowed);

      // Show warning if there were connection issues but we got data
      if (response.data.warning) {
        notifications.show({
          title: 'Connection Warning',
          message: response.data.warning,
          color: 'yellow',
          autoClose: 3000
        });
      }
    } catch (error) {
      console.error('Error fetching mock exams:', error);
      const errorResponse = (error as any)?.response;

      // Don't show error notification for 404 (tables don't exist yet)
      if (errorResponse?.status === 404) {
        return;
      }

      // Handle connection errors specifically with retry
      if (errorResponse?.status === 503 || errorResponse?.data?.code === 'CONNECTION_ERROR') {
        if (retryCount < 2) {
          setTimeout(() => fetchMockExams(retryCount + 1), 2000); // Retry after 2 seconds
          return;
        }

        notifications.show({
          title: t('common.error'),
          message: 'Database connection issue. Please refresh the page and try again.',
          color: 'yellow',
          autoClose: 5000
        });
        return;
      }

      // Handle other errors
      notifications.show({
        title: t('common.error'),
        message: t('readingListeningTests.errors.loadMockExams'),
        color: 'red'
      });
    } finally {
      setMockExamsLoading(false);
    }
  };

  const createMockExam = async () => {
    if (!validSection || validSection === 'writing' || validSection === 'speaking') return;
    
    if (mockExams.length >= maxAllowed) {
      notifications.show({
        title: t('readingListeningTests.errors.limitReached'),
        message: t('readingListeningTests.errors.maxExamsReached', { maxAllowed }),
        color: 'yellow'
      });
      return;
    }

    setCreating(true);
    try {
      const response = await api.post(`/mock-exams/${validSection}/create`);
      
      notifications.show({
        title: t('readingListeningTests.success.created'),
        message: t('readingListeningTests.success.createdMessage', { 
          examName: response.data.exam_name, 
          questionCount: response.data.total_questions 
        }),
        color: 'green'
      });

      // Refresh the list
      await fetchMockExams();
    } catch (error: any) {
      console.error('Error creating mock exam:', error);
      
      // Handle 409 conflict (duplicate exam) specially
      if (error.response?.status === 409) {
        const errorData = error.response.data;
        const existingExam = errorData.existing_exam;
        
        // Show a more user-friendly error with suggestion
        notifications.show({
          title: t('readingListeningTests.errors.examExists'),
          message: t('readingListeningTests.errors.examExistsMessage', { examName: existingExam }),
          color: 'yellow',
          autoClose: false,
          withCloseButton: true
        });

      } else {
        // Handle other errors normally
        const errorMessage = error.response?.data?.error || t('readingListeningTests.errors.createMockExam');
        notifications.show({
          title: t('common.error'),
          message: errorMessage,
          color: 'red'
        });
      }
    } finally {
      setCreating(false);
    }
  };

  const deleteMockExam = async (examId: string) => {
    if (!validSection) return;
    
    try {
      await api.delete(`/mock-exams/${validSection}/${examId}`);
      
      notifications.show({
        title: t('readingListeningTests.success.deleted'),
        message: t('readingListeningTests.success.deletedMessage'),
        color: 'green'
      });

      // Remove from local state
      setMockExams(prev => prev.filter(exam => exam.id !== examId));
    } catch (error) {
      console.error('Error deleting mock exam:', error);
      notifications.show({
        title: t('common.error'),
        message: t('readingListeningTests.errors.deleteMockExam'),
        color: 'red'
      });
    }
  };

  const recreateMockExam = async (examId: string) => {
    if (!validSection) return;
    
    try {
      const response = await api.post(`/mock-exams/${validSection}/${examId}/recreate`);
      
      notifications.show({
        title: t('readingListeningTests.success.recreated'),
        message: t('readingListeningTests.success.recreatedMessage', { 
          examName: response.data.exam_name, 
          questionCount: response.data.total_questions 
        }),
        color: 'green'
      });

      // Refresh the list
      await fetchMockExams();
    } catch (error: any) {
      console.error('Error recreating mock exam:', error);
      const errorMessage = error.response?.data?.error || t('readingListeningTests.errors.recreateMockExam');
      notifications.show({
        title: t('common.error'),
        message: errorMessage,
        color: 'red'
      });
    }
  };

  // Handle loading and error states - show basic tests quickly while waiting for progress data
  if (isAuthenticated ? basicLoading : isLoading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="blue" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.tests')}</Text>
          <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
        </Stack>
      </Container>
    );
  }

  if (isAuthenticated ? (basicTestsData === undefined && !basicLoading) : error) {
    return (
      <Container size="lg" py="xl">
        <Alert 
          variant="light" 
          color="red" 
          title={t('readingListeningTests.errors.loadingTitle')}
          icon={<IconInfoCircle />}
        >
          {t('readingListeningTests.errors.loadTests')}
        </Alert>
      </Container>
    );
  }

  // Get tests based on authentication status
  let tests: TestWithProgress[] = [];
  let freeTests: TestWithProgress[] = [];
  
  if (isAuthenticated) {
    // Use basic tests data for initial display
    if (basicTestsData) {
      // Convert basic test data to TestWithProgress format
      const basicTests = basicTestsData.tests || [];
      const basicFreeTests = basicTestsData.free_tests || [];
      
      // If we have detailed progress data, merge it; otherwise use basic data
      if (sectionTestsData) {
        tests = sectionTestsData.tests || [];
        freeTests = sectionTestsData.free_tests || [];
      } else {
        // Use basic data while waiting for progress data
        tests = basicTests.map(test => ({
          id: test.id,
          title: test.title,
          progress: undefined,
          total: test.total
        }));
        freeTests = basicFreeTests.map(test => ({
          id: test.id,
          title: test.title,
          progress: undefined,
          total: test.total
        }));
      }
    }
  } else if (!isAuthenticated && testsData) {
    const sectionTests = testsData[validSection as keyof TestsResponse] || [];
    tests = sectionTests.filter((test: Test) => !test.free).map(test => ({
      id: test.id,
      title: test.title,
      progress: undefined,
      total: 39 // Default total for non-authenticated users
    }));
    freeTests = sectionTests.filter((test: Test) => test.free).map(test => ({
      id: test.id,
      title: test.title,
      progress: undefined,
      total: 39 // Default total for non-authenticated users
    }));
  }

  const showMembershipWarning = isAuthenticated && (!isMember() || isMembershipExpired());
  const canAccessPremium = isAuthenticated && isMember() && !isMembershipExpired();

  // Check if section has tests available
  const hasFreeTests = freeTests.length > 0;
  const hasPremiumTests = tests.length > 0;
  const hasAnyTests = hasFreeTests || hasPremiumTests;

  return (
    <Container size="lg" py="xl">
      {/* Header */}
      <Stack gap="lg" mb="xl">
        <Breadcrumbs>
          <Anchor component={Link} to="/" style={{ textDecoration: 'none' }}>
            {t('readingListeningTests.breadcrumb.home')}
          </Anchor>
          <Text c="dimmed">{config.title}</Text>
        </Breadcrumbs>
        
        <Group align="center" gap="md">
          <Icon size={48} color={config.color} />
          <div style={{ flex: 1 }}>
            <Title order={1} mb="xs">{config.title}</Title>
            <Text c="dimmed" size="lg">{config.description}</Text>
          </div>
          {(validSection === 'reading' || validSection === 'listening') && (
            <ModificationNotification section={validSection} />
          )}
        </Group>
      </Stack>

      {/* Guide Section */}
      <Card withBorder mb="xl" style={{
        background: themeColors.surface,
        border: `1px solid ${themeColors.border}`,
        transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
      }}>
        <Stack align="center" gap="lg">
          <Group gap="md" justify="center" style={{ position: 'relative', width: '100%' }}>
            <Box style={{
              background: validSection === 'reading'
                ? `linear-gradient(135deg, ${themeColors.reading} 0%, ${themeColors.reading}dd 100%)`
                : `linear-gradient(135deg, ${themeColors.listening} 0%, ${themeColors.listening}dd 100%)`,
              borderRadius: '50%',
              padding: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <Icon size={24} color="white" />
            </Box>
            <Title order={3} c={validSection === 'reading' ? themeColors.reading : themeColors.listening} fw={600} ta="center">
              {t(`readingListeningTests.guide.${validSection}.title`)}
            </Title>
          </Group>

          <Stack gap="md" align="center" style={{ maxWidth: '800px' }}>
            <Box style={{ textAlign: 'center' }}>
              <Text fw={600} size="md" c={themeColors.textPrimary} mb="md">
                {t(`readingListeningTests.guide.${validSection}.overview.title`)}
              </Text>
              <Stack gap="sm" align="center">
                <Text size="sm" c="#6c757d" ta="center">
                  • {t(`readingListeningTests.guide.${validSection}.overview.format`)}
                </Text>
                <Text size="sm" c="#6c757d" ta="center">
                  • {t(`readingListeningTests.guide.${validSection}.overview.duration`)}
                </Text>
                <Text size="sm" c="#6c757d" ta="center">
                  • {t(`readingListeningTests.guide.${validSection}.overview.content`)}
                </Text>
              </Stack>
            </Box>
            
            <Box style={{ textAlign: 'center' }}>
              <Text fw={600} size="md" c="#495057" mb="md">
                {t(`readingListeningTests.guide.${validSection}.features.title`)}
              </Text>
              <Group gap="md" justify="center" wrap="wrap">
                {validSection === 'reading' ? (
                  <>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.highlight')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.notebook')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.collection')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.verified')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.optimized')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.translation')}</Badge>
                    <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.reading.features.mockExams')}</Badge>
                  </>
                ) : (
                  <>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.audio')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.transcript')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.notebook')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.collection')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.verified')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.optimized')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.translation')}</Badge>
                    <Badge variant="light" color="green" size="sm">{t('readingListeningTests.guide.listening.features.mockExams')}</Badge>
                  </>
                )}
              </Group>
            </Box>
          </Stack>
        </Stack>
      </Card>

      {/* Progress Loading Indicator */}
      {isAuthenticated && basicTestsData && sectionLoading && (
        <Alert 
          variant="light" 
          color="blue" 
          title={t('readingListeningTests.access.loadingTitle')}
          icon={<IconProgress />}
          mb="xl"
        >
          <Text size="sm">
            {t('readingListeningTests.access.loadingMessage')}
          </Text>
        </Alert>
      )}

      {hasAnyTests && (
        <Accordion
          mb="xl"
          key={`accordion-${isAuthenticated ? (sectionLoading ? 'loading' : 'loaded') : 'guest'}`} // Force re-render when data state changes
          styles={{
            root: {
              backgroundColor: themeColors.surface,
              border: `1px solid ${themeColors.border}`,
              borderRadius: '8px',
            },
            item: {
              backgroundColor: themeColors.surface,
              border: `1px solid ${themeColors.border}`,
              borderRadius: '6px',
              marginBottom: '8px',
            },
            control: {
              backgroundColor: themeColors.surface,
              color: themeColors.textPrimary,
              '&:hover': {
                backgroundColor: themeColors.surfaceHover,
              },
            },
            panel: {
              backgroundColor: themeColors.surface,
            },
          }}
        >
          {/* Free Tests Section */}
          {hasFreeTests && (
            <Accordion.Item value="free-tests">
              <Accordion.Control icon={<IconGift size={20} color={themeColors.primary} />}>
                <Text fw={600} c={themeColors.primary}>
                  {t('readingListeningTests.sectionTitles.freeTests', { count: freeTests.length })}
                </Text>
              </Accordion.Control>
              <Accordion.Panel>
                {/* Show loading state for authenticated users until detailed data is ready */}
                {isAuthenticated && sectionLoading ? (
                  <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
                    <Loader size="lg" color={themeColors.primary} />
                    <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.testProgress')}</Text>
                  </Stack>
                ) : (
                  <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
                    {freeTests.map((test: TestWithProgress) => (
                      <TestCard
                        key={`free-${test.id}`}
                        test={{
                          id: test.id,
                          title: test.title,
                          type: validSection,
                          free: true
                        }}
                        section={validSection}
                        accessible={true}
                        color="#007bff"
                        type="free"
                        progress={test.progress}
                        total={test.total || 39}
                        grading={test.grading}
                        isAuthenticated={isAuthenticated}
                      />
                    ))}
                  </SimpleGrid>
                )}
              </Accordion.Panel>
            </Accordion.Item>
          )}

          {/* Premium Tests Section */}
          {hasPremiumTests && (
            <Accordion.Item value="all-tests">
              <Accordion.Control icon={<IconList size={20} color={validSection === 'reading' ? themeColors.reading : themeColors.listening} />}>
                <Text fw={600} c={validSection === 'reading' ? themeColors.reading : themeColors.listening}>
                  {t('readingListeningTests.sectionTitles.allTests', { count: tests.length })}
                </Text>
              </Accordion.Control>
              <Accordion.Panel>
                {/* Show loading state for authenticated users until detailed data is ready */}
                {isAuthenticated && sectionLoading ? (
                  <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
                    <Loader size="lg" color={validSection === 'reading' ? themeColors.reading : themeColors.listening} />
                    <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.testProgress')}</Text>
                  </Stack>
                ) : (
                  <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
                    {tests.map((test: TestWithProgress) => (
                      <TestCard
                        key={`premium-${test.id}`}
                        test={{
                          id: test.id,
                          title: test.title,
                          type: validSection,
                          free: false
                        }}
                        section={validSection}
                        accessible={canAccessPremium}
                        color="#28a745"
                        type="premium"
                        progress={test.progress}
                        total={test.total || 39}
                        grading={test.grading}
                        isAuthenticated={isAuthenticated}
                      />
                    ))}
                  </SimpleGrid>
                )}
              </Accordion.Panel>
            </Accordion.Item>
          )}

          {/* Practice by Level Section - Available for all sections */}
          <Accordion.Item value="practice-level">
            <Accordion.Control icon={<IconStack size={20} color={themeColors.textPrimary} />}>
              <Text fw={600} c={themeColors.textPrimary}>{t('readingListeningTests.sectionTitles.practiceByLevel')}</Text>
            </Accordion.Control>
            <Accordion.Panel>
              {/* Info message about practice by level */}
              <Alert
                variant="light"
                color="blue"
                icon={<IconInfoCircle size={16} />}
                mb="md"
                style={{
                  backgroundColor: themeColors.surface,
                  borderColor: themeColors.primary,
                  color: themeColors.textPrimary
                }}
              >
                <Text size="sm" style={{ color: themeColors.textPrimary }}>
                  {t('readingListeningTests.practiceByLevel.infoMessage')}
                </Text>
              </Alert>

              <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 5 }} spacing="md">
                {levelGroups.map((level) => {
                  const LevelIcon = level.icon;
                  // Get group progress data for authenticated users
                  const groupProgress = isAuthenticated && sectionTestsData?.group_progress?.[level.id];
                  const progressValue = groupProgress && typeof groupProgress === 'object' ? groupProgress.progress : undefined;
                  const totalValue = groupProgress && typeof groupProgress === 'object' ? groupProgress.total : 0;
                  const gradingValue = groupProgress && typeof groupProgress === 'object' ? (groupProgress as any).grading : undefined;
                  return (
                    <LevelCard 
                      key={level.id}
                      level={level}
                      section={validSection}
                      accessible={canAccessPremium}
                      icon={LevelIcon}
                      progress={progressValue}
                      total={totalValue}
                      grading={gradingValue}
                      isAuthenticated={isAuthenticated}
                    />
                  );
                })}
              </SimpleGrid>
            </Accordion.Panel>
          </Accordion.Item>

          {/* Mock Exams Section - Only for reading and listening */}
          {(validSection === 'reading' || validSection === 'listening') && (
            <Accordion.Item value="mock-exams">
              <Accordion.Control icon={<IconTrophy size={20} color={themeColors.speaking} />}>
                <Text fw={600} c={themeColors.speaking}>
                  {t('readingListeningTests.mockExams.title')}
                </Text>
              </Accordion.Control>
              <Accordion.Panel>
                <Box>
                  <Group justify="space-between" align="center" mb="lg">
                    {canAccessPremium && (
                      <Button
                        leftSection={<IconPlus size={16} />}
                        onClick={createMockExam}
                        loading={creating}
                        disabled={mockExams.length >= maxAllowed}
                        variant="light"
                        color="red"
                        size="sm"
                      >
                        {t('readingListeningTests.mockExams.createExam')}
                      </Button>
                    )}
                  </Group>
                  
                  {/* Info Alert */}
                  <Alert
                    icon={<IconInfoCircle size={16} />}
                    title={t('readingListeningTests.mockExams.aboutTitle')}
                    color="blue"
                    variant="light"
                    mb="lg"
                  >
                    <Text size="sm">
                      {t('readingListeningTests.mockExams.aboutDescription', { maxAllowed })}
                    </Text>
                  </Alert>
                  
                  {!canAccessPremium ? (
                    <Alert
                      icon={<IconLock size={16} />}
                      title={t('readingListeningTests.mockExams.premiumRequired')}
                      color="orange"
                      variant="light"
                    >
                      <Text size="sm">
                        {t('readingListeningTests.mockExams.premiumDescription')}
                      </Text>
                    </Alert>
                  ) : mockExamsLoading ? (
                    <Stack align="center" justify="center" py="xl">
                      <Loader size="lg" color="red" />
                      <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.mockExam')}</Text>
                    </Stack>
                  ) : mockExams.length === 0 ? (
                    <Center py="xl">
                      <Stack align="center" gap="md">
                        <IconFlask size={48} color={themeColors.textSecondary} />
                        <Text c="dimmed" ta="center">
                          {t('readingListeningTests.mockExams.noExamsTitle')}
                        </Text>
                        <Text size="sm" c="dimmed" ta="center">
                          {t('readingListeningTests.mockExams.noExamsDescription')}
                        </Text>
                      </Stack>
                    </Center>
                  ) : (
                    <Box>
                      <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
                        {mockExams.map((exam) => (
                          <MockExamCard
                            key={exam.id}
                            exam={exam}
                            testType={validSection}
                            onDelete={deleteMockExam}
                            onRecreate={recreateMockExam}
                          />
                        ))}
                      </SimpleGrid>
                      
                      {/* Counter */}
                      {mockExams.length > 0 ? (
                        <Text size="sm" c="dimmed" ta="center" mt="md">
                          {t('readingListeningTests.mockExams.examCount', { count: mockExams.length, max: maxAllowed })}
                        </Text>
                      ) : null}
                    </Box>
                  )}
                </Box>
              </Accordion.Panel>
            </Accordion.Item>
          )}
        </Accordion>
      )}

      {/* Membership Upgrade Call to Action for Logged-in Non-members */}
      {isAuthenticated && !canAccessPremium && hasAnyTests && (
        <Card
          padding="xl"
          radius="md"
          withBorder
          style={{
            background: `linear-gradient(135deg, ${themeColors.primary} 0%, ${themeColors.primary}dd 100%)`,
            textAlign: 'center',
            color: 'white',
            transition: 'all 0.3s ease'
          }}
          mb="xl"
        >
          <Title order={3} mb="md" c="white">
            {t('readingListeningTests.membershipUpgrade.title')}
          </Title>
          <Text mb="lg" c="white" opacity={0.9}>
            {t('readingListeningTests.membershipUpgrade.subtitle')}
          </Text>
          <Group justify="center" gap="md">
            <Link to="/membership" style={{ textDecoration: 'none' }}>
              <Button size="lg" style={{ backgroundColor: 'white', color: themeColors.primary }} fw={600}>
                {t('readingListeningTests.membershipUpgrade.upgrade')}
              </Button>
            </Link>
            <Link to="/profile" style={{ textDecoration: 'none' }}>
              <Button variant="outline" size="lg" style={{ borderColor: 'white', color: 'white' }}>
                {t('readingListeningTests.membershipUpgrade.viewProfile')}
              </Button>
            </Link>
          </Group>
        </Card>
      )}

      {/* Call to Action for Non-authenticated Users */}
      {!isAuthenticated && hasAnyTests && (
        <Card 
          padding="xl" 
          radius="md" 
          withBorder
          style={{
            background: 'white',
            textAlign: 'center'
          }}
        >
          <Title order={3} mb="md">
            {t('readingListeningTests.callToAction.title')}
          </Title>
          <Text mb="lg" c="dimmed">
            {t('readingListeningTests.callToAction.subtitle')}
          </Text>
          <Group justify="center" gap="md">
            <Link to="/register" style={{ textDecoration: 'none' }}>
              <Button size="lg" color="blue">
                {t('readingListeningTests.callToAction.register')}
              </Button>
            </Link>
            <Link to="/login" style={{ textDecoration: 'none' }}>
              <Button variant="outline" size="lg" color="blue">
                {t('readingListeningTests.callToAction.login')}
              </Button>
            </Link>
          </Group>
        </Card>
      )}

      {/* Back to sections button */}
      <Group justify="center" mt="xl">
        <Button 
          component={Link} 
          to="/" 
          variant="outline" 
          size="lg" 
          leftSection={<IconHome size={16} />}
        >
          {t('readingListeningTests.backToSections')}
        </Button>
      </Group>
    </Container>
  );
}

function TestCard({ test, section, accessible, color, type, progress, total = 39, grading, isAuthenticated }: TestCardProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const sectionConfig = getSectionConfig(t);
  const config = sectionConfig[section as keyof typeof sectionConfig];
  const Icon = config.icon;
  const queryClient = useQueryClient();
  const [opened, { open, close }] = useDisclosure(false);
  const themeColors = useThemeColors();

  // Calculate progress percentage for progress bar
  const progressPercentage = progress && total ? (progress / total) * 100 : 0;
  const hasProgress = progress !== undefined && progress > 0;
  const hasHistory = hasProgress || grading; // Has history if there's progress or grading results

  // Handle click for non-authenticated users
  const handleCardClick = () => {
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { 
          message: t('readingListeningTests.auth.loginRequired'),
          from: `/${section}/${test.id}${test.free ? '?free=1' : ''}` 
        } 
      });
    }
  };

  // Delete test history mutation
  const deleteHistoryMutation = useMutation({
    mutationFn: () => testApi.deleteTestHistory(section, test.id, test.free),
    onSuccess: (data) => {
      notifications.show({
        title: t('readingListeningTests.success.historyDeleted'),
        message: t('readingListeningTests.success.historyDeletedMessage'),
        color: 'green',
      });
      
      // Invalidate and refetch the section tests data
      queryClient.invalidateQueries({ queryKey: ['section-tests', section] });
      close();
    },
    onError: (error: any) => {
      notifications.show({
        title: t('common.error'),
        message: `${t('profile.deleteError')}: ${error.response?.data?.error || error.message}`,
        color: 'red',
      });
      close();
    },
  });

  return (
    <Card 
      shadow="sm" 
      padding="sm" 
      radius="md" 
      withBorder
      style={{
        background: themeColors.surface,
        borderColor: themeColors.border,
        color: themeColors.textPrimary,
        transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
        opacity: accessible ? 1 : 0.7,
        height: '100%',
        position: 'relative'
      }}
    >
      {/* Icon in top left corner */}
      <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
        <Icon size={20} color={color} />
      </Box>

      {/* Three dots menu in top right corner */}
      {accessible && hasHistory && (
        <Box style={{ position: 'absolute', top: '8px', right: '8px', zIndex: 10 }}>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <ActionIcon variant="light" color="gray" size="sm">
                <IconDots size={14} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item 
                color="red" 
                leftSection={<IconTrash size={14} />}
                onClick={open}
              >
                {t('readingListeningTests.testCard.resetTitle')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </Box>
      )}

      <Stack gap="xs" style={{ height: '100%', paddingTop: '28px' }}>
        {/* Title */}
        <Box>
          <Text fw={500} size="sm" lineClamp={1} mb={4}>
            {type === 'free' ? `Test ${formatTestId(test.id)} (Gratuit)` : `Test ${formatTestId(test.id)}`}
          </Text>
        </Box>

        {/* Progress/Results Section */}
        <Box style={{ flex: 1 }}>
          {grading ? (
            <Stack gap="xs">
              <Group justify="space-between">
                <Group gap="xs">
                  <Group gap="2px">
                    <IconCheck size={12} color="green" />
                    <Text size="xs" c="green">{grading.correct_count || 0}</Text>
                  </Group>
                  <Group gap="2px">
                    <IconX size={12} color="red" />
                    <Text size="xs" c="red">{grading.wrong_count || 0}</Text>
                  </Group>
                </Group>
                <Badge size="xs" color="green" variant="light">
                  {grading.score}/{grading.max_score || total}
                </Badge>
              </Group>
              <Progress 
                value={grading.score && grading.max_score ? (grading.score / grading.max_score) * 100 : 0} 
                size="xs" 
                color="green"
              />
              <Text size="xs" c="dimmed" ta="center">
                {t('readingListeningTests.testCard.completedGraded')}
              </Text>
            </Stack>
          ) : hasProgress ? (
            <Stack gap="xs">
              <Group justify="space-between">
                <Text size="xs" c="dimmed">{t('profile.progress')}</Text>
                <Badge size="xs" variant="light" color={progressPercentage === 100 ? 'green' : 'blue'}>
                  {progress}/{total}
                </Badge>
              </Group>
              <Progress 
                value={progressPercentage} 
                size="xs" 
                color={progressPercentage === 100 ? 'green' : 'blue'}
              />
              <Text size="xs" c="dimmed" ta="center">
                {progressPercentage === 100 ? t('profile.completed') : `${progressPercentage.toFixed(0)}${t('profile.progressText.completed')}`}
              </Text>
            </Stack>
          ) : (
            <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '40px' }}>
              <Text size="xs" c="dimmed">
                {accessible ? '' : ''}
          </Text>
            </Box>
          )}
        </Box>

        {/* Action Button */}
        {accessible ? (
          isAuthenticated ? (
          <Button
            component={Link}
            to={`/${section}/${test.id}${test.free ? '?free=1' : ''}`}
            color={type === 'free' ? 'blue' : 'green'}
            leftSection={<IconPlayerPlay size={12} />}
            size="xs"
            fullWidth
            variant={grading ? 'outline' : 'filled'}
          >
              {grading ? t('readingListeningTests.testCard.results') : hasProgress ? t('readingListeningTests.testCard.continue') : t('readingListeningTests.testCard.start')}
          </Button>
        ) : (
          <Button
              onClick={handleCardClick}
              color={type === 'free' ? 'blue' : 'green'}
              leftSection={<IconPlayerPlay size={12} />}
              size="xs"
              fullWidth
              variant={grading ? 'outline' : 'filled'}
            >
              {grading ? t('readingListeningTests.testCard.results') : hasProgress ? t('readingListeningTests.testCard.continue') : t('readingListeningTests.testCard.start')}
            </Button>
          )
        ) : (
          <Button
            onClick={!isAuthenticated ? handleCardClick : undefined}
            variant="outline"
            color="orange"
            disabled={isAuthenticated}
            size="xs"
            fullWidth
            leftSection={<IconLock size={12} />}
          >
            {t('readingListeningTests.testCard.subscriptionRequired')}
          </Button>
        )}
      </Stack>
      
      {/* Confirmation Modal for deleting test history */}
      <Modal 
        opened={opened} 
        onClose={close} 
        title={t('readingListeningTests.testCard.resetTitle')}
        centered
      >
        <Stack gap="md">
          <Text>
            {t('readingListeningTests.testCard.resetMessage')}
            {' '}
            {t('readingListeningTests.testCard.resetWarning')}
          </Text>
          
          {grading && (
            <Alert color="orange" variant="light">
              <Text size="sm">
                {t('readingListeningTests.testCard.resetResultsWarning', {
                  score: grading.score,
                  maxScore: grading.max_score,
                  correct: grading.correct_count,
                  wrong: grading.wrong_count
                })}
              </Text>
            </Alert>
          )}
          
          <Group justify="flex-end">
            <Button variant="outline" onClick={close}>
              {t('common.cancel')}
            </Button>
            <Button 
              color="red" 
              onClick={() => deleteHistoryMutation.mutate()}
              loading={deleteHistoryMutation.isPending}
            >
              {t('profile.deleteHistoryButton')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Card>
  );
}

interface LevelCardProps {
  level: { id: number; title: string; description: string; color: string };
  section: string;
  accessible: boolean;
  icon: React.ComponentType<{ size: number; color: string }>;
  progress?: number;
  total?: number;
  grading?: {
    score?: number;
    max_score?: number;
    correct_count?: number;
    wrong_count?: number;
  };
  isAuthenticated: boolean;
}

function LevelCard({ level, section, accessible, icon: Icon, progress, total, grading, isAuthenticated }: LevelCardProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [opened, { open, close }] = useDisclosure(false);

  // Map level titles to question ranges
  const getQuestionRange = (levelTitle: string) => {
    const ranges: { [key: string]: string } = {
      'A1': 'Q1-Q4',
      'A2': 'Q5-Q10',
      'B1': 'Q11-Q19',
      'B2': 'Q20-Q29',
      'C1': 'Q30-Q35',
      'C2': 'Q36-Q39'
    };
    return ranges[levelTitle] || '';
  };

  // Calculate progress percentage for progress bar
  const progressPercentage = progress && total ? (progress / total) * 100 : 0;
  const hasProgress = progress !== undefined && progress > 0;
  const hasHistory = hasProgress || grading; // Has history if there's progress or grading results

  // Handle click for non-authenticated users
  const handleCardClick = () => {
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { 
          message: t('readingListeningTests.auth.loginRequired'),
          from: `/${section}/group/${level.id}` 
        } 
      });
    }
  };

  // Delete level test history mutation
  const deleteHistoryMutation = useMutation({
    mutationFn: () => testApi.deleteTestHistory(section, `group${level.id}`, false),
    onSuccess: (data) => {
      notifications.show({
        title: t('readingListeningTests.success.historyDeleted'),
        message: t('readingListeningTests.success.levelHistoryDeleted', { level: level.title }),
        color: 'green',
      });
      
      // Invalidate and refetch the section tests data
      queryClient.invalidateQueries({ queryKey: ['section-tests', section] });
      close();
    },
    onError: (error: any) => {
      notifications.show({
        title: t('common.error'),
        message: `${t('profile.deleteError')}: ${error.response?.data?.error || error.message}`,
        color: 'red',
      });
      close();
    },
  });

  return (
    <Card 
      shadow="sm" 
      padding="sm" 
      radius="md" 
      withBorder
      style={{
        background: 'white',
        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
        opacity: accessible ? 1 : 0.7,
        height: '100%',
        position: 'relative'
      }}
    >
      {/* Icon in top left corner */}
      <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
        <Icon size={20} color={level.color} />
      </Box>

      {/* Three dots menu in top right corner */}
      {accessible && hasHistory && (
        <Box style={{ position: 'absolute', top: '8px', right: '8px', zIndex: 10 }}>
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <ActionIcon variant="light" color="gray" size="sm">
                <IconDots size={14} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Item 
                color="red" 
                leftSection={<IconTrash size={14} />}
                onClick={open}
              >
                {t('readingListeningTests.testCard.resetTitle')}
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
          </Box>
      )}

      <Stack gap="xs" style={{ height: '100%', paddingTop: '28px' }}>
        {/* Title with question range */}
        <Box>
          <Text fw={500} size="sm" mb={4}>
            {level.title}({getQuestionRange(level.title)})
          </Text>
        </Box>

        {/* Progress/Results Section */}
        <Box style={{ flex: 1 }}>
          {grading ? (
            <Stack gap="xs">
              <Group justify="space-between">
                <Group gap="xs">
                  <Group gap="2px">
                    <IconCheck size={12} color="green" />
                    <Text size="xs" c="green">{grading.correct_count || 0}</Text>
                  </Group>
                  <Group gap="2px">
                    <IconX size={12} color="red" />
                    <Text size="xs" c="red">{grading.wrong_count || 0}</Text>
                  </Group>
                </Group>
                <Badge size="xs" color="green" variant="light">
                  {grading.score}/{grading.max_score || total}
                </Badge>
              </Group>
              <Progress 
                value={grading.score && grading.max_score ? (grading.score / grading.max_score) * 100 : 0} 
                size="xs" 
                color="green"
              />
              <Text size="xs" c="dimmed" ta="center">
                {t('readingListeningTests.testCard.completedGraded')}
              </Text>
            </Stack>
          ) : hasProgress && total ? (
            <Stack gap="xs">
              <Group justify="space-between">
                <Text size="xs" c="dimmed">{t('profile.progress')}</Text>
                <Badge size="xs" variant="light" color={progressPercentage === 100 ? 'green' : 'blue'}>
                  {progress}/{total}
                </Badge>
              </Group>
              <Progress 
                value={progressPercentage} 
                size="xs" 
                color={progressPercentage === 100 ? 'green' : 'blue'}
              />
              <Text size="xs" c="dimmed" ta="center">
                {progressPercentage === 100 ? t('profile.completed') : `${progressPercentage.toFixed(0)}${t('profile.progressText.completed')}`}
              </Text>
            </Stack>
          ) : (
            <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '40px' }}>
              <Text size="xs" c="dimmed">
                {accessible ? (total ? t('readingListeningTests.testCard.questions', { count: total }) : '') : ''}
              </Text>
            </Box>
          )}
        </Box>

        {/* Action Button */}
        {accessible ? (
          isAuthenticated ? (
          <Button
            component={Link}
              to={`/${section}/group/${level.id}`}
            color="blue"
            leftSection={<IconPlayerPlay size={12} />}
            size="xs"
            fullWidth
            variant={grading ? 'outline' : 'filled'}
          >
              {grading ? t('readingListeningTests.testCard.results') : hasProgress ? t('readingListeningTests.testCard.continue') : t('readingListeningTests.testCard.start')}
          </Button>
        ) : (
          <Button
              onClick={handleCardClick}
              color="blue"
              leftSection={<IconPlayerPlay size={12} />}
              size="xs"
              fullWidth
              variant={grading ? 'outline' : 'filled'}
            >
              {grading ? t('readingListeningTests.testCard.results') : hasProgress ? t('readingListeningTests.testCard.continue') : t('readingListeningTests.testCard.start')}
            </Button>
          )
        ) : (
          <Button
            onClick={!isAuthenticated ? handleCardClick : undefined}
            variant="outline"
            color="orange"
            disabled={isAuthenticated}
            size="xs"
            fullWidth
            leftSection={<IconLock size={12} />}
          >
            {t('readingListeningTests.testCard.subscriptionRequired')}
          </Button>
        )}
      </Stack>
      
      {/* Confirmation Modal for deleting test history */}
      <Modal 
        opened={opened} 
        onClose={close} 
        title={t('readingListeningTests.testCard.resetLevelTitle')}
        centered
      >
        <Stack gap="md">
          <Text>
            {t('readingListeningTests.testCard.resetLevelMessage')} {t('readingListeningTests.testCard.resetLevelWarning')}
          </Text>
          
          <Alert color="orange" variant="light">
            <Text size="sm">
              ⚠️ Test de niveau: {level.title} - {level.description}
              {grading && (
                <span>
                  <br />{t('readingListeningTests.testCard.resetLevelResultsWarning', { 
                    score: grading.score, 
                    maxScore: grading.max_score, 
                    correct: grading.correct_count, 
                    wrong: grading.wrong_count 
                  })}
                </span>
              )}
            </Text>
          </Alert>
          
          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={close}>
              {t('common.cancel')}
            </Button>
            <Button 
              color="red" 
              onClick={() => deleteHistoryMutation.mutate()}
              loading={deleteHistoryMutation.isPending}
              leftSection={<IconTrash size={14} />}
            >
              {t('profile.deleteHistoryButton')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Card>
  );
} 