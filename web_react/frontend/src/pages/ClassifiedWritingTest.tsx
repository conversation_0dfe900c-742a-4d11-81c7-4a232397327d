import { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Title,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Box,
  Paper,
  Divider,
  Alert,
  Loader,
  Grid,
  Breadcrumbs,
  Anchor,
  Unstyled<PERSON>utton,
  <PERSON>lapse
} from '@mantine/core';
import {
  IconPencil,
  IconArrowLeft,
  IconLock,
  IconEdit,
  IconBookmark,
  IconBookmarks,
  IconClock,
  IconTarget,
  IconChevronDown,
  IconChevronUp
} from '@tabler/icons-react';

import { classifiedWritingService } from '../services/classifiedWritingService';
import { useAuthStore } from '../store/useAuthStore';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { classifiedWritingTranslationService, type TacheTranslations } from '../services/classifiedWritingTranslationService';
import { useThemeColors } from '../store/useThemeStore';
import { WritingEditor } from '../components/WritingEditor';
import { ClassifiedWritingNavigation } from '../components/ClassifiedWritingNavigation';


// Import the ExpandedSidebarContent component from ClassifiedWritingNavigation
// We'll need to export it from that file
const ExpandedSidebarContent = ({ currentTacheData, loading, currentSubtopicId, onNavigate, themeColors, onKeyDown, expandedTopics, onToggleTopicExpansion, translations, tacheNumber }: any) => {
  const { t, i18n } = useTranslation();

  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  if (loading) {
    return (
      <Group justify="center" p="xl">
        <Loader size="sm" color="orange" />
        <Text size="sm" c="dimmed">
          {t('classifiedWriting.loading.navigation', 'Chargement...')}
        </Text>
      </Group>
    );
  }

  if (!currentTacheData) {
    return (
      <Text size="sm" c="dimmed" ta="center" p="xl">
        {t('classifiedWriting.noData.message', 'Aucune donnée disponible')}
      </Text>
    );
  }

  return (
    <Stack gap="xs">
      {/* Tâche Header */}
      <Box
        style={{
          padding: '12px',
          backgroundColor: themeColors.background,
          borderRadius: '8px',
          border: `1px solid ${themeColors.border}`,
          marginBottom: '8px'
        }}
      >
        <Group gap="sm">
          <IconPencil size={18} color={getTacheColor(currentTacheData.tache_number)} />
          <Text fw={600} size="sm">
            Tâche {currentTacheData.tache_number}
          </Text>
          <Badge size="xs" color={getTacheColor(currentTacheData.tache_number)} variant="light">
            {currentTacheData.metadata?.total_tasks || 0}
          </Badge>
        </Group>
      </Box>

      {/* Topics List */}
      {currentTacheData.topics?.map((topic: any) => {
        const isExpanded = expandedTopics.has(topic.topic_name);

        return (
          <Box key={topic.topic_name}>
            {/* Topic Header */}
            <UnstyledButton
              onClick={() => onToggleTopicExpansion(topic.topic_name)}
              onKeyDown={(e) => onKeyDown(e, { type: 'topic', topic_name: topic.topic_name })}
              style={{
                width: '100%',
                padding: '8px 12px',
                borderRadius: '6px',
                backgroundColor: isExpanded ? themeColors.background : 'transparent',
                border: `1px solid ${isExpanded ? themeColors.border : 'transparent'}`,
                transition: 'all 0.2s ease'
              }}
              styles={{
                root: {
                  '&:hover': {
                    backgroundColor: themeColors.background,
                    border: `1px solid ${themeColors.border}`
                  }
                }
              }}
            >
              <Group justify="space-between" wrap="nowrap">
                <Group gap="xs" wrap="nowrap" style={{ flex: 1, minWidth: 0 }}>
                  <IconBookmark size={14} color={themeColors.textSecondary} />
                  <Text size="xs" fw={500} truncate style={{ flex: 1 }}>
                    {getTranslatedTopicName(topic.topic_name, translations, i18n.language)}
                  </Text>
                  <Badge size="xs" variant="outline" color="gray">
                    {topic.subtopics?.length || 0}
                  </Badge>
                </Group>
                {topic.subtopics?.length > 0 && (
                  isExpanded ? (
                    <IconChevronUp size={12} color={themeColors.textSecondary} />
                  ) : (
                    <IconChevronDown size={12} color={themeColors.textSecondary} />
                  )
                )}
              </Group>
            </UnstyledButton>

            {/* Subtopics */}
            {topic.subtopics?.length > 0 && (
              <Collapse in={isExpanded}>
                <Stack gap="xs" pl="md" mt="xs">
                  {topic.subtopics.map((subtopic: any) => {
                    const isActive = currentSubtopicId === subtopic.id;

                    return (
                      <UnstyledButton
                        key={subtopic.id}
                        onClick={() => onNavigate(`/writing/classified_tache${tacheNumber}/${topic.topic_name}/${encodeURIComponent(encodeURIComponent(subtopic.id))}`)}
                        onKeyDown={(e) => onKeyDown(e, { type: 'subtopic', id: subtopic.id, topicName: topic.topic_name })}
                        style={{
                          width: '100%',
                          padding: '6px 8px',
                          borderRadius: '4px',
                          backgroundColor: isActive ? themeColors.primaryColor + '20' : 'transparent',
                          border: `1px solid ${isActive ? themeColors.primaryColor + '40' : 'transparent'}`,
                          transition: 'all 0.2s ease'
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: isActive ? themeColors.primaryColor + '30' : themeColors.background,
                              border: `1px solid ${isActive ? themeColors.primaryColor + '60' : themeColors.border}`
                            }
                          }
                        }}
                      >
                        <Group justify="space-between" wrap="nowrap">
                          <Group gap="xs" wrap="nowrap" style={{ flex: 1, minWidth: 0 }}>
                            <IconEdit size={12} color={isActive ? themeColors.primaryColor : themeColors.textSecondary} />
                            <Text
                              size="xs"
                              truncate
                              style={{ flex: 1 }}
                              c={isActive ? themeColors.primaryColor : themeColors.text}
                              fw={isActive ? 500 : 400}
                            >
                              {getTranslatedSubtopicName(subtopic.subtopic_name, translations, i18n.language)}
                            </Text>
                          </Group>
                          <Badge
                            size="xs"
                            variant="light"
                            color={isActive ? "blue" : "gray"}
                          >
                            {subtopic.task_count || 0}
                          </Badge>
                        </Group>
                      </UnstyledButton>
                    );
                  })}
                </Stack>
              </Collapse>
            )}
          </Box>
        );
      })}
    </Stack>
  );
};

// Helper functions to get translated names using the translation service
const getTranslatedTopicName = (topicName: string, translations: TacheTranslations | null | undefined, currentLanguage: string): string => {
  return classifiedWritingTranslationService.getTopicTranslation(topicName, currentLanguage, translations || undefined);
};

const getTranslatedSubtopicName = (subtopicName: string, translations: TacheTranslations | null | undefined, currentLanguage: string): string => {
  // Normalize subtopic name to match translation keys (replace spaces with underscores, lowercase)
  const normalizedSubtopicName = subtopicName.toLowerCase().replace(/\s+/g, '_');
  return classifiedWritingTranslationService.getSubtopicTranslation(normalizedSubtopicName, currentLanguage, translations || undefined);
};

interface SubtopicTemplateCardProps {
  subtopic: any;
  tacheNumber: number;
  topicName: string;
  sampleTasks: any[];
  translations?: TacheTranslations | null;
}

function SubtopicTemplateCard({ subtopic, tacheNumber, topicName, sampleTasks, translations }: SubtopicTemplateCardProps) {
  const [showWritingEditor, setShowWritingEditor] = useState(false);
  const [showExamples, setShowExamples] = useState(false);
  const { isMember, isMembershipExpired, isAuthenticated } = useAuthStore();
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const canWrite = true; // Temporarily enabled for testing

  const handleSubmissionSaved = (submissionId: string) => {
    // Draft saved successfully
  };

  const handleSubmissionSubmitted = (submissionId: string) => {
    setShowWritingEditor(false);
    console.log('Submission submitted:', submissionId);
  };

  // Get tâche color
  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  const tacheColor = getTacheColor(tacheNumber);

  return (
    <Card shadow="sm" padding="lg" radius="md" withBorder>
      <Stack gap="md">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Group gap="xs">
            <Badge color={tacheColor} variant="light">
              Tâche {tacheNumber}
            </Badge>
            <Badge color="blue" variant="outline" size="sm">
              {t('classifiedWriting.template.label', 'Template')}
            </Badge>
            <Badge color="gray" variant="light" size="sm">
              {sampleTasks.length} {t('classifiedWriting.examples.count', 'exemples')}
            </Badge>
          </Group>
          <Group gap="xs">
            <IconClock size={16} color={themeColors.textSecondary} />
            <Text size="sm" c="dimmed">60 min</Text>
          </Group>
        </Group>

        {/* Template Title */}
        <Box>
          <Text fw={600} size="lg" mb="xs">
            {getTranslatedSubtopicName(subtopic.subtopic_name, translations, i18n.language)}
          </Text>
          <Text size="sm" c="dimmed">
            {t('classifiedWriting.template.description', 'Écrivez une réponse qui couvre ce type de tâche. Votre réponse sera applicable à tous les exemples similaires.')}
          </Text>
        </Box>

        {/* Sample Tasks Toggle */}
        <Box>
          <UnstyledButton
            onClick={() => setShowExamples(!showExamples)}
            style={{
              width: '100%',
              padding: '8px',
              borderRadius: '6px',
              backgroundColor: showExamples ? themeColors.surface : 'transparent',
              transition: 'background-color 0.2s ease'
            }}
          >
            <Group justify="space-between">
              <Group gap="xs">
                <IconBookmark size={16} color={themeColors.textSecondary} />
                <Text size="sm" fw={500}>
                  {t('classifiedWriting.template.viewExamples', 'Voir les exemples de tâches')}
                </Text>
              </Group>
              {showExamples ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
            </Group>
          </UnstyledButton>

          <Collapse in={showExamples}>
            <Stack gap="xs" mt="md">
              {sampleTasks.slice(0, 3).map((task, index) => (
                <Paper key={index} p="sm" bg={themeColors.surface} radius="md">
                  <Stack gap="xs">
                    <Text size="xs" c="dimmed">
                      {t('classifiedWriting.examples.example', 'Exemple')} {index + 1}:
                    </Text>
                    <Text size="sm" style={{ lineHeight: 1.5 }}>
                      {task.task_content}
                    </Text>
                    
                    {/* Always show task IDs in consistent format */}
                    <Stack gap="xs">
                      <Text size="xs" fw={500} c="dimmed">
                        {task.is_duplicate_group && task.task_ids && task.task_ids.length > 1 
                          ? t('classifiedWriting.examples.taskIds', 'Identifiants des tâches') + ` (${task.task_ids.length}):`
                          : t('classifiedWriting.examples.taskId', 'Identifiant de la tâche') + ':'
                        }
                      </Text>
                      <Group gap="4px" style={{ flexWrap: 'wrap' }}>
                        {task.is_duplicate_group && task.task_ids && task.task_ids.length > 1 ? (
                          task.task_ids.map((taskId: string, idIndex: number) => (
                            <Badge key={idIndex} size="xs" variant="outline" color="gray" style={{ fontSize: '10px' }}>
                              {taskId}
                            </Badge>
                          ))
                        ) : (
                          <Badge size="xs" variant="outline" color="gray" style={{ fontSize: '10px' }}>
                            {task.representative_id || task.task_ids?.[0] || 'N/A'}
                          </Badge>
                        )}
                      </Group>
                    </Stack>
                  </Stack>
                </Paper>
              ))}
              {sampleTasks.length > 3 && (
                <Text size="xs" c="dimmed" ta="center">
                  {t('classifiedWriting.examples.moreExamples', '... et {{count}} autres exemples similaires', { count: sampleTasks.length - 3 })}
                </Text>
              )}
            </Stack>
          </Collapse>
        </Box>

        {/* Word Limits */}
        <Group gap="md">
          <Group gap="xs">
            <IconTarget size={16} color={themeColors.textSecondary} />
            <Text size="sm" c="dimmed">
              {t('classifiedWriting.task.wordLimit', 'Mots')}:
              {tacheNumber === 1 && ' 60-120'}
              {tacheNumber === 2 && ' 120-150'}
              {tacheNumber === 3 && ' 120-180'}
            </Text>
          </Group>
        </Group>

        {/* Classification Info */}
        <Group gap="md">
          <Badge color="gray" variant="outline" size="sm">
            {getTranslatedTopicName(topicName, translations, i18n.language)}
          </Badge>
          <Badge color="gray" variant="light" size="sm">
            {getTranslatedSubtopicName(subtopic.subtopic_name, translations, i18n.language)}
          </Badge>
        </Group>

        {/* Action Button */}
        <Divider />
        <Group justify="center">
          {canWrite ? (
            <Button
              leftSection={<IconEdit size={16} />}
              onClick={() => setShowWritingEditor(true)}
              color={tacheColor}
              variant="light"
              size="md"
            >
              {t('classifiedWriting.template.startWriting', 'Écrire pour ce template')}
            </Button>
          ) : (
            <Button
              leftSection={<IconLock size={16} />}
              component={Link}
              to="/membership"
              color="orange"
              variant="outline"
              size="md"
            >
              {t('classifiedWriting.task.membershipRequired', 'Abonnement requis')}
            </Button>
          )}
        </Group>
      </Stack>

      {/* Writing Editor Section */}
      {showWritingEditor && (
        <Box
          mt="md"
          p="md"
          style={{
            backgroundColor: themeColors.surfaceHover,
            borderRadius: '8px',
            border: `1px solid ${themeColors.border}`
          }}
        >
          <Text size="xs" fw={500} c={tacheColor} mb="xs">
            {t('classifiedWriting.template.writeResponse', 'Écrire votre réponse')}:
          </Text>
          <WritingEditor
            testType="writing"
            testIdentifier={`classified_tache${tacheNumber}`}
            taskNumber={parseInt(subtopic.id)}
            combinationNumber={`tache_${tacheNumber}_${subtopic.id}`}
            placeholder={`Template: ${getTranslatedSubtopicName(subtopic.subtopic_name, translations, i18n.language)}\n\nExemples de tâches:\n${sampleTasks.slice(0, 3).map((task, i) => `${i + 1}. ${task.task_content}`).join('\n\n')}`}
            minWords={tacheNumber === 1 ? 60 : 120}
            maxWords={tacheNumber === 1 ? 120 : tacheNumber === 2 ? 150 : 180}
            onSubmissionSaved={handleSubmissionSaved}
            onSubmissionSubmitted={handleSubmissionSubmitted}
          />
        </Box>
      )}
    </Card>
  );
}

export function ClassifiedWritingTest() {
  const { topicName, subtopicId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();

  // Extract tâche number from the URL path
  const getTacheNumberFromPath = () => {
    const path = location.pathname;
    const match = path.match(/classified_tache(\d+)/);

    if (!match) {
      // Redirect to tâche 1 if no valid tâche number found
      navigate('/writing/classified_tache1', { replace: true });
      return '1';
    }

    const tacheNum = match[1];

    // Validate tâche number (should be 1, 2, or 3)
    if (!['1', '2', '3'].includes(tacheNum)) {
      navigate('/writing/classified_tache1', { replace: true });
      return '1';
    }

    return tacheNum;
  };

  const tacheNumber = getTacheNumberFromPath();

  // Get tâche color
  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  const tacheColor = getTacheColor(parseInt(tacheNumber || '1'));

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subtopics, setSubtopics] = useState<any[]>([]);
  const [currentTopic, setCurrentTopic] = useState<any>(null);
  const [currentSubtopic, setCurrentSubtopic] = useState<any>(null);
  const [currentTacheData, setCurrentTacheData] = useState<any>(null);
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  const [translations, setTranslations] = useState<TacheTranslations | null>(null);

  // Check if user is premium
  const isPremiumUser = isAuthenticated && isMember() && !isMembershipExpired();

  // Toggle topic expansion
  const toggleTopicExpansion = (topicKey: string) => {
    const newExpanded = new Set(expandedTopics);
    if (newExpanded.has(topicKey)) {
      newExpanded.delete(topicKey);
    } else {
      newExpanded.add(topicKey);
    }
    setExpandedTopics(newExpanded);
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent, item: any) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (item.type === 'topic') {
        toggleTopicExpansion(item.topic_name);
      } else if (item.type === 'subtopic') {
        navigate(`/writing/classified_tache${tacheNumber}/${item.topicName}/${item.id}`);
      }
    }
  };

  useEffect(() => {
    if (tacheNumber) {
      loadTasks();
      loadTranslations();
    } else {
      setError('No tâche number found');
      setLoading(false);
    }
  }, [tacheNumber, topicName, subtopicId]);

  const loadTranslations = async () => {
    if (!tacheNumber) return;

    try {
      const tacheTranslations = await classifiedWritingTranslationService.getTacheTranslations(parseInt(tacheNumber));
      setTranslations(tacheTranslations);
    } catch (error) {
      console.error('Error loading translations:', error);
      setTranslations(null);
    }
  };

  const loadTasks = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!tacheNumber) {
        setError('Invalid tâche number');
        return;
      }

      const currentTacheNum = parseInt(tacheNumber);

      if (subtopicId) {
        // Load specific subtopic as a template
        const response = await classifiedWritingService.getSubtopicTasks(subtopicId);
        setCurrentSubtopic(response.subtopic);
        setCurrentTopic({ topic_name: response.subtopic.main_topic_name });
        // Show this subtopic as a single template
        setSubtopics([{
          ...response.subtopic,
          task_entries: response.tasks || []
        }]);
      } else {
        // Load tâche overview (only once!)
        const response = await classifiedWritingService.getTacheOverview(currentTacheNum);

        // Convert topics object to array format expected by navigation
        const topicsArray = Object.entries(response.topics || {}).map(([topicName, topicData]: [string, any]) => ({
          topic_name: topicName,
          total_tasks: topicData.total_tasks || 0,
          unique_tasks: topicData.unique_tasks || 0,
          subtopics: Object.entries(topicData.subtopics || {}).map(([subtopicName, subtopicData]: [string, any]) => ({
            id: subtopicName,
            subtopic_name: subtopicName,
            task_count: subtopicData.task_count || 0,
            unique_task_count: subtopicData.unique_task_count || 0
          }))
        }));

        setCurrentTacheData({
          tache_number: currentTacheNum,
          metadata: response.metadata || { total_tasks: 0 },
          topics: topicsArray
        });

        // If specific topic is requested, show subtopics for that topic
        if (topicName && response.topics) {
          const topicData = response.topics[topicName];
          if (topicData) {
            setCurrentTopic({ topic_name: topicName });
            // Convert subtopics to template format
            const topicSubtopics = Object.values(topicData.subtopics || {}).map((subtopic: any) => ({
              ...subtopic,
              task_entries: subtopic.task_entries || []
            }));
            setSubtopics(topicSubtopics);
          }
        } else {
          // Show all subtopics for the tâche as templates
          const allSubtopics: any[] = [];
          Object.values(response.topics || {}).forEach((topic: any) => {
            Object.values(topic.subtopics || {}).forEach((subtopic: any) => {
              allSubtopics.push({
                ...subtopic,
                task_entries: subtopic.task_entries || [],
                topic_name: topic.topic_name
              });
            });
          });
          setSubtopics(allSubtopics);
        }
      }

    } catch (err) {
      console.error('Error loading tasks:', err);
      setError(t('classifiedWriting.errors.loadTasks', 'Erreur lors du chargement des tâches'));
    } finally {
      setLoading(false);
    }
  };



  // Error boundary for invalid routes
  if (!tacheNumber || !['1', '2', '3'].includes(tacheNumber)) {
    return (
      <Box style={{ minHeight: '100vh', position: 'relative' }}>
        <Container size="xl" py="xl">
          <Group justify="center">
            <Text color="red">Invalid tâche number. Redirecting...</Text>
          </Group>
        </Container>
      </Box>
    );
  }

  return renderClassifiedWritingContent();

  function renderClassifiedWritingContent() {

  if (loading) {
    return (
      <Box style={{ minHeight: '100vh', position: 'relative' }}>
        {/* Always show navigation during loading */}
        <ClassifiedWritingNavigation />
        <Container size="xl" py="xl">
          <Group justify="center">
            <Loader size="lg" />
            <Text>{t('classifiedWriting.loading.tasks', 'Chargement des tâches...')}</Text>
          </Group>
        </Container>
      </Box>
    );
  }

  if (error) {
    return (
      <Box style={{ minHeight: '100vh', position: 'relative' }}>
        {/* Navigation - Only show when viewing specific topics/subtopics */}
        {(topicName || subtopicId) && <ClassifiedWritingNavigation />}
        <Container size="xl" py="xl">
          <Alert color="red" title={t('common.error', 'Erreur')}>
            {error}
          </Alert>
        </Container>
      </Box>
    );
  }

  // If we're on the tâche overview page (no topic or subtopic), show as big navigation page
  if (!topicName && !subtopicId) {
    return (
      <Box style={{ minHeight: '100vh', display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '2rem' }}>
        <Container size="md" style={{ width: '100%', maxWidth: '600px' }}>

          {/* Header */}
          <Stack align="center" mb="xl">
            <Title order={1} ta="center">
              {`Tâche ${tacheNumber} - ${t('classifiedWriting.overview.title', 'Vue d\'ensemble')}`}
            </Title>
            <Text c="dimmed" ta="center" size="lg">
              {t('classifiedWriting.overview.description', 'Sélectionnez un thème pour voir les sous-thèmes disponibles')}
            </Text>
          </Stack>

          {/* Big Navigation Content */}
          <Box
            style={{
              background: themeColors.surface,
              borderRadius: '16px',
              boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
              border: `1px solid ${themeColors.border}`,
              padding: '2rem',
              minHeight: '400px'
            }}
          >
            {/* Navigation Header */}
            <Group justify="center" mb="xl">
              <IconBookmarks size={24} color={themeColors.writing} />
              <Text fw={600} size="xl" c={themeColors.writing}>
                {t('classifiedWriting.navigation.title', 'Navigation')}
              </Text>
            </Group>

            {/* Navigation Content */}
            <ExpandedSidebarContent
              currentTacheData={currentTacheData}
              loading={loading}
              currentSubtopicId={subtopicId}
              onNavigate={(path: string) => navigate(path)}
              themeColors={themeColors}
              onKeyDown={handleKeyDown}
              expandedTopics={expandedTopics}
              onToggleTopicExpansion={toggleTopicExpansion}
              translations={translations}
              tacheNumber={tacheNumber}
            />
          </Box>

          {/* Back Button */}
          <Group justify="center" mt="xl">
            <Button
              leftSection={<IconArrowLeft size={16} />}
              variant="outline"
              component={Link}
              to="/writing"
              size="lg"
            >
              {t('common.back', 'Retour')}
            </Button>
          </Group>
        </Container>
      </Box>
    );
  }

  // For topic/subtopic views, show with floating navigation
  return (
    <Box style={{ minHeight: '100vh', position: 'relative' }}>
      {/* Floating Navigation for topic/subtopic views */}
      <ClassifiedWritingNavigation />

      {/* Main Content */}
      <Box style={{ padding: '1rem' }}>
        <Container size="xl">

          {/* Header */}
          <Group justify="space-between" mb="xl">
            <Stack gap="xs">
              <Title order={2}>
                {currentSubtopic
                  ? getTranslatedSubtopicName(currentSubtopic.subtopic_name, translations, i18n.language)
                  : currentTopic
                    ? getTranslatedTopicName(currentTopic.topic_name, translations, i18n.language)
                    : t('classifiedWriting.title', 'Tâches Classifiées')
                }
              </Title>
              <Text c="dimmed">
                {subtopics.length} {t('classifiedWriting.templates.count', 'templates d\'écriture')}
              </Text>
            </Stack>
            <Button
              leftSection={<IconArrowLeft size={16} />}
              variant="outline"
              component={Link}
              to={`/writing/classified_tache${tacheNumber}`}
            >
              {t('common.back', 'Retour')}
            </Button>
          </Group>

          {/* Subtopic Templates Grid */}
          <Grid>
            {subtopics.map((subtopic, index) => (
              <Grid.Col key={subtopic.id} span={{ base: 12, lg: 6 }}>
                <SubtopicTemplateCard
                  subtopic={subtopic}
                  tacheNumber={parseInt(tacheNumber || '1')}
                  topicName={subtopic.topic_name || currentTopic?.topic_name || ''}
                  sampleTasks={subtopic.task_entries || []}
                  translations={translations}
                />
              </Grid.Col>
            ))}
          </Grid>

          {subtopics.length === 0 && (
            <Paper p="xl" ta="center">
              <Text c="dimmed">
                {t('classifiedWriting.noTemplates', 'Aucun template disponible pour cette sélection')}
              </Text>
            </Paper>
          )}
        </Container>
      </Box>
    </Box>
  );
  }
}
