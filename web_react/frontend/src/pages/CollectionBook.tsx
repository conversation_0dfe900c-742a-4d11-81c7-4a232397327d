import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import {
  Container,
  Title,
  Paper,
  Stack,
  Group,
  Button,
  Text,
  Box,
  Alert,
  Loader,
  Flex,
  Collapse,
  ActionIcon,
  Badge,
  TextInput,
  Radio,
  Image,
  Divider,
  Card,
  SimpleGrid,
  ScrollArea
} from '@mantine/core';
import { 
  IconArrowLeft, 
  IconChevronDown, 
  IconChevronUp, 
  IconSearch,
  IconStar,
  IconCheck,
  IconX,
  IconEye,
  IconEyeOff,
  IconChevronLeft,
  IconNotebook,
  IconChevronRight,
  IconTrophy,
  IconNote,
  IconInfoCircle,
  IconPlayerPlay,
  IconPlayerPause,
  IconBook,
  IconBrain,
  IconLanguage
} from '@tabler/icons-react';
import { testApi, translationApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { notifications } from '@mantine/notifications';
import { BookmarkIcon } from '../components/BookmarkIcon';
import { NotebookSidebar } from '../components/NotebookSidebar';
import { AnalysisSidebar } from '../components/AnalysisSidebar';
import { useTestStore } from '../store/useTestStore';
import { UnifiedTextInteraction } from '../components/UnifiedTextInteraction';
import { useTranslation } from 'react-i18next';
import { useAudioSettings } from '../contexts/AudioSettingsContext';
import { CustomAudioPlayer } from '../components/CustomAudioPlayer';
import type { CustomAudioPlayerRef } from '../components/CustomAudioPlayer';
import { GradingResultsModal } from '../components/GradingResultsModal';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';
import { useAudio } from '../hooks/useMedia';
import { OptimizedImage } from '../components/OptimizedImage';

interface BookmarkedQuestion {
  id: string;
  user_id: string;
  question_id: string;
  test_type: 'reading' | 'listening';
  test_category: string;
  test_identifier: string;
  question_number: string;
  question_data: any;
  correct_answer?: any;
  metadata: any;
  created_at: string;
}

interface CollectionBookData {
  reading_collection: BookmarkedQuestion[];
  listening_collection: BookmarkedQuestion[];
  previous_answers?: Record<string, string | string[]>;
  grading_results?: {
    wrong_details: Array<{ question: string; yourAnswer: string; correctAnswer: string }>;
    correct?: Array<any>;
    incorrect?: Array<any>;
    correct_count?: number;
  };
}

interface GroupedQuestions {
  [testSource: string]: BookmarkedQuestion[];
}

function cleanCollectionBookAnswers(answers: Record<string, string | string[]>): Record<string, string | string[]> {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return Object.fromEntries(
    Object.entries(answers).filter(([key]) => uuidRegex.test(key))
  );
}

export function CollectionBook() {
  const navigate = useNavigate();
  const { user, isMember, isMembershipExpired } = useAuthStore();
  const { answers, setAnswer, setAnswers, clearCurrentTest } = useTestStore();
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [collapsedSections, setCollapsedSections] = useState<{ [key: string]: boolean }>({ reading: true, listening: true });
  const [selectedQuestion, setSelectedQuestion] = useState<BookmarkedQuestion | null>(null);
  
  // Wrong questions state - same as normal tests
  const [wrongQuestions, setWrongQuestions] = useState<string[]>([]); // Changed to string[] for UUIDs
  
  // Audio ref for transcript playback
  const audioRef = useRef<CustomAudioPlayerRef>(null);
  
  // Transcript state for listening questions
  const [showTranscript, setShowTranscript] = useState(false);
  const [showAllTranscript, setShowAllTranscript] = useState(false);
  const [transcriptVisible, setTranscriptVisible] = useState<{ [key: number]: boolean }>({});
  const [activeChunk, setActiveChunk] = useState<number | null>(null);
  const currentChunkListenerRef = useRef<(() => void) | null>(null);

  // Local state to track removed bookmarks
  const [removedBookmarks, setRemovedBookmarks] = useState<Set<string>>(new Set());

  // Get current language from i18n
  const currentLanguage = i18n.language;

  // Set default translation target based on current UI language
  const getDefaultTranslationTarget = (): 'en' | 'zh' => {
    if (currentLanguage.startsWith('zh')) {
      return 'zh'; // Chinese UI -> translate to Chinese
    } else {
      return 'en'; // English/French UI -> translate to English
    }
  };

  // Robust text display system - prioritizes OCR format while preventing wide text
  const prepareTextForDisplay = (text: string): {
    displayText: string;
    useFlexibleWrapping: boolean;
    hasOCRFormat: boolean;
  } => {
    if (!text) return { displayText: '', useFlexibleWrapping: false, hasOCRFormat: false };

    // Clean the text first - remove OCR artifacts but preserve structure
    const cleanedText = text
      .replace(/^```[\r\n]?/, '')   // Remove leading backticks
      .replace(/[\r\n]?```$/, '')   // Remove trailing backticks
      .replace(/^- Text:\s*```\s*/gm, '') // Remove "- Text:" prefixes
      .replace(/^Sure! Here.*?```\s*/gm, '') // Remove OCR artifacts
      .trim();

    // Analyze the text structure to determine if it has good OCR formatting
    const lines = cleanedText.split('\n');
    const nonEmptyLines = lines.filter(line => line.trim().length > 0);

    // Check if text has good OCR structure (multiple lines with reasonable lengths)
    const hasMultipleLines = nonEmptyLines.length > 1;
    const hasReasonableLineLengths = nonEmptyLines.every(line => line.trim().length <= 120);
    const hasVariedLineLengths = nonEmptyLines.some(line => line.trim().length < 80) &&
                                 nonEmptyLines.some(line => line.trim().length > 20);

    // Determine if we have good OCR format
    const hasOCRFormat = hasMultipleLines && hasReasonableLineLengths && hasVariedLineLengths;

    // Check for problematic cases that need flexible wrapping
    const hasVeryLongLines = lines.some(line => line.trim().length > 120);
    const hasSingleLongLine = lines.length === 1 && lines[0].length > 100;
    const hasExcessivelyLongLines = lines.some(line => line.trim().length > 200);

    // Decision logic:
    // 1. If we have good OCR format and no problematic lines -> use OCR format
    // 2. If we have problematic lines -> use flexible wrapping to prevent wide text
    const useFlexibleWrapping = hasVeryLongLines || hasSingleLongLine || hasExcessivelyLongLines;

    return {
      displayText: cleanedText,
      useFlexibleWrapping,
      hasOCRFormat: hasOCRFormat && !useFlexibleWrapping
    };
  };

  // Translation state for listening
  const [selectedTranscriptText, setSelectedTranscriptText] = useState('');
  const [showTranslateButton, setShowTranslateButton] = useState(false);
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationTarget, setTranslationTarget] = useState<'en' | 'zh'>(getDefaultTranslationTarget());
  const [translationResult, setTranslationResult] = useState<{
    text: string;
    language: string;
  } | null>(null);

  // Notebook state - Start collapsed for clean experience
  const [notebookExpanded, setNotebookExpanded] = useState(false);

  // Analysis sidebar state - Start collapsed for clean experience
  const [analysisExpanded, setAnalysisExpanded] = useState(false);

  // Auto-save state
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState<Date | null>(null);

  // Grading results state for navigation colors
  const [gradingResults, setGradingResults] = useState<{
    wrong_details: Array<{ question: string; yourAnswer: string; correctAnswer: string }>;
    correct_count: number;
    original_wrong_details?: Array<{ question: string; yourAnswer: string; correctAnswer: string }>; // Store original UUIDs
  } | null>(null);

  // Modal state for showing results
  const [showResultsModal, setShowResultsModal] = useState(false);

  // Toggle notebook
  const toggleNotebook = () => {
    const newExpanded = !notebookExpanded;
    setNotebookExpanded(newExpanded);
    localStorage.setItem('tcf-notebook-expanded', newExpanded.toString());
  };

  // Toggle analysis sidebar
  const toggleAnalysis = () => {
    const newExpanded = !analysisExpanded;
    setAnalysisExpanded(newExpanded);
    localStorage.setItem('tcf-analysis-expanded', newExpanded.toString());
  };

  // Load collection book data
  const { data: collectionData, isLoading, error, refetch } = useQuery<CollectionBookData>({
    queryKey: ['collection-book'],
    queryFn: () => testApi.getCollectionBook(),
    enabled: !!user,
  });

  // Auto-save mutation - Use 'reading' as test_type to comply with database constraints
  const autoSaveMutation = useMutation({
    mutationFn: async (answersData: Record<string, string | string[]>) => {
      if (!user) return;
      
      const cleaned = cleanCollectionBookAnswers(answersData);
      return await testApi.saveTestHistory({
        section: 'collection_book', // Use 'reading' instead of 'collection_book' for database compatibility
        test_id: 'collection_book_notebook', // Unique identifier for collection book
        free: false,
        answers: cleaned,
        current_question: 0, // Collection book doesn't have a specific current question
      });
    },
    onMutate: () => {
      setIsAutoSaving(true);
    },
    onSuccess: () => {
      setLastSaveTime(new Date());
    },
    onError: (error) => {
      console.error('❌ Failed to auto-save collection book answers:', error);
      // Don't show error notification for auto-save failures to avoid interrupting user experience
    },
    onSettled: () => {
      setIsAutoSaving(false);
    }
  });

  // Submit collection for final review - Use standard test grading
  const submitCollectionMutation = useMutation({
    mutationFn: async () => {
      if (!user || Object.keys(answers).length === 0) return;
      
      // Use the standard test API to calculate scores
      return await testApi.calculateScore({
        section: 'collection_book', // Use 'reading' section for compatibility
        test_id: 'collection_book_notebook',
        answers: answers,
        free: false
      });
    },
    onSuccess: (result) => {
      // Check if result exists before using it
      if (!result) {
        notifications.show({
          title: 'Submission Error',
          message: 'No result received from grading service.',
          color: 'red',
        });
        return;
      }
      
      // Set wrong questions exactly like normal tests do
      if (result?.wrong_details) {
        // For collection book, wrong_details contains UUIDs, not question numbers
        const wrongQuestionIds = result.wrong_details.map((detail: any) => detail.question);
        // Store the UUIDs directly for collection book navigation
        setWrongQuestions(wrongQuestionIds);
      }
      
      // Store grading results for modal
      setGradingResults({
        wrong_details: result.wrong_details || [],
        correct_count: result.correct_count || 0,
        original_wrong_details: result.wrong_details || [] // Store original UUIDs
      });
      
      // Save the final results using standard test history
      testApi.saveTestHistory({
        section: 'collection_book',
        test_id: 'collection_book_notebook', 
        free: false,
        answers: cleanCollectionBookAnswers(answers),
        current_question: 0,
        grading_results: {
          correct: [], // Collection book doesn't need correct/incorrect arrays since it uses UUIDs
          incorrect: [], // Collection book doesn't need correct/incorrect arrays since it uses UUIDs
          wrong_details: result.wrong_details || []
        }
      });
      
      // Show results modal instead of notification and reload
      setShowResultsModal(true);
    },
    onError: (error) => {
      notifications.show({
        title: 'Submission Failed',
        message: 'Please try again or contact support.',
        color: 'red',
      });
    }
  });

  // Load previous answers from session
  useEffect(() => {
    if (collectionData?.previous_answers) {
      setAnswers(collectionData.previous_answers);
    }
    
    // Load existing grading results if available (for previously graded collection book)
    if (collectionData?.grading_results) {

      // For collection book, wrong_details contains UUIDs
      const wrongQuestionIds = collectionData.grading_results.wrong_details?.map((detail: any) => detail.question) || [];
      setWrongQuestions(wrongQuestionIds);
      
      // Store grading results for modal
      setGradingResults({
        wrong_details: collectionData.grading_results.wrong_details || [],
        correct_count: collectionData.grading_results.correct_count || 0,
        original_wrong_details: collectionData.grading_results.wrong_details || [] // Store original UUIDs
      });
      
      // Show notification to user about graded collection book
      if (wrongQuestionIds.length > 0 || (collectionData.grading_results.correct?.length || 0) > 0) {
        notifications.show({
          title: t('test.graded.title'),
          message: `${collectionData.grading_results.correct?.length || 0} ${t('test.graded.correct')} ${wrongQuestionIds.length} ${t('test.graded.incorrect')}`,
          color: 'green',
          autoClose: 5000,
        });
      }
    }
  }, [collectionData, setAnswers, t]);

  // Reset transcript state when question changes
  useEffect(() => {
    setShowTranscript(false);
    setShowAllTranscript(false);
    setTranscriptVisible({});
    setActiveChunk(null);
  }, [selectedQuestion]);

  // Handle bookmark removal - also remove from answers
  const handleBookmarkRemoved = (questionId: string) => {
    setRemovedBookmarks(prev => new Set([...prev, questionId]));
    
    // Remove the answer for this question since it's no longer in collection
    if (answers[questionId]) {
      const newAnswers = { ...answers };
      delete newAnswers[questionId];
      const cleaned = cleanCollectionBookAnswers(newAnswers);
      setAnswers(cleaned);
      
      // Auto-save the updated answers (without the removed question)
      autoSaveMutation.mutate(cleaned);
    }
    
    // If the removed question is currently selected, clear the selection
    if (selectedQuestion?.question_id === questionId) {
      setSelectedQuestion(null);
    }
    
    notifications.show({
      title: t('collection.notifications.removed.title'),
      message: t('collection.notifications.removed.message'),
      color: 'green'
    });
  };

  // Filter out removed bookmarks from the data
  const filterRemovedBookmarks = (questions: BookmarkedQuestion[]) => {
    return questions.filter(q => !removedBookmarks.has(q.question_id));
  };

  const readingQuestions = filterRemovedBookmarks(collectionData?.reading_collection || []);
  const listeningQuestions = filterRemovedBookmarks(collectionData?.listening_collection || []);
  const allQuestions = [...readingQuestions, ...listeningQuestions];

  const { settings, toggleAutoPlay, setPlaybackSpeed } = useAudioSettings();

  // Function to generate user-friendly audio error messages
  const getAudioErrorMessage = (error: string, question: BookmarkedQuestion | null) => {
    if (!question) return error;

    // Check if this is a 403 error (access denied)
    if (error.includes('403') || error.includes('FORBIDDEN')) {
      const isPremiumContent = question.test_category !== 'free';
      const userHasMembership = isMember() && !isMembershipExpired();

      if (isPremiumContent && !userHasMembership) {
        return {
          title: t('collection.audio.premiumRequired.title'),
          message: t('collection.audio.premiumRequired.message'),
          action: t('collection.audio.premiumRequired.action'),
          color: 'orange' as const,
          showUpgradeButton: true
        };
      } else if (isPremiumContent && isMembershipExpired()) {
        return {
          title: t('collection.audio.membershipExpired.title'),
          message: t('collection.audio.membershipExpired.message'),
          action: t('collection.audio.membershipExpired.action'),
          color: 'red' as const,
          showUpgradeButton: true
        };
      }
    }

    // Default technical error
    return {
      title: t('collection.audio.error.title'),
      message: error,
      color: 'red' as const,
      showUpgradeButton: false
    };
  };

  // Audio hook for listening tests - moved to top level to follow Rules of Hooks
  const isListeningTest = selectedQuestion?.test_type === 'listening';
  const audioSection = selectedQuestion?.test_category === 'free' ? `${selectedQuestion?.test_type}_free` : selectedQuestion?.test_type || '';
  const {
    mediaUrl: audioUrl,
    isLoading: isAudioLoading,
    error: audioError
  } = useAudio(
    isListeningTest && selectedQuestion?.question_data?.audio_path ? selectedQuestion.question_data.audio_path : null,
    audioSection,
    selectedQuestion?.test_identifier || ''
  );

  // Auto-play functionality when audio ends
  const handleAudioEnd = useCallback(() => {
    if (settings.autoPlayEnabled && selectedQuestion) {
      const currentIndex = allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id);
      // Only advance if there's a next question
      if (currentIndex < allQuestions.length - 1) {
        // Add a small delay for better UX, then advance to next question
        setTimeout(() => {
          goToNextQuestion();
        }, 1500);
      }
    }
  }, [settings.autoPlayEnabled, selectedQuestion, allQuestions]);

  // Clear translation state when selected question changes
  useEffect(() => {
    if (selectedQuestion?.test_type === 'listening') {
      setSelectedTranscriptText('');
      setShowTranslateButton(false);
      setTranslationResult(null);
      // Clear any text selection
      window.getSelection()?.removeAllRanges();
    }
  }, [selectedQuestion]);

  // Handle text selection in transcript
  const handleTranscriptTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString().trim();
      const range = selection.getRangeAt(0);

      // Check if selection is within transcript area
      let isWithinTranscript = false;

      // Check if selection is within the transcript container
      const transcriptContainer = document.querySelector('.transcript-container');
      if (transcriptContainer && transcriptContainer.contains(range.commonAncestorContainer)) {
        isWithinTranscript = true;
      }

      // Also check individual transcript cards
      if (!isWithinTranscript) {
        const transcriptCards = document.querySelectorAll('.transcript-card');
        transcriptCards.forEach(card => {
          if (card.contains(range.commonAncestorContainer) ||
              card.contains(range.startContainer) ||
              card.contains(range.endContainer)) {
            isWithinTranscript = true;
          }
        });
      }

      if (isWithinTranscript) {

        setSelectedTranscriptText(selectedText);
        setShowTranslateButton(true);
      } else {
        setShowTranslateButton(false);
      }
    } else {
      setShowTranslateButton(false);
    }
  };

  // Translation function
  const handleTranslateText = async () => {
    if (!selectedTranscriptText.trim()) return;

    setIsTranslating(true);
    try {
      const result = await translationApi.translate(selectedTranscriptText, translationTarget, 'fr');

      if (result.success && result.translated_text) {
        setTranslationResult({
          text: result.translated_text,
          language: translationTarget === 'en' ? 'English' : '中文'
        });

        notifications.show({
          title: 'Translation Complete',
          message: `Translated to ${translationTarget === 'en' ? 'English' : 'Chinese'}`,
          color: 'green',
        });
      } else {
        notifications.show({
          title: 'Translation Failed',
          message: result.error || 'Unknown error occurred',
          color: 'red',
        });
      }
    } catch (error) {
      console.error('Translation error:', error);
      notifications.show({
        title: 'Translation Error',
        message: 'Failed to translate text. Please try again.',
        color: 'red',
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Toggle translation target language
  const toggleTranslationTarget = () => {
    setTranslationTarget(prev => prev === 'en' ? 'zh' : 'en');
    setTranslationResult(null); // Clear previous result when switching
  };

  // Update translation target when language changes
  useEffect(() => {
    const newTarget = getDefaultTranslationTarget();
    setTranslationTarget(newTarget);
    // Clear previous translation when language changes
    setTranslationResult(null);
  }, [currentLanguage]);

  // Add event listeners for text selection
  useEffect(() => {
    if (selectedQuestion?.test_type === 'listening' && showTranscript) {
      document.addEventListener('mouseup', handleTranscriptTextSelection);
      document.addEventListener('keyup', handleTranscriptTextSelection);

      return () => {
        document.removeEventListener('mouseup', handleTranscriptTextSelection);
        document.removeEventListener('keyup', handleTranscriptTextSelection);
      };
    }
  }, [selectedQuestion, showTranscript]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle navigation if no input/textarea is focused
      if (document.activeElement?.tagName === 'INPUT' ||
          document.activeElement?.tagName === 'TEXTAREA' ||
          document.activeElement?.getAttribute('contenteditable') === 'true') {
        return;
      }

      if (event.key === 'ArrowLeft' && selectedQuestion) {
        event.preventDefault();
        goToPreviousQuestion();
      } else if (event.key === 'ArrowRight' && selectedQuestion) {
        event.preventDefault();
        goToNextQuestion();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedQuestion, allQuestions]);

  // Cleanup chunk listener on unmount or audio change
  useEffect(() => {
    return () => {
      if (currentChunkListenerRef.current && audioRef.current?.audioElement) {
        audioRef.current.audioElement.removeEventListener('timeupdate', currentChunkListenerRef.current);
        currentChunkListenerRef.current = null;
      }
    };
  }, [audioUrl]); // Clean up when audio changes

  if (!user) {
    return (
      <Container size="lg" py="xl">
        <Alert
          variant="light"
          color="orange"
          title={t('collection.errors.loginRequired')}
          icon={<IconInfoCircle />}
        >
          {t('collection.errors.loginRequired')}
        </Alert>
      </Container>
    );
  }

  if (isLoading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="blue" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.collection')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert
          variant="light"
          color="red"
          title={t('common.error')}
          icon={<IconInfoCircle />}
        >
          {t('collection.errors.loadingError')}
        </Alert>
      </Container>
    );
  }

  // Group questions by test source (test_type, test_identifier, test_category)
  const groupQuestionsBySource = (questions: BookmarkedQuestion[]) => {
    const grouped: GroupedQuestions = {};
    
    questions.forEach(question => {
      // Use both test_identifier and test_category as the key
      const testKey = `${question.test_type}_${question.test_identifier}_${question.test_category}`;
      if (!grouped[testKey]) {
        grouped[testKey] = [];
      }
      grouped[testKey].push(question);
    });

    // Sort questions within each group by question number
    Object.keys(grouped).forEach(key => {
      grouped[key].sort((a, b) => parseInt(a.question_number) - parseInt(b.question_number));
    });

    return grouped;
  };

  const groupedReading = groupQuestionsBySource(readingQuestions);
  const groupedListening = groupQuestionsBySource(listeningQuestions);

  const totalQuestions = allQuestions.length;

  // Filter questions based on search
  const filterQuestions = (questions: BookmarkedQuestion[]) => {
    if (!searchQuery.trim()) return questions;
    return questions.filter(q => 
      q.test_identifier.toLowerCase().includes(searchQuery.toLowerCase()) ||
      q.question_number.includes(searchQuery)
    );
  };

  const toggleSection = (section: string) => {
    setCollapsedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handleAnswerChange = (questionId: string, value: string) => {
    // Use the question UUID directly as the key instead of sequential numbers
    setAnswer(questionId, value);
    
    // Auto-save answers after setting the answer using UUID keys
    const updatedAnswers = { ...answers, [questionId]: value };
    const cleaned = cleanCollectionBookAnswers(updatedAnswers);
    autoSaveMutation.mutate(cleaned);
  };

  const selectQuestion = (question: BookmarkedQuestion) => {
    setSelectedQuestion(question);
  };

  // Navigation functions
  const goToPreviousQuestion = () => {
    if (!selectedQuestion) return;

    const currentIndex = allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id);
    if (currentIndex > 0) {
      setSelectedQuestion(allQuestions[currentIndex - 1]);
      // Clear transcript and translation state when navigating
      setShowTranscript(false);
      setActiveChunk(null);
      setSelectedTranscriptText('');
      setShowTranslateButton(false);
      setTranslationResult(null);
      window.getSelection()?.removeAllRanges();
    }
  };

  const goToNextQuestion = () => {
    if (!selectedQuestion) return;

    const currentIndex = allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id);
    if (currentIndex < allQuestions.length - 1) {
      setSelectedQuestion(allQuestions[currentIndex + 1]);
      // Clear transcript and translation state when navigating
      setShowTranscript(false);
      setActiveChunk(null);
      setSelectedTranscriptText('');
      setShowTranslateButton(false);
      setTranslationResult(null);
      window.getSelection()?.removeAllRanges();
    }
  };

  // Transcript functions
  const toggleTranscriptChunk = (index: number) => {
    setTranscriptVisible(prev => ({
      ...prev,
      [index]: !prev[index]
    }));
  };

  const toggleAllTranscript = () => {
    const newShowAll = !showAllTranscript;
    setShowAllTranscript(newShowAll);
    
    if (selectedQuestion?.question_data?.chunks) {
      const newVisible: { [key: number]: boolean } = {};
      selectedQuestion.question_data.chunks.forEach((_: any, index: number) => {
        newVisible[index] = newShowAll;
      });
      setTranscriptVisible(newVisible);
    }
  };

  const playChunk = (chunk: { start: number; end: number; text: string }) => {
    if (!audioRef.current?.audioElement) return;

    // Remove any existing chunk listener to prevent conflicts
    if (currentChunkListenerRef.current) {
      audioRef.current.audioElement.removeEventListener('timeupdate', currentChunkListenerRef.current);
      currentChunkListenerRef.current = null;
    }

    setActiveChunk(chunk.start);
    audioRef.current.audioElement.currentTime = chunk.start;
    audioRef.current.audioElement.play();

    const onTimeUpdate = () => {
      if (audioRef.current?.audioElement && audioRef.current.audioElement.currentTime >= chunk.end + 0.5) {
        audioRef.current.pause(); // Use the CustomAudioPlayer's pause method to update state
        audioRef.current.audioElement.removeEventListener('timeupdate', onTimeUpdate);
        currentChunkListenerRef.current = null;
        setActiveChunk(null);
      }
    };

    // Store the listener reference and add it
    currentChunkListenerRef.current = onTimeUpdate;
    audioRef.current.audioElement.addEventListener('timeupdate', onTimeUpdate);
  };

  // Update getTestDisplayName to only show (Gratuit) for free tests, otherwise just 'Test 1'
  const getTestDisplayName = (testType: string, testIdentifier: string, testCategory: string) => {
    if (testIdentifier.startsWith('group')) {
      const groupNum = testIdentifier.replace('group', '');
      const levelMap: { [key: string]: string } = {
        '1': 'A1-A2', '2': 'B1', '3': 'B2', '4': 'C1', '5': 'C2'
      };
      return `Groupe ${groupNum} (${levelMap[groupNum] || `Niveau ${groupNum}`})`;
    }
    // Clean up test identifier to remove redundant "test" prefix
    const formatTestId = (testId: string): string => {
      const match = testId.match(/^test(\d+)$/i);
      if (match) {
        return match[1]; // Return just the number
      }
      return testId;
    };
    const cleanTestId = formatTestId(testIdentifier);
    // Only show (Gratuit) for free tests
    return testCategory === 'free' ? `Test ${cleanTestId} (Gratuit)` : `Test ${cleanTestId}`;
  };

  const getAssetUrl = (path: string, question: BookmarkedQuestion) => {
    if (!path) return '';
    
    const isGroupTest = question.test_identifier.startsWith('group');
    const section = question.test_type;
    const testIdentifier = question.test_identifier;
    const isFree = question.test_category === 'free';
    
    if (isGroupTest) {
      // For group tests, parse the full path to extract asset directory, media directory and filename
      // Path format: "listening_asset_free/media_test2/Q1.mp3" or "listening_asset/media_test28/Q1.mp3"
      const pathParts = path.split('/');
      if (pathParts.length >= 3) {
        const assetDir = pathParts[0]; // e.g., "listening_asset_free" or "listening_asset"
        const mediaDir = pathParts[1]; // e.g., "media_test2"
        const filename = pathParts[2]; // e.g., "Q1.mp3"
        
        // Determine the correct section based on the asset directory
        let assetSection = section;
        if (assetDir.endsWith('_free')) {
          assetSection = section + '_free'; // e.g., "listening_free"
        }
        
        // Use the media directory as the "test identifier" for the backend
        return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${assetSection}/${mediaDir}/${filename}`;
      } else {
        // Fallback: just use the filename
        const filename = path.split('/').pop();
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${section}/${testIdentifier}/${filename}`;
      }
    } else {
      const filename = path.split('/').pop();
      const assetSection = isFree ? `${section}_free` : section;
      return `${import.meta.env.VITE_API_URL || 'http://localhost:5001/api'}/assets/${assetSection}/${testIdentifier}/${filename}`;
    }
  };

  const renderQuestionBoxes = (questions: BookmarkedQuestion[], align: 'left' | 'right' = 'left') => {
    return (
      <Group
        gap={4}
        justify={align === 'right' ? 'flex-end' : 'flex-start'}
        style={{ width: '100%' }}
      >
        {questions.map((question) => {
          const questionId = question.question_id;
          const isSelected = selectedQuestion?.question_id === questionId;
          const isAnswered = !!answers[questionId]; // Check if question has been answered
          const isRemoved = removedBookmarks.has(questionId);

          // Check if this question is wrong - exactly like normal tests
          const isWrong = wrongQuestions.includes(questionId);

          return (
            <Button
              key={questionId}
              size="xs"
              w={28}
              h={28}
              p={0}
              variant={isSelected ? "filled" : "outline"}
              color={isRemoved ? "gray" : isWrong ? "red" : isAnswered ? "green" : isSelected ? "blue" : "gray"}
              onClick={() => !isRemoved && selectQuestion(question)}
              disabled={isRemoved}
              style={{
                backgroundColor: isRemoved
                  ? themeColors.surfaceHover
                  : isWrong
                    ? (resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0')
                    : isAnswered
                      ? (resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed')
                      : isSelected
                        ? themeColors.primary
                        : themeColors.surfaceHover,
                color: isRemoved
                  ? themeColors.textDimmed
                  : isWrong
                    ? themeColors.speaking
                    : isAnswered
                      ? themeColors.reading
                      : isSelected
                        ? '#fff'
                        : themeColors.textPrimary,
                border: `2px solid ${isRemoved
                  ? themeColors.border
                  : isWrong
                    ? themeColors.speaking
                    : isAnswered
                      ? themeColors.reading
                      : isSelected
                        ? themeColors.primary
                        : themeColors.border}`,
                cursor: isRemoved ? 'not-allowed' : 'pointer',
                opacity: isRemoved ? 0.6 : 1,
                fontSize: '12px',
                minWidth: '28px',
                minHeight: '28px',
                flexShrink: 0
              }}
            >
              {question.question_number}
            </Button>
          );
        })}
      </Group>
    );
  };

  const renderSelectedQuestion = () => {
    if (!selectedQuestion) {
      return (
        <Paper
          p="xl"
          shadow="sm"
          radius="md"
          style={{
            textAlign: 'center',
            backgroundColor: themeColors.surface,
            border: `1px solid ${themeColors.border}`,
          }}
        >
          <Text c="dimmed" size="lg">
            {t('collection.practice.selectQuestion')}
          </Text>
        </Paper>
      );
    }

    const questionId = selectedQuestion.question_id;
    const questionData = selectedQuestion.question_data;
    const userAnswer = answers[questionId];
    // const showAnswer = showAnswers[questionId];
    // const result = practiceResults[questionId];
    const isListeningTest = selectedQuestion.test_type === 'listening';

    // Audio hook moved to component top level to follow Rules of Hooks

    if (!questionData) {
      return (
        <Alert color="orange">
          {t('collection.errors.noData')} {selectedQuestion.question_number}
        </Alert>
      );
    }

    return (
      <Box style={{ flex: 'flex-grow', flexWrap: 'nowrap', display: 'flex', justifyContent: 'center'}}>
        {/* Analysis - Extends from button's middle position */}
        {analysisExpanded && (
          <Box
            style={{
              zIndex: 10,
              padding: '1rem',
            }}
          >
            <AnalysisSidebar
              section={selectedQuestion.test_type}
              testId={selectedQuestion.test_identifier}
              currentQuestion={parseInt(selectedQuestion.question_number)}
              isGroupTest={selectedQuestion.test_identifier.startsWith('group')}
              isFree={selectedQuestion.test_category === 'free'}
              isExpanded={analysisExpanded}
              onToggle={toggleAnalysis}
            />
          </Box>
        )}

        {/* Question Content */}
        <Paper
          p="xl"
          shadow="sm"
          radius="md"
          style={{
            position: 'relative',
            flex: 1,
            backgroundColor: themeColors.surface,
            border: `1px solid ${themeColors.border}`,
          }}
        >
          {/* Analysis Sidebar Toggle Button */}
          {(selectedQuestion?.test_type === 'reading' || selectedQuestion?.test_type === 'listening') && (
            <Box
              onClick={toggleAnalysis}
              style={{
                position: 'absolute',
                left: analysisExpanded ? '-20px' : '-20px',
                top: '50%',
                transform: 'translateY(-50%)',
                width: '40px',
                height: '40px',
                backgroundColor: themeColors.primary,
                border: `2px solid ${themeColors.primaryHover}`,
                borderRadius: '50%',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 20,
                transition: 'all 0.3s ease',
                boxShadow: `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.4)' : 'rgba(0, 123, 255, 0.3)'}`
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = themeColors.primaryHover;
                e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';
                e.currentTarget.style.boxShadow = `0 6px 16px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.5)' : 'rgba(0, 123, 255, 0.4)'}`;
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = themeColors.primary;
                e.currentTarget.style.transform = 'translateY(-50%)';
                e.currentTarget.style.boxShadow = `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.4)' : 'rgba(0, 123, 255, 0.3)'}`;
              }}
            >
              {analysisExpanded ? (
                <IconChevronLeft size={20} color="white" />
              ) : (
                <IconBrain size={20} color="white" />
              )}
            </Box>
          )}

          {/* Notebook Toggle Button */}
          <Box
            onClick={toggleNotebook}
            style={{
              position: 'absolute',
              right: notebookExpanded ? '-20px' : '-20px',
              top: '50%',
              transform: 'translateY(-50%)',
              width: '40px',
              height: '40px',
              backgroundColor: themeColors.primary,
              border: `2px solid ${themeColors.primaryHover}`,
              borderRadius: '50%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 20,
              transition: 'all 0.3s ease',
              boxShadow: `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.4)' : 'rgba(0, 123, 255, 0.3)'}`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = themeColors.primaryHover;
              e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';
              e.currentTarget.style.boxShadow = `0 6px 16px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.5)' : 'rgba(0, 123, 255, 0.4)'}`;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = themeColors.primary;
              e.currentTarget.style.transform = 'translateY(-50%)';
              e.currentTarget.style.boxShadow = `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.4)' : 'rgba(0, 123, 255, 0.3)'}`;
            }}
          >
            {notebookExpanded ? (
              <IconChevronRight size={20} color="white" />
            ) : (
              <IconNotebook size={20} color="white" />
            )}
          </Box>

          {/* Interactive Bookmark Icon */}
          {selectedQuestion?.question_id && (
            <BookmarkIcon 
              questionId={selectedQuestion.question_id}
              size="md"
              onBookmarkChange={(isBookmarked: boolean) => {
                if (!isBookmarked) {
                  handleBookmarkRemoved(selectedQuestion.question_id);
                }
              }}
            />
          )}

          <Stack gap="md">
            <Text fw={600} size="xl">Question {selectedQuestion.question_number}</Text>
            
            {/* Image - Only for listening tests, optimized to prevent layout shifts */}
            {isListeningTest && questionData.image_path && (
              <Group justify="center">
                <OptimizedImage
                  src={getAssetUrl(questionData.image_path, selectedQuestion)}
                  alt={`Question ${selectedQuestion.question_number}`}
                  maxWidth={400}
                  maxHeight={320}
                  aspectRatio={4/3}
                />
              </Group>
            )}

            {/* Audio Player - For listening tests */}
            {isListeningTest && questionData.audio_path && (
              <>
                {audioError ? (() => {
                  const errorInfo = getAudioErrorMessage(audioError, selectedQuestion);
                  return (
                    <Alert
                      color={typeof errorInfo === 'object' ? errorInfo.color : 'red'}
                      title={typeof errorInfo === 'object' ? errorInfo.title : 'Error'}
                      style={{ margin: '1.2rem 0 1.5rem 0' }}
                    >
                      <Stack gap="sm">
                        <Text>{typeof errorInfo === 'object' ? errorInfo.message : errorInfo}</Text>
                        {typeof errorInfo === 'object' && errorInfo.action && (
                          <Text size="sm" c="dimmed">{errorInfo.action}</Text>
                        )}
                        {typeof errorInfo === 'object' && errorInfo.showUpgradeButton && (
                          <Button
                            size="sm"
                            variant="light"
                            color="blue"
                            onClick={() => navigate('/membership')}
                            style={{ alignSelf: 'flex-start' }}
                          >
                            {t('collection.audio.upgradeButton')}
                          </Button>
                        )}
                      </Stack>
                    </Alert>
                  );
                })() : (
                  <CustomAudioPlayer
                    src={audioUrl || ''}
                    onEnded={handleAudioEnd}
                    playbackSpeed={settings.defaultPlaybackSpeed}
                    onPlaybackSpeedChange={setPlaybackSpeed}
                    showAutoPlayToggle={true} // Enable auto-play toggle for collection book
                    autoPlayEnabled={settings.autoPlayEnabled}
                    onAutoPlayToggle={toggleAutoPlay}
                    shouldAutoPlayOnLoad={settings.autoPlayEnabled}
                    isLoading={isAudioLoading}
                    style={{ margin: '1.2rem 0 1.5rem 0' }}
                    ref={audioRef}
                  />
                )}
              </>
            )}

            {/* Reading Passage - For reading tests only */}
            {!isListeningTest && questionData.extracted_text && (() => {
              const { displayText } = prepareTextForDisplay(questionData.extracted_text);

              // Create responsive text styling that prevents wide text while preserving OCR format
              const getTextContainerStyle = () => ({
                display: 'flex',
                justifyContent: 'center',
                width: '100%',
                padding: '0 20px', // Mobile-friendly padding
                // Ensure no horizontal overflow
                overflow: 'hidden',
              });

              const getTextStyle = () => ({
                fontFamily: 'system-ui, -apple-system, sans-serif',
                fontSize: '16px',
                lineHeight: 1.7,
                textAlign: 'left' as const,
                color: themeColors.textPrimary,
                maxWidth: '800px',
                width: '100%',

                // Core text wrapping strategy - always use flexible wrapping to prevent wide text
                whiteSpace: 'pre-wrap' as const,
                overflowWrap: 'break-word' as const,
                wordBreak: 'break-word' as const,

                // Ensure text never exceeds container width
                boxSizing: 'border-box' as const,
              });

              return (
                <Box style={getTextContainerStyle()}>
                  <UnifiedTextInteraction
                    mode="passage"
                    style={getTextStyle()}
                    questionIndex={selectedQuestion ? parseInt(selectedQuestion.question_number) : 0}
                  >
                    {displayText}
                  </UnifiedTextInteraction>
                </Box>
              );
            })()}

            {/* Question Text - For reading tests, show the actual question */}
            {!isListeningTest && questionData.question_text && (
              <Box style={{ textAlign: 'center', marginTop: '2rem' }}>
                <UnifiedTextInteraction mode="question" size="xl" fw={700} style={{ color: themeColors.textPrimary }} questionIndex={selectedQuestion ? parseInt(selectedQuestion.question_number) : 0}>
                  {questionData.question_text}
                </UnifiedTextInteraction>
              </Box>
            )}

            {/* Choices */}
            {questionData.choices && (
              <Box>
                <Radio.Group
                  value={typeof userAnswer === 'string' ? userAnswer : ''}
                  onChange={(value) => handleAnswerChange(questionId, value)}
                >
                  <Stack gap="lg">
                    {Object.entries(questionData.choices).map(([letter, text]) => {
                      const isSelected = userAnswer === letter;
                      
                      // Extract correct answer properly
                      const correctAnswerFromQuestionData = questionData.correct_answer;
                      const correctAnswerFromRoot = selectedQuestion.correct_answer;
                      const extractedCorrectAnswer = correctAnswerFromQuestionData || correctAnswerFromRoot;
                      
                      const isCorrect = letter === extractedCorrectAnswer;
                      
                      // Color logic for feedback
                      let borderColor = themeColors.border;
                      let backgroundColor = themeColors.surface;
                      let circleColor = themeColors.surfaceHover;
                      let circleTextColor = themeColors.textPrimary;
                      let textColor = themeColors.textPrimary;

                      if (isSelected) {
                        borderColor = themeColors.primary;
                        backgroundColor = resolvedTheme === 'dark' ? '#1a2332' : '#f0f8ff';
                        circleColor = themeColors.primary;
                        circleTextColor = 'white';
                        textColor = themeColors.primary;
                      }

                      return (
                        <Box
                          key={letter}
                          style={{
                            cursor: 'pointer',
                            borderRadius: '16px',
                            border: `3px solid ${borderColor}`,
                            transition: 'all 0.3s ease',
                            backgroundColor,
                            boxShadow: isSelected
                              ? `0 6px 20px ${resolvedTheme === 'dark' ? 'rgba(30, 144, 255, 0.2)' : 'rgba(34, 139, 230, 0.15)'}`
                              : `0 2px 8px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.06)'}`,
                            transform: isSelected ? 'translateY(-2px)' : 'none'
                          }}
                          onClick={() => {
                            handleAnswerChange(questionId, letter);
                          }}
                          onMouseEnter={(e) => {
                            if (!isSelected) {
                              e.currentTarget.style.borderColor = themeColors.primary;
                              e.currentTarget.style.backgroundColor = themeColors.surfaceHover;
                              e.currentTarget.style.transform = 'translateY(-1px)';
                              e.currentTarget.style.boxShadow = `0 4px 12px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.4)' : 'rgba(0,0,0,0.1)'}`;
                            }
                          }}
                          onMouseLeave={(e) => {
                            if (!isSelected) {
                              e.currentTarget.style.borderColor = themeColors.border;
                              e.currentTarget.style.backgroundColor = themeColors.surface;
                              e.currentTarget.style.transform = 'none';
                              e.currentTarget.style.boxShadow = `0 2px 8px ${resolvedTheme === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.06)'}`;
                            }
                          }}
                        >
                          <Group gap="xl" p={text ? "md" : "xs"} wrap="nowrap" align="center">
                            <Box
                              style={{
                                minWidth: '48px',
                                height: '48px',
                                borderRadius: '50%',
                                backgroundColor: circleColor,
                                color: circleTextColor,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontWeight: 'bold',
                                fontSize: '20px',
                                transition: 'all 0.3s ease',
                                border: isSelected ? 'none' : `2px solid ${themeColors.border}`
                              }}
                            >
                              {letter}
                            </Box>
                            <Box
                              style={{
                                lineHeight: 1.6,
                                color: textColor,
                                fontWeight: isSelected ? 500 : 400,
                                flex: 1,
                                fontSize: 'var(--mantine-font-size-lg)'
                              }}
                              dangerouslySetInnerHTML={{ __html: String(text) }}
                            />
                            <Radio
                              value={letter}
                              style={{ visibility: 'hidden', width: 0, margin: 0 }}
                            />
                          </Group>
                        </Box>
                      );
                    })}
                  </Stack>
                </Radio.Group>
              </Box>
            )}

            {/* Interactive Transcript */}
            {isListeningTest && questionData.chunks && (
              <Box>
                <Group gap="sm">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowTranscript(!showTranscript);
                      // Clear translation when hiding transcript
                      if (showTranscript) {
                        setTranslationResult(null);
                        setShowTranslateButton(false);
                        setSelectedTranscriptText('');
                        window.getSelection()?.removeAllRanges();
                      }
                    }}
                    leftSection={showTranscript ? <IconEyeOff size={16} /> : <IconEye size={16} />}
                  >
                    {showTranscript ? t('collection.practice.hideTranscript') : t('collection.practice.showTranscript')}
                  </Button>

                  {/* Translation button - only show when text is selected */}
                  {showTranslateButton && selectedTranscriptText && (
                    <Group gap={0}>
                      <Button
                        variant="filled"
                        color="blue"
                        size="sm"
                        onClick={handleTranslateText}
                        disabled={isTranslating}
                        leftSection={isTranslating ? <Loader size={14} /> : <IconLanguage size={14} />}
                        style={{
                          borderTopRightRadius: 0,
                          borderBottomRightRadius: 0,
                          borderRight: 'none',
                        }}
                      >
                        {isTranslating ? 'Translating...' : 'Translate'}
                      </Button>

                      {/* Language switcher dropdown-style button */}
                      <Button
                        variant="outline"
                        color="blue"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleTranslationTarget();
                        }}
                        style={{
                          borderTopLeftRadius: 0,
                          borderBottomLeftRadius: 0,
                          minWidth: '50px',
                          padding: '0 8px',
                          fontSize: '12px',
                          fontWeight: 'bold',
                        }}
                        title={`Switch to ${translationTarget === 'en' ? 'Chinese' : 'English'}`}
                      >
                        {translationTarget === 'en' ? 'EN' : '中文'}
                      </Button>
                    </Group>
                  )}
                </Group>

                {/* Translation Result Display */}
                {translationResult && (
                  <Box mt="md" p="md" style={{
                    borderRadius: '8px',
                    border: `1px solid ${themeColors.primary}`,
                    backgroundColor: themeColors.background
                  }}>
                    <Text size="sm" style={{ lineHeight: 1.6 }}>
                      {translationResult.text}
                    </Text>
                  </Box>
                )}

                {showTranscript && (
                  <Box mt="md">
                    <Button
                      size="sm"
                      variant="outline"
                      mb="md"
                      onClick={toggleAllTranscript}
                    >
                      {showAllTranscript ? t('collection.practice.hideAllText') : t('collection.practice.showAllText')}
                    </Button>
                    
                    <Stack gap="xs" className="transcript-container">
                      {questionData.chunks?.map((chunk: any, index: number) => {
                        return (
                        <Card
                          key={index}
                          p="sm"
                          style={{
                            backgroundColor: activeChunk === chunk.start
                              ? (resolvedTheme === 'dark' ? '#1a4d00' : '#fff78a')
                              : themeColors.surfaceHover,
                            cursor: 'pointer',
                            transition: 'background 0.2s',
                            border: activeChunk === chunk.start
                              ? `3px solid ${resolvedTheme === 'dark' ? '#4caf50' : '#ffc107'}`
                              : `1px solid ${themeColors.border}`,
                            boxShadow: activeChunk === chunk.start
                              ? (resolvedTheme === 'dark' ? '0 0 10px rgba(76, 175, 80, 0.5)' : '0 0 10px rgba(255, 193, 7, 0.5)')
                              : 'none'
                          }}
                          className="transcript-card"
                          data-transcript="true"
                          onClick={() => playChunk(chunk)}
                        >
                          <div style={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: '8px', 
                            width: '100%',
                            flexWrap: 'nowrap'
                          }}>
                            <Button
                              size="xs"
                              variant="subtle"
                              style={{
                                flexShrink: 0,
                                minWidth: 'auto',
                                height: '24px',
                                padding: '4px'
                              }}
                              styles={{
                                root: {
                                  color: themeColors.textPrimary,
                                  '&:hover': {
                                    backgroundColor: themeColors.surfaceHover,
                                  },
                                },
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                toggleTranscriptChunk(index);
                              }}
                            >
                              {transcriptVisible[index] ? <IconEye size={16} /> : <IconEyeOff size={16} />}
                            </Button>
                            {transcriptVisible[index] && (
                              <span style={{
                                flex: 1,
                                fontSize: '14px',
                                lineHeight: '1.4',
                                color: themeColors.textPrimary
                              }}>
                                {chunk.text}
                              </span>
                            )}
                          </div>
                        </Card>
                      );
                    })}
                    </Stack>
                  </Box>
                )}
              </Box>
            )}

            {/* Navigation Buttons */}
            <Group justify="space-between" mt="xl">
              <Button
                variant="outline"
                disabled={!selectedQuestion || allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id) === 0}
                onClick={goToPreviousQuestion}
                leftSection={<IconChevronLeft size={16} />}
              >
                {t('test.loading.previous')}
              </Button>

              {/* Position indicator */}
              {selectedQuestion && (
                <Text size="sm" c="dimmed">
                  {allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id) + 1} / {allQuestions.length}
                </Text>
              )}

              <Button
                variant="outline"
                disabled={!selectedQuestion || allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id) === allQuestions.length - 1}
                onClick={goToNextQuestion}
                rightSection={<IconChevronRight size={16} />}
              >
                {t('test.loading.next')}
              </Button>
            </Group>

            {/* Auto-save indicator */}
            {/* Removed 'Saving...' indicator as per user request */}
          </Stack>
        </Paper>
        {/* Universal Notebook - Extends from button's middle position */}
        {notebookExpanded && (
          <Box
            style={{
              zIndex: 10,
              padding: '1rem',
            }}
          >
            <NotebookSidebar isExpanded={notebookExpanded} />
          </Box>
        )}
      </Box>
    );
  };

  const renderTestSection = (
    title: string, 
    groupedQuestions: GroupedQuestions, 
    sectionKey: string
  ) => {
    const filteredGroups = Object.entries(groupedQuestions).reduce((acc, [key, questions]) => {
      const filtered = filterQuestions(questions);
      if (filtered.length > 0) {
        acc[key] = filtered;
      }
      return acc;
    }, {} as GroupedQuestions);

    const totalInSection = Object.values(filteredGroups).reduce((sum, questions) => sum + questions.length, 0);

    if (totalInSection === 0) return null;

    // For listening, reverse the group order and the question order within each group
    const groupEntries = Object.entries(filteredGroups);
    const displayGroups = sectionKey === 'listening' ? [...groupEntries].reverse() : groupEntries;

    return (
      <Box>
        {/* Mobile Layout */}
        <Group justify="space-between" mb="sm" hiddenFrom="sm">
          <Group gap="sm">
            <Text fw={600} size="md">{title}</Text>
            <Badge variant="light" color="blue" size="sm">
              {t('collection.questionsCount', { count: totalInSection })}
            </Badge>
          </Group>
          <ActionIcon
            variant="subtle"
            size="sm"
            onClick={() => toggleSection(sectionKey)}
            style={{
              minWidth: '32px',
              minHeight: '32px',
            }}
          >
            {collapsedSections[sectionKey] ? <IconChevronDown size={14} /> : <IconChevronUp size={14} />}
          </ActionIcon>
        </Group>

        {/* Desktop Layout */}
        <Group justify="space-between" mb="sm" visibleFrom="sm">
          {sectionKey === 'listening' ? (
            <>
              <ActionIcon
                variant="subtle"
                onClick={() => toggleSection(sectionKey)}
              >
                {collapsedSections[sectionKey] ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
              </ActionIcon>
              <Group gap="sm" style={{ marginLeft: 'auto', flex: 1, justifyContent: 'flex-end' }}>
                <Text fw={600} size="lg" style={{ textAlign: 'right', width: 'auto' }}>{title}</Text>
                <Badge variant="light" color="blue">
                  {t('collection.questionsCount', { count: totalInSection })}
                </Badge>
              </Group>
            </>
          ) : (
            <>
              <Group gap="sm">
                <Text fw={600} size="lg">{title}</Text>
                <Badge variant="light" color="blue">
                  {t('collection.questionsCount', { count: totalInSection })}
                </Badge>
              </Group>
              <ActionIcon
                variant="subtle"
                onClick={() => toggleSection(sectionKey)}
              >
                {collapsedSections[sectionKey] ? <IconChevronDown size={16} /> : <IconChevronUp size={16} />}
              </ActionIcon>
            </>
          )}
        </Group>

        <Collapse in={!collapsedSections[sectionKey]}>
          <Stack gap="xs">
            {displayGroups.map(([testKey, questions]) => {
              const firstQuestion = questions[0];
              // Use getTestDisplayName directly
              const displayName = getTestDisplayName(
                firstQuestion.test_type,
                firstQuestion.test_identifier,
                firstQuestion.test_category
              );
              // For listening, reverse the question order within the group
              const displayQuestions = sectionKey === 'listening' ? [...questions].reverse() : questions;
              return (
                <Box key={testKey}>
                  {/* Mobile Layout - Always left aligned */}
                  <Text
                    fw={500}
                    mb={4}
                    size="xs"
                    hiddenFrom="sm"
                    style={{ textAlign: 'left', width: '100%' }}
                  >
                    {displayName}
                  </Text>
                  <Box hiddenFrom="sm" mb="xs">
                    {renderQuestionBoxes(displayQuestions, 'left')}
                  </Box>

                  {/* Desktop Layout - Preserve original alignment */}
                  <Text
                    fw={500}
                    mb={4}
                    size="sm"
                    visibleFrom="sm"
                    style={sectionKey === 'listening' ? { textAlign: 'right', width: '100%' } : {}}
                  >
                    {displayName}
                  </Text>
                  <Box visibleFrom="sm">
                    {renderQuestionBoxes(displayQuestions, sectionKey === 'listening' ? 'right' : 'left')}
                  </Box>
                </Box>
              );
            })}
          </Stack>
        </Collapse>
      </Box>
    );
  };

  return (
    <Box style={{ display: 'flex', height: 'calc(100vh - 80px)' }}>
      {/* Main Content Area */}
      <Box style={{ 
        flex: 1, 
        overflow: 'auto', 
        position: 'relative'
      }}>
        <Container size="lg" py={{ base: "md", sm: "xl" }} px={{ base: "sm", sm: "md" }}>
          {/* Header */}
          <Box mb={{ base: "lg", sm: "xl" }}>
            {/* Mobile Header */}
            <Stack gap="md" hiddenFrom="sm">
              <Group justify="space-between" align="center">
                <Button
                  variant="outline"
                  leftSection={<IconArrowLeft size={14} />}
                  onClick={() => navigate('/')}
                  size="sm"
                >
                  {t('layout.home')}
                </Button>
              </Group>
              <Group justify="center">
                <Title order={2} ta="center">
                  <Group gap="sm" justify="center">
                    <IconStar size={24} style={{ color: resolvedTheme === 'dark' ? '#ffd43b' : '#ffc107' }} />
                    <Text size="lg" fw={700}>{t('collection.title')}</Text>
                  </Group>
                </Title>
              </Group>
            </Stack>

            {/* Desktop Header */}
            <Group justify="space-between" align="center" visibleFrom="sm">
              <Group>
                <Button
                  variant="outline"
                  leftSection={<IconArrowLeft size={16} />}
                  onClick={() => navigate('/')}
                >
                  {t('layout.home')}
                </Button>
                <Title order={1}>
                  <Group gap="sm">
                    <IconStar size={32} style={{ color: resolvedTheme === 'dark' ? '#ffd43b' : '#ffc107' }} />
                    {t('collection.title')}
                  </Group>
                </Title>
              </Group>

              {totalQuestions > 0 && (
                <></>
              )}
            </Group>
          </Box>

          {totalQuestions === 0 ? (
            <Paper
              p={{ base: "lg", sm: "xl" }}
              shadow="sm"
              radius="md"
              style={{
                textAlign: 'center',
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
              }}
            >
              <IconStar size={56} style={{ color: themeColors.textSecondary, marginBottom: '1rem' }} />
              <Title order={3} mb="md" c="dimmed">
                {t('collection.empty.title')}
              </Title>
              <Text c="dimmed" mb="lg">
                {t('collection.empty.message')}
              </Text>
              <Button
                onClick={() => navigate('/reading')}
                size="md"
                style={{ width: '100%', maxWidth: '250px' }}
              >
                {t('collection.empty.startReading')}
              </Button>
            </Paper>
          ) : (
            <>
              {/* Navigation Panel */}
              <Paper
                p={{ base: "md", sm: "lg" }}
                mb="xl"
                shadow="sm"
                radius="md"
                style={{
                  backgroundColor: themeColors.surface,
                  border: `1px solid ${themeColors.border}`,
                }}
              >
                <Text fw={600} mb="sm" c={themeColors.textPrimary} size="md" hiddenFrom="sm">
                  {t('navigation.title')}
                </Text>
                <Text fw={600} mb="sm" c={themeColors.textPrimary} size="lg" visibleFrom="sm">
                  {t('navigation.title')}
                </Text>

                {/* Legend - Mobile optimized */}
                <Stack gap={4} mb="sm" hiddenFrom="sm">
                  <Group gap="xs" justify="center">
                    <Box
                      w={16}
                      h={16}
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed',
                        border: `2px solid ${themeColors.reading}`,
                        borderRadius: 3
                      }}
                    />
                    <Text size="xs" c={themeColors.textPrimary}>{t('navigation.answered')}</Text>
                  </Group>
                  <Group gap="xs" justify="center">
                    <Box
                      w={16}
                      h={16}
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0',
                        border: `2px solid ${themeColors.speaking}`,
                        borderRadius: 3
                      }}
                    />
                    <Text size="xs" c={themeColors.textPrimary}>{t('navigation.wrongAnswer')}</Text>
                  </Group>
                  <Group gap="xs" justify="center">
                    <Box
                      w={16}
                      h={16}
                      style={{
                        backgroundColor: themeColors.surfaceHover,
                        border: `2px solid ${themeColors.border}`,
                        borderRadius: 3
                      }}
                    />
                    <Text size="xs" c={themeColors.textPrimary}>{t('navigation.notAnswered')}</Text>
                  </Group>
                </Stack>

                {/* Legend - Desktop */}
                <Group gap="xs" mb="sm" visibleFrom="sm">
                  <Group gap="xs">
                    <Box
                      w={18}
                      h={18}
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#1a4d2e' : '#e6f9ed',
                        border: `2px solid ${themeColors.reading}`,
                        borderRadius: 4
                      }}
                    />
                    <Text size="sm" c={themeColors.textPrimary}>{t('navigation.answered')}</Text>
                  </Group>
                  <Group gap="xs">
                    <Box
                      w={18}
                      h={18}
                      style={{
                        backgroundColor: resolvedTheme === 'dark' ? '#4d1a1a' : '#fff0f0',
                        border: `2px solid ${themeColors.speaking}`,
                        borderRadius: 4
                      }}
                    />
                    <Text size="sm" c={themeColors.textPrimary}>{t('navigation.wrongAnswer')}</Text>
                  </Group>
                  <Group gap="xs">
                    <Box
                      w={18}
                      h={18}
                      style={{
                        backgroundColor: themeColors.surfaceHover,
                        border: `2px solid ${themeColors.border}`,
                        borderRadius: 4
                      }}
                    />
                    <Text size="sm" c={themeColors.textPrimary}>{t('navigation.notAnswered')}</Text>
                  </Group>
                </Group>

                {/* Test Sections - Mobile Stack, Desktop Flex */}
                <Stack gap="md" hiddenFrom="sm">
                  <Box>
                    {renderTestSection(
                      t('collection.sections.reading'),
                      groupedReading,
                      'reading'
                    )}
                  </Box>
                  <Box>
                    {renderTestSection(
                      t('collection.sections.listening'),
                      groupedListening,
                      'listening'
                    )}
                  </Box>
                </Stack>

                {/* Test Sections - Desktop */}
                <Flex wrap="wrap" gap="md" visibleFrom="sm">
                  <Box style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                    {renderTestSection(
                      t('collection.sections.reading'),
                      groupedReading,
                      'reading'
                    )}
                  </Box>
                  <Box style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                    {renderTestSection(
                      t('collection.sections.listening'),
                      groupedListening,
                      'listening'
                    )}
                  </Box>
                </Flex>
              </Paper>

              {/* Selected Question Display */}
              {renderSelectedQuestion()}
            </>
          )}
        </Container>
        
        {/* Submit Button - Always show if there are questions */}
        {totalQuestions > 0 && (
          <Group justify="center" mt={{ base: "lg", sm: "xl" }} px={{ base: "sm", sm: "md" }}>
            <Button
              size="lg"
              color="green"
              loading={submitCollectionMutation.isPending}
              onClick={() => submitCollectionMutation.mutate()}
              leftSection={<IconTrophy size={20} />}
              disabled={Object.keys(answers).length === 0}
              style={{
                width: '100%',
                maxWidth: '300px'
              }}
              hiddenFrom="sm"
            >
              {t('test.loading.submitTest')}
            </Button>
            <Button
              size="lg"
              color="green"
              loading={submitCollectionMutation.isPending}
              onClick={() => submitCollectionMutation.mutate()}
              leftSection={<IconTrophy size={20} />}
              disabled={Object.keys(answers).length === 0}
              visibleFrom="sm"
            >
              {t('test.loading.submitTest')}
            </Button>
          </Group>
        )}
      </Box>

      {/* Use the standard GradingResultsModal */}
      {gradingResults && (
        <GradingResultsModal
          isOpen={showResultsModal}
          onClose={() => setShowResultsModal(false)}
          results={{
            score: Object.keys(answers).length - gradingResults.wrong_details.length,
            max_score: Object.keys(answers).length,
            correct_count: gradingResults.correct_count,
            wrong_details: gradingResults.wrong_details.map((detail: any) => {
              const question = allQuestions.find(q => q.question_id === detail.question);
              if (question) {
                const readableIdentifier = `${question.test_type}_${question.test_identifier}_q${question.question_number}` +
                  (question.test_category === 'free' ? '_gr' : '');
                return {
                  ...detail,
                  question: readableIdentifier
                };
              }
              return detail;
            })
          }}
          testType="reading"
          testId="collection_book_notebook"
          questionIds={{}}
          isGroupTest={false}
          onRemoveCorrectQuestions={async () => {
            // Remove only the questions the user got right (answered and not in wrong_details)
            // Use original_wrong_details which contains UUIDs, not formatted identifiers
            const wrongSet = new Set(gradingResults.original_wrong_details?.map(w => w.question) || []);
            const correctQuestions = allQuestions.filter(q => !wrongSet.has(q.question_id) && answers[q.question_id]);
            const removedIds = new Set(correctQuestions.map(q => q.question_id));
            // Call backend to remove each correct question from the collection book
            await Promise.all(correctQuestions.map(q => testApi.removeBookmark(q.question_id)));
            // Refetch collection book data
            refetch();
            // Update answers: remove any answers for removed questions
            const updatedAnswers = { ...answers };
            correctQuestions.forEach(q => { delete updatedAnswers[q.question_id]; });
            const cleanedAnswers = cleanCollectionBookAnswers(updatedAnswers);
            setAnswers(cleanedAnswers);
            // Update gradingResults to only include questions still in the collection
            const remainingQuestions = allQuestions.filter(q => wrongSet.has(q.question_id)).map(q => q.question_id);
            setGradingResults(prev => {
              if (!prev) return prev;
              const newWrongDetails = prev.original_wrong_details?.filter(w => remainingQuestions.includes(w.question)) || [];
              return {
                ...prev,
                wrong_details: newWrongDetails.map((detail: any) => {
                  // Re-format the remaining wrong details
                  const question = allQuestions.find(q => q.question_id === detail.question);
                  if (question) {
                    const testDisplayName = getTestDisplayName(
                      question.test_type, 
                      question.test_identifier, 
                      question.test_category
                    );
                    const readableIdentifier = `${testDisplayName} - Question ${question.question_number}`;
                    return { ...detail, question: readableIdentifier };
                  }
                  return detail;
                }),
                original_wrong_details: newWrongDetails,
                correct_count: remainingQuestions.length - newWrongDetails.length
              };
            });
            // Save updated answers and grading_results to test_history
            await testApi.saveTestHistory({
              section: 'collection_book',
              test_id: 'collection_book_notebook',
              free: false,
              answers: cleanedAnswers,
              current_question: 0,
              grading_results: {
                correct: [],
                incorrect: [],
                wrong_details: gradingResults?.original_wrong_details?.filter(w => remainingQuestions.includes(w.question)) || []
              }
            });
            // Auto-select next available question if current was removed
            if (selectedQuestion && removedIds.has(selectedQuestion.question_id)) {
              // Find all remaining questions (not removed)
              const remaining = allQuestions.filter(q => !removedIds.has(q.question_id));
              if (remaining.length > 0) {
                // Try to select the next question to the right
                const currentIdx = allQuestions.findIndex(q => q.question_id === selectedQuestion.question_id);
                let nextIdx = currentIdx + 1;
                if (nextIdx >= allQuestions.length || removedIds.has(allQuestions[nextIdx]?.question_id)) {
                  // If right is not available, try left
                  nextIdx = currentIdx - 1;
                }
                // Fallback: pick the first remaining question
                const nextQuestion = remaining[nextIdx] || remaining[0];
                setSelectedQuestion(nextQuestion);
              } else {
                setSelectedQuestion(null);
              }
            }
            setShowResultsModal(false);
          }}
        />
      )}
    </Box>
  );
} 