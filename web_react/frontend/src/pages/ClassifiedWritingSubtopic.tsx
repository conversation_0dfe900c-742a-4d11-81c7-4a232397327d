import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import {
  Container,
  Title,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Box,
  Paper,
  Divider,
  Alert,
  Loader,
  <PERSON>readcrumbs,
  <PERSON>chor,
  Textarea,
  ActionIcon
} from '@mantine/core';
import {
  IconPencil,
  IconArrowLeft,
  IconEdit,
  IconDeviceFloppy,
  IconClock,
  IconTarget,
  IconBookmark,
  IconFileText
} from '@tabler/icons-react';

import { classifiedWritingService } from '../services/classifiedWritingService';
import { useAuthStore } from '../store/useAuthStore';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { classifiedWritingTranslationService, type TacheTranslations } from '../services/classifiedWritingTranslationService';
import { useThemeColors } from '../store/useThemeStore';
import { ClassifiedWritingNavigation } from '../components/ClassifiedWritingNavigation';

import { testApi } from '../services/api';

// Helper functions to get translated names using the translation service
const getTranslatedTopicName = (topicName: string, translations: TacheTranslations | undefined, currentLanguage: string): string => {
  return classifiedWritingTranslationService.getTopicTranslation(topicName, currentLanguage, translations);
};

const getTranslatedSubtopicName = (subtopicName: string, translations: TacheTranslations | undefined, currentLanguage: string): string => {
  // Normalize subtopic name to match translation keys (replace spaces with underscores, lowercase)
  const normalizedSubtopicName = subtopicName.toLowerCase().replace(/\s+/g, '_');
  return classifiedWritingTranslationService.getSubtopicTranslation(normalizedSubtopicName, currentLanguage, translations);
};

export function ClassifiedWritingSubtopic() {
  const { topicName, subtopicId: rawSubtopicId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();

  // Decode the subtopic ID from URL (double-decode for forward slashes)
  const subtopicId = rawSubtopicId ? decodeURIComponent(decodeURIComponent(rawSubtopicId)) : undefined;

  // Extract tâche number from the URL path
  const getTacheNumberFromPath = () => {
    const path = location.pathname;
    const match = path.match(/classified_tache(\d+)/);
    return match ? match[1] : '1';
  };

  const tacheNumber = getTacheNumberFromPath();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [subtopicData, setSubtopicData] = useState<any>(null);
  const [tasks, setTasks] = useState<any[]>([]);
  const [templateResponse, setTemplateResponse] = useState('');
  const [saving, setSaving] = useState(false);
  const [translations, setTranslations] = useState<TacheTranslations | null>(null);
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [writingStartTime, setWritingStartTime] = useState<Date | null>(null);

  // Check if user is premium - temporarily disabled for testing
  const isPremiumUser = true; // isAuthenticated && isMember() && !isMembershipExpired();

  useEffect(() => {
    loadSubtopicData();
    loadTranslations();
    loadExistingSubmission();
  }, [tacheNumber, topicName, subtopicId]);

  // Auto-save effect with debouncing
  useEffect(() => {
    if (!templateResponse.trim()) return;
    
    const saveTimer = setTimeout(() => {
      autoSaveTemplate(templateResponse, false);
    }, 3000); // Auto-save after 3 seconds of inactivity

    return () => clearTimeout(saveTimer);
  }, [templateResponse]);

  // Start writing timer when user begins typing
  useEffect(() => {
    if (templateResponse.trim() && !writingStartTime) {
      setWritingStartTime(new Date());
    }
  }, [templateResponse, writingStartTime]);

  const loadExistingSubmission = async () => {
    if (!subtopicId || !tacheNumber) return;

    try {
      const testIdentifier = `classified_template_${subtopicId}`;
      const taskNumber = parseInt(tacheNumber);
      
      const response = await testApi.getWritingSubmissions(
        'writing',
        testIdentifier,
        taskNumber
      );
      
      if (response.submissions && response.submissions.length > 0) {
        // Get the most recent submission
        const latestSubmission = response.submissions[0];
        setTemplateResponse(latestSubmission.content);
        setSubmissionId(latestSubmission.id);
      }
    } catch (error) {
      console.error('Error loading existing submission:', error);
      // Don't show error to user, just continue without existing content
    }
  };

  const loadTranslations = async () => {
    if (!tacheNumber) return;

    try {
      const tacheTranslations = await classifiedWritingTranslationService.getTacheTranslations(parseInt(tacheNumber));
      setTranslations(tacheTranslations);
    } catch (error) {
      console.error('Error loading translations:', error);
      setTranslations(null);
    }
  };

  const loadSubtopicData = async () => {
    if (!subtopicId) return;

    try {
      setLoading(true);
      setError(null);

      const response = await classifiedWritingService.getSubtopicTasks(subtopicId);
      setSubtopicData(response.subtopic);
      setTasks(response.tasks || []);
    } catch (err) {
      console.error('Error loading subtopic data:', err);
      setError(t('classifiedWriting.errors.loadSubtopic', 'Erreur lors du chargement du sous-thème'));
    } finally {
      setLoading(false);
    }
  };

  // Auto-save function
  const autoSaveTemplate = async (content: string, isFinal: boolean = false) => {
    if (!content.trim() && !submissionId) return;

    try {
      setAutoSaveStatus('saving');
      const duration = writingStartTime ? Math.floor((Date.now() - writingStartTime.getTime()) / 1000) : 0;
      
      const submissionData = {
        test_type: 'writing',
        test_identifier: `classified_template_${subtopicId}`,
        task_number: parseInt(tacheNumber),
        content: content,
        content_format: 'plain_text',
        status: isFinal ? 'final' : 'draft',
        is_final_submission: isFinal,
        writing_duration: duration,
        metadata: {
          template_type: 'classified',
          subtopic_id: subtopicId,
          subtopic_name: subtopicData?.subtopic_name,
          tache_number: parseInt(tacheNumber)
        }
      };

      const response = await testApi.saveWritingSubmission({
        ...submissionData,
        status: submissionData.status as "draft" | "submitted"
      });
      setSubmissionId(response.submission_id);
      setAutoSaveStatus('saved');
      
      setTimeout(() => setAutoSaveStatus('idle'), 2000);
      return response;
    } catch (error) {
      console.error('Auto-save error:', error);
      setAutoSaveStatus('error');
      setTimeout(() => setAutoSaveStatus('idle'), 3000);
      throw error;
    }
  };

  const handleSaveTemplate = async () => {
    if (!templateResponse.trim()) return;

    try {
      setSaving(true);
      await autoSaveTemplate(templateResponse, true);
    } catch (err) {
      console.error('Error saving template:', err);
      // Error will be shown via autoSaveStatus at bottom left
    } finally {
      setSaving(false);
    }
  };

  // Get tâche color
  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  const tacheColor = getTacheColor(parseInt(tacheNumber || '1'));

  if (loading) {
    return (
      <Box style={{ minHeight: '100vh', position: 'relative' }}>
        <ClassifiedWritingNavigation />
        <Box style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          minHeight: '80vh',
          padding: '1rem' 
        }}>
          <Container size="md">
            <Group justify="center" py="xl">
              <Loader size="lg" />
              <Text>{t('classifiedWriting.loading.subtopic', 'Chargement du sous-thème...')}</Text>
            </Group>
          </Container>
        </Box>
      </Box>
    );
  }

  if (error) {
    return (
      <Box style={{ minHeight: '100vh', position: 'relative' }}>
        <ClassifiedWritingNavigation />
        <Box style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          minHeight: '80vh',
          padding: '1rem' 
        }}>
          <Container size="md">
            <Alert color="red" title={t('common.error', 'Erreur')} mt="xl">
              {error}
            </Alert>
          </Container>
        </Box>
      </Box>
    );
  }

  return (
    <Box style={{ minHeight: '100vh', position: 'relative' }}>
      {/* Floating Navigation */}
      <ClassifiedWritingNavigation />

      {/* Main Content - Centered Layout */}
      <Box style={{ display: 'flex', justifyContent: 'center', padding: '1rem' }}>
        <Container size="md" style={{ width: '100%', maxWidth: '800px' }}>

          {/* Header */}
          <Group justify="space-between" mb="xl">
            <Stack gap="xs">
              <Group gap="sm">
                <Badge color={tacheColor} variant="light">
                  Tâche {tacheNumber}
                </Badge>
                <Badge color="blue" variant="outline">
                  {t('classifiedWriting.template.label', 'Template')}
                </Badge>
              </Group>
              <Title order={2}>
                {getTranslatedSubtopicName(subtopicData?.subtopic_name || subtopicId, translations || undefined, i18n.language)}
              </Title>
              <Text c="dimmed">
                {t('classifiedWriting.subtopic.description', 'Écrivez une réponse template qui couvre ce type de tâche')}
              </Text>
            </Stack>
            <Button
              leftSection={<IconArrowLeft size={16} />}
              variant="outline"
              onClick={() => navigate(`/writing/classified_tache${tacheNumber}`)}
            >
              {t('common.back', 'Retour')}
            </Button>
          </Group>

          {/* Template Writing Section */}
          <Card shadow="sm" padding="xl" radius="md" withBorder mb="xl">
            <Stack gap="md">
              <Group justify="space-between">
                <Group gap="sm">
                  <IconEdit size={20} color={tacheColor} />
                  <Text fw={600} size="lg">
                    {t('classifiedWriting.template.writeResponse', 'Votre réponse template')}
                  </Text>
                </Group>
                <Group gap="xs">
                  <IconTarget size={16} color={themeColors.textSecondary} />
                  <Text size="sm" c="dimmed">
                    {tacheNumber === '1' && '60-120 mots'}
                    {tacheNumber === '2' && '120-150 mots'}
                    {tacheNumber === '3' && '120-180 mots'}
                  </Text>
                </Group>
              </Group>

              <Textarea
                placeholder={t('classifiedWriting.template.placeholder', 'Écrivez votre réponse template ici. Cette réponse devrait couvrir le pattern général de ce type de tâche...')}
                value={templateResponse}
                onChange={(event) => setTemplateResponse(event.currentTarget.value)}
                minRows={15}
                maxRows={25}
                autosize
                styles={{
                  input: {
                    fontSize: '16px',
                    lineHeight: 1.8,
                    padding: '20px'
                  }
                }}
              />

              <Group justify="space-between">
                <Group gap="md">
                  <Text size="sm" c="dimmed">
                    {templateResponse.split(' ').filter(word => word.length > 0).length} {t('classifiedWriting.template.words', 'mots')}
                  </Text>
                  {/* Auto-save status indicator */}
                  {autoSaveStatus !== 'idle' && (
                    <Text size="xs" c={autoSaveStatus === 'error' ? 'red' : autoSaveStatus === 'saved' ? 'green' : 'dimmed'}>
                      {autoSaveStatus === 'saving' && t('classifiedWriting.template.autoSaving', 'Sauvegarde automatique...')}
                      {autoSaveStatus === 'saved' && t('classifiedWriting.template.autoSaved', 'Sauvegardé automatiquement')}
                      {autoSaveStatus === 'error' && t('classifiedWriting.template.autoSaveError', 'Erreur de sauvegarde')}
                    </Text>
                  )}
                </Group>
                <Button
                  leftSection={<IconDeviceFloppy size={16} />}
                  onClick={handleSaveTemplate}
                  loading={saving}
                  disabled={!templateResponse.trim()}
                  color={tacheColor}
                >
                  {t('classifiedWriting.template.save', 'Sauvegarder le template')}
                </Button>
              </Group>
            </Stack>
          </Card>

          {/* Task Examples Section */}
          <Card shadow="sm" padding="xl" radius="md" withBorder>
            <Stack gap="md">
              <Group gap="sm">
                <IconBookmark size={20} color={themeColors.textSecondary} />
                <Text fw={600} size="lg">
                  {t('classifiedWriting.examples.title', 'Exemples de tâches')}
                </Text>
                <Badge size="sm" variant="light" color="gray">
                  {tasks.length} {t('classifiedWriting.examples.count', 'exemples')}
                </Badge>
              </Group>

              <Text size="sm" c="dimmed">
                {t('classifiedWriting.examples.description', 'Utilisez ces exemples comme référence pour comprendre le pattern de ce type de tâche.')}
              </Text>

              <Stack gap="md">
                {tasks.map((task, index) => (
                  <Paper key={index} p="md" bg={themeColors.surface} radius="md">
                    <Stack gap="xs">
                      <Text size="xs" c="dimmed">
                        {t('classifiedWriting.examples.example', 'Exemple')} {index + 1}:
                      </Text>
                      <Text size="sm" style={{ lineHeight: 1.6 }}>
                        {task.task_content}
                      </Text>
                      
                      {/* Always show task IDs in consistent format */}
                      <Stack gap="xs">
                        <Text size="xs" fw={500} c="dimmed">
                          {task.is_duplicate_group && task.task_ids && task.task_ids.length > 1 
                            ? t('classifiedWriting.examples.taskIds', 'Identifiants des tâches') + ` (${task.task_ids.length}):`
                            : t('classifiedWriting.examples.taskId', 'Identifiant de la tâche') + ':'
                          }
                        </Text>
                        <Group gap="4px" style={{ flexWrap: 'wrap' }}>
                          {task.is_duplicate_group && task.task_ids && task.task_ids.length > 1 ? (
                            task.task_ids.map((taskId: string, idIndex: number) => (
                              <Badge key={idIndex} size="xs" variant="outline" color="gray" style={{ fontSize: '10px' }}>
                                {taskId}
                              </Badge>
                            ))
                          ) : (
                            <Badge size="xs" variant="outline" color="gray" style={{ fontSize: '10px' }}>
                              {task.representative_id || task.task_ids?.[0] || 'N/A'}
                            </Badge>
                          )}
                        </Group>
                      </Stack>
                    </Stack>
                  </Paper>
                ))}
              </Stack>

              {tasks.length === 0 && (
                <Text size="sm" c="dimmed" ta="center" py="xl">
                  {t('classifiedWriting.examples.noTasks', 'Aucun exemple disponible')}
                </Text>
              )}
            </Stack>
          </Card>
        </Container>
      </Box>
    </Box>
  );
}
