import { Container, Paper, TextInput, PasswordInput, Button, Title, Text, Alert, Divider, Group } from '@mantine/core';
import { useForm } from '@mantine/form';
import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { authApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { signInWithGoogle } from '../lib/supabase';
import { IconBrandGoogle, IconInfoCircle } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { notifications } from '@mantine/notifications';

export function Register() {
  const { t } = useTranslation();
  const [error, setError] = useState('');
  const [googleLoading, setGoogleLoading] = useState(false);
  const [pendingInfo, setPendingInfo] = useState<{
    has_pending_registration: boolean;
    email?: string;
    username?: string;
  } | null>(null);
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  const [searchParams] = useSearchParams();

  // Check if user came from a "register again" link
  const fromVerification = searchParams.get('from') === 'verification';

  const form = useForm({
    initialValues: {
      username: '',
      email: searchParams.get('email') || '', // Pre-fill email if coming from verification page
      password: '',
      confirmPassword: '',
    },
    validate: {
      username: (value) => (value.length < 3 ? t('auth.register.errors.usernameMinLength') : null),
      email: (value) => (/^\S+@\S+$/.test(value) ? null : t('auth.register.errors.emailInvalid')),
      password: (value) => (value.length < 6 ? t('auth.register.errors.passwordMinLength') : null),
      confirmPassword: (value, values) => 
        value !== values.password ? t('auth.register.errors.passwordsDontMatch') : null,
    },
  });

  // Check for pending registration when email changes
  useEffect(() => {
    const checkPending = async () => {
      if (form.values.email && form.values.email.includes('@')) {
        try {
          const result = await authApi.checkPendingRegistration(form.values.email);
          setPendingInfo(result);
        } catch (error) {
          // Ignore errors for this check
          setPendingInfo(null);
        }
      } else {
        setPendingInfo(null);
      }
    };

    const timeoutId = setTimeout(checkPending, 500); // Debounce
    return () => clearTimeout(timeoutId);
  }, [form.values.email]);

  const registerMutation = useMutation({
    mutationFn: async (values: typeof form.values) => {
      const response = await authApi.register(values.username, values.email, values.password);
      return response;
    },
    onSuccess: (data) => {
      if (data.requires_verification && data.email) {
        // Show success notification
        notifications.show({
          title: t('auth.register.success.title'),
          message: data.message.includes('updated') 
            ? t('auth.register.success.updated') 
            : t('auth.register.success.created'),
          color: 'green',
        });
        
        // Redirect to email verification page
        navigate(`/verify-email?email=${encodeURIComponent(data.email)}`);
      } else if (data.user) {
        // Fallback: set user if verification not required (shouldn't happen with new flow)
        setUser(data.user);
        navigate('/');
      }
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.error || t('common.error');
      setError(errorMessage);
      
      // If it's a username conflict with different email, suggest using different username
      if (errorMessage.includes('Username already exists in pending registrations')) {
        notifications.show({
          title: t('auth.register.errors.usernameConflict'),
          message: t('auth.register.errors.usernameConflictMessage'),
          color: 'orange',
        });
      }
    },
  });

  const handleSubmit = (values: typeof form.values) => {
    setError('');
    registerMutation.mutate(values);
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      setGoogleLoading(true);
      await signInWithGoogle();
      // The OAuth flow will redirect to /auth/callback
    } catch (error: any) {
      console.error('Google sign-in failed:', error);
      setError(t('auth.register.errors.googleSignInFailed'));
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleGoToVerification = () => {
    if (pendingInfo?.email) {
      navigate(`/verify-email?email=${encodeURIComponent(pendingInfo.email)}`);
    }
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center" mb="lg">
        {t('auth.register.title')}
      </Title>
      
      <Paper withBorder shadow="md" p={30} radius="md">
        {fromVerification && (
          <Alert color="blue" mb="md" icon={<IconInfoCircle />}>
            {t('auth.register.fromVerification')}
          </Alert>
        )}

        {pendingInfo?.has_pending_registration && (
          <Alert color="orange" mb="md" icon={<IconInfoCircle />}>
            <Text size="sm" mb="xs">
              {t('auth.register.pendingExists', { email: pendingInfo.email })}
            </Text>
            <Group gap="xs">
              <Button size="xs" variant="light" onClick={handleGoToVerification}>
                {t('auth.register.goToVerification')}
              </Button>
            </Group>
          </Alert>
        )}

        {error && (
          <Alert color="red" mb="md">
            {error}
          </Alert>
        )}
          
        {/* Google Sign-In Button */}
        <Button
          fullWidth
          variant="outline"
          leftSection={<IconBrandGoogle size={18} />}
          onClick={handleGoogleSignIn}
          loading={googleLoading}
          color="red"
          mb="md"
        >
          {t('auth.register.continueWithGoogle')}
        </Button>

        <Divider label={t('auth.register.orDivider')} labelPosition="center" mb="md" />

        {/* Traditional Registration Form */}
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <TextInput
            label={t('auth.register.username')}
            placeholder={t('auth.register.usernamePlaceholder')}
            required
            {...form.getInputProps('username')}
          />
          
          <TextInput
            label={t('auth.register.email')}
            placeholder={t('auth.register.emailPlaceholder')}
            required
            mt="md"
            {...form.getInputProps('email')}
          />
          
          <PasswordInput
            label={t('auth.register.password')}
            placeholder={t('auth.register.passwordPlaceholder')}
            required
            mt="md"
            {...form.getInputProps('password')}
          />
          
          <PasswordInput
            label={t('auth.register.confirmPassword')}
            placeholder={t('auth.register.confirmPasswordPlaceholder')}
            required
            mt="md"
            {...form.getInputProps('confirmPassword')}
          />
          
          <Button fullWidth mt="xl" type="submit" loading={registerMutation.isPending}>
            {pendingInfo?.has_pending_registration 
              ? t('auth.register.updateRegistration')
              : t('auth.register.createAccount')
            }
          </Button>
        </form>
        
        <Text ta="center" mt="md">
          {t('auth.register.haveAccount')}{' '}
          <Link to="/login" style={{ color: 'var(--mantine-color-blue-6)' }}>
            {t('auth.register.signIn')}
          </Link>
        </Text>
      </Paper>
    </Container>
  );
} 