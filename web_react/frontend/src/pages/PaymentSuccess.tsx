import { Container, Title, Text, Group, But<PERSON>, Card, Stack } from '@mantine/core';
import { IconCheck, IconHome } from '@tabler/icons-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useAuthStore } from '../store/useAuthStore';
import { paymentsApi } from '../services/api';
import { useTranslation } from 'react-i18next';

export function PaymentSuccess() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, setUser } = useAuthStore();
  const sessionId = searchParams.get('session_id');
  const previousMembershipDataRef = useRef<any>(null);
  const { t } = useTranslation();

  // Refresh membership status after successful payment
  const { data: membershipData } = useQuery({
    queryKey: ['membership-status', sessionId],
    queryFn: paymentsApi.getMembershipStatus,
    enabled: !!sessionId, // Only run if we have a session ID
    refetchOnMount: true,
    refetchOnWindowFocus: false,
  });

  // Update user data when membership status is fetched
  useEffect(() => {
    if (membershipData?.user && user) {
      // Check if this is new data by comparing with previous data
      const isDifferentFromPrevious = 
        !previousMembershipDataRef.current ||
        previousMembershipDataRef.current.membership_type !== membershipData.user.membership_type ||
        previousMembershipDataRef.current.membership_expires_at !== membershipData.user.membership_expires_at;
      
      // Also check if it's different from current user data
      const isDifferentFromCurrentUser = 
        membershipData.user.membership_type !== user.membership_type ||
        membershipData.user.membership_expires_at !== user.membership_expires_at;
      
      if (isDifferentFromPrevious && isDifferentFromCurrentUser) {
        const updatedUser = {
          ...user,
          membership_type: membershipData.user.membership_type || user.membership_type,
          membership_expires_at: membershipData.user.membership_expires_at || user.membership_expires_at,
        };
        setUser(updatedUser);
        
        // Update the ref to track this data
        previousMembershipDataRef.current = {
          membership_type: membershipData.user.membership_type,
          membership_expires_at: membershipData.user.membership_expires_at,
        };
      }
    }
  }, [membershipData, setUser]); // Removed 'user' from dependencies to prevent infinite loop

  return (
    <Container size="sm" py="xl">
      <Card shadow="md" radius="md" p="xl">
        <Stack align="center" gap="lg">
          <div style={{ 
            width: 80, 
            height: 80, 
            borderRadius: '50%', 
            backgroundColor: '#28a745', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center' 
          }}>
            <IconCheck size={40} color="white" />
          </div>
          
          <Title order={2} ta="center" c="green">
            {t('payments.success.title')}
          </Title>
          
          <Text ta="center" c="dimmed" size="lg">
            {t('payments.success.description')}
          </Text>
          
          {membershipData && (
            <Text ta="center" size="sm" c="green" fw={500}>
              {t('payments.success.membershipUpdated')}
            </Text>
          )}
          
          {sessionId && (
            <Text ta="center" size="sm" c="dimmed">
              {t('payments.success.sessionId', { sessionId })}
            </Text>
          )}
          
          <Group gap="md">
            <Button 
              leftSection={<IconHome size={16} />}
              onClick={() => navigate('/')}
              size="lg"
            >
              {t('payments.success.backToHome')}
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => navigate('/profile')}
              size="lg"
            >
              {t('payments.success.viewProfile')}
            </Button>
          </Group>
        </Stack>
      </Card>
    </Container>
  );
} 