import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Contain<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Loader } from '@mantine/core';
import { supabase } from '../lib/supabase';
import { authApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';

export function AuthCallback() {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const { login } = useAuthStore();
  const { t } = useTranslation();

  useEffect(() => {
    const handleAuth = async () => {
      try {
        // Get the session from Supabase
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Supabase session error:', sessionError);
          setError(t('authCallback.error'));
          setLoading(false);
          return;
        }

        if (!session?.access_token) {
          setError(t('authCallback.noToken'));
          setLoading(false);
          return;
        }

        // Send the tokens to our backend
            const response = await authApi.googleCallback(
              session.access_token,
              session.refresh_token || '',
              session.user
            );
            
        if (response.user) {
          login(response.user);
            navigate('/');
        } else {
          setError(t('authCallback.authFailed'));
        }
      } catch (error: any) {
        console.error('Auth callback failed:', error);
        setError(t('authCallback.authFailed'));
      } finally {
        setLoading(false);
      }
    };

    handleAuth();
  }, [navigate, login, t]);

  if (loading) {
  return (
    <Container size="sm" py="xl">
        <Stack align="center" gap="lg">
          <Loader size="lg" />
          <Text>{t('loadingStates.authenticating')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="sm" py="xl">
        <Stack align="center" gap="lg">
          <Alert color="red" style={{ width: '100%' }}>
            {error}
          </Alert>
          <Button onClick={() => navigate('/login')}>
            {t('authCallback.backToLogin')}
        </Button>
        </Stack>
    </Container>
  );
  }

  return null;
} 