import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { 
  Container, 
  Title, 
  Text, 
  Button, 
  Group, 
  Box, 
  Card, 
  Badge, 
  Accordion,
  <PERSON><PERSON>,
  <PERSON>lapse,
  Loader,
  Stack
} from '@mantine/core';
import { IconArrowLeft, IconBulb, IconEye, IconEyeOff, IconLock, IconInfoCircle } from '@tabler/icons-react';
import { speakingService } from '../services/speakingService';
import type { Task3TopicData } from '../services/speakingService';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';

export default function SpeakingTask3Details() {
  const { topicId } = useParams<{ topicId: string }>();
  const [topicData, setTopicData] = useState<Task3TopicData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [visibleAnswers, setVisibleAnswers] = useState<Record<number, boolean>>({});
  const { isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();

  const canViewFullContent = isMember() && !isMembershipExpired();

  useEffect(() => {
    const loadData = async () => {
      if (!topicId) {
        setError(t('speakingTask3.dataNotFound'));
        setLoading(false);
        return;
      }

      try {
        const data = await speakingService.getTask3TopicData(topicId);
        if (!data) {
          setError(t('speakingTask3.dataNotFound'));
        } else {
          setTopicData(data);
        }
      } catch (error) {
        console.error('Error loading topic data:', error);
        setError(t('speakingTask3.loadingError'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [topicId, t]);

  const toggleAnswer = (taskNumber: number) => {
    setVisibleAnswers(prev => ({
      ...prev,
      [taskNumber]: !prev[taskNumber]
    }));
  };

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="orange" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.questions')}</Text>
          <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error || !topicData) {
    return (
      <Container size="lg" py="xl">
        <Alert color="red" title="Erreur">
          {error || t('speakingTask3.dataUnavailable')}
        </Alert>
        <Button component={Link} to="/speaking" mt="md">
          {t('speakingTask3.backToSpeaking')}
        </Button>
      </Container>
    );
  }

  // Check if user has access to premium content
  const hasExemplaryAnswers = topicData.questions.some(question => 
    question.exemplary_answer && 
    (typeof question.exemplary_answer === 'string' ? question.exemplary_answer.length > 0 : question.exemplary_answer.full_text)
  );

  return (
    <Container size="lg" py="xl">
      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Button 
          component={Link} 
          to="/speaking" 
          variant="subtle" 
          leftSection={<IconArrowLeft size={16} />}
        >
          {t('speakingTask3.backToSpeaking')}
        </Button>
        <Badge size="lg" color="orange">{t('speakingTask3.subtitle')}</Badge>
      </Group>

      {/* Title and Info */}
      <Box ta="center" mb="xl">
        <Group justify="center" mb="md">
          <IconBulb size={32} color="#fd7e14" />
          <Title order={1} c="orange">{topicData.topic}</Title>
        </Group>
        
        <Text size="lg" c="dimmed" mb="md">
          {t('speakingTask3.title')}
        </Text>

        {/* Statistics */}
        <Group grow>
          <Card withBorder p="md">
            <Text size="lg" fw={600} c="orange">{topicData.questions.length}</Text>
            <Text size="sm" c="dimmed">{t('speakingTask3.questions')}</Text>
          </Card>
        </Group>
      </Box>

      {/* Questions and Answers */}
      <Box>
        {topicData.questions.map((question, questionIndex) => {
          const hasAnswer = question.exemplary_answer && 
            (typeof question.exemplary_answer === 'string' ? question.exemplary_answer.length > 0 : question.exemplary_answer.full_text);
          
          return (
            <Card key={question.task_number} shadow="sm" padding="lg" radius="md" withBorder mb="lg">
              <Group justify="space-between" mb="md">
                <Badge size="md" color="orange" variant="light">
                  {t('speakingTask3.task')} {question.task_number}
                </Badge>
                <Group gap="xs">
                  {canViewFullContent && hasAnswer ? (
                    <Button
                      size="xs"
                      variant={visibleAnswers[question.task_number] ? "filled" : "outline"}
                      color={visibleAnswers[question.task_number] ? "green" : "orange"}
                      leftSection={visibleAnswers[question.task_number] ? <IconEyeOff size={12} /> : <IconEye size={12} />}
                      onClick={() => toggleAnswer(question.task_number)}
                    >
                      {visibleAnswers[question.task_number] ? t('speakingTask3.hideCorrection') : t('speakingTask3.correction')}
                    </Button>
                  ) : (
                    <Button
                      size="xs"
                      variant="outline"
                      color="gray"
                      leftSection={<IconLock size={12} />}
                      disabled
                    >
                      {t('writing.taskDetails.premiumRequired')}
                    </Button>
                  )}
                </Group>
              </Group>

              {/* Question */}
              <Box mb="lg">
                <Group mb="sm">
                  <IconBulb size={20} color="#fd7e14" />
                  <Text fw={600} c="orange">{t('speakingTask3.question')}</Text>
                </Group>
                
                <Card withBorder p="md" bg="orange.0">
                  <Text fw={500} size="md" style={{
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'system-ui, -apple-system, sans-serif',
                    letterSpacing: '0.025em',
                    lineHeight: 1.6
                  }}>
                    {question.question}
                  </Text>
                </Card>
              </Box>

                  {/* Exemplary Answer */}
              {canViewFullContent && hasAnswer && (
                  <Collapse in={visibleAnswers[question.task_number]}>
                    <Box>
                    <Text fw={600} mb="sm" c="green">{t('speakingTask3.exemplaryAnswer')}</Text>
                      <Card withBorder p="md" bg="green.0">
                        <Text size="sm" style={{ 
                          whiteSpace: 'pre-wrap',
                          fontFamily: 'system-ui, -apple-system, sans-serif',
                          letterSpacing: '0.025em',
                          lineHeight: 1.6 
                        }}>
                          {typeof question.exemplary_answer === 'string' ? 
                            question.exemplary_answer : 
                            question.exemplary_answer.full_text
                          }
                        </Text>
                      </Card>
                      

                    </Box>
                  </Collapse>
              )}
            </Card>
          );
        })}
      </Box>

      {/* Back Button */}
      <Group justify="center" mt="xl">
        <Button 
          component={Link} 
          to="/speaking" 
          size="lg"
          leftSection={<IconArrowLeft size={18} />}
        >
          {t('speakingTask3.backToSpeaking')}
        </Button>
      </Group>
    </Container>
  );
} 