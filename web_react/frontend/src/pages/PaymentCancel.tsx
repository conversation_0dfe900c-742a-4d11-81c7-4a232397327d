import { Container, Title, Text, Group, But<PERSON>, Card, Stack } from '@mantine/core';
import { IconX, IconHome, IconCreditCard } from '@tabler/icons-react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

export function PaymentCancel() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Container size="sm" py="xl">
      <Card shadow="md" radius="md" p="xl">
        <Stack align="center" gap="lg">
          <div style={{ 
            width: 80, 
            height: 80, 
            borderRadius: '50%', 
            backgroundColor: '#dc3545', 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center' 
          }}>
            <IconX size={40} color="white" />
          </div>
          
          <Title order={2} ta="center" c="red">
            {t('payments.cancel.title')}
          </Title>
          
          <Text ta="center" c="dimmed" size="lg">
            {t('payments.cancel.description')}
          </Text>
          
          <Text ta="center" c="dimmed">
            {t('payments.cancel.tryAgainInfo')}
          </Text>
          
          <Group gap="md">
            <Button 
              leftSection={<IconHome size={16} />}
              onClick={() => navigate('/')}
              size="lg"
              variant="outline"
            >
              {t('payments.cancel.backToHome')}
            </Button>
            
            <Button 
              leftSection={<IconCreditCard size={16} />}
              onClick={() => navigate('/membership')}
              size="lg"
            >
              {t('payments.cancel.tryAgain')}
            </Button>
          </Group>
        </Stack>
      </Card>
    </Container>
  );
} 