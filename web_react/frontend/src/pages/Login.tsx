import { TextInput, PasswordInput, Button, Paper, Title, Text, Container, Divider, Group, Alert, Modal, Stack, Box } from '@mantine/core';
import { useForm } from '@mantine/form';
import { Link, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { authApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { signInWithGoogle } from '../lib/supabase';
import { IconBrandGoogle, IconMail, IconKey, IconCheck } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';

export function Login() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  const [error, setError] = useState('');
  const [googleLoading, setGoogleLoading] = useState(false);
  const [forgotPasswordOpened, setForgotPasswordOpened] = useState(false);
  const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);

  // Add custom styles for modal centering
  const modalStyles = {
    content: {
      backgroundColor: '#ffffff',
      position: 'fixed' as const,
      top: '30%',
      left: '50%',
      transform: 'translate(-50%, -50%)',
      margin: 0,
      maxWidth: '500px',
      width: '90%',
      maxHeight: '90vh',
      overflowY: 'auto' as const,
    },
    header: {
      backgroundColor: '#f8f9fa',
      borderBottom: '1px solid #e9ecef',
      padding: '20px 24px 16px 24px',
    },
    body: {
      padding: '24px',
    },
    title: {
      margin: 0,
    },
    overlay: {
      backgroundColor: 'rgba(0, 0, 0, 0.55)',
      backdropFilter: 'blur(3px)',
    },
  };

  const form = useForm({
    initialValues: {
      email: '',
      password: '',
    },
    validate: {
      email: (value) => (value.length < 3 ? t('auth.login.errors.usernameRequired') : null),
      password: (value) => (value.length < 6 ? t('auth.login.errors.passwordMinLength') : null),
    },
  });

  const forgotPasswordForm = useForm({
    initialValues: {
      email: '',
    },
    validate: {
      email: (value) => (!/^\S+@\S+$/.test(value) ? t('auth.register.errors.emailInvalid') : null),
    },
  });

  const handleSubmit = async (values: { email: string; password: string }) => {
    try {
      setError('');
      const response = await authApi.login(values.email, values.password);
      if (response.user) {
        setUser(response.user);
        navigate('/');
      }
    } catch (error: any) {
      console.error('Login failed:', error);
      
      // Check if it's an email verification error
      if (error.response?.data?.code === 'EMAIL_NOT_VERIFIED') {
        notifications.show({
          title: t('auth.login.errors.emailNotVerified'),
          message: t('auth.login.errors.verifyEmailMessage'),
          color: 'orange',
        });
        // Redirect to email verification page
        navigate(`/verify-email?email=${encodeURIComponent(error.response.data.email)}`);
        return;
      }
      
      setError(t('auth.login.errors.invalidCredentials'));
      form.setErrors({ email: t('auth.login.errors.invalidCredentials') });
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      setError('');
      setGoogleLoading(true);
      await signInWithGoogle();
      // The OAuth flow will redirect to /auth/callback
    } catch (error: any) {
      console.error('Google sign-in failed:', error);
      setError(t('auth.login.errors.googleSignInFailed'));
    } finally {
      setGoogleLoading(false);
    }
  };

  const handleForgotPassword = async (values: { email: string }) => {
    try {
      setForgotPasswordLoading(true);
      await authApi.forgotPassword(values.email);
      
      notifications.show({
        title: t('auth.forgotPassword.success.title'),
        message: t('auth.forgotPassword.success.message'),
        color: 'green',
      });
      
      setForgotPasswordOpened(false);
      forgotPasswordForm.reset();
    } catch (error: any) {
      console.error('Forgot password failed:', error);
      notifications.show({
        title: t('auth.forgotPassword.error.title'),
        message: t('auth.forgotPassword.error.message'),
        color: 'red',
      });
    } finally {
      setForgotPasswordLoading(false);
    }
  };

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        {t('auth.login.title')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('auth.login.noAccount')}{' '}
        <Link to="/register" style={{ color: 'inherit' }}>
          {t('auth.login.createAccount')}
        </Link>
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {error && (
          <Alert color="red" mb="md" radius="md">
            {error}
          </Alert>
        )}

        {/* Google Sign-In Button */}
        <Button
          fullWidth
          variant="outline"
          leftSection={<IconBrandGoogle size={18} />}
          onClick={handleGoogleSignIn}
          loading={googleLoading}
          color="red"
          mb="md"
          size="md"
          radius="md"
          styles={{
            root: {
              height: '48px',
              fontSize: '15px',
              fontWeight: 500,
            }
          }}
        >
          {t('auth.login.continueWithGoogle')}
        </Button>

        <Divider label={t('auth.login.orDivider')} labelPosition="center" mb="md" />

        {/* Traditional Login Form */}
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <TextInput
            label={t('auth.login.usernameEmail')}
            placeholder={t('auth.login.usernamePlaceholder')}
            required
            {...form.getInputProps('email')}
            size="md"
            radius="md"
            styles={{
              input: {
                fontSize: '16px',
                height: '48px',
              },
              label: {
                fontWeight: 600,
                marginBottom: '8px',
                fontSize: '14px',
              }
            }}
          />
          <PasswordInput
            label={t('auth.login.password')}
            placeholder={t('auth.login.passwordPlaceholder')}
            required
            mt="md"
            {...form.getInputProps('password')}
            size="md"
            radius="md"
            styles={{
              input: {
                fontSize: '16px',
                height: '48px',
              },
              label: {
                fontWeight: 600,
                marginBottom: '8px',
                fontSize: '14px',
              }
            }}
          />
          
          <Group justify="space-between" mt="md">
            <Button
              variant="subtle"
              size="sm"
              onClick={() => setForgotPasswordOpened(true)}
              c="blue"
              fw={500}
              styles={{
                root: {
                  height: 'auto',
                  padding: '8px 12px',
                  fontSize: '14px',
                  '&:hover': {
                    backgroundColor: '#e7f3ff',
                  }
                }
              }}
              leftSection={<IconKey size={14} />}
            >
              {t('auth.login.forgotPassword')}
            </Button>
          </Group>
          
          <Button 
            fullWidth 
            mt="xl" 
            type="submit"
            size="md"
            radius="md"
            gradient={{ from: 'blue', to: 'cyan', deg: 45 }}
            styles={{
              root: {
                border: 0,
                height: '48px',
                fontSize: '16px',
                fontWeight: 600,
              }
            }}
          >
            {t('auth.login.signIn')}
          </Button>
        </form>
      </Paper>

      {/* Enhanced Forgot Password Modal */}
      <Modal
        opened={forgotPasswordOpened}
        onClose={() => setForgotPasswordOpened(false)}
        title={
          <Group gap="sm">
            <IconKey size={24} color="#007bff" />
            <Text fw={700} size="lg">{t('auth.forgotPassword.title')}</Text>
          </Group>
        }
        size="md"
        radius="md"
        shadow="xl"
        closeOnClickOutside
        closeOnEscape
        overlayProps={{
          backgroundOpacity: 0.55,
          blur: 3,
        }}
        styles={modalStyles}
        transitionProps={{
          transition: 'fade',
          duration: 200,
        }}
        keepMounted={false}
        trapFocus
        lockScroll
      >
        <form onSubmit={forgotPasswordForm.onSubmit(handleForgotPassword)}>
          <Stack gap="lg">
            <Alert color="blue" variant="light" radius="md">
              <Group gap="sm" align="flex-start">
                <IconMail size={20} style={{ marginTop: '2px' }} />
                <Box>
                  <Text size="sm" fw={500} mb="xs">
                    {t('auth.forgotPassword.description')}
                  </Text>
                  <Text size="sm" c="dimmed">
                    {t('auth.forgotPassword.instructions')}
                  </Text>
                </Box>
              </Group>
            </Alert>
            
            <TextInput
              label={t('auth.forgotPassword.emailLabel')}
              placeholder={t('auth.forgotPassword.emailPlaceholder')}
              required
              leftSection={<IconMail size={18} />}
              {...forgotPasswordForm.getInputProps('email')}
              size="md"
              radius="md"
              styles={{
                input: {
                  fontSize: '16px',
                  padding: '12px 40px 12px 40px',
                },
                label: {
                  fontWeight: 600,
                  marginBottom: '8px',
                  fontSize: '14px',
                }
              }}
            />
            
            <Alert color="orange" variant="light" radius="md">
              <Group gap="sm" align="flex-start">
                <Text size="24px" style={{ lineHeight: 1 }}>💡</Text>
                <Box>
                  <Text size="sm" fw={500} mb="xs">
                    {t('auth.forgotPassword.tips.title')}
                  </Text>
                  <Text size="sm" c="dimmed">
                    {t('auth.forgotPassword.tips.spam')}<br />
                    {t('auth.forgotPassword.tips.expires')}<br />
                    {t('auth.forgotPassword.tips.correctEmail')}
                  </Text>
                </Box>
              </Group>
            </Alert>
            
            <Group justify="flex-end" gap="md" mt="md">
              <Button 
                variant="subtle" 
                onClick={() => setForgotPasswordOpened(false)}
                size="md"
                radius="md"
                c="dimmed"
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: '#f8f9fa',
                    }
                  }
                }}
              >
                {t('auth.forgotPassword.cancel')}
              </Button>
              <Button 
                type="submit" 
                loading={forgotPasswordLoading}
                leftSection={<IconCheck size={18} />}
                size="md"
                radius="md"
                gradient={{ from: 'blue', to: 'cyan', deg: 45 }}
                styles={{
                  root: {
                    border: 0,
                    height: '42px',
                    paddingLeft: '24px',
                    paddingRight: '24px',
                  }
                }}
              >
                {forgotPasswordLoading ? t('auth.forgotPassword.sending') : t('auth.forgotPassword.sendLink')}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </Container>
  );
} 