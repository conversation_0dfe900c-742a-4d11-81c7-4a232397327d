import { Container, Paper, TextInput, Button, Title, Text, Alert, Stack, Group, PinInput, Box } from '@mantine/core';
import { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { authApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { IconMail, IconCheck, IconAlertCircle, IconRefresh } from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';

export function EmailVerification() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { setUser } = useAuthStore();
  const [searchParams] = useSearchParams();
  const [verificationCode, setVerificationCode] = useState('');
  const [email, setEmail] = useState(searchParams.get('email') || '');
  const [timeLeft, setTimeLeft] = useState(15 * 60); // 15 minutes in seconds

  // Countdown timer
  useEffect(() => {
    if (timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const verifyEmailMutation = useMutation({
    mutationFn: async (code: string) => {
      if (!email) throw new Error('Email is required');
      return authApi.verifyEmail(email, code);
    },
    onSuccess: (data) => {
      if (data.user) {
        setUser(data.user);
        notifications.show({
          title: t('emailVerification.success.title'),
          message: t('emailVerification.success.message'),
          color: 'green',
        });
        navigate('/');
      }
    },
    onError: (error: any) => {
      notifications.show({
        title: t('emailVerification.error.title'),
        message: error.response?.data?.error || t('emailVerification.error.invalidCode'),
        color: 'red',
      });
    },
  });

  const resendMutation = useMutation({
    mutationFn: () => authApi.resendVerification(email),
    onSuccess: () => {
      setTimeLeft(15 * 60); // Reset timer
      notifications.show({
        title: t('emailVerification.resend.title'),
        message: t('emailVerification.resend.message'),
        color: 'green',
      });
    },
    onError: (error: any) => {
      notifications.show({
        title: t('emailVerification.error.title'),
        message: error.response?.data?.error || t('emailVerification.error.resendFailed'),
        color: 'red',
      });
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (verificationCode.length === 6) {
      verifyEmailMutation.mutate(verificationCode);
    }
  };

  const handlePinChange = (value: string) => {
    setVerificationCode(value);
    // Auto-submit when 6 digits are entered
    if (value.length === 6) {
      verifyEmailMutation.mutate(value);
    }
  };

  if (!email) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <Alert color="red" icon={<IconAlertCircle />}>
            {t('emailVerification.missingEmail')}
          </Alert>
          <Group justify="center" mt="md">
            <Button component={Link} to="/register">
              {t('emailVerification.backToRegister')}
            </Button>
          </Group>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        {t('emailVerification.title')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('emailVerification.subtitle')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <Alert color="blue" variant="light" mb="md">
          <Group gap="xs">
            <IconMail size={16} />
            <Box>
              <Text size="sm">
                {t('emailVerification.codeSent')}
              </Text>
              <Text size="sm" fw={500}>
                {email}
              </Text>
            </Box>
          </Group>
        </Alert>

        <form onSubmit={handleSubmit}>
          <Stack gap="md">
            <Box>
              <Text size="sm" fw={500} mb="xs">
                {t('emailVerification.codeLabel')}
              </Text>
              <Group justify="center">
                <PinInput
                  length={6}
                  value={verificationCode}
                  onChange={handlePinChange}
                  size="lg"
                  placeholder="0"
                  type="number"
                  oneTimeCode
                />
              </Group>
            </Box>

            {timeLeft > 0 ? (
              <Alert color="orange" variant="light">
                <Text size="sm">
                  ⏰ {t('emailVerification.codeValid')} <strong>{formatTime(timeLeft)}</strong>
                </Text>
              </Alert>
            ) : (
              <Alert color="red" variant="light">
                <Text size="sm">
                  ⚠️ {t('emailVerification.codeExpired')}
                </Text>
              </Alert>
            )}

            <Button 
              fullWidth 
              type="submit" 
              loading={verifyEmailMutation.isPending}
              disabled={verificationCode.length !== 6}
              leftSection={<IconCheck size={16} />}
            >
              {t('emailVerification.verifyButton')}
            </Button>
          </Stack>
        </form>

        <Stack gap="sm" mt="lg">
          <Text size="sm" c="dimmed" ta="center">
            {t('emailVerification.noCodeReceived')}
          </Text>
          
          <Group justify="center" gap="sm">
            <Button
              variant="light"
              size="sm"
              onClick={() => resendMutation.mutate()}
              loading={resendMutation.isPending}
              disabled={timeLeft > 13 * 60} // Allow resend after 2 minutes
              leftSection={<IconRefresh size={14} />}
            >
              {t('emailVerification.resendCode')}
            </Button>
          </Group>

          {timeLeft > 13 * 60 && (
            <Text size="xs" c="dimmed" ta="center">
              {t('emailVerification.canResendIn', { time: formatTime(timeLeft - 13 * 60) })}
            </Text>
          )}
        </Stack>

        <Alert color="gray" variant="light" mt="lg">
          <Text size="xs">
            💡 <strong>{t('emailVerification.tips.title')}</strong><br />
            • {t('emailVerification.tips.checkSpam')}<br />
            • {t('emailVerification.tips.validFor')}<br />
            • {t('emailVerification.tips.caseSensitive')}
          </Text>
        </Alert>

        <Group justify="center" mt="lg">
          <Text size="xs" c="dimmed">
            {t('emailVerification.wrongEmail')}{' '}
            <Link to={`/register?email=${encodeURIComponent(email)}&from=verification`} style={{ color: 'var(--mantine-color-blue-6)' }}>
              {t('emailVerification.registerAgain')}
            </Link>
          </Text>
        </Group>
      </Paper>
    </Container>
  );
} 