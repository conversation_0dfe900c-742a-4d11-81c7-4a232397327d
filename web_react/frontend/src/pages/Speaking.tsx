import { useState, useEffect, useMemo, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Container, 
  Title, 
  Text, 
  Card, 
  Stack, 
  Group, 
  Badge, 
  Button, 
  Accordion,
  SimpleGrid,
  Loader,
  Alert,
  Box,
  <PERSON>readcrumbs,
  Anchor,
  Progress
} from '@mantine/core';
import { IconMicrophone, IconInfoCircle, IconPlayerPlay, IconLock, IconBulb } from '@tabler/icons-react';
import { speakingService } from '../services/speakingService';
import type { Task2MonthData, Task3TopicData } from '../services/speakingService';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';

export default function Speaking() {
  const [task2Data, setTask2Data] = useState<Record<string, Task2MonthData>>({});
  const [task3Data, setTask3Data] = useState<Record<string, Task3TopicData>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();
  const themeColors = useThemeColors();

  // Format month name for display
  const formatMonthName = (monthYear: string): string => {
    const [month, year] = monthYear.split('-');

    const monthNames: { [key: string]: string } = {
      'janvier': 'Janvier', 'fevrier': 'Février', 'mars': 'Mars',
      'avril': 'Avril', 'mai': 'Mai', 'juin': 'Juin',
      'juillet': 'Juillet', 'aout': 'Août', 'septembre': 'Septembre',
      'octobre': 'Octobre', 'novembre': 'Novembre', 'decembre': 'Décembre'
    };

    return `${monthNames[month.toLowerCase()] || month} ${year}`;
  };

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const [task2Response, task3Response] = await Promise.all([
          speakingService.loadTask2Data(),
          speakingService.loadTask3Data()
        ]);
        setTask2Data(task2Response);
        setTask3Data(task3Response);
      } catch (err) {
        console.error('Error loading speaking data:', err);
        setError(t('speaking.errors.loadingData'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [t]);

  const statistics = useMemo(() => {
    const totalTask2Months = Object.keys(task2Data).length;
    const totalTask2Parties = Object.values(task2Data).reduce((total, month) =>
      total + (month.statistics?.total_parties || 0), 0
    );
    const totalTask3Topics = Object.keys(task3Data).length;
    const totalTask3Questions = Object.values(task3Data).reduce((total, topic) =>
      total + (topic.statistics?.total_tasks || 0), 0
    );

    return {
      totalTask2Months,
      totalTask2Parties,
      totalTask3Topics,
      totalTask3Questions
    };
  }, [task2Data, task3Data]);

  const handleTask2Click = useCallback((monthId: string) => {
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { 
          message: t('readingListeningTests.auth.loginRequired'),
          from: `/speaking/task2/${monthId}` 
        } 
      });
    }
  }, [isAuthenticated, navigate, t]);

  const handleTask3Click = useCallback((topicId: string) => {
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { 
          message: t('readingListeningTests.auth.loginRequired'),
          from: `/speaking/task3/${topicId}` 
        } 
      });
    }
  }, [isAuthenticated, navigate, t]);

  const Task2Card = useMemo(() => {
    return ({ monthId, data }: { monthId: string; data: Task2MonthData }) => {
      const totalParties = data.statistics?.total_parties || 0;
      const totalQuestions = data.statistics?.total_questions || 0;

      const hasExampleQuestions = totalQuestions > 0;

      const handleClick = () => {
        if (!isAuthenticated) {
          handleTask2Click(monthId);
        }
      };

      return (
        <Card
          shadow="sm"
          padding="sm"
          radius="md"
          withBorder
          style={{
            background: themeColors.surface,
            borderColor: themeColors.border,
            color: themeColors.textPrimary,
            transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
            height: '100%',
            position: 'relative'
          }}
        >
          {/* Icon in top left corner */}
          <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
            <IconMicrophone size={20} color="#dc3545" />
          </Box>

          <Stack gap="xs" style={{ height: '100%', paddingTop: '28px' }}>
            {/* Title */}
            <Box>
              <Text fw={500} size="sm" lineClamp={1} mb={4} ta="center">
                {formatMonthName(data.month_year)}
              </Text>
            </Box>

            {/* Content Section */}
            <Box style={{ flex: 1 }}>
              <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '40px' }}>
                <Text size="xs" c="dimmed" ta="center">
                  {t('speaking.task2.parties', { count: totalParties })}
                </Text>
              </Box>
            </Box>

            {/* Action Button */}
            {isAuthenticated ? (
              <Button
                component={Link}
                to={`/speaking/task2/${monthId}`}
                color="red"
                leftSection={<IconPlayerPlay size={12} />}
                size="xs"
                fullWidth
              >
                {t('speaking.task2.explore')}
              </Button>
            ) : (
              <Button
                onClick={handleClick}
                color="red"
                leftSection={<IconPlayerPlay size={12} />}
                size="xs"
                fullWidth
              >
                {t('speaking.task2.explore')}
              </Button>
            )}
          </Stack>
        </Card>
      );
    };
  }, [isAuthenticated, handleTask2Click, t, isMember, themeColors]);

  const Task3Card = useMemo(() => {
    return ({ topicId, data }: { topicId: string; data: Task3TopicData }) => {
      const totalTasks = data.statistics?.total_tasks || 0;
      
      const hasExemplaryAnswers = totalTasks > 0;

      const handleClick = () => {
        if (!isAuthenticated) {
          handleTask3Click(topicId);
        }
      };

      return (
        <Card
          shadow="sm"
          padding="sm"
          radius="md"
          withBorder
          style={{
            background: themeColors.surface,
            borderColor: themeColors.border,
            color: themeColors.textPrimary,
            transition: 'transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
            height: '100%',
            position: 'relative'
          }}
        >
          {/* Icon in top left corner */}
          <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
            <IconBulb size={20} color="#fd7e14" />
          </Box>

          <Stack gap="xs" style={{ height: '100%', paddingTop: '28px' }}>
            {/* Title */}
            <Box>
              <Text fw={500} size="sm" lineClamp={2} mb={4} ta="center">
                {data.topic}
              </Text>
            </Box>

            {/* Content Section */}
            <Box style={{ flex: 1 }}>
              <Box style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', minHeight: '40px' }}>
                <Text size="xs" c="dimmed" ta="center">
                  {t('speaking.task3.questions', { count: totalTasks })}
                </Text>
              </Box>
            </Box>

            {/* Action Button */}
            {isAuthenticated ? (
              <Button
                component={Link}
                to={`/speaking/task3/${topicId}`}
                color="orange"
                leftSection={<IconPlayerPlay size={12} />}
                size="xs"
                fullWidth
              >
                {t('speaking.task3.explore')}
              </Button>
            ) : (
              <Button
                onClick={handleClick}
                color="orange"
                leftSection={<IconPlayerPlay size={12} />}
                size="xs"
                fullWidth
              >
                {t('speaking.task3.explore')}
              </Button>
            )}
          </Stack>
        </Card>
      );
    };
  }, [isAuthenticated, handleTask3Click, t, themeColors]);

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" gap="md">
          <Loader size="lg" />
          <Text>{t('loadingStates.speaking')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert variant="light" color="red" title={t('common.error')}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        <Stack gap="lg" mb="xl">
          <Breadcrumbs>
            <Anchor component={Link} to="/" style={{ textDecoration: 'none' }}>
              {t('readingListeningTests.breadcrumb.home')}
            </Anchor>
            <Text c="dimmed">{t('speaking.title')}</Text>
          </Breadcrumbs>
          
          <Group align="center" gap="md">
            <IconMicrophone size={48} color="#228be6" />
            <div>
              <Title order={1} mb="xs">{t('speaking.title')}</Title>
              <Text c="dimmed" size="lg">{t('speaking.subtitle')}</Text>
            </div>
          </Group>
        </Stack>

        <Card withBorder mb="xl" style={{
          background: themeColors.surface,
          border: `1px solid ${themeColors.border}`,
          transition: 'all 0.3s ease'
        }}>
          <Stack align="center" gap="lg">
            <Group gap="md" justify="center">
              <Box style={{
                background: `linear-gradient(135deg, ${themeColors.speaking} 0%, ${themeColors.speaking}dd 100%)`,
                borderRadius: '50%',
                padding: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <IconMicrophone size={24} color="white" />
              </Box>
              <Title order={3} c={themeColors.speaking} fw={600} ta="center">
                {t('readingListeningTests.guide.speaking.title')}
              </Title>
            </Group>

            <Stack gap="md" align="center" style={{ maxWidth: '800px' }}>
              <Box style={{ textAlign: 'center' }}>
                <Text fw={600} size="md" c={themeColors.textPrimary} mb="md">
                  {t('readingListeningTests.guide.speaking.overview.title')}
                </Text>
                <Stack gap="sm" align="center">
                  <Text size="sm" c={themeColors.textSecondary} ta="center">
                    • {t('readingListeningTests.guide.speaking.overview.duration')}
                  </Text>
                  <Box>
                    <Text size="sm" c={themeColors.textSecondary} mb="xs" ta="center">• {t('readingListeningTests.guide.speaking.overview.tasks.title')}</Text>
                    <Stack gap="xs" align="center">
                      <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.speaking.overview.tasks.task1')}</Text>
                      <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.speaking.overview.tasks.task2')}</Text>
                      <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.speaking.overview.tasks.task3')}</Text>
                    </Stack>
                  </Box>
                </Stack>
              </Box>

              <Box style={{ textAlign: 'center' }}>
                <Text fw={600} size="md" c="#495057" mb="md">
                  {t('readingListeningTests.guide.speaking.features.title')}
                </Text>
                <Group gap="md" justify="center" wrap="wrap">
                  <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.speaking.features.collection')}</Badge>
                  <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.speaking.features.practice')}</Badge>
                  <Badge variant="light" color="blue" size="sm">{t('readingListeningTests.guide.speaking.features.guides')}</Badge>
                </Group>
              </Box>
            </Stack>
          </Stack>
        </Card>

        {statistics.totalTask2Months + statistics.totalTask3Topics > 0 ? (
          <Accordion
            mb="xl"
            styles={{
              root: {
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
                borderRadius: '8px',
              },
              item: {
                backgroundColor: themeColors.surface,
                border: `1px solid ${themeColors.border}`,
                borderRadius: '6px',
                marginBottom: '8px',
              },
              control: {
                backgroundColor: themeColors.surface,
                color: themeColors.textPrimary,
                '&:hover': {
                  backgroundColor: themeColors.surfaceHover,
                },
              },
              panel: {
                backgroundColor: themeColors.surface,
              },
            }}
          >
            {/* Tâche 2 Section */}
            {statistics.totalTask2Months > 0 && (
              <Accordion.Item value="task2">
                <Accordion.Control icon={<IconMicrophone size={20} color="#dc3545" />}>
                  <Text fw={600} c="#dc3545">
                    Tâche 2 - Partie Simulation ({statistics.totalTask2Months})
                  </Text>
                </Accordion.Control>
                <Accordion.Panel>
                  {loading ? (
                    <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
                      <Loader size="lg" color="#dc3545" />
                      <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.speaking')}</Text>
                    </Stack>
                  ) : (
                    <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
                      {Object.entries(task2Data).map(([monthId, data]) => (
                        <Task2Card key={`task2-${monthId}`} monthId={monthId} data={data} />
                      ))}
                    </SimpleGrid>
                  )}
                </Accordion.Panel>
              </Accordion.Item>
            )}

            {/* Tâche 3 Section */}
            {statistics.totalTask3Topics > 0 && (
              <Accordion.Item value="task3">
                <Accordion.Control icon={<IconBulb size={20} color="#fd7e14" />}>
                  <Text fw={600} c="#fd7e14">
                    Tâche 3 - Monologue Suivi ({statistics.totalTask3Topics})
                  </Text>
                </Accordion.Control>
                <Accordion.Panel>
                  {loading ? (
                    <Stack align="center" justify="center" style={{ minHeight: '300px' }}>
                      <Loader size="lg" color="#fd7e14" />
                      <Text size="sm" c="dimmed" mt="xs">{t('loadingStates.speaking')}</Text>
                    </Stack>
                  ) : (
                    <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
                      {Object.entries(task3Data).map(([topicId, data]) => (
                        <Task3Card key={`task3-${topicId}`} topicId={topicId} data={data} />
                      ))}
                    </SimpleGrid>
                  )}
                </Accordion.Panel>
              </Accordion.Item>
            )}
          </Accordion>
        ) : (
          <Alert variant="light" color="yellow" title={t('speaking.noTasks.title')} icon={<IconInfoCircle />}>
            {t('speaking.noTasks.message')}
          </Alert>
        )}

        {isAuthenticated && !isMember() && statistics.totalTask2Months + statistics.totalTask3Topics > 0 && (
          <Card 
            padding="xl" 
            radius="md" 
            withBorder
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              textAlign: 'center',
              color: 'white'
            }}
            mb="xl"
          >
            <Title order={3} mb="md" c="white">
              {t('readingListeningTests.membershipUpgrade.title')}
            </Title>
            <Text mb="lg" c="white" opacity={0.9}>
              {t('readingListeningTests.membershipUpgrade.subtitle')}
            </Text>
            <Group justify="center" gap="md">
              <Link to="/membership" style={{ textDecoration: 'none' }}>
                <Button size="lg" style={{ backgroundColor: 'white', color: '#667eea' }} fw={600}>
                  {t('readingListeningTests.membershipUpgrade.upgrade')}
                </Button>
              </Link>
              <Link to="/profile" style={{ textDecoration: 'none' }}>
                <Button variant="outline" size="lg" style={{ borderColor: 'white', color: 'white' }}>
                  {t('readingListeningTests.membershipUpgrade.viewProfile')}
                </Button>
              </Link>
            </Group>
          </Card>
        )}

        {!isAuthenticated && statistics.totalTask2Months + statistics.totalTask3Topics > 0 && (
          <Card 
            padding="xl" 
            radius="md" 
            withBorder
            style={{
              background: 'white',
              textAlign: 'center'
            }}
            mb="xl"
          >
            <Title order={3} mb="md">
              {t('readingListeningTests.callToAction.title')}
            </Title>
            <Text mb="lg" c="dimmed">
              {t('readingListeningTests.callToAction.subtitle')}
            </Text>
            <Group justify="center" gap="md">
              <Link to="/register" style={{ textDecoration: 'none' }}>
                <Button size="lg" color="blue">
                  {t('readingListeningTests.callToAction.register')}
                </Button>
              </Link>
              <Link to="/login" style={{ textDecoration: 'none' }}>
                <Button variant="outline" size="lg" color="blue">
                  {t('readingListeningTests.callToAction.login')}
                </Button>
              </Link>
            </Group>
          </Card>
        )}

        <Group justify="center" mt="xl">
          <Button 
            component={Link} 
            to="/" 
            variant="outline" 
            size="lg" 
            leftSection={<IconMicrophone size={16} />}
          >
            {t('speaking.backToSections')}
          </Button>
        </Group>
      </Stack>
    </Container>
  );
} 