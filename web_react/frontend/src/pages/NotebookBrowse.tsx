import {
  Container,
  Paper,
  Title,
  Text,
  Button,
  Group,
  Stack,
  Card,
  Divider,
  Alert,
  Loader,
  Box,
  Pagination,
  TextInput,
  ActionIcon,
  Tooltip,
  Badge,
  Modal,
  Flex,
  Center
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import {
  IconNotebook,
  IconSearch,
  IconTrash,
  IconCalendar,
  IconFileText,
  IconAlertCircle,
  IconPlus,
  IconEdit
} from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useThemeColors } from '../store/useThemeStore';
import { useAuthStore } from '../store/useAuthStore';
import { notebookApi } from '../services/api';

interface NotebookItem {
  test_path: string;
  notes: string;
  updated_at: string;
}

interface NotebookResponse {
  notebooks: NotebookItem[];
  pagination: {
    page: number;
    per_page: number;
    total_count: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

export function NotebookBrowse() {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const themeColors = useThemeColors();
  const { user } = useAuthStore();
  const queryClient = useQueryClient();
  
  // State
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteOpened, { open: openDelete, close: closeDelete }] = useDisclosure(false);
  const [notebookToDelete, setNotebookToDelete] = useState<NotebookItem | null>(null);

  // Edit state
  const [editOpened, { open: openEdit, close: closeEdit }] = useDisclosure(false);
  const [notebookToEdit, setNotebookToEdit] = useState<NotebookItem | null>(null);
  const [editContent, setEditContent] = useState('');

  const perPage = 10;

  // Fetch notebooks
  const { data: notebooksData, isLoading, error, refetch } = useQuery<NotebookResponse>({
    queryKey: ['notebooks', currentPage, searchQuery],
    queryFn: () => notebookApi.getAllNotes(currentPage, perPage),
    enabled: !!user,
    staleTime: 30000, // 30 seconds
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (testPath: string) => notebookApi.deleteSpecificNote(testPath),
    onSuccess: () => {
      notifications.show({
        title: t('notebook.deleteSuccess', 'Notebook deleted'),
        message: t('notebook.deleteSuccessMessage', 'Your notebook has been deleted successfully'),
        color: 'green',
        autoClose: 3000,
      });
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
      closeDelete();
      setNotebookToDelete(null);
    },
    onError: (error: any) => {
      notifications.show({
        title: t('notebook.deleteError', 'Delete failed'),
        message: error.response?.data?.message || t('notebook.deleteErrorMessage', 'Failed to delete notebook'),
        color: 'red',
        autoClose: 5000,
      });
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ testPath, notes }: { testPath: string; notes: string }) =>
      notebookApi.updateSpecificNote(testPath, notes),
    onSuccess: () => {
      notifications.show({
        title: t('notebook.updateSuccess', 'Notebook updated'),
        message: t('notebook.updateSuccessMessage', 'Your notebook has been updated successfully'),
        color: 'green',
        autoClose: 3000,
      });
      queryClient.invalidateQueries({ queryKey: ['notebooks'] });
      closeEdit();
      setNotebookToEdit(null);
      setEditContent('');
    },
    onError: (error: any) => {
      notifications.show({
        title: t('notebook.updateError', 'Update failed'),
        message: error.response?.data?.message || t('notebook.updateErrorMessage', 'Failed to update notebook'),
        color: 'red',
        autoClose: 5000,
      });
    },
  });

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(undefined, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Get preview text
  const getPreviewText = (notes: string, maxLength: number = 150) => {
    // Strip HTML tags for preview
    const textContent = notes.replace(/<[^>]*>/g, '');
    return textContent.length > maxLength 
      ? textContent.substring(0, maxLength) + '...'
      : textContent;
  };

  // Handle delete
  const handleDeleteClick = (notebook: NotebookItem) => {
    setNotebookToDelete(notebook);
    openDelete();
  };

  const handleDeleteConfirm = () => {
    if (notebookToDelete) {
      deleteMutation.mutate(notebookToDelete.test_path);
    }
  };

  // Handle edit (now the main action)
  const handleOpenNotebook = (notebook: NotebookItem) => {
    setNotebookToEdit(notebook);
    setEditContent(notebook.notes);
    openEdit();
  };

  const handleEditSave = () => {
    if (notebookToEdit && editContent.trim()) {
      updateMutation.mutate({
        testPath: notebookToEdit.test_path,
        notes: editContent.trim()
      });
    }
  };

  // Filter notebooks based on search
  const filteredNotebooks = notebooksData?.notebooks?.filter(notebook =>
    notebook.notes.toLowerCase().includes(searchQuery.toLowerCase()) ||
    notebook.test_path.toLowerCase().includes(searchQuery.toLowerCase())
  ) || [];

  if (!user) {
    return (
      <Container size="md" py="xl">
        <Center>
          <Alert
            icon={<IconAlertCircle size={16} />}
            title={t('auth.loginRequired', 'Login Required')}
            color="blue"
          >
            {t('notebook.loginMessage', 'Please log in to view your saved notebooks.')}
          </Alert>
        </Center>
      </Container>
    );
  }

  return (
    <Container size="lg" py="xl">
      <Stack gap="xl">
        {/* Header */}
        <Box>
          <Group justify="space-between" align="center" mb="md">
            <Group gap="sm">
              <IconNotebook size={32} color={themeColors.primary} />
              <Title order={1} size="h2" c={themeColors.textPrimary}>
                {t('notebook.title', 'My Notebooks')}
              </Title>
            </Group>
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => navigate('/')}
              variant="light"
            >
              {t('notebook.createNew', 'Create New')}
            </Button>
          </Group>
          
          <Text c={themeColors.textSecondary} size="sm">
            {t('notebook.description', 'Browse and manage your saved notebook entries')}
          </Text>
        </Box>

        {/* Search */}
        <TextInput
          placeholder={t('notebook.searchPlaceholder', 'Search notebooks...')}
          leftSection={<IconSearch size={16} />}
          value={searchQuery}
          onChange={(event) => setSearchQuery(event.currentTarget.value)}
          style={{ maxWidth: 400 }}
        />

        {/* Content */}
        {isLoading ? (
          <Center py="xl">
            <Loader size="lg" />
          </Center>
        ) : error ? (
          <Alert
            icon={<IconAlertCircle size={16} />}
            title={t('notebook.loadError', 'Failed to load notebooks')}
            color="red"
          >
            {t('notebook.loadErrorMessage', 'There was an error loading your notebooks. Please try again.')}
          </Alert>
        ) : filteredNotebooks.length === 0 ? (
          <Center py="xl">
            <Stack align="center" gap="md">
              <IconNotebook size={64} color={themeColors.textDimmed} />
              <Text c={themeColors.textSecondary} size="lg" ta="center">
                {searchQuery 
                  ? t('notebook.noSearchResults', 'No notebooks found matching your search')
                  : t('notebook.empty', 'No notebooks saved yet')
                }
              </Text>
              {!searchQuery && (
                <Button
                  leftSection={<IconPlus size={16} />}
                  onClick={() => navigate('/')}
                  variant="light"
                >
                  {t('notebook.createFirst', 'Create Your First Notebook')}
                </Button>
              )}
            </Stack>
          </Center>
        ) : (
          <>
            {/* Notebooks List */}
            <Stack gap="md">
              {filteredNotebooks.map((notebook, index) => (
                <Card
                  key={`${notebook.test_path}-${index}`}
                  p="lg"
                  radius="md"
                  withBorder
                  style={{
                    backgroundColor: themeColors.surface,
                    borderColor: themeColors.border,
                    transition: 'all 0.2s ease',
                  }}
                  styles={{
                    root: {
                      '&:hover': {
                        backgroundColor: themeColors.surfaceHover,
                        transform: 'translateY(-2px)',
                        boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
                      }
                    }
                  }}
                >
                  <Group justify="space-between" align="flex-start">
                    <Box style={{ flex: 1 }}>
                      <Group gap="xs" mb="xs">
                        <Badge
                          variant="light"
                          color={notebook.test_path === 'global' ? 'blue' : 'gray'}
                          size="sm"
                        >
                          {notebook.test_path === 'global' 
                            ? t('notebook.universal', 'Universal') 
                            : notebook.test_path
                          }
                        </Badge>
                        <Group gap="xs">
                          <IconCalendar size={14} color={themeColors.textDimmed} />
                          <Text size="xs" c={themeColors.textDimmed}>
                            {formatDate(notebook.updated_at)}
                          </Text>
                        </Group>
                      </Group>
                      
                      <Text
                        size="sm"
                        c={themeColors.textSecondary}
                        lineClamp={3}
                        mb="sm"
                      >
                        {getPreviewText(notebook.notes)}
                      </Text>
                      
                      <Group gap="xs">
                        <IconFileText size={14} color={themeColors.textDimmed} />
                        <Text size="xs" c={themeColors.textDimmed}>
                          {notebook.notes.replace(/<[^>]*>/g, '').length} {t('notebook.characters', 'characters')}
                        </Text>
                      </Group>
                    </Box>
                    
                    <Group gap="xs">
                      <Tooltip label={t('notebook.open', 'Open')}>
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => handleOpenNotebook(notebook)}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      </Tooltip>

                      <Tooltip label={t('notebook.delete', 'Delete')}>
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => handleDeleteClick(notebook)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                  </Group>
                </Card>
              ))}
            </Stack>

            {/* Pagination */}
            {notebooksData?.pagination && notebooksData.pagination.total_pages > 1 && (
              <Center>
                <Pagination
                  value={currentPage}
                  onChange={setCurrentPage}
                  total={notebooksData.pagination.total_pages}
                  size="sm"
                />
              </Center>
            )}
          </>
        )}
      </Stack>

      {/* Delete Confirmation Modal */}
      <Modal
        opened={deleteOpened}
        onClose={closeDelete}
        title={t('notebook.confirmDelete', 'Confirm Delete')}
        centered
      >
        <Stack gap="md">
          <Text>
            {t('notebook.deleteConfirmMessage', 'Are you sure you want to delete this notebook? This action cannot be undone.')}
          </Text>
          
          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={closeDelete}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              color="red"
              onClick={handleDeleteConfirm}
              loading={deleteMutation.isPending}
            >
              {t('common.delete', 'Delete')}
            </Button>
          </Group>
        </Stack>
      </Modal>

      {/* Notebook Modal */}
      <Modal
        opened={editOpened}
        onClose={closeEdit}
        title={
          <Group gap="sm">
            <IconNotebook size={20} />
            <Text fw={600}>
              {notebookToEdit?.test_path === 'global'
                ? t('notebook.universal', 'Universal Notebook')
                : notebookToEdit?.test_path
              }
            </Text>
          </Group>
        }
        size="xl"
        centered
      >
        <Stack gap="md">
          <Text size="sm" c={themeColors.textSecondary}>
            {t('notebook.editDescription', 'Edit your notebook content below. Changes will be saved automatically.')}
          </Text>

          <Box
            style={{
              border: `1px solid ${themeColors.border}`,
              borderRadius: '8px',
              minHeight: '300px',
              backgroundColor: themeColors.surface,
            }}
          >
            <div
              contentEditable
              style={{
                padding: '1rem',
                minHeight: '280px',
                outline: 'none',
                color: themeColors.textPrimary,
                fontSize: '14px',
                lineHeight: '1.5',
              }}
              dangerouslySetInnerHTML={{ __html: editContent }}
              onInput={(e) => setEditContent(e.currentTarget.innerHTML)}
              onBlur={(e) => setEditContent(e.currentTarget.innerHTML)}
            />
          </Box>

          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={closeEdit} disabled={updateMutation.isPending}>
              {t('common.cancel', 'Cancel')}
            </Button>
            <Button
              onClick={handleEditSave}
              loading={updateMutation.isPending}
              leftSection={<IconEdit size={16} />}
              disabled={!editContent.trim()}
            >
              {t('notebook.saveChanges', 'Save Changes')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
