import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import {
  Container,
  Title,
  Text,
  Card,
  Stack,
  Group,
  Badge,
  Button,
  Divider,
  Alert,
  Accordion,
  Box,
  Loader,
  Collapse
} from '@mantine/core';
import { IconArrowLeft, IconMicrophone, IconLock, IconInfoCircle, IconEye, IconEyeOff } from '@tabler/icons-react';
import { speakingService } from '../services/speakingService';
import type { Task2MonthData } from '../services/speakingService';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';

// Add targeted CSS to fix space calculation issue only for expanded accordion panels
const accordionFixStyles = `
  /* Only apply space fixes to EXPANDED accordion panels */
  .mantine-Accordion-panel[data-active="true"],
  .mantine-Accordion-panel[aria-expanded="true"],
  .mantine-Accordion-item[data-active="true"] .mantine-Accordion-panel,
  .mantine-Accordion-item[data-active="true"] .mantine-Accordion-content {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
  }

  /* Force collapse component to show full content when expanded */
  .mantine-Collapse-root[data-collapse-in="true"],
  .mantine-Collapse-content[data-collapse-in="true"] {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
  }

  /* Ensure collapsed panels maintain normal behavior */
  .mantine-Accordion-panel:not([data-active="true"]),
  .mantine-Accordion-panel:not([aria-expanded="true"]) {
    height: 0 !important;
    overflow: hidden !important;
  }
`;

// Inject styles with targeted selectors
if (typeof document !== 'undefined') {
  const existingStyle = document.getElementById('accordion-space-fix');
  if (existingStyle) {
    existingStyle.remove();
  }

  const styleSheet = document.createElement('style');
  styleSheet.id = 'accordion-space-fix';
  styleSheet.textContent = accordionFixStyles;
  document.head.appendChild(styleSheet);
}

export default function SpeakingTask2Details() {
  const { monthId } = useParams<{ monthId: string }>();
  const [data, setData] = useState<Task2MonthData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [visibleCorrections, setVisibleCorrections] = useState<Record<string, boolean>>({});
  const { isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();

  const canViewFullContent = isMember() && !isMembershipExpired();

  const toggleCorrection = (partieIndex: number, scenarioIndex: number) => {
    const key = `${partieIndex}-${scenarioIndex}`;
    setVisibleCorrections(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  useEffect(() => {
    const loadData = async () => {
      if (!monthId) {
        setError(t('speakingTask2.dataNotFound'));
        setLoading(false);
        return;
      }

      try {
        const monthData = await speakingService.getTask2MonthData(monthId);
        if (monthData) {
          setData(monthData);
        } else {
          setError(t('speakingTask2.dataNotFound'));
        }
      } catch (err) {
        setError(t('speakingTask2.loadingError'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [monthId, t]);

  // Force accordion panels to show full content only when expanded
  useEffect(() => {
    const forceExpandedAccordionStyles = () => {
      // Only target expanded accordion panels
      const expandedPanels = document.querySelectorAll(
        '.mantine-Accordion-panel[data-active="true"], .mantine-Accordion-panel[aria-expanded="true"]'
      );
      expandedPanels.forEach(panel => {
        const element = panel as HTMLElement;
        element.style.overflow = 'visible';
        element.style.maxHeight = 'none';
        element.style.height = 'auto';
      });

      // Only target expanded collapse elements
      const expandedCollapses = document.querySelectorAll('.mantine-Collapse-root[data-collapse-in="true"]');
      expandedCollapses.forEach(collapse => {
        const element = collapse as HTMLElement;
        element.style.overflow = 'visible';
        element.style.maxHeight = 'none';
        element.style.height = 'auto';
      });
    };

    // Apply styles after a delay to ensure DOM is ready
    const timer = setTimeout(forceExpandedAccordionStyles, 300);

    return () => clearTimeout(timer);
  }, [data, visibleCorrections]);

  if (loading) {
    return (
      <Container py="xl">
        <Stack align="center">
          <Loader size="lg" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.speaking')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error || !data) {
    return (
      <Container py="xl">
        <Alert color="red" icon={<IconInfoCircle size={16} />}>
          {error || t('speakingTask2.dataUnavailable')}
        </Alert>
        <Button component={Link} to="/speaking" leftSection={<IconArrowLeft size={16} />} mt="md">
          {t('speakingTask2.backToSpeaking')}
        </Button>
      </Container>
    );
  }

  return (
    <Container py="xl">
      <Stack gap="xl">
      {/* Header */}
        <Stack gap="md">
          <Group>
            <Button component={Link} to="/speaking" leftSection={<IconArrowLeft size={16} />} variant="light">
              {t('speakingTask2.backToSpeaking')}
        </Button>
        </Group>
        
          <Group gap="md" align="center">
            <IconMicrophone size={40} color="var(--mantine-color-blue-6)" />
            <div>
              <Group gap="xs" align="center">
                <Title order={1}>{t('speakingTask2.title')}</Title>
                <Badge size="lg" color="blue">{t('speakingTask2.subtitle')}</Badge>
              </Group>
            </div>
          </Group>
        </Stack>

        {/* Statistics */}
        <Group grow>
          <Card withBorder p="md">
            <Text size="lg" fw={600} c="blue">{data.parties.length}</Text>
            <Text size="sm" c="dimmed">{t('speakingTask2.parties')}</Text>
          </Card>
        </Group>

        {/* Content */}
        <Card withBorder p="lg">
          <Accordion variant="contained" multiple>
            {data.parties.map((partie, partieIndex) => (
              <Accordion.Item key={partieIndex} value={`partie-${partieIndex}`}>
                <Accordion.Control>
                  <Group>
                    <Text fw={500}>Partie {partieIndex + 1}</Text>
                  </Group>
                </Accordion.Control>
                <Accordion.Panel>
                  <Stack gap="md">
                    {partie.scenarios.map((scenario, scenarioIndex) => {
                      const correctionKey = `${partieIndex}-${scenarioIndex}`;
                      const showCorrection = visibleCorrections[correctionKey];
                      const hasQuestions = scenario.example_questions && scenario.example_questions.length > 0;

                      return (
                        <Card key={scenarioIndex} withBorder p="md" bg="gray.0">
                          <Stack gap="sm">
                            <Group justify="space-between" align="flex-start">
                              <Text fw={500} size="sm" style={{ flex: 1 }}>
                                {scenario.scenario}
                              </Text>

                              {hasQuestions && (
                                canViewFullContent ? (
                                  <Button
                                    size="xs"
                                    variant={showCorrection ? "filled" : "outline"}
                                    color={showCorrection ? "green" : "orange"}
                                    leftSection={showCorrection ? <IconEyeOff size={12} /> : <IconEye size={12} />}
                                    onClick={() => toggleCorrection(partieIndex, scenarioIndex)}
                                  >
                                    {showCorrection ? t('speakingTask3.hideCorrection') : t('speakingTask3.correction')}
                                  </Button>
                                ) : (
                                  <Button
                                    size="xs"
                                    variant="outline"
                                    color="gray"
                                    leftSection={<IconLock size={12} />}
                                    disabled
                                  >
                                    {t('writing.taskDetails.premiumRequired')}
                                  </Button>
                                )
                              )}
                            </Group>

                            <Collapse in={showCorrection && canViewFullContent}>
                              {hasQuestions && (
                                <Box style={{ paddingBottom: '20px' }}>
                                  <Text size="sm" fw={500} mb="xs" c="green">{t('speakingTask2.exampleQuestions')}</Text>
                                  <Card withBorder p="md" bg="green.0">
                                    <Stack gap="xs">
                                      {scenario.example_questions?.map((question, qIndex) => (
                                        <Text key={qIndex} size="sm" pl="sm" style={{
                                          whiteSpace: 'pre-wrap',
                                          fontFamily: 'system-ui, -apple-system, sans-serif',
                                          letterSpacing: '0.025em',
                                          lineHeight: 1.6
                                        }}>
                                          • {question}
                                        </Text>
                                      ))}
                                    </Stack>
                                  </Card>
                                </Box>
                              )}
                            </Collapse>
                          </Stack>
                        </Card>
                      );
                    })}
                  </Stack>
                </Accordion.Panel>
              </Accordion.Item>
            ))}
          </Accordion>
        </Card>

      {/* Back Button */}
        <Group justify="center">
          <Button component={Link} to="/speaking" leftSection={<IconArrowLeft size={16} />}>
            {t('speakingTask2.backToSpeaking')}
        </Button>
      </Group>
      </Stack>
    </Container>
  );
} 