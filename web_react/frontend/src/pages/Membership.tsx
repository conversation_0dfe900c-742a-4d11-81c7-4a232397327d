import {
  Container,
  Title,
  Text,
  Paper,
  Button,
  Group,
  Badge,
  Stack,
  SimpleGrid,
  List,
  Alert,
  Divider,
  Box,
} from '@mantine/core';
import { 
  IconCheck, 
  IconStar, 
  IconInfoCircle, 
  IconCreditCard,
  IconBook,
  IconEar,
  IconPencil,
  IconMicrophone,
  IconDatabase,
  IconChartBar,
  IconClock,
  IconShield,
  IconTrophy,
  IconInfinity
} from '@tabler/icons-react';
import { useMutation } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { paymentsApi } from '../services/api';
import { useAuthStore } from '../store/useAuthStore';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { formatDateWithUserTimezone } from '../utils/dateUtils';

export function Membership() {
  const { user, isAuthenticated, isMember, isMembershipExpired, isLifetimeMember } = useAuthStore();
  const { t } = useTranslation();

const features = [
  {
    icon: IconBook,
      title: t('membership.features.readingComprehension.title'),
      description: t('membership.features.readingComprehension.description')
  },
  {
    icon: IconEar,
      title: t('membership.features.listeningComprehension.title'),
      description: t('membership.features.listeningComprehension.description')
  },
  {
    icon: IconPencil,
      title: t('membership.features.writtenExpression.title'),
      description: t('membership.features.writtenExpression.description')
  },
  {
    icon: IconMicrophone,
      title: t('membership.features.oralExpression.title'),
      description: t('membership.features.oralExpression.description')
    }
  ];

  const commonFeatures = [
    { icon: IconDatabase, text: t('membership.features.accessAllTests') },
    { icon: IconChartBar, text: t('membership.features.progressTracking') },
    { icon: IconPencil, text: t('membership.features.detailedCorrections') },
    { icon: IconShield, text: t('membership.features.prioritySupport') },
    { icon: IconTrophy, text: t('membership.features.mockExams') }
  ];

const plans = [
  {
    id: '1w',
    name: t('membership.plans.week.name'),
    price: 6.99,
    duration: t('membership.plans.week.duration'),
    planId: '1w',
    popular: false,
    color: '#e74c3c',
    gradient: 'linear-gradient(135deg, #e74c3c 0%, #c0392b 100%)',
    features: commonFeatures
  },
  {
    id: '1m',
    name: t('membership.plans.monthly.name'),
    price: 13.99,
    duration: t('membership.plans.monthly.duration'),
    planId: '1m',
    popular: false,
    color: '#3498db',
    gradient: 'linear-gradient(135deg, #3498db 0%, #2980b9 100%)',
    features: commonFeatures
  },
  {
    id: '3m',
    name: t('membership.plans.quarterly.name'),
    price: 26.99,
    duration: t('membership.plans.quarterly.duration'),
    planId: '3m',
    popular: true,
    color: '#27ae60',
    gradient: 'linear-gradient(135deg, #27ae60 0%, #229954 100%)',
    features: commonFeatures
  },
  {
    id: '12m',
    name: '1 Year',
    price: 54.99,
    duration: '12 months',
    planId: '12m',
    popular: false,
    color: '#f39c12',
    gradient: 'linear-gradient(135deg, #f39c12 0%, #e67e22 100%)',
    features: commonFeatures
  }
];

  const createCheckoutMutation = useMutation({
    mutationFn: (plan: string) => paymentsApi.createCheckoutSession(plan),
    onSuccess: (data) => {
      // Redirect to Stripe checkout using standard API
      if (import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY) {
        import('@stripe/stripe-js').then(({ loadStripe }) => {
          loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY).then((stripe) => {
            if (stripe) {
              stripe.redirectToCheckout({ sessionId: data.sessionId });
            }
          });
        });
      } else {
        console.error('Stripe publishable key not found');
        notifications.show({
          title: t('membership.errors.stripeNotConfigured'),
          message: t('membership.errors.stripeKeyMissing'),
          color: 'red',
        });
      }
    },
    onError: (error: any) => {
      notifications.show({
        title: t('membership.errors.paymentError'),
        message: error.response?.data?.error || t('membership.errors.paymentSessionError'),
        color: 'red',
      });
    },
  });

  const handleSubscribe = (planId: string) => {
    if (!isAuthenticated) {
      notifications.show({
        title: t('membership.errors.loginRequiredTitle'),
        message: t('membership.errors.loginRequiredMessage'),
        color: 'orange',
      });
      return;
    }
    createCheckoutMutation.mutate(planId);
  };

  const currentMembershipStatus = () => {
    if (!isAuthenticated) return null;
    if (isLifetimeMember()) return 'lifetime';
    if (!isMember()) return 'free';
    if (isMembershipExpired()) return 'expired';
    return 'active';
  };

  const status = currentMembershipStatus();

  return (
    <Container size="lg" py="xl">
      <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
        <Title order={1} mb="md">
          {t('membership.title')}
        </Title>
        <Text size="lg" c="dimmed" mb="xl">
          {t('membership.subtitle')}
        </Text>
      </div>

      {/* Current Status Alert */}
      {status && (
        <Alert
          variant="light"
          color={
            status === 'lifetime' ? 'green' :
            status === 'active' ? 'green' : 
            status === 'expired' ? 'red' : 'blue'
          }
          title={
            status === 'lifetime' ? t('membership.lifetimeSubscription') :
            status === 'active' ? t('membership.activeSubscription') :
            status === 'expired' ? t('membership.expiredSubscription') :
            t('membership.freeAccount')
          }
          icon={<IconInfoCircle />}
          mb="xl"
        >
          {status === 'lifetime' && (
            <>{t('membership.lifetimeMessage')}</>
          )}
          {status === 'active' && user?.membership_expires_at && (
            <>{t('membership.activeMessage')} {formatDateWithUserTimezone(user.membership_expires_at)}</>
          )}
          {status === 'expired' && (
            <>{t('membership.expiredMessage')}</>
          )}
          {status === 'free' && (
            <>{t('membership.freeMessage')}</>
          )}
        </Alert>
      )}

      {/* Hide pricing plans for lifetime users */}
      {status !== 'lifetime' && (
        <>
          {/* Features Overview */}
          <Paper shadow="sm" p="lg" radius="md" withBorder mb="xl">
            <Title order={3} mb="md" ta="center">
              {t('membership.featuresTitle')}
            </Title>
            
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="lg">
              {features.map((feature, index) => {
                const Icon = feature.icon;
                return (
                  <div key={index} style={{ textAlign: 'center' }}>
                    <Icon size={48} style={{ margin: '0 auto 1rem', color: 'var(--mantine-color-blue-6)' }} />
                    <Title order={5} mb="xs">{feature.title}</Title>
                    <Text size="sm" c="dimmed">{feature.description}</Text>
                  </div>
                );
              })}
            </SimpleGrid>
          </Paper>

          {/* Pricing Plans */}
          <Title order={2} mb="lg" ta="center">
            {t('membership.choosePlan')}
          </Title>

          <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="lg" mb="xl">
            {plans.map((plan) => (
              <Paper
                key={plan.id}
                shadow="lg"
                p="lg"
                radius="md"
                withBorder
                style={{
                  position: 'relative',
                  border: plan.popular ? `3px solid ${plan.color}` : `1px solid #e9ecef`,
                  background: plan.popular ? 
                    `linear-gradient(135deg, ${plan.color}10 0%, ${plan.color}05 100%)` : 
                    'white',
                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-4px)';
                  e.currentTarget.style.boxShadow = `0 8px 25px ${plan.color}30`;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12)';
                }}
              >
                <Stack gap="md" style={{ height: '100%' }}>
                  <Box>
                    <Title order={4} mb="xs" style={{ color: plan.color }}>
                      {plan.name}
                    </Title>
                    <Group align="baseline" gap="xs">
                      <Title order={2} style={{ 
                        background: plan.gradient,
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                        backgroundClip: 'text'
                      }}>
                        {plan.price}$
                      </Title>
                      <Text c="dimmed">/ {plan.duration}</Text>
                    </Group>
                  </Box>

                  <Divider style={{ borderColor: `${plan.color}20` }} />

                  <List
                    spacing="sm"
                    size="sm"
                    center
                    icon={<IconCheck size={16} style={{ color: plan.color }} />}
                  >
                    {plan.features.map((feature, index) => {
                      const FeatureIcon = feature.icon;
                      return (
                        <List.Item key={index}>
                          <Group gap="xs" align="center">
                            <FeatureIcon size={16} style={{ color: plan.color }} />
                            <Text size="sm">{feature.text}</Text>
                          </Group>
                        </List.Item>
                      );
                    })}
                  </List>

                  <Button
                    size="lg"
                    variant={plan.popular ? 'filled' : 'outline'}
                    style={{
                      marginTop: 'auto',
                      background: plan.popular ? plan.gradient : 'transparent',
                      borderColor: plan.color,
                      color: plan.popular ? 'white' : plan.color,
                      transition: 'all 0.2s ease',
                    }}
                    onMouseEnter={(e) => {
                      if (!plan.popular) {
                        e.currentTarget.style.background = plan.gradient;
                        e.currentTarget.style.color = 'white';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!plan.popular) {
                        e.currentTarget.style.background = 'transparent';
                        e.currentTarget.style.color = plan.color;
                      }
                    }}
                    leftSection={<IconCreditCard size={16} />}
                    onClick={() => handleSubscribe(plan.planId)}
                    loading={createCheckoutMutation.isPending}
                  >
                    {(() => {
                      if (!isAuthenticated) {
                        return t('membership.buttons.subscribe');
                      }
                      
                      if (status === 'free') {
                        return t('membership.buttons.becomeMember');
                      }
                      
                      if (status === 'active') {
                        if (plan.planId === 'lifetime') {
                          return t('membership.buttons.upgradeToLifetime');
                        }
                        return t('membership.buttons.changePlan');
                      }
                      
                      if (status === 'expired') {
                        if (plan.planId === 'lifetime') {
                          return t('membership.buttons.upgradeToLifetime');
                        }
                        return t('membership.buttons.renew');
                      }
                      
                      return t('membership.buttons.subscribe');
                    })()}
                  </Button>
                </Stack>
              </Paper>
            ))}
          </SimpleGrid>
        </>
      )}

      {/* Show special message for lifetime users */}
      {status === 'lifetime' && (
        <Paper shadow="sm" p="xl" radius="md" withBorder ta="center">
          <Title order={3} mb="md" c="green">
            {t('membership.lifetimeThankYou.title')}
          </Title>
          <Text size="lg" mb="md">
            {t('membership.lifetimeThankYou.message')}
          </Text>
          <Group justify="center" gap="md">
            <Link to="/" style={{ textDecoration: 'none' }}>
              <Button size="lg" variant="filled">
                {t('membership.lifetimeThankYou.startTests')}
              </Button>
            </Link>
            <Link to="/profile" style={{ textDecoration: 'none' }}>
              <Button size="lg" variant="outline">
                {t('membership.lifetimeThankYou.viewProfile')}
              </Button>
            </Link>
          </Group>
        </Paper>
      )}

      {/* Promo Code Section */}
      <Paper shadow="sm" p="lg" radius="md" withBorder>
        <Group justify="space-between" align="center">
          <div>
            <Title order={4} mb="xs">{t('membership.promoSection.title')}</Title>
            <Text size="sm" c="dimmed">
              {t('membership.promoSection.description')}
            </Text>
          </div>
          <Link to="/redeem-promo" style={{ textDecoration: 'none' }}>
            <Button variant="outline">
              {t('membership.promoSection.button')}
            </Button>
          </Link>
        </Group>
      </Paper>

      {/* Free Features */}
      <Paper shadow="sm" p="lg" radius="md" withBorder mt="xl">
        <Title order={4} mb="md">{t('membership.freeAccountFeatures.title')}</Title>
        <List
          spacing="xs"
          size="sm"
          icon={<IconCheck size={16} color="var(--mantine-color-blue-6)" />}
        >
          {(t('membership.freeAccountFeatures.list', { returnObjects: true }) as string[]).map((feature, index) => (
            <List.Item key={index}>{feature}</List.Item>
          ))}
        </List>
        <Text size="sm" c="dimmed" mt="sm">
          {t('membership.freeAccountFeatures.description')}
        </Text>
      </Paper>
    </Container>
  );
} 