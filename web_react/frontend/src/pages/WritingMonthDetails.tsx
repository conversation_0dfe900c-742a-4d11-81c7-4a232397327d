import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Title,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Box,
  Paper,
  Divider,
  Alert,
  Loader
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconPencil,
  IconCalendar,
  IconList,
  IconCheck,
  IconEye,
  IconEyeOff,
  IconInfoCircle,
  IconArrowLeft,
  IconLock,
  IconEdit
} from '@tabler/icons-react';

import { writingService, type Task, type Combination, type MonthData } from '../services/writingService';
import { useAuthStore } from '../store/useAuthStore';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';
import { WritingEditor } from '../components/WritingEditor';
import { WritingNavigation } from '../components/WritingNavigation';

function TaskCard({ task, combinationNumber, monthYear }: { task: Task; combinationNumber: string; monthYear: string }) {
  const [showCorrection, setShowCorrection] = useState(false);
  const [showWritingEditor, setShowWritingEditor] = useState(false);
  const { isMember, isMembershipExpired, isAuthenticated } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const canViewCorrection = isMember() && !isMembershipExpired();

  const handleSubmissionSaved = (submissionId: string) => {
    // Draft saved successfully
  };

  const handleSubmissionSubmitted = (submissionId: string) => {
    setShowWritingEditor(false);
  };

  return (
    <>
      <Card withBorder shadow="sm" p="md" mb="sm">
        <Group justify="space-between" mb="xs">
          <Badge size="sm" variant="light" color="blue">
            {t('writingMonthDetails.task', { number: task.task_number })}
          </Badge>
          <Group gap="xs">
            {/* Write Response Button */}
            {isAuthenticated && (
              <Button
                size="xs"
                variant={showWritingEditor ? "filled" : "outline"}
                color={showWritingEditor ? "blue" : "blue"}
                leftSection={showWritingEditor ? <IconEyeOff size={12} /> : <IconEdit size={12} />}
                onClick={() => setShowWritingEditor(!showWritingEditor)}
              >
                {showWritingEditor ? t('writing.taskDetails.hide') : t('writing.taskDetails.writeResponse')}
              </Button>
            )}

            {/* Correction Button */}
            {canViewCorrection ? (
              <Button
                size="xs"
                variant={showCorrection ? "filled" : "outline"}
                color={showCorrection ? "green" : "orange"}
                leftSection={showCorrection ? <IconEyeOff size={12} /> : <IconCheck size={12} />}
                onClick={() => setShowCorrection(!showCorrection)}
              >
                {showCorrection ? t('writing.taskDetails.hide') : t('writing.taskDetails.correction')}
              </Button>
            ) : (
              <Button
                size="xs"
                variant="outline"
                color="gray"
                leftSection={<IconLock size={12} />}
                disabled
              >
                {t('writing.taskDetails.premiumRequired')}
              </Button>
            )}
          </Group>
        </Group>

        <Text
          size="sm"
          c="dimmed"
          mb="md"
          style={{
            lineHeight: 1.6,
            whiteSpace: 'pre-wrap',
            fontFamily: 'system-ui, -apple-system, sans-serif',
            letterSpacing: '0.025em'
          }}
        >
          {task.task_content}
        </Text>

        {/* Writing Editor Section */}
        {showWritingEditor && isAuthenticated && (
          <Box
            mt="md"
            p="md"
            style={{
              backgroundColor: themeColors.surfaceHover,
              borderRadius: '8px',
              border: `2px solid ${themeColors.borderLight}`,
              transition: 'all 0.2s ease'
            }}
          >
            <Text size="xs" fw={500} c="blue" mb="xs">
              {t('writing.taskDetails.writeResponse')}:
            </Text>
            <WritingEditor
              testType="monthly_test"
              testIdentifier={monthYear}
              taskNumber={task.task_number}
              combinationNumber={combinationNumber}
              placeholder={t('writing.editor.placeholder')}
              onSubmissionSaved={handleSubmissionSaved}
              onSubmissionSubmitted={handleSubmissionSubmitted}
            />
          </Box>
        )}

        {/* Correction Section */}
        {showCorrection && canViewCorrection && task.correction_content && (
          <Box
            mt="md"
            p="md"
            style={{
              backgroundColor: themeColors.surfaceHover,
              borderRadius: '8px',
              border: `2px solid ${themeColors.borderLight}`,
              transition: 'all 0.2s ease'
            }}
          >
            <Text size="xs" fw={500} c="green" mb="xs">
              {t('writing.taskDetails.correctionLabel')}
            </Text>
            <Text
              size="sm"
              style={{
                lineHeight: 1.6,
                whiteSpace: 'pre-wrap',
                fontFamily: 'system-ui, -apple-system, sans-serif',
                letterSpacing: '0.025em',
                color: themeColors.textPrimary
              }}
            >
              {task.correction_content}
            </Text>
          </Box>
        )}
      </Card>
    </>
  );
}

function CombinationSection({ combination, monthYear }: { combination: Combination; monthYear: string }) {
  const { t } = useTranslation();
  
  return (
    <Paper p="lg" withBorder mb="lg" style={{ backgroundColor: '#fdfdfd' }}>
      <Group justify="flex-start" mb="md">
        <IconList size={20} color="#fd7e14" />
        <Title order={4} c="#fd7e14">
          {t('writing.taskDetails.combinationTask', { combination: combination.combination_number, task: '' }).replace(' - Task', '')}
        </Title>
      </Group>

      <Divider mb="md" />

      <Stack gap="sm">
        {combination.tasks.map((task) => (
          <TaskCard
            key={`${monthYear}-${combination.combination_number}-${task.task_number}`}
            task={task}
            combinationNumber={combination.combination_number}
            monthYear={monthYear}
          />
        ))}
      </Stack>
    </Paper>
  );
}

export function WritingMonthDetails() {
  const { monthId } = useParams<{ monthId: string }>();
  const navigate = useNavigate();
  const [monthData, setMonthData] = useState<MonthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();

  const isPremiumUser = isAuthenticated && isMember() && !isMembershipExpired();

  useEffect(() => {
    const loadMonthData = async () => {
      if (!monthId) {
        setError(t('writingMonthDetails.errors.missingMonthId'));
        setLoading(false);
        return;
      }

      // Note: Classified writing routes are now handled by separate components
      // and should not reach this WritingMonthDetails component
      console.log('📝 WritingMonthDetails loading month:', monthId);

      try {
        setLoading(true);
        setError(null);
        const data = await writingService.loadMonth(monthId);
        setMonthData(data);
      } catch (err) {
        console.error('Failed to load month data:', err);
        setError(err instanceof Error ? err.message : t('writingMonthDetails.errors.loadingData'));
      } finally {
        setLoading(false);
      }
    };

    loadMonthData();
  }, [monthId, t, navigate]);

  if (loading) {
    return (
      <>
        <WritingNavigation />
        <Container size="lg" py="xl" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
          <Stack align="center" justify="center">
            <Loader size="xl" color="orange" />
            <Text size="lg" fw={500} mt="md">{t('loadingStates.month')}</Text>
            <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
          </Stack>
        </Container>
      </>
    );
  }

  if (error || !monthData) {
    return (
      <>
        <WritingNavigation />
        <Container size="lg" py="xl" style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', minHeight: '80vh' }}>
          <Alert variant="light" color="red" title={t('writingMonthDetails.errors.title')} icon={<IconInfoCircle />}>
            {error || t('writingMonthDetails.errors.monthNotFound')}
          </Alert>
          <Group justify="center" mt="xl">
            <Button
              variant="outline"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => navigate('/writing')}
            >
              {t('writingMonthDetails.navigation.backToExercises')}
            </Button>
          </Group>
        </Container>
      </>
    );
  }

  const totalTasks = monthData.combinations.reduce((sum, combo) => sum + combo.tasks.length, 0);
  const formattedName = writingService.formatMonthName(monthData.month_year);

  return (
    <>
      <WritingNavigation />
      <Container size="lg" py="xl" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        {/* Header */}
        <Box mb="xl" style={{ width: '100%', maxWidth: '800px' }}>
          {/* Back Button */}
          <Group justify="flex-start" mb="lg">
            <Button
              variant="outline"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => navigate('/writing')}
            >
              {t('writingMonthDetails.navigation.backToExercises')}
            </Button>
          </Group>

          {/* Centered Month Title */}
          <Box ta="center" mb="lg">
            <Group justify="center" mb="md">
              <IconCalendar size={32} color="#fd7e14" />
              <Title order={1} c="#fd7e14">
                {formattedName}
              </Title>
            </Group>
          </Box>

          {/* Statistics */}
          <Group justify="center" gap="xl" mb="lg">
            <Card withBorder p="md" ta="center">
              <Text size="xl" fw={700} c="orange">{monthData.combinations.length}</Text>
              <Text size="sm" c="dimmed">{t('writingMonthDetails.combinations')}</Text>
            </Card>
            <Card withBorder p="md" ta="center">
              <Text size="xl" fw={700} c="blue">{totalTasks}</Text>
              <Text size="sm" c="dimmed">{t('writingMonthDetails.tasksLabel')}</Text>
            </Card>
          </Group>
        </Box>

        {/* Combinations */}
        <Stack gap="lg" style={{ width: '100%', maxWidth: '800px' }}>
          {monthData.combinations.map((combination) => (
            <CombinationSection
              key={`${monthData.month_year}-${combination.combination_number}`}
              combination={combination}
              monthYear={monthData.month_year}
            />
          ))}
        </Stack>
      </Container>
    </>
  );
}