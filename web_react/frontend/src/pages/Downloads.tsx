import { Container, Title, Card, Text, Button, Group, Stack, Box, SimpleGrid, Alert } from '@mantine/core';
import { IconDownload, IconExternalLink, IconInfoCircle, IconCloud, IconFileText, IconHeadphones, IconPencil, IconMicrophone } from '@tabler/icons-react';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { useThemeStore } from '../store/useThemeStore';

const downloadCategories = [
  {
    id: 'writing' as const,
    icon: IconPencil,
    color: '#fd7e14',
    googleDriveLink: 'https://drive.google.com/drive/folders/1z_qNr2oOhf7aBK6dpgKkQXYJNv89aGUr?usp=drive_link',
    baiduLink: 'https://pan.baidu.com/s/1XGn9MkEWnPK9yAkhPiembQ',
    baiduCode: 'chez',
  },
  {
    id: 'speaking' as const,
    icon: IconMicrophone,
    color: '#dc3545',
    googleDriveLink: 'https://drive.google.com/drive/folders/1bSsG1p_Vkj6_uwGzgtsbRRATQObZ6j8C?usp=drive_link',
    baiduLink: 'https://pan.baidu.com/s/1QOHdqZiaCWj-350xHwLjiw',
    baiduCode: 'chez',
  },
];

export function Downloads() {
  const { isAuthenticated } = useAuthStore();
  const { t } = useTranslation();
  const { resolvedTheme } = useThemeStore();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Alert
            variant="light"
            color="blue"
            title={t('downloads.loginRequired.title')}
            icon={<IconInfoCircle />}
          >
            <Text mb="md">{t('downloads.loginRequired.message')}</Text>
            <Link to="/login" style={{ textDecoration: 'none' }}>
              <Button color="blue">
                {t('downloads.loginRequired.button')}
              </Button>
            </Link>
          </Alert>
        </Stack>
      </Container>
    );
  }

  return (
    <Container size="lg" py="xl">
      {/* Page Header */}
      <Box ta="center" mb={60}>
        <Title order={1} size="3rem" fw={700} mb="lg" c={resolvedTheme === 'dark' ? '#f8f9fa' : 'dark'}>
          📚 {t('downloads.title')}
        </Title>
        <Text size="lg" c="dimmed" maw={800} mx="auto" mb="lg">
          {t('downloads.subtitle')}
        </Text>
        <Text size="md" c="dimmed" maw={900} mx="auto">
          {t('downloads.description')}
        </Text>
      </Box>

      {/* Downloads Categories */}
      <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg" mb="xl">
        {downloadCategories.map((category) => {
          const Icon = category.icon;
          return (
            <Card
              key={category.id}
              shadow="sm"
              padding="lg"
              radius="md"
              withBorder
              style={{
                background: resolvedTheme === 'dark' ? '#1a1b1e' : 'white',
                transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                borderColor: resolvedTheme === 'dark' ? '#373A40' : undefined,
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                  ? '0 4px 20px rgba(255,255,255,0.1)'
                  : '0 4px 20px rgba(0,0,0,0.1)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = resolvedTheme === 'dark'
                  ? '0 1px 3px rgba(255,255,255,0.12)'
                  : '0 1px 3px rgba(0,0,0,0.12)';
              }}
            >
              <Stack gap="md">
                <Group gap="md">
                  <Icon size={32} color={category.color} />
                  <Title order={3} fw={600} c={resolvedTheme === 'dark' ? '#f8f9fa' : 'dark'}>
                    {t(`downloads.categories.${category.id}.title`)}
                  </Title>
                </Group>

                <Text size="sm" c={resolvedTheme === 'dark' ? '#adb5bd' : 'dimmed'}>
                  {t(`downloads.categories.${category.id}.description`)}
                </Text>

                <Stack gap="sm">
                  {/* Google Drive Download */}
                  <Card
                    padding="sm"
                    radius="sm"
                    style={{
                      background: resolvedTheme === 'dark'
                        ? 'linear-gradient(135deg, #1e3a8a 0%, #1d4ed8 100%)'
                        : 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                      border: resolvedTheme === 'dark' ? '1px solid #3b82f6' : '1px solid #2196f3',
                    }}
                  >
                    <Group justify="space-between">
                      <Group gap="xs">
                        <IconCloud size={20} color={resolvedTheme === 'dark' ? '#60a5fa' : '#1976d2'} />
                        <Text size="sm" fw={500} c={resolvedTheme === 'dark' ? '#60a5fa' : '#1976d2'}>
                          Google Drive
                        </Text>
                      </Group>
                      <Button
                        variant="light"
                        color="blue"
                        size="xs"
                        rightSection={<IconExternalLink size={14} />}
                        component="a"
                        href={category.googleDriveLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {t('downloads.downloadButton')}
                      </Button>
                    </Group>
                  </Card>

                  {/* Baidu Wangpan Download */}
                  <Card
                    padding="sm"
                    radius="sm"
                    style={{
                      background: resolvedTheme === 'dark'
                        ? 'linear-gradient(135deg, #ea580c 0%, #f97316 100%)'
                        : 'linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%)',
                      border: resolvedTheme === 'dark' ? '1px solid #fb923c' : '1px solid #ff9800',
                    }}
                  >
                    <Group justify="space-between">
                      <Group gap="xs">
                        <IconDownload size={20} color={resolvedTheme === 'dark' ? '#fb923c' : '#f57c00'} />
                        <Text size="sm" fw={500} c={resolvedTheme === 'dark' ? '#fb923c' : '#f57c00'}>
                          百度网盘 / Baidu Pan
                        </Text>
                      </Group>
                      <Button
                        variant="light"
                        color="orange"
                        size="xs"
                        rightSection={<IconExternalLink size={14} />}
                        component="a"
                        href={category.baiduLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {t('downloads.downloadButton')}
                      </Button>
                    </Group>
                  </Card>
                </Stack>
              </Stack>
            </Card>
          );
        })}
      </SimpleGrid>

      {/* Additional Information */}
      <Card
        shadow="sm"
        padding="lg"
        radius="md"
        withBorder
        style={{
          background: resolvedTheme === 'dark'
            ? 'linear-gradient(135deg, #7c3aed 0%, #8b5cf6 100%)'
            : 'linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%)',
          border: resolvedTheme === 'dark' ? '2px solid #a855f7' : '2px solid #9c27b0',
        }}
      >
        <Stack gap="md">
          <Group gap="md">
            <IconInfoCircle size={32} color={resolvedTheme === 'dark' ? '#c084fc' : '#7b1fa2'} />
            <Title order={3} fw={600} c={resolvedTheme === 'dark' ? '#c084fc' : '#7b1fa2'}>
              {t('downloads.info.title')}
            </Title>
          </Group>
          
          <Text size="sm" c={resolvedTheme === 'dark' ? '#f8f9fa' : '#424242'} style={{ lineHeight: 1.6 }}>
            {t('downloads.info.description')}
          </Text>

          <Group gap="md">
            <Text size="xs" c="dimmed">
              {t('downloads.info.lastUpdated')}: {new Date().toLocaleDateString()}
            </Text>
          </Group>
        </Stack>
      </Card>
    </Container>
  );
} 