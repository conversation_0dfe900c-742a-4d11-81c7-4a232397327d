import { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Accordion,
  Card,
  Text,
  Button,
  Group,
  Badge,
  Stack,
  Box,
  Paper,
  Divider,
  Alert,
  Loader,
  Modal,
  ScrollArea,
  SimpleGrid,
  Progress,
  Breadcrumbs,
  Anchor
} from '@mantine/core';
import {
  IconPencil,
  IconCalendar,
  IconList,
  IconCheck,
  IconEye,
  IconEyeOff,
  IconInfoCircle,
  IconPlayerPlay,
  IconHome,
  IconLock,
  IconTarget
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { Link, useNavigate } from 'react-router-dom';
import { writingService, type Task, type Combination, type MonthData } from '../services/writingService';
import { useAuthStore } from '../store/useAuthStore';
import { useTranslation } from 'react-i18next';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';
import { ClassifiedWritingCards } from '../components/ClassifiedWritingCards';

function TaskCard({ task, combinationNumber }: { task: Task; combinationNumber: string }) {
  const [opened, { open, close }] = useDisclosure(false);
  const [showCorrection, setShowCorrection] = useState(false);
  const { isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();
  const canViewCorrection = isMember() && !isMembershipExpired();

  return (
    <>
      <Card withBorder shadow="sm" p="md" mb="sm">
        <Group justify="space-between" mb="xs">
          <Badge size="sm" variant="light" color="blue">
            {t('writing.taskDetails.task', { number: task.task_number })}
          </Badge>
          <Group gap="xs">
            <Button
              size="xs"
              variant="light"
              leftSection={<IconEye size={12} />}
              onClick={open}
            >
              {t('writing.taskDetails.viewDetails')}
            </Button>
            {canViewCorrection ? (
            <Button
              size="xs"
              variant={showCorrection ? "filled" : "outline"}
              color={showCorrection ? "green" : "orange"}
              leftSection={showCorrection ? <IconEyeOff size={12} /> : <IconCheck size={12} />}
              onClick={() => setShowCorrection(!showCorrection)}
            >
              {showCorrection ? t('writing.taskDetails.hide') : t('writing.taskDetails.correction')}
            </Button>
            ) : (
              <Button
                size="xs"
                variant="outline"
                color="gray"
                leftSection={<IconLock size={12} />}
                disabled
              >
                {t('writing.taskDetails.premiumRequired')}
              </Button>
            )}
          </Group>
        </Group>

        <Text 
          size="sm" 
          c="dimmed" 
          lineClamp={3} 
          style={{ 
            whiteSpace: 'pre-wrap',
            fontFamily: 'system-ui, -apple-system, sans-serif',
            letterSpacing: '0.025em'
          }}
        >
          {task.task_content}
        </Text>

        {showCorrection && canViewCorrection && task.correction_content && (
          <Box
            mt="md"
            p="sm"
            style={{
              backgroundColor: themeColors.surfaceHover,
              borderRadius: '4px',
              border: `1px solid ${themeColors.borderLight}`,
              transition: 'all 0.2s ease'
            }}
          >
            <Text size="xs" fw={500} c="green" mb="xs">
              {t('writing.taskDetails.correctionLabel')}
            </Text>
            <Text
              size="sm"
              lineClamp={4}
              style={{
                whiteSpace: 'pre-wrap',
                fontFamily: 'system-ui, -apple-system, sans-serif',
                letterSpacing: '0.025em',
                color: themeColors.textPrimary
              }}
            >
              {task.correction_content}
            </Text>
          </Box>
        )}
      </Card>

      {/* Modal for full task details */}
      <Modal
        opened={opened}
        onClose={close}
        title={
          <Group>
            <IconPencil size={20} />
            <Text fw={600}>{t('writing.taskDetails.combinationTask', { combination: combinationNumber, task: task.task_number })}</Text>
          </Group>
        }
        size="lg"
        centered
      >
        <Stack gap="md">
          <Paper p="md" withBorder>
            <Text fw={500} mb="sm" c="blue">
              {t('writing.taskDetails.instructionLabel')}
            </Text>
            <Text 
              size="sm" 
              style={{ 
                lineHeight: 1.6, 
                whiteSpace: 'pre-wrap',
                fontFamily: 'system-ui, -apple-system, sans-serif',
                letterSpacing: '0.025em'
              }}
            >
              {task.task_content}
            </Text>
          </Paper>

          {canViewCorrection && task.correction_content ? (
          <Paper
            p="md"
            withBorder
            style={{
              backgroundColor: themeColors.surfaceHover,
              borderColor: themeColors.border,
              transition: 'all 0.2s ease'
            }}
          >
            <Text fw={500} mb="sm" c="green">
              {t('writing.taskDetails.correctionLabel')}
            </Text>
            <ScrollArea h={200}>
                <Text
                  size="sm"
                  style={{
                    lineHeight: 1.6,
                    whiteSpace: 'pre-wrap',
                    fontFamily: 'system-ui, -apple-system, sans-serif',
                    letterSpacing: '0.025em',
                    color: themeColors.textPrimary
                  }}
                >
                {task.correction_content}
              </Text>
            </ScrollArea>
          </Paper>
          ) : (
            <Paper
              p="md"
              withBorder
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#2d1b00' : '#fff3cd',
                borderColor: resolvedTheme === 'dark' ? '#4a3000' : '#ffeaa7',
                transition: 'all 0.2s ease'
              }}
            >
              <Group>
                <IconLock size={20} color="#d68910" />
                <Text fw={500} c="#d68910">
                  {t('writing.taskDetails.correctionAvailable')}
                </Text>
              </Group>
            </Paper>
          )}
        </Stack>
      </Modal>
    </>
  );
}

function MonthCard({ monthData }: { monthData: MonthData }) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const totalTasks = monthData.combinations.reduce((sum, combo) => sum + combo.tasks.length, 0);
  const formattedName = writingService.formatMonthName(monthData.month_year);
  
  // Handle click for non-authenticated users
  const handleCardClick = () => {
    if (!isAuthenticated) {
      navigate('/login', { 
        state: { 
          message: t('readingListeningTests.auth.loginRequired'),
          from: `/writing/${monthData.month_year}` 
        } 
      });
    }
  };
  
  return (
    <Card 
      shadow="sm" 
      padding="sm" 
      radius="md" 
      withBorder
      style={{
        background: 'white',
        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
        height: '100%',
        position: 'relative'
      }}
    >
      {/* Icon in top left corner */}
      <Box style={{ position: 'absolute', top: '8px', left: '8px', zIndex: 5 }}>
        <IconCalendar size={20} color="#fd7e14" />
      </Box>

      <Stack gap="md" style={{ height: '100%', paddingTop: '28px' }}>
        {/* Title */}
        <Box style={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
          <Text fw={500} size="sm" lineClamp={1} mb="md" ta="center">
            {formattedName}
          </Text>
          <Text size="xs" c="dimmed" ta="center">
            {t('writing.monthCard.tasks', { count: totalTasks })}
          </Text>
        </Box>

        {/* Action Button */}
        {isAuthenticated ? (
        <Button
          component={Link}
          to={`/writing/${monthData.month_year}`}
          color="orange"
          leftSection={<IconPlayerPlay size={12} />}
          size="xs"
          fullWidth
        >
            {t('writing.monthCard.explore')}
          </Button>
        ) : (
          <Button
            onClick={handleCardClick}
            color="orange"
            leftSection={<IconPlayerPlay size={12} />}
            size="xs"
            fullWidth
          >
            {t('writing.monthCard.explore')}
        </Button>
        )}
        </Stack>
    </Card>
  );
}

export function Writing() {
  const [writingData, setWritingData] = useState<MonthData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const navigate = useNavigate();

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const data = await writingService.getWritingTestsAsMonthData();
        setWritingData(data);
      } catch (err) {
        console.error('Failed to load writing data:', err);
        setError(err instanceof Error ? err.message : t('speaking.errors.loadingData'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const totalMonths = writingData.length;
  const totalCombinations = writingData.reduce((sum, month) => sum + month.combinations.length, 0);
  const totalTasks = writingData.reduce((sum, month) => 
    sum + month.combinations.reduce((comboSum, combo) => comboSum + combo.tasks.length, 0), 0
  );

  const isPremiumUser = isAuthenticated && isMember() && !isMembershipExpired();

  if (loading) {
    return (
      <Container size="lg" py="xl">
        <Stack align="center" justify="center" style={{ minHeight: '60vh' }}>
          <Loader size="xl" color="orange" />
          <Text size="lg" fw={500} mt="md">{t('loadingStates.writing')}</Text>
          <Text size="sm" c="dimmed">{t('common.pleaseWait')}</Text>
        </Stack>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="lg" py="xl">
        <Alert variant="light" color="red" title={t('common.error')} icon={<IconInfoCircle />}>
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="lg" py="xl">
      {/* Header */}
      <Stack gap="lg" mb="xl">
        <Breadcrumbs>
          <Anchor component={Link} to="/" style={{ textDecoration: 'none' }}>
            {t('readingListeningTests.breadcrumb.home')}
          </Anchor>
          <Text c="dimmed">{t('writing.title')}</Text>
        </Breadcrumbs>
        
        <Group align="center" gap="md">
          <IconPencil size={48} color="#228be6" />
          <div>
            <Title order={1} mb="xs">{t('writing.title')}</Title>
            <Text c="dimmed" size="lg">{t('writing.subtitle')}</Text>
          </div>
        </Group>
      </Stack>

      {/* Guide Section */}
      <Card withBorder mb="xl" style={{
        background: themeColors.surface,
        border: `1px solid ${themeColors.border}`,
        transition: 'all 0.3s ease'
      }}>
        <Stack align="center" gap="lg">
          <Group gap="md" justify="center">
            <Box style={{
              background: `linear-gradient(135deg, ${themeColors.writing} 0%, ${themeColors.writing}dd 100%)`,
              borderRadius: '50%',
              padding: '12px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              <IconPencil size={24} color="white" />
            </Box>
            <Title order={3} c={themeColors.writing} fw={600} ta="center">
              {t('readingListeningTests.guide.writing.title')}
            </Title>
          </Group>

          <Stack gap="md" align="center" style={{ maxWidth: '800px' }}>
            <Box style={{ textAlign: 'center' }}>
              <Text fw={600} size="md" c={themeColors.textPrimary} mb="md">
                {t('readingListeningTests.guide.writing.overview.title')}
              </Text>
              <Stack gap="sm" align="center">
                <Text size="sm" c={themeColors.textSecondary} ta="center">
                  • {t('readingListeningTests.guide.writing.overview.format')}
          </Text>
                <Box>
                  <Text size="sm" c={themeColors.textSecondary} mb="xs" ta="center">• {t('readingListeningTests.guide.writing.overview.tasks.title')}</Text>
                  <Stack gap="xs" align="center">
                    <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.writing.overview.tasks.task1')}</Text>
                    <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.writing.overview.tasks.task2')}</Text>
                    <Text size="xs" c={themeColors.textSecondary} ta="center">- {t('readingListeningTests.guide.writing.overview.tasks.task3')}</Text>
                  </Stack>
                </Box>
              </Stack>
        </Box>

            <Box style={{ textAlign: 'center' }}>
              <Text fw={600} size="md" c={themeColors.textPrimary} mb="md">
                {t('readingListeningTests.guide.writing.features.title')}
            </Text>
              <Group gap="md" justify="center" wrap="wrap">
                <Badge variant="light" color="orange" size="sm">{t('readingListeningTests.guide.writing.features.collection')}</Badge>
                <Badge variant="light" color="orange" size="sm">{t('readingListeningTests.guide.writing.features.practice')}</Badge>
                <Badge variant="light" color="orange" size="sm">{t('readingListeningTests.guide.writing.features.guidance')}</Badge>
        </Group>
            </Box>
          </Stack>
      </Stack>
      </Card>

      {/* Main Content */}
      {writingData.length > 0 ? (
        <Accordion
          mb="xl"
          styles={{
            root: {
              backgroundColor: themeColors.surface,
              border: `1px solid ${themeColors.border}`,
              borderRadius: '8px',
            },
            item: {
              backgroundColor: themeColors.surface,
              border: `1px solid ${themeColors.border}`,
              borderRadius: '6px',
              marginBottom: '8px',
            },
            control: {
              backgroundColor: themeColors.surface,
              color: themeColors.textPrimary,
              '&:hover': {
                backgroundColor: themeColors.surfaceHover,
              },
            },
            panel: {
              backgroundColor: themeColors.surface,
            },
          }}
        >
          {/* All Tasks Section */}
          <Accordion.Item value="all-taches">
            <Accordion.Control icon={<IconPencil size={20} color={themeColors.writing} />}>
              <Text fw={600} c={themeColors.writing}>
                {t('writing.sections.allTasks', { count: totalMonths })}
              </Text>
            </Accordion.Control>
            <Accordion.Panel>
              <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4, xl: 5 }} spacing="md">
          {writingData.map((monthData) => (
                  <MonthCard key={monthData.month_year} monthData={monthData} />
          ))}
              </SimpleGrid>
            </Accordion.Panel>
          </Accordion.Item>

          {/* Classified Writing Section - Temporarily available for testing */}
          {true ? (
            <Accordion.Item value="classified-writing">
              <Accordion.Control icon={
                <Box style={{
                  background: 'linear-gradient(135deg, #fd7e14 0%, #20c997 50%, #6f42c1 100%)',
                  borderRadius: '50%',
                  padding: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <IconTarget size={16} color="white" />
                </Box>
              }>
                <Text fw={600} style={{
                  background: 'linear-gradient(135deg, #fd7e14 0%, #20c997 50%, #6f42c1 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}>
                  {t('classifiedWriting.title', 'Tâches Classifiées')}
                </Text>
              </Accordion.Control>
              <Accordion.Panel>
                <ClassifiedWritingCards />
              </Accordion.Panel>
            </Accordion.Item>
          ) : isAuthenticated && (
            /* Premium Upgrade Section for Non-Premium Users */
            <Accordion.Item value="classified-writing-upgrade">
              <Accordion.Control icon={
                <Box style={{
                  background: 'linear-gradient(135deg, #fd7e14 0%, #20c997 50%, #6f42c1 100%)',
                  borderRadius: '50%',
                  padding: '4px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <IconLock size={16} color="white" />
                </Box>
              }>
                <Group gap="sm">
                  <Text fw={600} style={{
                    background: 'linear-gradient(135deg, #fd7e14 0%, #20c997 50%, #6f42c1 100%)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}>
                    {t('classifiedWriting.title', 'Tâches Classifiées')}
                  </Text>
                  <Badge size="sm" variant="gradient" gradient={{ from: 'orange', to: 'red' }}>
                    {t('common.premium', 'Premium')}
                  </Badge>
                </Group>
              </Accordion.Control>
              <Accordion.Panel>
                <Card
                  padding="xl"
                  radius="md"
                  withBorder
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    textAlign: 'center',
                    color: 'white'
                  }}
                >
                  <Stack gap="md" align="center">
                    <IconLock size={48} color="white" opacity={0.8} />
                    <Title order={3} c="white">
                      {t('classifiedWriting.premium.title', 'Fonctionnalité Premium')}
                    </Title>
                    <Text c="white" opacity={0.9} ta="center" maw={500}>
                      {t('classifiedWriting.premium.description', 'Accédez à plus de 1000 tâches d\'écriture organisées par thèmes avec déduplication intelligente. Parfait pour une pratique ciblée et efficace.')}
                    </Text>
                    <Group gap="md" mt="md">
                      <Link to="/membership" style={{ textDecoration: 'none' }}>
                        <Button size="lg" style={{ backgroundColor: 'white', color: '#667eea' }} fw={600}>
                          {t('readingListeningTests.membershipUpgrade.upgrade')}
                        </Button>
                      </Link>
                    </Group>
                  </Stack>
                </Card>
              </Accordion.Panel>
            </Accordion.Item>
          )}
        </Accordion>
      ) : (
        <Alert variant="light" color="yellow" title={t('writing.noExercises.title')} icon={<IconInfoCircle />}>
          {t('writing.noExercises.message')}
        </Alert>
      )}

      {/* Membership Upgrade Call to Action for Logged-in Non-members */}
      {isAuthenticated && !isPremiumUser && writingData.length > 0 && (
        <Card 
          padding="xl" 
          radius="md" 
          withBorder
          style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            textAlign: 'center',
            color: 'white'
          }}
          mb="xl"
        >
          <Title order={3} mb="md" c="white">
            {t('readingListeningTests.membershipUpgrade.title')}
          </Title>
          <Text mb="lg" c="white" opacity={0.9}>
            {t('readingListeningTests.membershipUpgrade.subtitle')}
          </Text>
          <Group justify="center" gap="md">
            <Link to="/membership" style={{ textDecoration: 'none' }}>
              <Button size="lg" style={{ backgroundColor: 'white', color: '#667eea' }} fw={600}>
                {t('readingListeningTests.membershipUpgrade.upgrade')}
              </Button>
            </Link>
            <Link to="/profile" style={{ textDecoration: 'none' }}>
              <Button variant="outline" size="lg" style={{ borderColor: 'white', color: 'white' }}>
                {t('readingListeningTests.membershipUpgrade.viewProfile')}
              </Button>
            </Link>
          </Group>
        </Card>
      )}

      {/* Call to Action for Non-authenticated Users */}
      {!isAuthenticated && writingData.length > 0 && (
        <Card 
          padding="xl" 
          radius="md" 
          withBorder
          style={{
            background: 'white',
            textAlign: 'center'
          }}
          mb="xl"
        >
          <Title order={3} mb="md">
            {t('readingListeningTests.callToAction.title')}
          </Title>
          <Text mb="lg" c="dimmed">
            {t('readingListeningTests.callToAction.subtitle')}
          </Text>
          <Group justify="center" gap="md">
            <Link to="/register" style={{ textDecoration: 'none' }}>
              <Button size="lg" color="blue">
                {t('readingListeningTests.callToAction.register')}
              </Button>
            </Link>
            <Link to="/login" style={{ textDecoration: 'none' }}>
              <Button variant="outline" size="lg" color="blue">
                {t('readingListeningTests.callToAction.login')}
              </Button>
            </Link>
          </Group>
        </Card>
      )}

      {/* Back to sections button */}
      <Group justify="center" mt="xl">
        <Button
          component={Link}
          to="/"
          variant="outline"
          size="lg"
          leftSection={<IconHome size={16} />}
        >
          {t('writing.backToSections')}
        </Button>
      </Group>
    </Container>
  );
}