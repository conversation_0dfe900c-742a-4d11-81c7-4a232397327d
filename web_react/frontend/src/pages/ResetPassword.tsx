import { PasswordInput, But<PERSON>, Paper, Title, Text, Container, <PERSON><PERSON>, Stack } from '@mantine/core';
import { useForm } from '@mantine/form';
import { useNavigate, useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { authApi } from '../services/api';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';

export function ResetPassword() {
  const navigate = useNavigate();
  const { token } = useParams<{ token: string }>();
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { t } = useTranslation();

  const form = useForm({
    initialValues: {
      password: '',
      confirmPassword: '',
    },
    validate: {
      password: (value) => (value.length < 6 ? t('resetPassword.validation.minLength') : null),
      confirmPassword: (value, values) => (value !== values.password ? t('resetPassword.validation.noMatch') : null),
    },
  });

  useEffect(() => {
    if (!token) {
      setError(t('resetPassword.missingToken'));
    }
  }, [token, t]);

  const handleSubmit = async (values: { password: string; confirmPassword: string }) => {
    if (!token) {
      setError(t('resetPassword.missingToken'));
      return;
    }

    try {
      setError('');
      setLoading(true);
      
      await authApi.resetPassword(token, values.password);
      
      notifications.show({
        title: t('resetPassword.success'),
        message: t('resetPassword.successMessage'),
        color: 'green',
      });
      
      navigate('/login');
    } catch (error: any) {
      console.error('Password reset failed:', error);
      const errorMessage = error.response?.data?.error || t('resetPassword.error');
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  if (!token) {
    return (
      <Container size={420} my={40}>
        <Paper withBorder shadow="md" p={30} radius="md">
          <Alert color="red">
            {t('resetPassword.invalidLink')}
          </Alert>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size={420} my={40}>
      <Title ta="center" fw={900}>
        {t('resetPassword.title')}
      </Title>
      <Text c="dimmed" size="sm" ta="center" mt={5}>
        {t('resetPassword.subtitle')}
      </Text>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        {error && (
          <Alert color="red" mb="md">
            {error}
          </Alert>
        )}

        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack>
            <PasswordInput
              label={t('resetPassword.newPassword')}
              placeholder={t('resetPassword.newPasswordPlaceholder')}
              required
              {...form.getInputProps('password')}
            />
            
            <PasswordInput
              label={t('resetPassword.confirmPassword')}
              placeholder={t('resetPassword.confirmPasswordPlaceholder')}
              required
              {...form.getInputProps('confirmPassword')}
            />
            
            <Button 
              fullWidth 
              mt="xl" 
              type="submit" 
              loading={loading}
            >
              {t('resetPassword.submit')}
            </Button>
          </Stack>
        </form>
      </Paper>
    </Container>
  );
} 