import { BrowserRouter as Router, Routes, Route, Navigate, useParams } from 'react-router-dom';
import { MantineProvider, createTheme } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HelmetProvider } from 'react-helmet-async';
import { useEffect, useState, useMemo } from 'react';

// Import Mantine styles
import '@mantine/core/styles.css';
import '@mantine/notifications/styles.css';

// Import your actual application components
import { Home } from './pages/Home';
import { ReadingListening } from './pages/ReadingListening';
import { ReadingListeningTests } from './pages/ReadingListeningTests';
import { MockExam } from './pages/MockExam';
import { CollectionBook } from './pages/CollectionBook';
import { Downloads } from './pages/Downloads';
import { Profile } from './pages/Profile';
import { Membership } from './pages/Membership';
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { ResetPassword } from './pages/ResetPassword';
import { EmailVerification } from './pages/EmailVerification';
import { AuthCallback } from './pages/AuthCallback';
import { PaymentSuccess } from './pages/PaymentSuccess';
import { PaymentCancel } from './pages/PaymentCancel';
import { RedeemPromo } from './pages/RedeemPromo';
import { Writing } from './pages/Writing';
import { WritingMonthDetails } from './pages/WritingMonthDetails';
import { ClassifiedWritingTest } from './pages/ClassifiedWritingTest';
import { ClassifiedWritingSubtopic } from './pages/ClassifiedWritingSubtopic';

import Speaking from './pages/Speaking';
import SpeakingTask2Details from './pages/SpeakingTask2Details';
import SpeakingTask3Details from './pages/SpeakingTask3Details';
import { NotebookBrowse } from './pages/NotebookBrowse';
import { Layout } from './components/Layout';
import { useAuthStore } from './store/useAuthStore';
import { authApi } from './services/api';
import './i18n/config';
import { useTranslation, Trans } from 'react-i18next';
import { initializeLanguageFromSystem } from './store/useLanguageStore';
import { RedirectOldTestUrl, RedirectOldGroupUrl, RedirectOldMockUrl, RedirectOldClassifiedWriting } from './components/RedirectOldUrls';
import { Loader, Center, Stack, Text } from '@mantine/core';
import { AudioSettingsProvider } from './contexts/AudioSettingsContext';
import { useThemeStore } from './store/useThemeStore';
import { useSessionCheck } from './hooks/useSessionCheck';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Create Mantine theme with dark mode support
const theme = createTheme({
  fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  defaultRadius: 'md',
  components: {
    Container: {
      defaultProps: {
        sizes: {
          xs: 540,
          sm: 720,
          md: 960,
          lg: 1140,
          xl: 1320,
        },
      },
    },
  },
  colors: {
    // Custom brand colors
    brand: [
      '#e3f2fd',
      '#bbdefb',
      '#90caf9',
      '#64b5f6',
      '#42a5f5',
      '#2196f3',
      '#1e88e5',
      '#1976d2',
      '#1565c0',
      '#0d47a1',
    ],
  },
  primaryColor: 'blue',
});

// Theme initialization component
function ThemeInitializer({ children }: { children: React.ReactNode }) {
  const { initializeTheme } = useThemeStore();

  useEffect(() => {
    initializeTheme();
  }, [initializeTheme]);

  return <>{children}</>;
}

// Session restoration component
function SessionRestoration({ children }: { children: React.ReactNode }) {
  const { user, setUser, logout, setLoading } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const { t } = useTranslation();

  useEffect(() => {
    const restoreSession = async () => {
      // If user exists in Zustand store, validate session with backend
      if (user) {
        try {
          setLoading(true);
          
          // Check if backend session is still valid
          const response = await authApi.getProfile();
          
          if (response.user) {
            // Update user data with fresh info from backend
            setUser(response.user);
          } else {
            // Backend session invalid, clear local state
            logout();
          }
        } catch (error: any) {
          // If 401 or other auth error, clear local state
          if (error.response?.status === 401) {
            logout();
          }
        } finally {
          setLoading(false);
        }
      }
      
      setIsInitialized(true);
    };

    restoreSession();
  }, []); // Only run once on app startup

  // Show loading screen while initializing
  if (!isInitialized) {
    return (
      <Center style={{ height: '100vh' }}>
        <Stack align="center" gap="md">
          <Loader size="lg" />
          <Text size="sm" c="dimmed">{t('loadingStates.initializing')}</Text>
        </Stack>
      </Center>
    );
  }

  return <>{children}</>;
}

function App() {
  const { t } = useTranslation();
  const { resolvedTheme, initializeTheme } = useThemeStore();

  // Initialize session monitoring for immediate invalidation detection
  // Re-enabled after fixing Google OAuth session creation
  useSessionCheck();

  // Initialize theme and language on app start
  useEffect(() => {
    initializeTheme();
    initializeLanguageFromSystem();
  }, [initializeTheme]);

  // Create a more responsive color scheme manager for better synchronization
  const colorSchemeManager = useMemo(() => {
    let unsubscribeFunction: (() => void) | null = null;

    return {
      get: () => resolvedTheme,
      set: () => {}, // Theme changes are handled by our store
      clear: () => {},
      subscribe: (onUpdate: (colorScheme: any) => void) => {
        // This ensures Mantine components update immediately when theme changes
        unsubscribeFunction = useThemeStore.subscribe(
          (state) => onUpdate(state.resolvedTheme)
        );
        return unsubscribeFunction;
      },
      unsubscribe: () => {
        if (unsubscribeFunction) {
          unsubscribeFunction();
          unsubscribeFunction = null;
        }
      }
    };
  }, [resolvedTheme]);

  useEffect(() => {
    // Remove initial loader once React has mounted and styles are ready
    const removeInitialLoader = () => {
      const loader = document.querySelector('.initial-loader') as HTMLElement;
      if (loader) {
        loader.style.opacity = '0';
        loader.style.transition = 'opacity 0.3s ease';
        setTimeout(() => {
          loader.remove();
        }, 300);
      }
    };
    
    // Ensure all styles are loaded before removing loader
    const timer = setTimeout(removeInitialLoader, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <MantineProvider
          theme={theme}
          defaultColorScheme={resolvedTheme}
          colorSchemeManager={colorSchemeManager}
        >
        <Notifications />
        <ThemeInitializer>
          <AudioSettingsProvider>
            <Router>
              <SessionRestoration>
              <Layout>
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/reading" element={<ReadingListeningTests />} />
                  <Route path="/reading/:testId" element={<ReadingListening />} />
                  <Route path="/reading/group/:groupId" element={<ReadingListening />} />
                  <Route path="/reading/mock/:testId" element={<MockExam />} />
                  <Route path="/listening" element={<ReadingListeningTests />} />
                  <Route path="/listening/:testId" element={<ReadingListening />} />
                  <Route path="/listening/group/:groupId" element={<ReadingListening />} />
                  <Route path="/listening/mock/:testId" element={<MockExam />} />
                  <Route path="/collection" element={<CollectionBook />} />
                  <Route path="/downloads" element={<Downloads />} />
                  <Route path="/writing" element={<Writing />} />

                  {/* Classified Writing Routes - Simplified paths (must come before generic /writing/:monthId) */}
                  <Route path="/writing/classified_tache1" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache2" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache3" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache1/:topicName" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache2/:topicName" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache3/:topicName" element={<ClassifiedWritingTest />} />
                  <Route path="/writing/classified_tache1/:topicName/:subtopicId" element={<ClassifiedWritingSubtopic />} />
                  <Route path="/writing/classified_tache2/:topicName/:subtopicId" element={<ClassifiedWritingSubtopic />} />
                  <Route path="/writing/classified_tache3/:topicName/:subtopicId" element={<ClassifiedWritingSubtopic />} />

                  {/* Generic writing month route (must come after specific classified routes) */}
                  <Route path="/writing/:monthId" element={<WritingMonthDetails />} />

                  {/* Redirect old classified writing paths to new simplified paths */}
                  <Route path="/classified-writing/tache/:tacheNumber" element={<RedirectOldClassifiedWriting />} />
                  <Route path="/classified-writing/tache/:tacheNumber/topic/:topicName" element={<RedirectOldClassifiedWriting />} />
                  <Route path="/classified-writing/tache/:tacheNumber/topic/:topicName/subtopic/:subtopicId" element={<RedirectOldClassifiedWriting />} />

                  <Route path="/speaking" element={<Speaking />} />
                  <Route path="/speaking/task2/:monthId" element={<SpeakingTask2Details />} />
                  <Route path="/speaking/task3/:topicId" element={<SpeakingTask3Details />} />
                  <Route path="/profile" element={<Profile />} />
                  <Route path="/membership" element={<Membership />} />
                  <Route path="/notebook" element={<NotebookBrowse />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route path="/verify-email" element={<EmailVerification />} />
                  <Route path="/reset-password/:token" element={<ResetPassword />} />
                  <Route path="/auth/callback" element={<AuthCallback />} />
                  <Route path="/payment/success" element={<PaymentSuccess />} />
                  <Route path="/payment/cancel" element={<PaymentCancel />} />
                  <Route path="/redeem-promo" element={<RedeemPromo />} />
                  
                  {/* Redirect old URL patterns to new structure */}
                  <Route 
                    path="/test/:section/:testId" 
                    element={<RedirectOldTestUrl />} 
                  />
                  <Route 
                    path="/test-by-difficulty/:section/:groupId" 
                    element={<RedirectOldGroupUrl />} 
                  />
                  <Route 
                    path="/mock-exam/:section/:testId" 
                    element={<RedirectOldMockUrl />} 
                  />
                </Routes>
              </Layout>
              </SessionRestoration>
            </Router>
          </AudioSettingsProvider>
        </ThemeInitializer>
      </MantineProvider>
    </QueryClientProvider>
    </HelmetProvider>
  );
}

export default App;
