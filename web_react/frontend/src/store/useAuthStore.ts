import { create } from 'zustand';
import { persist, devtools } from 'zustand/middleware';
import type { User } from '../types';

interface AuthStore {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setUser: (user: User | null) => void;
  updateUser: (updates: Partial<User>) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  login: (user: User) => void;
  logout: () => void;
  clearError: () => void;
  
  // Computed
  isMember: () => boolean;
  isLifetimeMember: () => boolean;
  isMembershipExpired: () => boolean;
}

export const useAuthStore = create<AuthStore>()(
  devtools(persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user,
        error: null 
      }),
      
      updateUser: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null
      })),
      
      setLoading: (loading) => set({ isLoading: loading }),
      
      setError: (error) => set({ error }),
      
      login: (user) => set({ 
        user, 
        isAuthenticated: true, 
        error: null,
        isLoading: false 
      }),
      
      logout: () => set({ 
        user: null, 
        isAuthenticated: false, 
        error: null,
        isLoading: false 
      }),
      
      clearError: () => set({ error: null }),
      
      isMember: () => {
        const { user } = get();
        return user?.membership_type === 'premium' || user?.membership_type === 'lifetime';
      },
      
      isLifetimeMember: () => {
        const { user } = get();
        // Only premium users with null expires_at are lifetime members
        // Explicitly exclude free users
        return user?.membership_type === 'lifetime' || 
               (user?.membership_type === 'premium' && user?.membership_expires_at === null);
      },
      
      isMembershipExpired: () => {
        const { user } = get();
        
        // Free users don't have memberships to expire
        if (!user || user.membership_type === 'free') {
          return false;
        }
        
        // Premium users without expiration date are lifetime members (never expired)
        if (user.membership_type === 'premium' && user.membership_expires_at === null) {
          return false;
        }
        
        // Premium users with expiration date - check if expired
        if (user.membership_type === 'premium' && user.membership_expires_at) {
        try {
            const expiresDate = new Date(user.membership_expires_at);
          return expiresDate < new Date();
        } catch {
          return true;
        }
        }
        
        // Lifetime members never expire
        if (user.membership_type === 'lifetime') {
          return false;
        }
        
        // Default: consider expired if we can't determine
        return true;
      },
    }),
    {
      name: 'tcf-auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  ), { enabled: import.meta.env.MODE === 'development' })
);