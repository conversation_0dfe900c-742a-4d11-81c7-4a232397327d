import { create } from 'zustand';
import type { TestData, TestHistory, Highlight, NotebookNote } from '../types';

interface TestStore {
  // Current test state
  currentTest: TestData | null;
  currentSection: string | null;
  currentTestId: string | null;
  isFreeTest: boolean;
  answers: Record<string, string | string[]>;
  currentQuestionIndex: number;
  
  // UI state
  isLoading: boolean;
  showNotebook: boolean;
  notebookContent: string;
  highlights: Record<number, Highlight>;
  
  // History
  testHistory: TestHistory[];
  
  // Actions
  setCurrentTest: (test: TestData, section: string, testId: string, isFree?: boolean) => void;
  setAnswer: (questionId: string, answer: string | string[]) => void;
  setAnswers: (answers: Record<string, string | string[]>) => void;
  setCurrentQuestionIndex: (index: number) => void;
  setLoading: (loading: boolean) => void;
  setShowNotebook: (show: boolean) => void;
  setNotebookContent: (content: string) => void;
  setHighlight: (questionIndex: number, highlight: Highlight) => void;
  removeHighlight: (questionIndex: number) => void;
  setTestHistory: (history: TestHistory[]) => void;
  addTestHistory: (history: TestHistory) => void;
  clearCurrentTest: () => void;
  
  // Computed
  getAnswer: (questionId: string) => string | string[] | undefined;
  getProgress: () => number;
  getTotalQuestions: () => number;
  getAnsweredCount: () => number;
}

export const useTestStore = create<TestStore>((set, get) => ({
  // Initial state
  currentTest: null,
  currentSection: null,
  currentTestId: null,
  isFreeTest: false,
  answers: {},
  currentQuestionIndex: 0,
  isLoading: false,
  showNotebook: false,
  notebookContent: '',
  highlights: {},
  testHistory: [],

  // Actions
  setCurrentTest: (test, section, testId, isFree = false) => set({
    currentTest: test,
    currentSection: section,
    currentTestId: testId,
    isFreeTest: isFree,
    answers: {},
    currentQuestionIndex: 0,
    highlights: {},
  }),

  setAnswer: (questionId, answer) => set((state) => ({
    answers: { ...state.answers, [questionId]: answer }
  })),

  setAnswers: (answers) => set({ answers }),

  setCurrentQuestionIndex: (index) => set({ currentQuestionIndex: index }),

  setLoading: (loading) => set({ isLoading: loading }),

  setShowNotebook: (show) => set({ showNotebook: show }),

  setNotebookContent: (content) => set({ notebookContent: content }),

  setHighlight: (questionIndex, highlight) => set((state) => ({
    highlights: { ...state.highlights, [questionIndex]: highlight }
  })),

  removeHighlight: (questionIndex) => set((state) => {
    const newHighlights = { ...state.highlights };
    delete newHighlights[questionIndex];
    return { highlights: newHighlights };
  }),

  setTestHistory: (history) => set({ testHistory: history }),

  addTestHistory: (history) => set((state) => ({
    testHistory: [history, ...state.testHistory]
  })),

  clearCurrentTest: () => set({
    currentTest: null,
    currentSection: null,
    currentTestId: null,
    isFreeTest: false,
    answers: {},
    currentQuestionIndex: 0,
    highlights: {},
    notebookContent: '',
    showNotebook: false,
  }),

  // Computed
  getAnswer: (questionId) => {
    const { answers } = get();
    return answers[questionId];
  },

  getProgress: () => {
    const { answers, currentTest } = get();
    if (!currentTest || !currentTest.questions) return 0;
    const totalQuestions = currentTest.questions.length;
    const answeredCount = Object.keys(answers).length;
    return totalQuestions > 0 ? (answeredCount / totalQuestions) * 100 : 0;
  },

  getTotalQuestions: () => {
    const { currentTest } = get();
    return currentTest?.questions?.length || 0;
  },

  getAnsweredCount: () => {
    const { answers } = get();
    return Object.keys(answers).length;
  },
})); 