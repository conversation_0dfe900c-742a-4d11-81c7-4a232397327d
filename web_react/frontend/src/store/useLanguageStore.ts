import { create } from 'zustand';
import i18n from '../i18n/config';
const supportedLangs = ['fr', 'en', 'zh'] as const;
type LangType = typeof supportedLangs[number];

interface LanguageState {
  lang: LangType;
  setLang: (lang: LangType) => void;
}

// Get system language preference
const getSystemLanguage = (): LangType => {
  if (typeof navigator === 'undefined') return 'fr';

  // Get user's preferred languages from browser
  const languages = navigator.languages || [navigator.language];

  for (const lang of languages) {
    // Check exact match first
    if (supportedLangs.includes(lang as LangType)) {
      return lang as LangType;
    }

    // Check language code without region (e.g., 'en-US' -> 'en')
    const langCode = lang.split('-')[0];
    if (supportedLangs.includes(langCode as LangType)) {
      return langCode as LangType;
    }

    // Handle Chinese variants
    if (lang.startsWith('zh')) {
      return 'zh';
    }
  }

  // Default to French if no supported language found
  return 'fr';
};

const getInitialLang = (): LangType => {
  const stored = localStorage.getItem('lang');
  if (supportedLangs.includes(stored as LangType)) {
    return stored as LangType;
  }
  const systemLang = getSystemLanguage();
  return systemLang;
};

export const useLanguageStore = create<LanguageState>((set) => ({
  lang: getInitialLang(),
  setLang: (lang: LangType) => {
    set(() => ({ lang }));
    localStorage.setItem('lang', lang);
    i18n.changeLanguage(lang)
  },
}));

// Initialize language on first visit if not already set
export const initializeLanguageFromSystem = () => {
  const stored = localStorage.getItem('lang');
  if (!stored) {
    const systemLang = getSystemLanguage();
    localStorage.setItem('lang', systemLang);
    i18n.changeLanguage(systemLang);
    // Update the store state
    useLanguageStore.setState({ lang: systemLang });
  }
};
