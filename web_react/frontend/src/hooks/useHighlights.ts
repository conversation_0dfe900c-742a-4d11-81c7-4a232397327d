import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { highlightApi } from '../services/api';
import { notifications } from '@mantine/notifications';
import { useThemeStore } from '../store/useThemeStore';

export interface HighlightData {
  start: number;
  end: number;
  text: string;
  color?: string;
}

interface UseHighlightsProps {
  section: string;
  testId: string;
  questionIndex: number;
  isFree?: boolean;
}

export function useHighlights({ section, testId, questionIndex, isFree = false }: UseHighlightsProps) {
  const queryClient = useQueryClient();
  const { resolvedTheme } = useThemeStore();

  const queryKey = ['highlights', section, testId, questionIndex, isFree];

  // Fetch existing highlights for this question
  const { data: highlightsData, isLoading, refetch } = useQuery({
    queryKey,
    queryFn: () => highlightApi.getHighlights(section, testId, questionIndex, isFree),
    staleTime: 0, // Don't cache for debugging
  });

  const highlights = highlightsData?.highlights || [];

  // Save highlight mutation
  const saveHighlightMutation = useMutation({
    mutationFn: (data: {
      start: number;
      end: number;
      text: string;
      color?: string;
    }) => {
      return highlightApi.saveHighlight({
        section,
        test_id: testId,
        question_index: questionIndex,
        start: data.start,
        end: data.end,
        text: data.text,
        color: data.color || (resolvedTheme === 'dark' ? '#ffd43b' : '#ffeb3b'),
        free: isFree,
      });
    },
    onSuccess: () => {
      refetch(); // Immediately refetch instead of invalidate
      notifications.show({
        title: 'Succès',
        message: 'Surlignage ajouté avec succès',
        color: 'green',
      });
    },
    onError: (error: any) => {
      notifications.show({
        title: 'Erreur',
        message: error.response?.data?.message || 'Erreur lors de l\'ajout du surlignage',
        color: 'red',
      });
    },
  });

  // Remove highlight mutation
  const removeHighlightMutation = useMutation({
    mutationFn: (data: { start: number; end: number }) => {
      return highlightApi.removeHighlight({
        section,
        test_id: testId,
        question_index: questionIndex,
        start: data.start,
        end: data.end,
        free: isFree,
      });
    },
    onSuccess: () => {
      refetch(); // Immediately refetch instead of invalidate
      notifications.show({
        title: 'Succès',
        message: 'Surlignage supprimé avec succès',
        color: 'green',
      });
    },
    onError: (error: any) => {
      notifications.show({
        title: 'Erreur',
        message: error.response?.data?.message || 'Erreur lors de la suppression du surlignage',
        color: 'red',
      });
    },
  });

  // Simple add highlight function with theme-aware default color
  const addHighlight = (start: number, end: number, text: string, color?: string) => {
    const defaultColor = color || (resolvedTheme === 'dark' ? '#ffd43b' : '#ffeb3b');
    saveHighlightMutation.mutate({ start, end, text, color: defaultColor });
  };

  // Simple remove highlight function
  const removeHighlight = (highlight: HighlightData) => {
    removeHighlightMutation.mutate({
      start: highlight.start,
      end: highlight.end,
    });
  };

  return {
    highlights,
    isLoading,
    addHighlight,
    removeHighlight,
    isAddingHighlight: saveHighlightMutation.isPending,
    isRemovingHighlight: removeHighlightMutation.isPending,
  };
} 