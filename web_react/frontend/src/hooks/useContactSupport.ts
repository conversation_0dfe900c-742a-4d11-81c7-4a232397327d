import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { utilsApi } from '../services/api';

export function useContactSupport() {
  const { t } = useTranslation();
  const [contactOpened, { open: openContact, close: closeContact }] = useDisclosure(false);
  const [isSubmittingSupport, setIsSubmittingSupport] = useState(false);

  const contactForm = useForm({
    initialValues: {
      message: '',
    },
    validate: {
      message: (val) => (val.length < 10 ? t('contact.validation.minLength') : null),
    },
  });

  const handleContactSupport = async (values: typeof contactForm.values) => {
    setIsSubmittingSupport(true);
    try {
      const response = await utilsApi.contactSupport(values.message);
      
      notifications.show({
        title: t('contact.success.title'),
        message: response.message || t('contact.success.message'),
        color: 'green',
      });
      
      contactForm.reset();
      closeContact();
    } catch (error: any) {
      console.error('Contact support error:', error);
      notifications.show({
        title: t('contact.error.title'),
        message: error.response?.data?.error || t('contact.error.message'),
        color: 'red',
      });
    } finally {
      setIsSubmittingSupport(false);
    }
  };

  return {
    contactOpened,
    openContact,
    closeContact,
    contactForm,
    isSubmittingSupport,
    handleContactSupport,
  };
} 