import { useState, useEffect, useCallback, useRef } from 'react';

interface UseAutoHideNavigationOptions {
  isTestPage: boolean;
  showThreshold?: number;
  hideDelay?: number;
}

/**
 * Hook to manage auto-hiding navigation behavior
 * Navigation shows when mouse is near top, hides when mouse leaves the area
 */
export function useAutoHideNavigation({
  isTestPage,
  showThreshold = 80,
  hideDelay = 300
}: UseAutoHideNavigationOptions) {
  // Always call all hooks unconditionally and in the same order
  const [isVisible, setIsVisible] = useState(!isTestPage); // Start hidden on test pages
  const [isHovered, setIsHovered] = useState(false);
  const [mouseInTopArea, setMouseInTopArea] = useState(false);
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Clear timeout helper
  const clearHideTimeout = useCallback(() => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current);
      hideTimeoutRef.current = null;
    }
  }, []);

  // Handle mouse movement - always define this callback
  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (!isTestPage) return;

    const mouseY = event.clientY;
    const inTopArea = mouseY <= showThreshold;

    setMouseInTopArea(inTopArea);
  }, [isTestPage, showThreshold]);

  // Set hide timeout helper
  const setHideTimeout = useCallback(() => {
    clearHideTimeout();
    hideTimeoutRef.current = setTimeout(() => {
      setIsVisible(false);
    }, hideDelay);
  }, [hideDelay, clearHideTimeout]);

  // Update visibility based on mouse position and hover state
  useEffect(() => {
    if (!isTestPage) {
      setIsVisible(true);
      return;
    }

    clearHideTimeout();

    if (mouseInTopArea || isHovered) {
      // Show immediately when mouse is in top area or hovering navigation
      setIsVisible(true);
    } else {
      // Hide with small delay when mouse leaves top area and not hovering
      setHideTimeout();
    }

    return () => clearHideTimeout();
  }, [isTestPage, mouseInTopArea, isHovered, setHideTimeout, clearHideTimeout]);

  // Handle navigation hover
  const handleNavigationMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleNavigationMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  // Handle page visibility changes
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden) {
      clearHideTimeout();
    }
  }, [clearHideTimeout]);

  // Setup event listeners - always run this effect
  useEffect(() => {
    // Always add event listeners, but only act on them if isTestPage
    document.addEventListener('mousemove', handleMouseMove, { passive: true });
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Set initial state based on test page status
    if (isTestPage) {
      setIsVisible(false);
      setMouseInTopArea(false);
    } else {
      setIsVisible(true);
      setMouseInTopArea(false);
      clearHideTimeout();
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      clearHideTimeout();
    };
  }, [isTestPage, handleMouseMove, handleVisibilityChange, clearHideTimeout]);

  // Reset state when leaving test pages
  useEffect(() => {
    if (!isTestPage) {
      setIsVisible(true);
      setIsHovered(false);
      setMouseInTopArea(false);
      clearHideTimeout();
    }
  }, [isTestPage, clearHideTimeout]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearHideTimeout();
    };
  }, [clearHideTimeout]);

  return {
    isVisible,
    isHovered,
    mouseInTopArea,
    handleNavigationMouseEnter,
    handleNavigationMouseLeave,
    // Force show/hide methods for manual control
    forceShow: () => {
      setIsVisible(true);
      setMouseInTopArea(true);
      clearHideTimeout();
    },
    forceHide: () => {
      if (isTestPage) {
        setIsVisible(false);
        setMouseInTopArea(false);
        clearHideTimeout();
      }
    }
  };
}
