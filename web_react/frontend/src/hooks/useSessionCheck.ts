/**
 * Session monitoring hook that periodically checks if session is still valid
 * This ensures users get notified immediately when their session is invalidated
 */

import { useEffect, useRef } from 'react';
import { showSessionInvalidatedNotification } from '../utils/sessionUtils';

const SESSION_CHECK_INTERVAL = 10000; // Check every 10 seconds
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

interface SessionCheckResponse {
  authenticated: boolean;
  error?: string;
  message?: string;
  messageData?: any;
  code?: string;
}

export function useSessionCheck() {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRef = useRef(false);

  const checkSession = async (): Promise<void> => {
    // Prevent multiple simultaneous checks
    if (isCheckingRef.current) return;
    isCheckingRef.current = true;

    // Emit event for monitoring indicator
    window.dispatchEvent(new CustomEvent('session-check'));

    try {
      const response = await fetch(`${API_URL}/auth/session`, {
        method: 'GET',
        credentials: 'include', // Include cookies
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.status === 401) {
        // Session is invalid
        const errorData: SessionCheckResponse = await response.json();
        
        if (errorData.code === 'SESSION_INVALID') {
          // Stop monitoring since session is invalid
          stopMonitoring();
          

          
          // Show the session invalidation notification
          showSessionInvalidatedNotification(errorData.message, errorData.messageData);
          
          // Redirect will be handled by the notification
          return;
        }
      } else if (response.status === 200) {
        // Session is valid, continue monitoring
        const data: SessionCheckResponse = await response.json();
        if (!data.authenticated) {
          // User is not logged in, stop monitoring
          stopMonitoring();
        }
      }
      // For other status codes, just continue monitoring
      
    } catch (error) {
      // Network error or other issue, continue monitoring silently
      // Don't spam console with network errors
    } finally {
      isCheckingRef.current = false;
    }
  };

  const startMonitoring = (): void => {
    // Don't start if already monitoring
    if (intervalRef.current) return;
    

    intervalRef.current = setInterval(checkSession, SESSION_CHECK_INTERVAL);
  };

  const stopMonitoring = (): void => {
    if (intervalRef.current) {

      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  // Auto-start monitoring when hook is used
  useEffect(() => {
    startMonitoring();
    
    // Cleanup on unmount
    return () => {
      stopMonitoring();
    };
  }, []);

  // Also check immediately when the page becomes visible again
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && intervalRef.current) {
        // Check session immediately when user returns to tab
        checkSession();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return {
    startMonitoring,
    stopMonitoring,
    checkSession,
  };
}
