import { useState, useEffect, useRef } from 'react';
import { useAuthStore } from '../store/useAuthStore';

interface UseMediaReturn {
  mediaUrl: string | null;
  isLoading: boolean;
  error: string | null;
}

// Cache for media URLs to prevent duplicate requests
const mediaUrlCache = new Map<string, { url: string; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const pendingRequests = new Map<string, Promise<string>>();

export function useMedia(
  mediaPath: string | null,
  section: string,
  testId: string
): UseMediaReturn {
  const [mediaUrl, setMediaUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { isAuthenticated } = useAuthStore();
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Immediately clear the old URL when path changes to prevent race conditions
    setMediaUrl(null);
    setError(null);

    if (!mediaPath || !isAuthenticated) {
      setIsLoading(false);
      return;
    }

    const fetchMediaUrl = async () => {
      setIsLoading(true);

      // Create new abort controller for this request
      abortControllerRef.current = new AbortController();

      try {
        // Check if this is a full path (for group tests) or just a filename
        if (mediaPath.includes('/')) {
          // Full path like "listening_asset/media_test12/Q1.mp3"
          const pathParts = mediaPath.split('/');
          if (pathParts.length >= 3) {
            const assetDir = pathParts[0]; // e.g., "listening_asset" or "listening_asset_free"
            const mediaDir = pathParts[1]; // e.g., "media_test12"
            const filename = pathParts[2]; // e.g., "Q1.mp3"

            // Determine section from asset directory
            let mediaSection = section;
            if (assetDir.endsWith('_free') && !section.endsWith('_free')) {
              mediaSection = section + '_free';
            }

            // Extract test number from media directory (e.g., "media_test12" -> "test12")
            const testNumber = mediaDir.replace('media_', '');

            const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';
            const requestUrl = `${apiUrl}/media/${mediaSection}/${testNumber}/${filename}`;

            // Check cache first
            const cacheKey = requestUrl;
            const cached = mediaUrlCache.get(cacheKey);
            if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
              setMediaUrl(cached.url);
              setIsLoading(false);
              return;
            }

            // Check if there's already a pending request for this URL
            if (pendingRequests.has(cacheKey)) {
              try {
                const url = await pendingRequests.get(cacheKey)!;
                setMediaUrl(url);
                setIsLoading(false);
                return;
              } catch (error) {
                // If pending request failed, continue with new request
                pendingRequests.delete(cacheKey);
              }
            }

            // Create new request promise
            const requestPromise = (async () => {
              const response = await fetch(requestUrl, {
                method: 'GET',
                credentials: 'include',
                headers: {
                  'Content-Type': 'application/json',
                },
                signal: abortControllerRef.current?.signal
              });

              if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
              }

              const data = await response.json();
              if (data.success && data.media_url) {
                // Cache the result
                mediaUrlCache.set(cacheKey, {
                  url: data.media_url,
                  timestamp: Date.now()
                });
                return data.media_url;
              } else {
                throw new Error(data.error || 'Failed to get media URL');
              }
            })();

            // Store the pending request
            pendingRequests.set(cacheKey, requestPromise);

            try {
              const url = await requestPromise;
              setMediaUrl(url);
            } finally {
              pendingRequests.delete(cacheKey);
            }
            return;
          }
        }

        // Fallback: treat as simple filename (for normal tests)
        const filename = mediaPath.split('/').pop();
        if (!filename) {
          throw new Error('Invalid media path');
        }

        const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';
        const requestUrl = `${apiUrl}/media/${section}/${testId}/${filename}`;

        // Check cache first
        const cacheKey = requestUrl;
        const cached = mediaUrlCache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
          setMediaUrl(cached.url);
          setIsLoading(false);
          return;
        }

        // Check if there's already a pending request for this URL
        if (pendingRequests.has(cacheKey)) {
          try {
            const url = await pendingRequests.get(cacheKey)!;
            setMediaUrl(url);
            setIsLoading(false);
            return;
          } catch (error) {
            // If pending request failed, continue with new request
            pendingRequests.delete(cacheKey);
          }
        }

        // Create new request promise
        const requestPromise = (async () => {
          const response = await fetch(requestUrl, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
            signal: abortControllerRef.current?.signal
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          if (data.success && data.media_url) {
            // Cache the result
            mediaUrlCache.set(cacheKey, {
              url: data.media_url,
              timestamp: Date.now()
            });
            return data.media_url;
          } else {
            throw new Error(data.error || 'Failed to get media URL');
          }
        })();

        // Store the pending request
        pendingRequests.set(cacheKey, requestPromise);

        try {
          const url = await requestPromise;
          setMediaUrl(url);
        } finally {
          pendingRequests.delete(cacheKey);
        }

      } catch (err) {
        // Don't set error if request was aborted
        if (err instanceof Error && err.name === 'AbortError') {
          return;
        }
        const errorMessage = err instanceof Error ? err.message : 'Unknown error';
        setError(errorMessage);
        setMediaUrl(null);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMediaUrl();

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [mediaPath, section, testId, isAuthenticated]);

  return {
    mediaUrl,
    isLoading,
    error
  };
}

// Convenience hooks for specific media types
export function useAudio(audioPath: string | null, section: string, testId: string) {
  return useMedia(audioPath, section, testId);
}

export function useImage(imagePath: string | null, section: string, testId: string) {
  return useMedia(imagePath, section, testId);
}
