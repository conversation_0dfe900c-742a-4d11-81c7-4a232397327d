import { useLocation } from 'react-router-dom';
import { useMemo } from 'react';

/**
 * Hook to detect if the current page is a test page where the navigation should auto-hide
 */
export function useTestPageDetection() {
  const location = useLocation();
  
  const isTestPage = useMemo(() => {
    const pathname = location.pathname;
    
    // Define test page patterns
    const testPagePatterns = [
      // Reading tests
      /^\/reading\/[^/]+$/,           // /reading/test12
      /^\/reading\/group\/\d+$/,      // /reading/group/1
      /^\/reading\/mock\/[^/]+$/,     // /reading/mock/mock1
      
      // Listening tests
      /^\/listening\/[^/]+$/,         // /listening/test12
      /^\/listening\/group\/\d+$/,    // /listening/group/1
      /^\/listening\/mock\/[^/]+$/,   // /listening/mock/mock1
      
      // Writing tests
      /^\/writing\/[^/]+$/,           // /writing/2024-01
      
      // Speaking tests
      /^\/speaking\/task2\/[^/]+$/,   // /speaking/task2/2024-01
      /^\/speaking\/task3\/[^/]+$/,   // /speaking/task3/topic1
      
      // Collection book
      /^\/collection$/,               // /collection
    ];
    
    // Check if current path matches any test page pattern
    return testPagePatterns.some(pattern => pattern.test(pathname));
  }, [location.pathname]);
  
  const testPageType = useMemo(() => {
    const pathname = location.pathname;
    
    if (pathname.startsWith('/reading/')) return 'reading';
    if (pathname.startsWith('/listening/')) return 'listening';
    if (pathname.startsWith('/writing/') && pathname !== '/writing') return 'writing';
    if (pathname.startsWith('/speaking/') && pathname !== '/speaking') return 'speaking';
    if (pathname === '/collection') return 'collection';
    
    return null;
  }, [location.pathname]);
  
  return {
    isTestPage,
    testPageType,
    pathname: location.pathname
  };
}
