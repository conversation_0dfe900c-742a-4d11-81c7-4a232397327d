import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';

interface AudioSettings {
  autoPlayEnabled: boolean;
  defaultPlaybackSpeed: number;
  defaultVolume: number;
  rememberSettings: boolean;
}

interface AudioSettingsContextType {
  settings: AudioSettings;
  updateSettings: (newSettings: Partial<AudioSettings>) => void;
  toggleAutoPlay: () => void;
  setPlaybackSpeed: (speed: number) => void;
  setVolume: (volume: number) => void;
}

const DEFAULT_SETTINGS: AudioSettings = {
  autoPlayEnabled: false,
  defaultPlaybackSpeed: 1,
  defaultVolume: 1,
  rememberSettings: true
};

const STORAGE_KEY = 'tcf-audio-settings';

const AudioSettingsContext = createContext<AudioSettingsContextType | undefined>(undefined);

export const useAudioSettings = () => {
  const context = useContext(AudioSettingsContext);
  if (!context) {
    throw new Error('useAudioSettings must be used within an AudioSettingsProvider');
  }
  return context;
};

interface AudioSettingsProviderProps {
  children: ReactNode;
}

export const AudioSettingsProvider: React.FC<AudioSettingsProviderProps> = ({ children }) => {
  const [settings, setSettings] = useState<AudioSettings>(DEFAULT_SETTINGS);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem(STORAGE_KEY);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prevSettings => ({
          ...prevSettings,
          ...parsedSettings
        }));
      }
    } catch (error) {
      console.warn('Failed to load audio settings from localStorage:', error);
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    if (settings.rememberSettings) {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
      } catch (error) {
        console.warn('Failed to save audio settings to localStorage:', error);
      }
    }
  }, [settings]);

  const updateSettings = (newSettings: Partial<AudioSettings>) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      ...newSettings
    }));
  };

  const toggleAutoPlay = () => {
    setSettings(prevSettings => ({
      ...prevSettings,
      autoPlayEnabled: !prevSettings.autoPlayEnabled
    }));
  };

  const setPlaybackSpeed = (speed: number) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      defaultPlaybackSpeed: speed
    }));
  };

  const setVolume = (volume: number) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      defaultVolume: volume
    }));
  };

  const contextValue: AudioSettingsContextType = {
    settings,
    updateSettings,
    toggleAutoPlay,
    setPlaybackSpeed,
    setVolume
  };

  return (
    <AudioSettingsContext.Provider value={contextValue}>
      {children}
    </AudioSettingsContext.Provider>
  );
}; 