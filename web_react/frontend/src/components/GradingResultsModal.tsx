import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from '@tanstack/react-query';
import { testApi } from '../services/api';
import { useNavigate } from 'react-router-dom';
import { useThemeStore } from '../store/useThemeStore';

interface GradingResultsModalProps {
  isOpen: boolean;
  onClose: () => void;
  results: {
    score: number;
    max_score: number;
    score_699?: number;
    percent_699?: number;
    correct_count: number;
    wrong_details: Array<{
      question: string;
      yourAnswer: string;
      correctAnswer: string;
    }>;
  };
  testType: 'reading' | 'listening';
  testId: string;
  questionIds?: Record<string, string>; // Map question numbers to actual question IDs
  isGroupTest?: boolean; // Flag to indicate if this is a group test
  onRemoveCorrectQuestions?: () => void; // Callback to remove all correct questions from collection
}

export function GradingResultsModal({ 
  isOpen, 
  onClose, 
  results, 
  testType, 
  testId, 
  questionIds,
  isGroupTest = false,
  onRemoveCorrectQuestions
}: GradingResultsModalProps) {
  const { t, ready } = useTranslation();
  const navigate = useNavigate();
  const { resolvedTheme } = useThemeStore();
  const [showCollectionSuccess, setShowCollectionSuccess] = useState(false);
  const [collectionResult, setCollectionResult] = useState<{
    added_count: number;
    already_bookmarked: number;
    errors: string[];
    total_processed: number;
  } | null>(null);

  // Calculate CLB level based on TCF score
  const calculateCLBLevel = (tcfScore: number): string => {
    if (tcfScore < 342) return '<CLB4';
    if (tcfScore >= 342 && tcfScore <= 374) return 'CLB4';
    if (tcfScore >= 375 && tcfScore <= 405) return 'CLB5';
    if (tcfScore >= 406 && tcfScore <= 452) return 'CLB6';
    if (tcfScore >= 453 && tcfScore <= 498) return 'CLB7';
    if (tcfScore >= 499 && tcfScore <= 523) return 'CLB8';
    if (tcfScore >= 524 && tcfScore <= 548) return 'CLB9';
    if (tcfScore >= 549 && tcfScore <= 699) return 'CLB10';
    return '<CLB4';
  };

  const addToCollectionMutation = useMutation({
    mutationFn: async () => {
      
      // Build the questions array for bulk collection
      // Only include questions that have valid UUID question IDs (database questions)
      const questions = results.wrong_details.map(detail => {
        const questionId = questionIds?.[detail.question];
        
        // Check if this is a real database UUID (36 chars, contains hyphens, no underscores)
        // Real UUIDs look like: "550e8400-e29b-41d4-a716-************"
        // Fake IDs look like: "test12_q1" or "group1_q2" 
        const isValidUUID = questionId && 
                           questionId.length === 36 && 
                           questionId.includes('-') && 
                           !questionId.includes('_') &&
                           !questionId.includes('test') &&
                           !questionId.includes('group') &&
                           !questionId.includes('mock');
        
        return isValidUUID ? { question_id: questionId } : null;
      }).filter(q => q !== null) as Array<{ question_id: string }>;
      
      if (questions.length === 0) {
        throw new Error(t('test.gradingResults.errors.noValidQuestions') || 'No questions with valid database IDs found for collection. This may be because this test type doesn\'t support collection yet.');
      }
      
      // Call the bulk bookmark API
      const result = await testApi.addBulkBookmarks({
        questions: questions,
        testType: testType
      });

      return result;
    },
    onSuccess: (data) => {
      setCollectionResult(data);
      setShowCollectionSuccess(true);
      
      // Trigger a refresh of bookmark icons on the current page
      window.dispatchEvent(new CustomEvent('refreshBookmarks'));
    },
    onError: (error: any) => {
      console.error('❌ Failed to add questions to collection:', error);
      
      // Show more specific error message with translation
      let errorMessage = t('test.gradingResults.errors.failedToAdd') || 'Failed to add questions to collection. ';
      if (error.message) {
        errorMessage += error.message;
      } else if (error.response?.data?.error) {
        errorMessage += error.response.data.error;
      } else {
        errorMessage += t('test.gradingResults.errors.tryAgain') || 'Please try again or contact support if the problem persists.';
      }
      
      alert(errorMessage);
    }
  });

  // Check if collection is available for this test type
  const canCollectQuestions = () => {
    if (!results.wrong_details || results.wrong_details.length === 0) return false;
    
    // Check if we have any real database question IDs (UUIDs)
    const hasValidQuestionIds = results.wrong_details.some(detail => {
      const questionId = questionIds?.[detail.question];
      // Real UUIDs: 36 chars, contains hyphens, no underscores, no test/group/mock prefixes
      return questionId && 
             questionId.length === 36 && 
             questionId.includes('-') && 
             !questionId.includes('_') &&
             !questionId.includes('test') &&
             !questionId.includes('group') &&
             !questionId.includes('mock');
    });
    
    return hasValidQuestionIds;
  };

  const handleAddToCollection = () => {
    addToCollectionMutation.mutate();
  };

  const handleViewCollection = () => {
    navigate('/collection');
    onClose();
  };

  const handleClose = () => {
    setShowCollectionSuccess(false);
    setCollectionResult(null);
    onClose();
  };

  if (!isOpen) return null;

  // Wait for translations to be ready
  if (!ready) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg p-6 shadow-xl">
          <div className="text-center">{t('common.loading') || 'Loading...'}</div>
        </div>
      </div>
    );
  }

  // Calculate percentage for regular display
  const percentage = results.max_score > 0 ? Math.round((results.score / results.max_score) * 100) : 0;
  
  // Determine performance level for styling
  const getPerformanceStyle = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-50 border-green-200';
    if (percentage >= 60) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (percentage >= 40) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  return (
    <div className="fixed inset-0 backdrop-blur-sm bg-black/10 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-lg w-full max-h-[80vh] overflow-y-auto shadow-2xl ${
        resolvedTheme === 'dark'
          ? 'bg-gray-900 border border-gray-700'
          : 'bg-white border border-gray-200'
      }`}>
        <div className="p-5">
          {!showCollectionSuccess ? (
            <>
              {/* Results Header */}
              <div className="text-center mb-4">
                <h2 className={`text-xl font-bold mb-3 ${
                  resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {t('test.gradingResults.title') || 'Test Results'}
                </h2>
              </div>

              {/* TCF Score with CLB Level - Only for non-group tests */}
              {!isGroupTest && results.score_699 !== undefined && (
                <div className={`p-4 rounded-lg mb-4 shadow-sm ${
                  resolvedTheme === 'dark'
                    ? 'bg-gray-800 text-white border border-gray-600'
                    : 'bg-gray-100 text-gray-900 border border-gray-200'
                }`}>
                  <div className="text-center">
                    <div className="text-lg font-bold mb-1">
                      {results.score_699} / 699
                    </div>
                    <div className="text-sm opacity-90">
                      {calculateCLBLevel(results.score_699)}
                    </div>
                  </div>
                </div>
              )}

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-3 mb-4">
                {/* Correct Answers */}
                <div className={`p-3 rounded-lg ${
                  resolvedTheme === 'dark'
                    ? 'bg-green-900 border border-green-700'
                    : 'bg-green-100 border border-green-200'
                }`}>
                  <div className="text-center">
                    <div className={`text-lg font-bold mb-1 ${
                      resolvedTheme === 'dark' ? 'text-green-300' : 'text-green-700'
                    }`}>
                      {results.correct_count}
                    </div>
                    <div className={`text-xs font-semibold ${
                      resolvedTheme === 'dark' ? 'text-green-200' : 'text-green-800'
                    }`}>
                      {t('test.gradingResults.correctAnswers') || 'Correct'}
                    </div>
                  </div>
                </div>

                {/* Wrong Answers */}
                <div className={`p-3 rounded-lg ${
                  resolvedTheme === 'dark'
                    ? 'bg-red-900 border border-red-700'
                    : 'bg-red-100 border border-red-200'
                }`}>
                  <div className="text-center">
                    <div className={`text-lg font-bold mb-1 ${
                      resolvedTheme === 'dark' ? 'text-red-300' : 'text-red-700'
                    }`}>
                      {results.wrong_details.length}
                    </div>
                    <div className={`text-xs font-semibold ${
                      resolvedTheme === 'dark' ? 'text-red-200' : 'text-red-800'
                    }`}>
                      {t('test.gradingResults.incorrectAnswers') || 'Incorrect'}
                    </div>
                  </div>
                </div>
              </div>

              {/* Wrong Questions Details */}
              {results.wrong_details.length > 0 && (
                <div className="mb-4">
                  <h3 className={`text-sm font-semibold mb-2 flex items-center ${
                    resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {t('test.gradingResults.reviewIncorrect') || 'Review Incorrect Questions'} ({results.wrong_details.length})
                  </h3>
                  <div className={`space-y-2 max-h-64 overflow-y-auto p-3 rounded-lg ${
                    resolvedTheme === 'dark'
                      ? 'bg-gray-800 border border-gray-600'
                      : 'bg-gray-50 border border-gray-100'
                  }`}>
                    {results.wrong_details.map((wrong, index) => (
                      <div key={index} className={`p-3 rounded-lg shadow-sm flex flex-row flex-wrap items-center gap-4 text-sm ${
                        resolvedTheme === 'dark'
                          ? 'bg-gray-700 border border-gray-600'
                          : 'bg-white border border-gray-200'
                      }`}>
                        <span className={`font-semibold ${
                          resolvedTheme === 'dark' ? 'text-white' : 'text-gray-900'
                        }`}>
                          {wrong.question}
                        </span>
                        <span className={resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>
                          {t('test.gradingResults.yourAnswer') || 'Your answer'}: <span className={`font-medium ${
                            resolvedTheme === 'dark' ? 'text-red-400' : 'text-red-700'
                          }`}>{wrong.yourAnswer}</span>
                        </span>
                        <span className={resolvedTheme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>
                          {t('test.gradingResults.correctAnswer') || 'Correct answer'}: <span className={`font-medium ${
                            resolvedTheme === 'dark' ? 'text-green-400' : 'text-green-700'
                          }`}>{wrong.correctAnswer}</span>
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Collection Book Section - Only for collectable test types */}
              {results.wrong_details.length > 0 && canCollectQuestions() && (
                <div className={`p-4 rounded-lg mb-4 shadow-sm ${
                  resolvedTheme === 'dark'
                    ? 'bg-yellow-900 text-yellow-100 border border-yellow-700'
                    : 'bg-yellow-50 text-gray-900 border border-yellow-200'
                }`}>
                  <div className="text-center">
                    <h3 className="text-lg font-bold mb-2">
                      {t('test.gradingResults.saveToCollection') || 'Save to Collection Book'}
                    </h3>
                    <p className="mb-3 opacity-90 text-sm">
                      {t('test.gradingResults.saveToCollectionDescription', { count: results.wrong_details.length }) || `Save all ${results.wrong_details.length} incorrect questions to your collection for later review and practice.`}
                    </p>
                    <button
                      onClick={handleAddToCollection}
                      disabled={addToCollectionMutation.isPending}
                      className={`font-bold px-4 py-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-sm text-sm ${
                        resolvedTheme === 'dark'
                          ? 'bg-gray-800 text-yellow-300 border border-yellow-600 hover:bg-gray-700'
                          : 'bg-white text-yellow-700 border border-yellow-300 hover:bg-gray-100'
                      }`}
                    >
                      {addToCollectionMutation.isPending ? (
                        <>
                          <span className="inline-block animate-spin mr-2">⏳</span>
                          {t('test.gradingResults.addingToCollection') || 'Adding to Collection...'}
                        </>
                      ) : (
                        <>
                          {t('test.gradingResults.addToCollectionButton', { count: results.wrong_details.length })}
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Remove correct questions from collection book - always show if any correct questions */}
              {results.correct_count > 0 && onRemoveCorrectQuestions && (
                <div className={`p-4 rounded-lg mb-4 ${
                  resolvedTheme === 'dark'
                    ? 'bg-gray-800 border border-gray-600'
                    : 'bg-gray-50 border border-gray-200'
                }`}>
                  <div className="text-center">
                    <button
                      onClick={onRemoveCorrectQuestions}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-semibold shadow-sm border border-red-700 hover:shadow-md text-sm"
                    >
                      {t('test.gradingResults.removeCorrectFromCollection') || 'Remove all correct answers from collection'}
                    </button>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3 justify-center">
                <button
                  onClick={onClose}
                  className={`px-6 py-2 rounded-lg transition-colors font-semibold shadow-sm hover:shadow-md ${
                    resolvedTheme === 'dark'
                      ? 'bg-blue-700 text-white border border-blue-600 hover:bg-blue-600'
                      : 'bg-blue-600 text-white border border-blue-700 hover:bg-blue-700'
                  }`}
                >
                  {t('test.gradingResults.closeButton') || 'Continue'}
                </button>
              </div>
            </>
          ) : (
            <>
              {/* Collection Success Screen */}
              <div className="text-center">
                <h2 className={`text-xl font-bold mb-3 ${
                  resolvedTheme === 'dark' ? 'text-green-400' : 'text-green-800'
                }`}>
                  {t('test.gradingResults.addedToCollection') || 'Added to Collection!'}
                </h2>
                
                <div className="bg-green-50 p-4 rounded-lg mb-4 border border-green-200">
                  {collectionResult?.added_count === 0 && collectionResult?.already_bookmarked > 0 ? (
                    <p className="text-sm text-green-800 mb-2">
                      {t('test.gradingResults.allAlreadyInCollection') || 'All questions were already in your collection.'}
                    </p>
                  ) : null}
                  {collectionResult && typeof collectionResult.added_count === 'number' && collectionResult.added_count > 0 && (
                    <p className="text-sm text-green-800 mb-2">
                      <strong>{collectionResult.added_count}</strong> {t('test.gradingResults.questionsAdded', { count: collectionResult.added_count })}
                    </p>
                  )}
                  {/* Only show the duplicate message if already_bookmarked > 0 and added_count > 0 */}
                  {collectionResult && typeof collectionResult.already_bookmarked === 'number' && collectionResult.already_bookmarked > 0 && collectionResult.added_count > 0 && (
                    <p className="text-yellow-700 text-xs">
                      {t('test.gradingResults.questionsAlreadySaved', { count: collectionResult.already_bookmarked })}
                    </p>
                  )}
                </div>

                {collectionResult?.errors && collectionResult.errors.length > 0 && (
                  <div className={`p-3 rounded-lg mb-4 ${
                    resolvedTheme === 'dark'
                      ? 'bg-red-900 border border-red-700'
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    <p className={`font-medium mb-2 text-sm ${
                      resolvedTheme === 'dark' ? 'text-red-300' : 'text-red-800'
                    }`}>{t('test.gradingResults.someIssues') || 'Some issues occurred:'}</p>
                    <ul className={`text-xs text-left ${
                      resolvedTheme === 'dark' ? 'text-red-200' : 'text-red-700'
                    }`}>
                      {collectionResult.errors.map((error, index) => (
                        <li key={index} className="mb-1">• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}

                <div className="flex gap-3 justify-center">
                  <button
                    onClick={handleViewCollection}
                    className={`px-4 py-2 rounded-lg transition-colors font-semibold shadow-sm hover:shadow-md text-sm ${
                      resolvedTheme === 'dark'
                        ? 'bg-green-700 text-white border border-green-600 hover:bg-green-600'
                        : 'bg-green-600 text-white border border-green-700 hover:bg-green-700'
                    }`}
                  >
                    {t('test.gradingResults.viewCollection') || 'View Collection'}
                  </button>
                  <button
                    onClick={handleClose}
                    className={`px-4 py-2 rounded-lg transition-colors font-semibold shadow-sm hover:shadow-md text-sm ${
                      resolvedTheme === 'dark'
                        ? 'bg-gray-700 text-gray-300 border border-gray-600 hover:bg-gray-600'
                        : 'bg-gray-300 text-gray-700 border border-gray-400 hover:bg-gray-400'
                    }`}
                  >
                    {t('test.gradingResults.closeButton') || 'Continue'}
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
} 