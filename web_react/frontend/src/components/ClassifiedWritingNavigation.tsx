import React, { useState, useEffect } from 'react';
import {
  Box,
  Stack,
  Text,
  ScrollArea,
  ActionIcon,
  Drawer,
  Group,
  Badge,
  Tooltip,
  Loader,
  UnstyledButton,
  Collapse
} from '@mantine/core';
import {
  IconBookmarks,
  IconChevronLeft,
  IconChevronRight,
  IconChevronDown,
  IconChevronUp,
  IconPencil,
  IconFolder,
  IconFolderOpen,
  IconFileText
} from '@tabler/icons-react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import { useThemeColors } from '../store/useThemeStore';
import { classifiedWritingService } from '../services/classifiedWritingService';
import { useTranslation } from 'react-i18next';
import { classifiedWritingTranslationService, type TacheTranslations } from '../services/classifiedWritingTranslationService';



interface ClassifiedWritingNavigationProps {
  variant?: 'sidebar' | 'compact' | 'mobile';
}

export function ClassifiedWritingNavigation({ variant = 'sidebar' }: ClassifiedWritingNavigationProps = {}) {
  const { subtopicId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  // Extract tâche number from path
  const getTacheNumberFromPath = () => {
    const path = location.pathname;
    const match = path.match(/classified_tache(\d+)/);
    return match ? match[1] : '1';
  };

  const tacheNumber = getTacheNumberFromPath();

  const [currentTacheData, setCurrentTacheData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [drawerOpened, { open: openDrawer, close: closeDrawer }] = useDisclosure(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true); // Default collapsed like WritingNavigation
  const [expandedTopics, setExpandedTopics] = useState<Set<string>>(new Set());
  const [translations, setTranslations] = useState<TacheTranslations | null>(null);
  const [loadingTranslations, setLoadingTranslations] = useState(false);
  const [compactNavigationHeight, setCompactNavigationHeight] = useState<number>(300);

  // Responsive breakpoints - match WritingNavigation exactly
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');

  // Determine effective variant based on screen size
  const effectiveVariant = isMobile ? 'mobile' : isTablet ? 'compact' : variant;

  useEffect(() => {
    loadNavigationData();
    loadTranslations();
  }, [tacheNumber]);

  // Auto-expand topics when data loads for better UX (only if 2 or fewer topics)
  useEffect(() => {
    if (currentTacheData?.topics) {
      const topics = Object.keys(currentTacheData.topics);
      // Auto-expand only if there are very few topics to avoid overwhelming UI
      if (topics.length <= 2) {
        setExpandedTopics(new Set(topics));
      }
    }
  }, [currentTacheData]);

  // Simplified height calculation - no longer needed for desktop but kept for compact mode
  const calculateCompactNavigationHeight = () => {
    if (loading || !currentTacheData) return 300;
    
    // For compact mode, still use dynamic height but with better constraints
    const baseHeight = 300;
    const expandedHeight = Math.min(500, window.innerHeight * 0.7); // Max 70% of viewport
    
    return expandedTopics.size > 0 ? expandedHeight : baseHeight;
  };

  // Update compact height when expanded topics change
  useEffect(() => {
    if (currentTacheData && effectiveVariant === 'compact') {
      const compactHeight = calculateCompactNavigationHeight();
      setCompactNavigationHeight(compactHeight);
    }
  }, [expandedTopics, currentTacheData, loading, effectiveVariant]);


  const loadTranslations = async () => {
    if (!tacheNumber) return;

    try {
      setLoadingTranslations(true);
      const tacheTranslations = await classifiedWritingTranslationService.getTacheTranslations(parseInt(tacheNumber));
      setTranslations(tacheTranslations);
    } catch (error) {
      console.error('Error loading translations:', error);
      setTranslations(null);
    } finally {
      setLoadingTranslations(false);
    }
  };

  const loadNavigationData = async () => {
    if (!tacheNumber) return;

    try {
      setLoading(true);
      const currentTacheNum = parseInt(tacheNumber);

      const response = await classifiedWritingService.getTacheOverview(currentTacheNum);

      // Convert topics object to array format expected by navigation
      const topicsArray = Object.entries(response.topics || {}).map(([topicName, topicData]: [string, any]) => ({
        topic_name: topicName,
        total_tasks: topicData.total_tasks || 0,
        unique_tasks: topicData.unique_tasks || 0,
        translations: topicData.translations || {},
        subtopics: Object.entries(topicData.subtopics || {}).map(([subtopicName, subtopicData]: [string, any]) => ({
          id: subtopicName,
          subtopic_name: subtopicName,
          task_count: subtopicData.task_count || 0,
          unique_task_count: subtopicData.unique_task_count || 0,
          translations: subtopicData.translations || {}
        }))
      }));

      setCurrentTacheData({
        tache_number: currentTacheNum,
        metadata: response.metadata || { total_tasks: 0 },
        topics: topicsArray
      });
    } catch (err) {
      console.error(`Error loading tâche ${tacheNumber} navigation data:`, err);
      setCurrentTacheData(null);
    } finally {
      setLoading(false);
    }
  };

  const toggleTopicExpansion = (topicKey: string) => {
    setExpandedTopics(prevExpanded => {
      const newExpanded = new Set(prevExpanded);
      if (newExpanded.has(topicKey)) {
        newExpanded.delete(topicKey);
      } else {
        newExpanded.add(topicKey);
      }

      return newExpanded;
    });
  };

  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  // Handle keyboard navigation
  const handleKeyDown = (event: React.KeyboardEvent, item: any) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      if (item.type === 'topic') {
        toggleTopicExpansion(item.topic_name);
      } else if (item.type === 'subtopic') {
        navigate(`/writing/classified_tache${tacheNumber}/${item.topicName}/${item.id}`);
      }
    }
  };

  // Render mobile navigation (drawer)
  if (effectiveVariant === 'mobile') {
    return (
      <>
        {/* Mobile floating button */}
        <Box
          style={{
            position: 'fixed',
            top: '80px',
            left: '16px',
            zIndex: 100
          }}
        >
          <Tooltip label={t('classifiedWriting.navigation.openMenu', 'Ouvrir le menu')} position="left">
            <ActionIcon
              size="lg"
              variant="subtle"
              color="orange"
              onClick={openDrawer}
              aria-label={t('classifiedWriting.navigation.openMenu', 'Ouvrir le menu')}
              style={{ borderRadius: '50%' }}
            >
              <IconBookmarks size={20} />
            </ActionIcon>
          </Tooltip>
        </Box>

        {/* Mobile Drawer */}
        <Drawer
          opened={drawerOpened}
          onClose={closeDrawer}
          title={
            <Group gap="sm">
              <IconBookmarks size={20} color={themeColors.writing} />
              <Text fw={600} c={themeColors.writing}>
                {t('classifiedWriting.navigation.title', 'Navigation')}
              </Text>
            </Group>
          }
          position="right"
          size="sm"
          styles={{
            header: {
              backgroundColor: themeColors.surface,
              borderBottom: `1px solid ${themeColors.border}`
            },
            body: {
              backgroundColor: themeColors.background,
              padding: 0
            }
          }}
        >
          <MobileNavigationContent
            currentTacheData={currentTacheData}
            loading={loading}
            currentSubtopicId={subtopicId}
            onNavigate={(path: string) => {
              navigate(path);
              closeDrawer();
            }}
            themeColors={themeColors}
            onKeyDown={handleKeyDown}
            expandedTopics={expandedTopics}
            onToggleTopicExpansion={toggleTopicExpansion}
            translations={translations}
            tacheNumber={tacheNumber}
          />
        </Drawer>
      </>
    );
  }

  // Render compact navigation (for tablets)
  if (effectiveVariant === 'compact') {

    return (
      <Box
        style={{
          position: 'fixed',
          top: '50%',
          left: '16px',
          transform: 'translateY(-50%)',
          zIndex: 100,
          background: themeColors.surface,
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          border: `1px solid ${themeColors.border}`,
          padding: '8px',
          height: 'auto',
          minHeight: '300px',
          maxHeight: '75vh',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Collapse/Expand Button */}
        <Tooltip
          label={sidebarCollapsed ? t('classifiedWriting.navigation.expand', 'Développer') : t('classifiedWriting.navigation.collapse', 'Réduire')}
          position="right"
        >
          <ActionIcon
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            variant="filled"
            color="orange"
            size="md"
            radius="md"
            style={{
              position: 'absolute',
              top: '50%',
              right: '-20px',
              transform: 'translateY(-50%)',
              zIndex: 102,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              border: `2px solid ${themeColors.surface}`,
              transition: 'all 0.2s ease'
            }}
            aria-label={sidebarCollapsed ? t('classifiedWriting.navigation.expand', 'Développer') : t('classifiedWriting.navigation.collapse', 'Réduire')}
          >
            {sidebarCollapsed ? <IconChevronRight size={16} /> : <IconChevronLeft size={16} />}
          </ActionIcon>
        </Tooltip>

        <CompactNavigationContent
          currentTacheData={currentTacheData}
          loading={loading}
          currentSubtopicId={subtopicId}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          onNavigate={(path: string) => navigate(path)}
          themeColors={themeColors}
          onKeyDown={handleKeyDown}
          expandedTopics={expandedTopics}
          onToggleTopicExpansion={toggleTopicExpansion}
          translations={translations}
          tacheNumber={tacheNumber}
        />
      </Box>
    );
  }


  // Render full sidebar navigation (for desktop) - vertically centered
  return (
    <Box
      style={{
        position: 'fixed',
        top: '50%',
        left: sidebarCollapsed ? '-240px' : '16px',
        transform: 'translateY(-50%)', // Center vertically
        zIndex: 100,
        background: themeColors.surface,
        borderRadius: '16px',
        boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
        border: `1px solid ${themeColors.border}`,
        width: sidebarCollapsed ? '280px' : '320px',
        height: 'auto',
        maxHeight: '85vh', // Ensure it never exceeds viewport height
        minHeight: '400px', // Minimum height for usability
        overflow: 'hidden',
        transition: 'left 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Modern Collapse/Expand Button */}
      <Tooltip
        label={sidebarCollapsed ? t('classifiedWriting.navigation.expand', 'Développer') : t('classifiedWriting.navigation.collapse', 'Réduire')}
        position="right"
      >
        <ActionIcon
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          variant="filled"
          color="orange"
          size="lg"
          radius="md"
          style={{
            position: 'absolute',
            top: '50%',
            right: '-22px',
            transform: 'translateY(-50%)',
            zIndex: 102,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            border: `2px solid ${themeColors.surface}`,
            transition: 'all 0.2s ease'
          }}
          aria-label={sidebarCollapsed ? t('classifiedWriting.navigation.expand', 'Développer') : t('classifiedWriting.navigation.collapse', 'Réduire')}
        >
          {sidebarCollapsed ? <IconChevronRight size={18} /> : <IconChevronLeft size={18} />}
        </ActionIcon>
      </Tooltip>

      <SidebarNavigationContent
        currentTacheData={currentTacheData}
        loading={loading}
        currentSubtopicId={subtopicId}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        onNavigate={(path: string) => navigate(path)}
        themeColors={themeColors}
        onKeyDown={handleKeyDown}
        expandedTopics={expandedTopics}
        onToggleTopicExpansion={toggleTopicExpansion}
        translations={translations}
        tacheNumber={tacheNumber}
      />
    </Box>
  );
}

// Sidebar Navigation Content Component (for desktop)
function SidebarNavigationContent({
  currentTacheData,
  loading,
  currentSubtopicId,
  collapsed,
  onToggleCollapse,
  onNavigate,
  themeColors,
  onKeyDown,
  expandedTopics,
  onToggleTopicExpansion,
  translations,
  tacheNumber
}: any) {
  const { t } = useTranslation();

  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  return (
    <Stack gap={0}>
      {/* Header */}
      <Box
        p="md"
        style={{
          borderBottom: `1px solid ${themeColors.border}`,
          background: `linear-gradient(135deg, ${themeColors.writing}08 0%, ${themeColors.writing}03 100%)`
        }}
      >
        <Group justify="center" align="center">
          {!collapsed && (
            <Group gap="sm">
              <IconBookmarks size={20} color={themeColors.writing} />
              <Text fw={600} c={themeColors.writing}>
                {t('classifiedWriting.navigation.title', 'Navigation')}
              </Text>
            </Group>
          )}
        </Group>
      </Box>

      {/* Content */}
      <ScrollArea 
        style={{ 
          flex: 1,
          minHeight: 0, // Allow flex item to shrink
          height: '100%', // Take full available height
          overflowY: 'auto' // Ensure vertical scrolling
        }} 
        p={collapsed ? "xs" : "md"}
        scrollbarSize={10}
        scrollHideDelay={0}
        type="always"
        offsetScrollbars={false}
        styles={{
          viewport: {
            paddingBottom: '16px', // Add bottom padding for last items
            paddingRight: '12px' // Add right padding for scrollbar
          },
          scrollbar: {
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '6px',
            width: '10px'
          },
          thumb: {
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '6px',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.4)'
            }
          }
        }}
      >
        <ExpandedSidebarContent
          currentTacheData={currentTacheData}
          loading={loading}
          currentSubtopicId={currentSubtopicId}
          onNavigate={onNavigate}
          themeColors={themeColors}
          onKeyDown={onKeyDown}
          expandedTopics={expandedTopics}
          onToggleTopicExpansion={onToggleTopicExpansion}
          translations={translations}
          tacheNumber={tacheNumber}
        />
      </ScrollArea>
    </Stack>
  );
}

// Expanded Sidebar Content Component
function ExpandedSidebarContent({
  currentTacheData,
  loading,
  currentSubtopicId,
  onNavigate,
  themeColors,
  onKeyDown,
  expandedTopics,
  onToggleTopicExpansion,
  translations,
  tacheNumber
}: any) {
  const { t, i18n } = useTranslation();

  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  if (loading) {
    return (
      <Group justify="center" p="xl">
        <Loader size="sm" color="orange" />
        <Text size="sm" c="dimmed">
          {t('classifiedWriting.loading.navigation', 'Chargement...')}
        </Text>
      </Group>
    );
  }

  if (!currentTacheData) {
    return (
      <Text size="sm" c="dimmed" ta="center" p="xl">
        {t('classifiedWriting.noData.message', 'Aucune donnée disponible')}
      </Text>
    );
  }

  return (
    <Stack gap="xs" style={{ 
      minHeight: '100%',
      justifyContent: 'flex-start'
    }}>
      {/* Tâche Header */}
      <Box
        style={{
          padding: '12px',
          backgroundColor: themeColors.background,
          borderRadius: '8px',
          border: `1px solid ${themeColors.border}`,
          marginBottom: '8px',
          position: 'sticky',
          top: 0,
          zIndex: 10
        }}
      >
        <Group gap="sm">
          <IconPencil size={18} color={getTacheColor(currentTacheData.tache_number)} />
          <Text fw={600} size="sm">
            Tâche {currentTacheData.tache_number}
          </Text>
          <Badge size="xs" color={getTacheColor(currentTacheData.tache_number)} variant="light">
            {currentTacheData.metadata?.total_tasks || 0}
          </Badge>
        </Group>
      </Box>

      {/* Topics List */}
      {currentTacheData.topics?.map((topic: any, topicIndex: number) => {
        const isExpanded = expandedTopics.has(topic.topic_name);

        return (
          <Box key={topic.topic_name}>
            {/* Topic Header */}
            <UnstyledButton
              onClick={() => onToggleTopicExpansion(topic.topic_name)}
              onKeyDown={(e) => onKeyDown(e, { type: 'topic', topic_name: topic.topic_name })}
              style={{
                width: '100%',
                padding: '10px 12px',
                borderRadius: '8px',
                backgroundColor: isExpanded ? themeColors.background : 'transparent',
                border: `1px solid ${isExpanded ? themeColors.border : 'transparent'}`,
                transition: 'all 0.2s ease',
                marginBottom: '2px'
              }}
              styles={{
                root: {
                  '&:hover': {
                    backgroundColor: themeColors.background,
                    border: `1px solid ${themeColors.border}`,
                    transform: 'translateX(2px)'
                  }
                }
              }}
            >
              <Group justify="space-between" wrap="nowrap">
                <Group gap="xs" wrap="nowrap" style={{ flex: 1, minWidth: 0 }}>
                  {isExpanded ? (
                    <IconFolderOpen size={14} color={themeColors.textSecondary} />
                  ) : (
                    <IconFolder size={14} color={themeColors.textSecondary} />
                  )}
                  <Text size="xs" fw={500} truncate style={{ flex: 1 }}>
                    {classifiedWritingTranslationService.getTopicTranslation(topic.topic_name, i18n.language, translations)}
                  </Text>
                  <Badge 
                    size="xs" 
                    variant="outline" 
                    color={isExpanded ? "blue" : "gray"}
                    style={{ 
                      transition: 'all 0.2s ease',
                      fontWeight: isExpanded ? 600 : 400
                    }}
                  >
                    {topic.subtopics?.length || 0}
                  </Badge>
                </Group>
                {topic.subtopics?.length > 0 && (
                  isExpanded ? (
                    <IconChevronUp size={12} color={themeColors.textSecondary} />
                  ) : (
                    <IconChevronDown size={12} color={themeColors.textSecondary} />
                  )
                )}
              </Group>
            </UnstyledButton>

            {/* Subtopics */}
            {topic.subtopics?.length > 0 && (
              <Collapse in={isExpanded}>
                <Stack gap="2px" pl="lg" mt="xs" mb="sm">
                  {topic.subtopics.map((subtopic: any) => {
                    const isActive = currentSubtopicId === subtopic.id;

                    return (
                      <UnstyledButton
                        key={subtopic.id}
                        onClick={() => onNavigate(`/writing/classified_tache${tacheNumber}/${topic.topic_name}/${encodeURIComponent(encodeURIComponent(subtopic.id))}`)}
                        onKeyDown={(e) => onKeyDown(e, { type: 'subtopic', id: subtopic.id, topicName: topic.topic_name })}
                        style={{
                          width: '100%',
                          padding: '8px 10px',
                          borderRadius: '6px',
                          backgroundColor: isActive ? themeColors.primaryColor + '20' : 'transparent',
                          border: `1px solid ${isActive ? themeColors.primaryColor + '40' : 'transparent'}`,
                          transition: 'all 0.2s ease',
                          marginBottom: '1px'
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: isActive ? themeColors.primaryColor + '30' : themeColors.background,
                              border: `1px solid ${isActive ? themeColors.primaryColor + '60' : themeColors.border}`
                            }
                          }
                        }}
                      >
                        <Group justify="space-between" wrap="nowrap">
                          <Group gap="xs" wrap="nowrap" style={{ flex: 1, minWidth: 0 }}>
                            <IconFileText size={12} color={isActive ? themeColors.primaryColor : themeColors.textSecondary} />
                            <Text
                              size="xs"
                              truncate
                              style={{ flex: 1 }}
                              c={isActive ? themeColors.primaryColor : themeColors.text}
                              fw={isActive ? 500 : 400}
                            >
                              {classifiedWritingTranslationService.getSubtopicTranslation(subtopic.subtopic_name.toLowerCase().replace(/\s+/g, '_'), i18n.language, translations)}
                            </Text>
                          </Group>
                          <Badge
                            size="xs"
                            variant="light"
                            color={isActive ? "blue" : "gray"}
                          >
                            {subtopic.task_count || 0}
                          </Badge>
                        </Group>
                      </UnstyledButton>
                    );
                  })}
                </Stack>
              </Collapse>
            )}
          </Box>
        );
      })}
    </Stack>
  );
}

// Collapsed Sidebar Content Component
function CollapsedSidebarContent({
  currentTacheData,
  loading,
  currentSubtopicId,
  onNavigate,
  themeColors
}: any) {
  const { t } = useTranslation();
  const { tacheNumber } = useParams();

  const getTacheColor = (tache: number) => {
    const colors = ['orange', 'teal', 'violet'];
    return colors[tache - 1] || 'blue';
  };

  if (loading || !currentTacheData) {
    return (
      <Box p="xs" ta="center">
        <Loader size="sm" color="orange" />
      </Box>
    );
  }

  return (
    <Stack gap="xs" p="xs" align="center">
      <Tooltip label={`Tâche ${currentTacheData.tache_number}`} position="right">
        <ActionIcon
          size="lg"
          variant="filled"
          color={getTacheColor(currentTacheData.tache_number)}
          onClick={() => onNavigate(`/writing/classified_tache${tacheNumber}`)}
        >
          <IconPencil size={18} />
        </ActionIcon>
      </Tooltip>

      {/* Show active subtopic indicator */}
      {currentSubtopicId && (
        <Box
          style={{
            width: '4px',
            height: '20px',
            backgroundColor: themeColors.primaryColor,
            borderRadius: '2px'
          }}
        />
      )}
    </Stack>
  );
}

// Compact Navigation Content Component (for tablets)
function CompactNavigationContent({
  currentTacheData,
  loading,
  currentSubtopicId,
  collapsed,
  onToggleCollapse,
  onNavigate,
  themeColors,
  onKeyDown,
  expandedTopics,
  onToggleTopicExpansion,
  translations,
  tacheNumber
}: any) {
  if (collapsed) {
    return (
      <CollapsedSidebarContent
        currentTacheData={currentTacheData}
        loading={loading}
        currentSubtopicId={currentSubtopicId}
        onNavigate={onNavigate}
        themeColors={themeColors}
      />
    );
  }

  return (
    <Box style={{ width: '240px', height: '100%', overflow: 'hidden' }}>
      <ScrollArea 
        style={{ 
          height: '100%',
          flex: 1,
          overflowY: 'auto'
        }} 
        p="sm"
        scrollbarSize={10}
        scrollHideDelay={0}
        type="always"
        offsetScrollbars={false}
        styles={{
          viewport: {
            paddingBottom: '16px',
            paddingRight: '12px'
          },
          scrollbar: {
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '6px',
            width: '10px'
          },
          thumb: {
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            borderRadius: '6px',
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.4)'
            }
          }
        }}
      >
        <ExpandedSidebarContent
          currentTacheData={currentTacheData}
          loading={loading}
          currentSubtopicId={currentSubtopicId}
          onNavigate={onNavigate}
          themeColors={themeColors}
          onKeyDown={onKeyDown}
          expandedTopics={expandedTopics}
          onToggleTopicExpansion={onToggleTopicExpansion}
          translations={translations}
          tacheNumber={tacheNumber}
        />
      </ScrollArea>
    </Box>
  );
}

// Mobile Navigation Content Component
function MobileNavigationContent({
  currentTacheData,
  loading,
  currentSubtopicId,
  onNavigate,
  themeColors,
  onKeyDown,
  expandedTopics,
  onToggleTopicExpansion,
  translations,
  tacheNumber
}: any) {
  return (
    <ScrollArea 
      style={{ 
        height: '100%',
        flex: 1,
        overflowY: 'auto'
      }} 
      p="md"
      scrollbarSize={12}
      scrollHideDelay={0}
      type="always"
      offsetScrollbars={false}
      styles={{
        viewport: {
          paddingBottom: '16px',
          paddingRight: '14px'
        },
        scrollbar: {
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
          borderRadius: '6px',
          width: '12px'
        },
        thumb: {
          backgroundColor: 'rgba(0, 0, 0, 0.3)',
          borderRadius: '6px',
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.4)'
          }
        }
      }}
    >
      <ExpandedSidebarContent
        currentTacheData={currentTacheData}
        loading={loading}
        currentSubtopicId={currentSubtopicId}
        onNavigate={onNavigate}
        themeColors={themeColors}
        onKeyDown={onKeyDown}
        expandedTopics={expandedTopics}
        onToggleTopicExpansion={onToggleTopicExpansion}
        translations={translations}
        tacheNumber={tacheNumber}
      />
    </ScrollArea>
  );
}


