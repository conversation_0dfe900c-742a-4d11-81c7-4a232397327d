import React, { useState, useEffect } from 'react';
import { ActionIcon, Tooltip } from '@mantine/core';
import { IconStar, IconStarFilled } from '@tabler/icons-react';
import { testApi } from '../services/api';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';

interface BookmarkIconProps {
  questionId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onBookmarkChange?: (isBookmarked: boolean) => void;
}

export const BookmarkIcon: React.FC<BookmarkIconProps> = ({ 
  questionId, 
  className = '',
  size = 'md',
  onBookmarkChange
}) => {
  const { t } = useTranslation();
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [loading, setLoading] = useState(false);

  // Check initial bookmark status
  useEffect(() => {
    const checkStatus = async () => {
      try {
        const response = await testApi.checkBookmarkStatus(questionId);
        setIsBookmarked(response.bookmarked);
      } catch (error) {
        // If user not authenticated, assume not bookmarked
        setIsBookmarked(false);
      }
    };

    if (questionId) {
      checkStatus();
    }
  }, [questionId]);

  // Listen for refresh events from bulk bookmark operations
  useEffect(() => {
    const handleRefreshBookmarks = () => {
      const checkStatus = async () => {
        try {
          const response = await testApi.checkBookmarkStatus(questionId);
          setIsBookmarked(response.bookmarked);
        } catch (error) {
          // If user not authenticated, assume not bookmarked
          setIsBookmarked(false);
        }
      };

      if (questionId) {
        checkStatus();
      }
    };

    window.addEventListener('refreshBookmarks', handleRefreshBookmarks);
    return () => window.removeEventListener('refreshBookmarks', handleRefreshBookmarks);
  }, [questionId]);

  const handleBookmarkToggle = async () => {
    setLoading(true);
    
    try {
      if (isBookmarked) {
        // Remove bookmark
        await testApi.removeBookmark(questionId);
        setIsBookmarked(false);
        onBookmarkChange?.(false);
        notifications.show({
          title: t('common.bookmarks.removed.title'),
          message: t('common.bookmarks.removed.message'),
          color: 'blue',
        });
      } else {
        // Add bookmark
        await testApi.addBookmark(questionId);
        setIsBookmarked(true);
        onBookmarkChange?.(true);
        notifications.show({
          title: t('common.bookmarks.added.title'),
          message: t('common.bookmarks.added.message'),
          color: 'green',
        });
      }
      
      // Trigger refresh event for all bookmark icons to update their state
      // This ensures consistency between individual and bulk bookmark operations
      window.dispatchEvent(new CustomEvent('refreshBookmarks'));
      
    } catch (error: any) {
      console.error('Error toggling bookmark:', error);
      
      // Handle specific error cases
      if (error.response?.status === 401) {
        notifications.show({
          title: t('common.bookmarks.errors.loginRequired.title'),
          message: t('common.bookmarks.errors.loginRequired.message'),
          color: 'orange',
        });
      } else if (error.response?.status === 409) {
        notifications.show({
          title: t('common.bookmarks.errors.alreadySaved.title'),
          message: t('common.bookmarks.errors.alreadySaved.message'),
          color: 'yellow',
        });
      } else {
        notifications.show({
          title: t('common.bookmarks.errors.saveFailed.title'),
          message: t('common.bookmarks.errors.saveFailed.message'),
          color: 'red',
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const iconSize = size === 'sm' ? 16 : size === 'lg' ? 24 : 20;

  return (
    <Tooltip 
      label={isBookmarked ? t('common.bookmarks.remove') : t('common.bookmarks.add')}
      position="bottom"
    >
      <ActionIcon
        variant="subtle"
        color={isBookmarked ? 'yellow' : 'gray'}
        onClick={handleBookmarkToggle}
        loading={loading}
        className={`${className} ${isBookmarked ? 'bookmark-highlighted' : ''}`}
        size={size}
        style={{ 
          position: 'absolute', 
          top: '8px', 
          right: '8px',
          zIndex: 10,
          // Add visual highlighting for bookmarked items
          backgroundColor: isBookmarked ? 'rgba(255, 193, 7, 0.1)' : 'transparent',
          border: isBookmarked ? '2px solid rgba(255, 193, 7, 0.3)' : 'none',
          borderRadius: '50%',
          boxShadow: isBookmarked ? '0 0 8px rgba(255, 193, 7, 0.2)' : 'none',
          transition: 'all 0.2s ease'
        }}
      >
        {isBookmarked ? (
          <IconStarFilled size={iconSize} />
        ) : (
          <IconStar size={iconSize} />
        )}
      </ActionIcon>
    </Tooltip>
  );
}; 