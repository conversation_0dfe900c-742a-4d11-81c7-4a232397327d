import { Paper } from '@mantine/core';
import { useThemeColors } from '../store/useThemeStore';
import React from 'react';

interface ThemeAwarePaperProps {
  children?: React.ReactNode;
  style?: React.CSSProperties;
  [key: string]: any; // Allow any other Paper props
}

export function ThemeAwarePaper({
  children,
  style,
  ...props
}: ThemeAwarePaperProps) {
  const themeColors = useThemeColors();

  const paperStyle: React.CSSProperties = {
    backgroundColor: themeColors.surface,
    borderColor: themeColors.border,
    color: themeColors.textPrimary,
    transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    ...style,
  };

  return (
    <Paper
      {...props}
      style={paperStyle}
      withBorder
    >
      {children}
    </Paper>
  );
}
