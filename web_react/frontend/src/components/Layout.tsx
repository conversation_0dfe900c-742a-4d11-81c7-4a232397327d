import {
  App<PERSON><PERSON>,
  Container,
  Group,
  Button,
  Text,
  Menu,
  Burger,
  Box,
  Stack,
  ActionIcon,
  Tooltip,
  Transition,
  Collapse
} from '@mantine/core';
import { useDisclosure, useWindowScroll } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import {
  IconUser,
  IconLogout,
  IconChevronDown,
  IconHome,
  IconLogin,
  IconUserPlus,
  IconCrown,
  IconSchool,
  IconHeadphones,
  IconStar,
  IconWorld,
  IconSun,
  IconMoon,
  IconNotebook,
  IconList,
  IconTools,
  IconDownload,
  IconEar,
  IconBook,
  IconPencil,
  IconMicrophone,
  IconChevronUp
} from '@tabler/icons-react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/useAuthStore';
import { authApi } from '../services/api';
import { useLanguage } from '../hooks/useLanguage';
import { useContactSupport } from '../hooks/useContactSupport';
import { ContactSupportModal } from './ContactSupportModal';
import { useTranslation } from 'react-i18next';
import { useThemeStore, useThemeColors } from '../store/useThemeStore';
import { useTestPageDetection } from '../hooks/useTestPageDetection';
import { useAutoHideNavigation } from '../hooks/useAutoHideNavigation';
import { NavigationHint } from './NavigationHint';

export function Layout({ children }: { children: React.ReactNode }) {
  // Always call hooks in the same order - never conditionally
  const { isAuthenticated, logout, isMember, isLifetimeMember } = useAuthStore();
  const navigate = useNavigate();
  const [mobileNavOpened, { toggle: toggleMobileNav, close: closeMobileNav }] = useDisclosure();
  const [mobileSectionsOpened, { toggle: toggleMobileSections, close: closeMobileSections }] = useDisclosure(false);
  const [mobileToolsOpened, { toggle: toggleMobileTools, close: closeMobileTools }] = useDisclosure(false);

  // Function to handle mobile nav toggle and reset collapsible sections
  const handleMobileNavToggle = () => {
    if (mobileNavOpened) {
      // If closing nav, also close sections
      closeMobileSections();
      closeMobileTools();
    }
    toggleMobileNav();
  };
  const [scroll] = useWindowScroll();
  const { lang, setLang } = useLanguage();
  const { t } = useTranslation();
  const { resolvedTheme, toggleTheme } = useThemeStore();
  const themeColors = useThemeColors();

  // Auto-hide navigation for test pages - always call these hooks
  const { isTestPage, testPageType } = useTestPageDetection();
  const {
    isVisible: navVisible,
    handleNavigationMouseEnter,
    handleNavigationMouseLeave
  } = useAutoHideNavigation({ isTestPage });

  // Contact support hook - always call this
  const {
    contactOpened,
    openContact,
    closeContact,
    contactForm,
    isSubmittingSupport,
    handleContactSupport
  } = useContactSupport();

  const handleLogout = async () => {
    try {
      await authApi.logout();
      logout();
      notifications.show({
        title: t('auth.logout.success'),
        message: t('auth.logout.success'),
        color: 'blue',
      });
      navigate('/');
    } catch (error) {
      notifications.show({
        title: t('contact.error.title'),
        message: t('auth.logout.error'),
        color: 'red',
      });
    }
  };

  // Determine membership button display
  const getMembershipButtonProps = () => {
    if (!isAuthenticated) {
      return {
        show: true,
        text: t('layout.membership.becomeMember'),
        color: 'orange'
      };
    }
    
    if (!isMember()) {
      // Free user
      return {
        show: true,
        text: t('layout.membership.becomeMember'),
        color: 'orange'
      };
    }
    
    if (isMember() && !isLifetimeMember()) {
      // Time-limited premium user
      return {
        show: true,
        text: t('layout.membership.extendMembership'),
        color: 'blue'
      };
    }
    
    // Lifetime member - don't show membership button
    return {
      show: false,
      text: '',
      color: 'blue'
    };
  };

  const membershipButton = getMembershipButtonProps();

  return (
    <AppShell
      header={{ height: 72 }}
      navbar={{ width: 0, breakpoint: 0 }}
      padding={0}
      style={{
        background: themeColors.background,
        transition: 'background-color 0.3s ease',
      }}
    >
      <AppShell.Header
        onMouseEnter={handleNavigationMouseEnter}
        onMouseLeave={handleNavigationMouseLeave}
        style={{
          background: scroll.y > 20
            ? (resolvedTheme === 'dark' ? 'rgba(26, 27, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)')
            : themeColors.surface,
          borderBottom: `1px solid ${themeColors.border}`,
          boxShadow: scroll.y > 20
            ? (resolvedTheme === 'dark'
                ? '0 8px 32px rgba(0,0,0,0.6)'
                : '0 4px 20px rgba(0,0,0,0.15)')
            : (resolvedTheme === 'dark'
                ? '0 4px 20px rgba(0,0,0,0.4)'
                : '0 2px 12px rgba(0,0,0,0.08)'),
          transition: isTestPage
            ? 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1), transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            : 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1), backdrop-filter 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          backdropFilter: scroll.y > 20 ? 'blur(12px)' : 'blur(8px)',
          position: 'sticky',
          top: 0,
          zIndex: 100,
          transform: isTestPage && !navVisible ? 'translateY(-100%)' : 'translateY(0)',
        }}
      >
        <Container size="xl" h={72} style={{ display: 'flex', alignItems: 'center' }}>
          <Group justify="space-between" w="100%" wrap="nowrap">
            {/* Enhanced Brand */}
            <Link
              to="/"
              style={{
                textDecoration: 'none',
                color: 'inherit',
                borderRadius: '12px',
                transition: 'all 0.2s ease',
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'scale(1.02)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'scale(1)';
              }}
              aria-label="TCF Canada - Go to homepage"
            >
              <Group gap="sm" wrap="nowrap">
                <Box
                  style={{
                    borderRadius: '10px',
                    padding: '4px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    boxShadow: resolvedTheme === 'dark'
                      ? '0 4px 12px rgba(0,0,0,0.4)'
                      : '0 2px 8px rgba(0,0,0,0.15)',
                    transition: 'all 0.2s ease',
                  }}
                >
                  <img
                    src="/android-chrome-192x192.png?v=2025"
                    alt="Chez-TCFCA Logo"
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '6px'
                    }}
                  />
                </Box>
                <Box style={{ textAlign: 'center', position: 'relative' }}>
                  <Text
                    fw={700}
                    size="xl"
                    c={themeColors.primary}
                    lh={1}
                    style={{
                      fontFamily: 'system-ui, -apple-system, "Segoe UI", sans-serif',
                      letterSpacing: '-0.02em',
                      transform: 'translateX(-2.5px)',
                      display: 'block',
                    }}
                  >
                    Chez
                  </Text>
                  <Text
                    fw={600}
                    size="sm"
                    c={themeColors.primary}
                    lh={1}
                    mt={-2}
                    style={{
                      fontFamily: 'system-ui, -apple-system, "Segoe UI", sans-serif',
                      letterSpacing: '0.15em',
                      textTransform: 'uppercase',
                      opacity: 0.9,
                      display: 'block',
                    }}
                  >
                    TCFCA
                  </Text>
                </Box>
              </Group>
            </Link>

            {/* Enhanced Mobile burger */}
            <Box hiddenFrom="sm">
              <Burger
                opened={mobileNavOpened}
                onClick={handleMobileNavToggle}
                size="sm"
                color={themeColors.textPrimary}
                aria-label={mobileNavOpened ? "Close navigation menu" : "Open navigation menu"}
                style={{
                  borderRadius: '10px',
                  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                  transform: mobileNavOpened ? 'rotate(90deg)' : 'rotate(0deg)',
                  backgroundColor: mobileNavOpened
                    ? (resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)')
                    : 'transparent',
                  padding: '10px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      transform: mobileNavOpened ? 'rotate(90deg) scale(1.05)' : 'rotate(0deg) scale(1.05)',
                    }
                  }
                }}
              />
            </Box>

            {/* Enhanced Desktop Navigation */}
            <Group gap="md" visibleFrom="sm" wrap="nowrap">
              <Button
                component={Link}
                to="/"
                variant="subtle"
                size="sm"
                leftSection={<IconHome size={16} style={{ color: themeColors.textPrimary }} />}
                style={{
                  color: themeColors.textPrimary,
                  fontWeight: 500,
                  borderRadius: '8px',
                  transition: 'all 0.2s ease',
                }}
                styles={{
                  root: {
                    '&:hover': {
                      backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                      transform: 'translateY(-1px)',
                    }
                  }
                }}
              >
                {t('layout.home')}
              </Button>

              {isAuthenticated ? (
                <>
                  {/* Profile Button */}
                  <Button
                    component={Link}
                    to="/profile"
                    variant="subtle"
                    size="sm"
                    leftSection={<IconUser size={16} style={{ color: themeColors.textPrimary }} />}
                    style={{
                      color: themeColors.textPrimary,
                      fontWeight: 500,
                      borderRadius: '8px',
                      transition: 'all 0.2s ease',
                    }}
                    styles={{
                      root: {
                        '&:hover': {
                          backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          transform: 'translateY(-1px)',
                        }
                      }
                    }}
                  >
                    {t('layout.profile')}
                  </Button>
                </>
              ) : null}

              {/* Sections Dropdown - Available for all users */}
              <Menu shadow="lg" width={220} position="bottom-start" offset={8}>
                <Menu.Target>
                  <Button
                    variant="subtle"
                    size="sm"
                    leftSection={<IconList size={16} style={{ color: themeColors.textPrimary }} />}
                    rightSection={<IconChevronDown size={14} style={{ color: themeColors.textPrimary }} />}
                    style={{
                      color: themeColors.textPrimary,
                      fontWeight: 500,
                      borderRadius: '8px',
                      transition: 'all 0.2s ease',
                    }}
                    styles={{
                      root: {
                        '&:hover': {
                          backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          transform: 'translateY(-1px)',
                        }
                      }
                    }}
                  >
                    {t('layout.quickNavigation.title')}
                  </Button>
                </Menu.Target>
                <Menu.Dropdown style={{ borderRadius: '12px' }}>
                  <Menu.Item
                    component={Link}
                    to="/listening"
                    leftSection={<IconEar size={16} style={{ color: themeColors.textPrimary }} />}
                    style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                  >
                    {t('layout.quickNavigation.listening')}
                  </Menu.Item>
                  <Menu.Item
                    component={Link}
                    to="/reading"
                    leftSection={<IconBook size={16} style={{ color: themeColors.textPrimary }} />}
                    style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                  >
                    {t('layout.quickNavigation.reading')}
                  </Menu.Item>
                  <Menu.Item
                    component={Link}
                    to="/writing"
                    leftSection={<IconPencil size={16} style={{ color: themeColors.textPrimary }} />}
                    style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                  >
                    {t('layout.quickNavigation.writing')}
                  </Menu.Item>
                  <Menu.Item
                    component={Link}
                    to="/speaking"
                    leftSection={<IconMicrophone size={16} style={{ color: themeColors.textPrimary }} />}
                    style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                  >
                    {t('layout.quickNavigation.speaking')}
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>

              {isAuthenticated ? (
                <>

                  {/* Tools Dropdown */}
                  <Menu shadow="lg" width={220} position="bottom-start" offset={8}>
                    <Menu.Target>
                      <Button
                        variant="subtle"
                        size="sm"
                        leftSection={<IconTools size={16} style={{ color: themeColors.textPrimary }} />}
                        rightSection={<IconChevronDown size={14} style={{ color: themeColors.textPrimary }} />}
                        style={{
                          color: themeColors.textPrimary,
                          fontWeight: 500,
                          borderRadius: '8px',
                          transition: 'all 0.2s ease',
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              transform: 'translateY(-1px)',
                            }
                          }
                        }}
                      >
                        {t('layout.tools.title')}
                      </Button>
                    </Menu.Target>
                    <Menu.Dropdown style={{ borderRadius: '12px' }}>
                      <Menu.Item
                        component={Link}
                        to="/collection"
                        leftSection={<IconStar size={16} style={{ color: themeColors.textPrimary }} />}
                        style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                      >
                        {t('layout.collection')}
                      </Menu.Item>
                      <Menu.Item
                        component={Link}
                        to="/notebook"
                        leftSection={<IconNotebook size={16} style={{ color: themeColors.textPrimary }} />}
                        style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                      >
                        {t('layout.notebook', 'Notebook')}
                      </Menu.Item>
                      <Menu.Item
                        component={Link}
                        to="/downloads"
                        leftSection={<IconDownload size={16} style={{ color: themeColors.textPrimary }} />}
                        style={{ borderRadius: '8px', color: themeColors.textPrimary }}
                      >
                        {t('layout.tools.downloadMaterials')}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>



                  {/* Divider for visual separation */}
                  <Box
                    style={{
                      width: '1px',
                      height: '24px',
                      backgroundColor: themeColors.border,
                      margin: '0 8px',
                    }}
                  />

                  {/* Action Buttons Group */}
                  <Group gap="xs" wrap="nowrap">
                    {membershipButton.show && (
                      <Button
                        component={Link}
                        to="/membership"
                        variant="gradient"
                        gradient={{ from: membershipButton.color, to: `${membershipButton.color}.7` }}
                        size="sm"
                        leftSection={<IconCrown size={16} />}
                        style={{
                          borderRadius: '8px',
                          fontWeight: 600,
                          boxShadow: resolvedTheme === 'dark'
                            ? '0 2px 8px rgba(0,0,0,0.3)'
                            : '0 2px 8px rgba(0,0,0,0.1)',
                        }}
                      >
                        {membershipButton.text}
                      </Button>
                    )}

                    <Button
                      variant="light"
                      size="sm"
                      color="red"
                      onClick={handleLogout}
                      leftSection={<IconLogout size={16} />}
                      style={{
                        borderRadius: '8px',
                        fontWeight: 500,
                      }}
                    >
                      {t('layout.logout')}
                    </Button>
                  </Group>

                  {/* Settings Group */}
                  <Group gap="xs" wrap="nowrap">
                    <Tooltip
                      label={resolvedTheme === 'dark' ? t('theme.switchToLight', 'Switch to Light Mode') : t('theme.switchToDark', 'Switch to Dark Mode')}
                      position="bottom"
                      offset={12}
                    >
                      <ActionIcon
                        variant="subtle"
                        size="lg"
                        onClick={toggleTheme}
                        style={{
                          borderRadius: '10px',
                          transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                          backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          color: themeColors.textPrimary,
                          border: `1px solid ${themeColors.border}`,
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                              transform: 'scale(1.05)',
                            }
                          }
                        }}
                      >
                        {resolvedTheme === 'dark' ?
                          <IconSun size={18} style={{ color: themeColors.textPrimary }} /> :
                          <IconMoon size={18} style={{ color: themeColors.textPrimary }} />
                        }
                      </ActionIcon>
                    </Tooltip>

                    <Menu shadow="lg" width={180} position="bottom-end" offset={8}>
                      <Menu.Target>
                        <Button
                          variant="subtle"
                          size="sm"
                          leftSection={<IconWorld size={16} />}
                          rightSection={<IconChevronDown size={14} />}
                          style={{
                            borderRadius: '8px',
                            fontWeight: 500,
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                              }
                            }
                          }}
                        >
                          {lang === 'fr' ? 'FR' : lang === 'en' ? 'EN' : '中文'}
                        </Button>
                      </Menu.Target>
                      <Menu.Dropdown style={{ borderRadius: '12px' }}>
                        <Menu.Item
                          onClick={() => setLang('fr')}
                          leftSection="🇫🇷"
                          style={{ borderRadius: '8px' }}
                        >
                          Français
                        </Menu.Item>
                        <Menu.Item
                          onClick={() => setLang('en')}
                          leftSection="🇬🇧"
                          style={{ borderRadius: '8px' }}
                        >
                          English
                        </Menu.Item>
                        <Menu.Item
                          onClick={() => setLang('zh')}
                          leftSection="🇨🇳"
                          style={{ borderRadius: '8px' }}
                        >
                          中文
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>
                </>
              ) : (
                <>
                  {/* Auth Buttons Group */}
                  <Group gap="xs" wrap="nowrap">
                    <Button
                      variant="subtle"
                      size="sm"
                      component={Link}
                      to="/login"
                      leftSection={<IconLogin size={16} />}
                      style={{
                        borderRadius: '8px',
                        fontWeight: 500,
                      }}
                    >
                      {t('layout.login')}
                    </Button>
                    <Button
                      size="sm"
                      component={Link}
                      to="/register"
                      leftSection={<IconUserPlus size={16} />}
                      style={{
                        borderRadius: '8px',
                        fontWeight: 600,
                        background: `linear-gradient(135deg, ${themeColors.primary} 0%, ${themeColors.primary}dd 100%)`,
                        boxShadow: resolvedTheme === 'dark'
                          ? '0 2px 8px rgba(0,0,0,0.3)'
                          : '0 2px 8px rgba(0,0,0,0.1)',
                      }}
                    >
                      {t('layout.register')}
                    </Button>
                    {membershipButton.show && (
                      <Button
                        variant="outline"
                        size="sm"
                        color={membershipButton.color}
                        component={Link}
                        to="/membership"
                        leftSection={<IconCrown size={16} />}
                        style={{
                          borderRadius: '8px',
                          fontWeight: 500,
                          borderWidth: '2px',
                        }}
                      >
                        {membershipButton.text}
                      </Button>
                    )}
                  </Group>

                  {/* Divider */}
                  <Box
                    style={{
                      width: '1px',
                      height: '24px',
                      backgroundColor: themeColors.border,
                      margin: '0 8px',
                    }}
                  />

                  {/* Settings Group for non-authenticated users */}
                  <Group gap="xs" wrap="nowrap">
                    <Tooltip
                      label={resolvedTheme === 'dark' ? t('theme.switchToLight', 'Switch to Light Mode') : t('theme.switchToDark', 'Switch to Dark Mode')}
                      position="bottom"
                      offset={12}
                    >
                      <ActionIcon
                        variant="subtle"
                        size="lg"
                        onClick={toggleTheme}
                        style={{
                          borderRadius: '10px',
                          transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                          backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          color: themeColors.textPrimary,
                          border: `1px solid ${themeColors.border}`,
                        }}
                        styles={{
                          root: {
                            '&:hover': {
                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                              transform: 'scale(1.05)',
                            }
                          }
                        }}
                      >
                        {resolvedTheme === 'dark' ?
                          <IconSun size={18} style={{ color: themeColors.textPrimary }} /> :
                          <IconMoon size={18} style={{ color: themeColors.textPrimary }} />
                        }
                      </ActionIcon>
                    </Tooltip>

                    <Menu shadow="lg" width={180} position="bottom-end" offset={8}>
                      <Menu.Target>
                        <Button
                          variant="subtle"
                          size="sm"
                          leftSection={<IconWorld size={16} />}
                          rightSection={<IconChevronDown size={14} />}
                          style={{
                            borderRadius: '8px',
                            fontWeight: 500,
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                              }
                            }
                          }}
                        >
                          {lang === 'fr' ? 'FR' : lang === 'en' ? 'EN' : '中文'}
                        </Button>
                      </Menu.Target>
                      <Menu.Dropdown style={{ borderRadius: '12px' }}>
                        <Menu.Item
                          onClick={() => setLang('fr')}
                          leftSection="🇫🇷"
                          style={{ borderRadius: '8px' }}
                        >
                          Français
                        </Menu.Item>
                        <Menu.Item
                          onClick={() => setLang('en')}
                          leftSection="🇬🇧"
                          style={{ borderRadius: '8px' }}
                        >
                          English
                        </Menu.Item>
                        <Menu.Item
                          onClick={() => setLang('zh')}
                          leftSection="🇨🇳"
                          style={{ borderRadius: '8px' }}
                        >
                          中文
                        </Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>
                </>
              )}
            </Group>
          </Group>
        </Container>

        {/* Enhanced Mobile Navigation */}
        <Transition
          mounted={mobileNavOpened}
          transition="slide-down"
          duration={300}
          timingFunction="ease"
        >
          {(styles) => (
            <Box
              hiddenFrom="sm"
              style={{
                ...styles,
                background: themeColors.surface,
                borderTop: `1px solid ${themeColors.border}`,
                boxShadow: resolvedTheme === 'dark'
                  ? '0 4px 20px rgba(0,0,0,0.4)'
                  : '0 4px 20px rgba(0,0,0,0.1)',
              }}
            >
              <Container size="xl" p="lg">
                <Stack gap="md">
                  {/* Settings Row (Mobile) */}
                  <Group justify="space-between" align="center">
                    <Text size="sm" fw={600} c={themeColors.textSecondary}>
                      {t('layout.settings', 'Settings')}
                    </Text>
                    <Group gap="sm">
                      <Tooltip
                        label={resolvedTheme === 'dark' ? t('theme.switchToLight', 'Switch to Light Mode') : t('theme.switchToDark', 'Switch to Dark Mode')}
                        position="bottom"
                        offset={12}
                      >
                        <ActionIcon
                          variant="subtle"
                          size="lg"
                          onClick={toggleTheme}
                          style={{
                            borderRadius: '10px',
                            transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), transform 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                            color: themeColors.textPrimary,
                            border: `1px solid ${themeColors.border}`,
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.15)' : 'rgba(0,0,0,0.1)',
                                transform: 'scale(1.05)',
                              }
                            }
                          }}
                        >
                          {resolvedTheme === 'dark' ?
                            <IconSun size={18} style={{ color: themeColors.textPrimary }} /> :
                            <IconMoon size={18} style={{ color: themeColors.textPrimary }} />
                          }
                        </ActionIcon>
                      </Tooltip>

                      <Menu shadow="lg" width={180} position="bottom-end" offset={8}>
                        <Menu.Target>
                          <Button
                            variant="subtle"
                            size="sm"
                            leftSection={<IconWorld size={16} />}
                            rightSection={<IconChevronDown size={14} />}
                            style={{
                              borderRadius: '8px',
                              fontWeight: 500,
                              backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                            }}
                          >
                            {lang === 'fr' ? 'FR' : lang === 'en' ? 'EN' : '中文'}
                          </Button>
                        </Menu.Target>
                        <Menu.Dropdown style={{ borderRadius: '12px' }}>
                          <Menu.Item
                            onClick={() => setLang('fr')}
                            leftSection="🇫🇷"
                            style={{ borderRadius: '8px' }}
                          >
                            Français
                          </Menu.Item>
                          <Menu.Item
                            onClick={() => setLang('en')}
                            leftSection="🇬🇧"
                            style={{ borderRadius: '8px' }}
                          >
                            English
                          </Menu.Item>
                          <Menu.Item
                            onClick={() => setLang('zh')}
                            leftSection="🇨🇳"
                            style={{ borderRadius: '8px' }}
                          >
                            中文
                          </Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>
                  </Group>

                  {/* Navigation Divider */}
                  <Box
                    style={{
                      height: '1px',
                      backgroundColor: themeColors.border,
                      margin: '8px 0',
                    }}
                  />
                  {/* Navigation Items */}
                  <Stack gap="xs">
                    <Button
                      component={Link}
                      to="/"
                      onClick={handleMobileNavToggle}
                      variant="subtle"
                      size="md"
                      leftSection={<IconHome size={18} style={{ color: themeColors.textPrimary }} />}
                      justify="flex-start"
                      style={{
                        borderRadius: '12px',
                        height: '48px',
                        fontWeight: 500,
                        color: themeColors.textPrimary,
                      }}
                      styles={{
                        root: {
                          '&:hover': {
                            backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                          }
                        }
                      }}
                    >
                      {t('layout.home')}
                    </Button>

                    {isAuthenticated ? (
                      <>
                        <Button
                          component={Link}
                          to="/profile"
                          onClick={handleMobileNavToggle}
                          variant="subtle"
                          size="md"
                          leftSection={<IconUser size={18} style={{ color: themeColors.textPrimary }} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.profile')}
                        </Button>
                      </>
                    ) : null}

                    {/* Sections - Mobile (Available for all users) - Collapsible */}
                    <Group
                      onClick={toggleMobileSections}
                      style={{
                        borderRadius: '12px',
                        height: '48px',
                        padding: '0 16px',
                        marginTop: '16px',
                        cursor: 'pointer',
                        backgroundColor: 'transparent',
                        transition: 'background-color 0.2s ease',
                        width: '100%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <Group gap="sm" align="center">
                        <IconList size={18} style={{ color: themeColors.textPrimary }} />
                        <Text
                          size="md"
                          fw={600}
                          style={{ color: themeColors.textPrimary }}
                        >
                          {t('layout.quickNavigation.title')}
                        </Text>
                      </Group>
                      {mobileSectionsOpened ? 
                        <IconChevronUp size={16} style={{ color: themeColors.textPrimary }} /> : 
                        <IconChevronDown size={16} style={{ color: themeColors.textPrimary }} />
                      }
                    </Group>
                    <Collapse in={mobileSectionsOpened}>
                      <Stack gap="xs" mt="xs">
                        <Button
                          component={Link}
                          to="/listening"
                          onClick={handleMobileNavToggle}
                          variant="subtle"
                          size="md"
                          leftSection={<IconEar size={18} style={{ color: themeColors.textPrimary }} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                            marginLeft: '16px',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.quickNavigation.listening')}
                        </Button>
                        <Button
                          component={Link}
                          to="/reading"
                          onClick={handleMobileNavToggle}
                          variant="subtle"
                          size="md"
                          leftSection={<IconBook size={18} style={{ color: themeColors.textPrimary }} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                            marginLeft: '16px',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.quickNavigation.reading')}
                        </Button>
                        <Button
                          component={Link}
                          to="/writing"
                          onClick={handleMobileNavToggle}
                          variant="subtle"
                          size="md"
                          leftSection={<IconPencil size={18} style={{ color: themeColors.textPrimary }} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                            marginLeft: '16px',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.quickNavigation.writing')}
                        </Button>
                        <Button
                          component={Link}
                          to="/speaking"
                          onClick={handleMobileNavToggle}
                          variant="subtle"
                          size="md"
                          leftSection={<IconMicrophone size={18} style={{ color: themeColors.textPrimary }} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                            marginLeft: '16px',
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.quickNavigation.speaking')}
                        </Button>
                      </Stack>
                    </Collapse>

                    {isAuthenticated ? (
                      <>

                        {/* Tools Section - Mobile - Collapsible */}
                        <Group
                          onClick={toggleMobileTools}
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            padding: '0 16px',
                            marginTop: '16px',
                            cursor: 'pointer',
                            backgroundColor: 'transparent',
                            transition: 'background-color 0.2s ease',
                            width: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                          }}
                        >
                          <Group gap="sm" align="center">
                            <IconTools size={18} style={{ color: themeColors.textPrimary }} />
                            <Text
                              size="md"
                              fw={600}
                              style={{ color: themeColors.textPrimary }}
                            >
                              {t('layout.tools.title')}
                            </Text>
                          </Group>
                          {mobileToolsOpened ? 
                            <IconChevronUp size={16} style={{ color: themeColors.textPrimary }} /> : 
                            <IconChevronDown size={16} style={{ color: themeColors.textPrimary }} />
                          }
                        </Group>
                        <Collapse in={mobileToolsOpened}>
                          <Stack gap="xs" mt="xs">
                            <Button
                              component={Link}
                              to="/collection"
                              onClick={handleMobileNavToggle}
                              variant="subtle"
                              size="md"
                              leftSection={<IconStar size={18} style={{ color: themeColors.textPrimary }} />}
                              justify="flex-start"
                              style={{
                                borderRadius: '12px',
                                height: '48px',
                                fontWeight: 500,
                                color: themeColors.textPrimary,
                                marginLeft: '16px',
                              }}
                              styles={{
                                root: {
                                  '&:hover': {
                                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                  }
                                }
                              }}
                            >
                              {t('layout.collection')}
                            </Button>

                            <Button
                              component={Link}
                              to="/notebook"
                              onClick={handleMobileNavToggle}
                              variant="subtle"
                              size="md"
                              leftSection={<IconNotebook size={18} style={{ color: themeColors.textPrimary }} />}
                              justify="flex-start"
                              style={{
                                borderRadius: '12px',
                                height: '48px',
                                fontWeight: 500,
                                color: themeColors.textPrimary,
                                marginLeft: '16px',
                              }}
                              styles={{
                                root: {
                                  '&:hover': {
                                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                  }
                                }
                              }}
                            >
                              {t('layout.notebook', 'Notebook')}
                            </Button>

                            <Button
                              component={Link}
                              to="/downloads"
                              onClick={handleMobileNavToggle}
                              variant="subtle"
                              size="md"
                              leftSection={<IconDownload size={18} style={{ color: themeColors.textPrimary }} />}
                              justify="flex-start"
                              style={{
                                borderRadius: '12px',
                                height: '48px',
                                fontWeight: 500,
                                color: themeColors.textPrimary,
                                marginLeft: '16px',
                              }}
                              styles={{
                                root: {
                                  '&:hover': {
                                    backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                                  }
                                }
                              }}
                            >
                              {t('layout.tools.downloadMaterials')}
                            </Button>
                          </Stack>
                        </Collapse>



                        {membershipButton.show && (
                          <Button
                            component={Link}
                            to="/membership"
                            onClick={handleMobileNavToggle}
                            variant="gradient"
                            gradient={{ from: membershipButton.color, to: `${membershipButton.color}.7` }}
                            size="md"
                            leftSection={<IconCrown size={18} />}
                            justify="flex-start"
                            style={{
                              borderRadius: '12px',
                              height: '48px',
                              fontWeight: 600,
                              marginTop: '8px',
                            }}
                          >
                            {membershipButton.text}
                          </Button>
                        )}

                        <Button
                          variant="light"
                          color="red"
                          size="md"
                          onClick={handleLogout}
                          leftSection={<IconLogout size={18} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            marginTop: '16px',
                          }}
                        >
                          {t('layout.logout')}
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="subtle"
                          size="md"
                          component={Link}
                          to="/login"
                          onClick={handleMobileNavToggle}
                          leftSection={<IconLogin size={18} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 500,
                            color: themeColors.textPrimary,
                          }}
                          styles={{
                            root: {
                              '&:hover': {
                                backgroundColor: resolvedTheme === 'dark' ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.05)',
                              }
                            }
                          }}
                        >
                          {t('layout.login')}
                        </Button>
                        <Button
                          size="md"
                          component={Link}
                          to="/register"
                          onClick={handleMobileNavToggle}
                          leftSection={<IconUserPlus size={18} />}
                          justify="flex-start"
                          style={{
                            borderRadius: '12px',
                            height: '48px',
                            fontWeight: 600,
                            background: `linear-gradient(135deg, ${themeColors.primary} 0%, ${themeColors.primary}dd 100%)`,
                          }}
                        >
                          {t('layout.register')}
                        </Button>
                        {membershipButton.show && (
                          <Button
                            variant="outline"
                            size="md"
                            color={membershipButton.color}
                            component={Link}
                            to="/membership"
                            onClick={handleMobileNavToggle}
                            leftSection={<IconCrown size={18} />}
                            justify="flex-start"
                            style={{
                              borderRadius: '12px',
                              height: '48px',
                              fontWeight: 500,
                              borderWidth: '2px',
                              marginTop: '8px',
                            }}
                          >
                            {membershipButton.text}
                          </Button>
                        )}
                      </>
                    )}
                  </Stack>
                </Stack>
              </Container>
            </Box>
          )}
        </Transition>
      </AppShell.Header>
      
      <AppShell.Main
        style={{
          background: themeColors.background,
          minHeight: '80vh',
          paddingTop: isTestPage ? '1rem' : 'calc(72px + 1rem)',
          paddingBottom: '2rem',
          transition: isTestPage
            ? 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), padding-top 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            : 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {children}

        {/* Navigation hint for test pages */}
        <NavigationHint isTestPage={isTestPage} testPageType={testPageType} />
      </AppShell.Main>
      
      {/* Footer */}
      <Box
        style={{
          background: themeColors.surface,
          borderTop: `1px solid ${themeColors.border}`,
          color: themeColors.textSecondary,
          textAlign: 'center',
          padding: '1.5rem 0',
          fontSize: '0.95rem',
          transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        <Container size="xl">
          <Text size="sm" c="dimmed" mb="sm">
            © {new Date().getFullYear()} Chez-TCFCA. {t('layout.footer.copyright')}
          </Text>
          <Button
            variant="gradient"
            gradient={{ from: '#007bff', to: '#0056b3' }}
            size="sm"
            radius="xl"
            leftSection={<IconHeadphones size={16} />}
            onClick={openContact}
            style={{
              transition: 'all 0.3s ease',
            }}
          >
            {t('layout.footer.contactSupport')}
          </Button>
        </Container>
      </Box>

      {/* Contact Support Modal */}
      <ContactSupportModal
        opened={contactOpened}
        onClose={closeContact}
        contactForm={contactForm}
        isSubmitting={isSubmittingSupport}
        onSubmit={handleContactSupport}
      />
    </AppShell>
  );
}