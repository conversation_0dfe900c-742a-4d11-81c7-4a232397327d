/**
 * Visual indicator that session monitoring is active
 * This is for testing/debugging purposes
 */

import React, { useState, useEffect } from 'react';
import { Badge, Tooltip } from '@mantine/core';

interface SessionMonitorIndicatorProps {
  show?: boolean; // Whether to show the indicator
}

export function SessionMonitorIndicator({ show = false }: SessionMonitorIndicatorProps) {
  const [isActive, setIsActive] = useState(false);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  useEffect(() => {
    if (!show) return;

    // Listen for session check events (we'll emit these from the hook)
    const handleSessionCheck = () => {
      setIsActive(true);
      setLastCheck(new Date());
      
      // Flash effect
      setTimeout(() => setIsActive(false), 1000);
    };

    // Custom event listener for session checks
    window.addEventListener('session-check', handleSessionCheck);
    
    return () => {
      window.removeEventListener('session-check', handleSessionCheck);
    };
  }, [show]);

  if (!show) return null;

  return (
    <Tooltip
      label={`Session monitoring active. Last check: ${lastCheck ? lastCheck.toLocaleTimeString() : 'Never'}`}
      position="bottom"
    >
      <Badge
        color={isActive ? 'green' : 'gray'}
        variant={isActive ? 'filled' : 'light'}
        size="xs"
        style={{
          position: 'fixed',
          top: 10,
          right: 10,
          zIndex: 1000,
          cursor: 'pointer',
          transition: 'all 0.3s ease'
        }}
      >
        🔍 Session Monitor
      </Badge>
    </Tooltip>
  );
}
