import React, { useState, useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import {
  Box,
  Paper,
  Title,
  ActionIcon,
  Text,
  Stack,
  Group,
  Divider,
  Loader,
  Tooltip,
  ScrollArea,
  Badge,
  Alert,
  Collapse
} from '@mantine/core';
import {
  IconBrain,
  IconChevronRight,
  IconChevronLeft,
  IconAlertCircle,
  IconLanguage
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { api } from '../services/api';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';

interface AnalysisSidebarProps {
  section: string;
  testId: string;
  currentQuestion: number;
  isGroupTest?: boolean;
  isFree?: boolean;
  className?: string;
  isExpanded?: boolean;
  onToggle?: () => void;
}

interface AnalysisData {
  en?: string;
  cn?: string;
  fr?: string;
}

interface ApiResponse {
  analysis: AnalysisData;
  question_info: {
    section: string;
    test_id: string;
    question_number: number;
    test_category: string;
  };
}

export const AnalysisSidebar: React.FC<AnalysisSidebarProps> = ({
  section,
  testId,
  currentQuestion,
  isGroupTest = false,
  isFree = false,
  className = '',
  isExpanded = true,
  onToggle
}) => {
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasAnalysis, setHasAnalysis] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<'en' | 'cn' | 'fr'>('en');
  
  // 🆕 Add this to track the latest request and prevent race conditions
  const latestRequestRef = useRef<number>(0);

  // Show for both reading and listening tests
  const shouldShow = section === 'reading' || section === 'listening';

  // Update language based on i18n current language
  useEffect(() => {
    const lang = i18n.language;
    if (lang.startsWith('zh')) {
      setCurrentLanguage('cn');
    } else if (lang.startsWith('fr')) {
      setCurrentLanguage('fr');
    } else {
      setCurrentLanguage('en');
    }
  }, [i18n.language]);

  // Fetch analysis data for current question
  useEffect(() => {
    if (!shouldShow || !testId || currentQuestion < 1) {
      return;
    }

    const fetchAnalysis = async () => {
      // 🆕 Track this request to prevent race conditions
      const requestId = ++latestRequestRef.current;
      
      setIsLoading(true);
      setError(null);
      

      
      try {
        const params = new URLSearchParams();
        if (isFree) {
          params.append('free', '1');
        }
        
        const response = await api.get(
          `/analysis/question/${section}/${testId}/${currentQuestion}?${params.toString()}`
        );
        
        // 🆕 Only update state if this is still the latest request
        if (requestId === latestRequestRef.current) {
          setAnalysisData(response.data.analysis);
          setHasAnalysis(true);
        }
      } catch (err: any) {
        // 🆕 Only update state if this is still the latest request
        if (requestId === latestRequestRef.current) {
          if (err.response?.status === 404) {
            setAnalysisData(null);
            setHasAnalysis(false);
          } else {
            console.error('🔍 AnalysisSidebar: Error fetching analysis:', err, 'requestId:', requestId);
            setError(err.response?.data?.error || 'Failed to load analysis');
            setHasAnalysis(false);
          }
        }
      } finally {
        // 🆕 Only update loading if this is still the latest request
        if (requestId === latestRequestRef.current) {
          setIsLoading(false);
        }
      }
    };

    fetchAnalysis();
  }, [section, testId, currentQuestion, isFree, shouldShow]);

  // Toggle between languages
  const toggleLanguage = () => {
    const languages: ('en' | 'cn' | 'fr')[] = ['en', 'cn', 'fr'];
    const currentIndex = languages.indexOf(currentLanguage);
    const nextIndex = (currentIndex + 1) % languages.length;
    setCurrentLanguage(languages[nextIndex]);
  };

  // Get language display name
  const getLanguageDisplayName = (lang: 'en' | 'cn' | 'fr') => {
    switch (lang) {
      case 'en': return 'English';
      case 'cn': return '中文';
      case 'fr': return 'Français';
      default: return lang;
    }
  };

  // Don't render if not a reading or listening test
  if (!shouldShow) {
    return null;
  }

  return <>
    {isExpanded && (
      <Box
        style={{
          position: 'relative',
          display: 'flex',
          top: '50%',
          transform: 'translateY(-50%)',
          height: '500px', // Fixed height instead of dynamic
          minHeight: '500px', // Same as height for consistency
          maxHeight: '500px', // Same as height for consistency
          maxWidth: '20em',
          zIndex: 10,
          overflow: 'hidden', // Prevent overflow on container
        }}
      >
        <Paper
          shadow="lg"
          style={{
            width: '100%',
            height: '100%',
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
            borderTopRightRadius: '12px',
            borderBottomRightRadius: '12px',
            overflow: 'hidden', // Prevent overflow on paper
            backgroundColor: themeColors.primary,
            color: 'white',
            transition: 'all 0.3s ease',
            display: 'flex',
            flexDirection: 'column' // Use flex to control layout
          }}
        >
          {/* Content */}
          <Box p="sm" style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            {/* Question indicator */}
            {/* Removed question number badge as per user request */}
            <Group justify="space-between" mb="sm" style={{ flexShrink: 0 }}>
              {/* Language toggle button */}
              {hasAnalysis && analysisData && (
                <Tooltip label={`Switch to ${getLanguageDisplayName(currentLanguage === 'en' ? 'cn' : currentLanguage === 'cn' ? 'fr' : 'en')}`}>
                  <ActionIcon
                    variant="subtle"
                    color="white"
                    size="sm"
                    onClick={toggleLanguage}
                  >
                    <IconLanguage size={16} />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>

            <Divider color="rgba(255,255,255,0.3)" mb="sm" style={{ flexShrink: 0 }} />

            {/* Loading state */}
            {isLoading && (
              <Group justify="center" p="md" style={{ flex: 1, alignItems: 'center' }}>
                <Loader size="sm" color="white" />
                <Text size="sm">Loading analysis...</Text>
              </Group>
            )}

            {/* Error state */}
            {error && (
              <Alert
                icon={<IconAlertCircle size={16} />}
                color="red"
                variant="light"
                style={{ flexShrink: 0 }}
              >
                <Text size="xs">{error}</Text>
              </Alert>
            )}

            {/* No analysis available */}
            {!isLoading && !error && !hasAnalysis && (
              <Alert
                icon={<IconAlertCircle size={16} />}
                color="yellow"
                variant="light"
                style={{ flexShrink: 0 }}
              >
                <Text size="xs">
                  {t('analysis.notAvailable', 'No analysis available for this question')}
                </Text>
              </Alert>
            )}

            {/* Analysis content */}
            {!isLoading && !error && hasAnalysis && analysisData && (
              <ScrollArea style={{ flex: 1, minHeight: 0 }} scrollbars="y">
                <Stack gap="sm">
                  {/* Language indicator */}
                  <Badge variant="light" color="white" size="xs">
                    {getLanguageDisplayName(currentLanguage)}
                  </Badge>

                  {/* Analysis text */}
                  <Box
                    style={{
                      backgroundColor: 'rgba(255,255,255,0.1)',
                      borderRadius: '8px',
                      padding: '12px'
                    }}
                  >
                    <ReactMarkdown>
                      {analysisData[currentLanguage] ||
                        analysisData.en ||
                        t('analysis.noContent', 'No analysis content available in this language')}
                    </ReactMarkdown>
                  </Box>
                </Stack>
              </ScrollArea>
            )}
          </Box>
        </Paper>
      </Box>
    )}</>
}; 