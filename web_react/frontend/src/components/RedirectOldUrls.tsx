import { useParams, Navigate, useSearchParams } from 'react-router-dom';

// Redirect old test URLs like /test/listening/test12 to /listening/test12
export function RedirectOldTestUrl() {
  const { section, testId } = useParams<{ section: string; testId: string }>();
  const [searchParams] = useSearchParams();
  
  if (!section || !testId) {
    return <Navigate to="/" replace />;
  }
  
  // Preserve query parameters like ?free=1
  const queryString = searchParams.toString();
  const newUrl = `/${section}/${testId}${queryString ? `?${queryString}` : ''}`;
  
  return <Navigate to={newUrl} replace />;
}

// Redirect old group test URLs like /test-by-difficulty/listening/1 to /listening/group/1
export function RedirectOldGroupUrl() {
  const { section, groupId } = useParams<{ section: string; groupId: string }>();
  const [searchParams] = useSearchParams();
  
  if (!section || !groupId) {
    return <Navigate to="/" replace />;
  }
  
  // Preserve query parameters
  const queryString = searchParams.toString();
  const newUrl = `/${section}/group/${groupId}${queryString ? `?${queryString}` : ''}`;
  
  return <Navigate to={newUrl} replace />;
}

// Redirect old mock exam URLs like /mock-exam/listening/test123 to /listening/mock/test123
export function RedirectOldMockUrl() {
  const { section, testId } = useParams<{ section: string; testId: string }>();
  const [searchParams] = useSearchParams();

  if (!section || !testId) {
    return <Navigate to="/" replace />;
  }

  // Preserve query parameters
  const queryString = searchParams.toString();
  const newUrl = `/${section}/mock/${testId}${queryString ? `?${queryString}` : ''}`;

  return <Navigate to={newUrl} replace />;
}

// Redirect old classified writing URLs to new simplified format
export function RedirectOldClassifiedWriting() {
  const { tacheNumber, topicName, subtopicId } = useParams<{
    tacheNumber: string;
    topicName?: string;
    subtopicId?: string;
  }>();

  if (!tacheNumber) {
    return <Navigate to="/writing" replace />;
  }

  // Build new URL based on what parameters we have
  let newUrl = `/writing/classified_tache${tacheNumber}`;

  if (topicName && subtopicId) {
    // Full subtopic URL: /classified-writing/tache/1/topic/recommendation/subtopic/recommendation_sport
    // -> /writing/classified_tache1/recommendation/recommendation_sport
    newUrl += `/${topicName}/${subtopicId}`;
  } else if (topicName) {
    // Topic URL: /classified-writing/tache/1/topic/recommendation
    // -> /writing/classified_tache1/recommendation
    newUrl += `/${topicName}`;
  }
  // Otherwise just redirect to tâche overview: /writing/classified_tache1

  return <Navigate to={newUrl} replace />;
}