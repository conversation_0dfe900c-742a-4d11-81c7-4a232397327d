import React, { useState, useEffect, useRef } from 'react';
import { Box, Button, Text, Loader, Group, Paper, Stack, ActionIcon } from '@mantine/core';
import { IconLanguage, IconX, IconCheck } from '@tabler/icons-react';
import { translationApi } from '../services/api';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';

interface TranslationPopupProps {
  selectedText: string;
  onClose: () => void;
  position: { x: number; y: number };
  fixedPosition?: boolean; // If true, popup stays completely fixed and doesn't move with scroll
}

export function TranslationPopup({ selectedText, onClose, position, fixedPosition = false }: TranslationPopupProps) {
  const { t, i18n } = useTranslation();
  const themeColors = useThemeColors();
  const [isTranslating, setIsTranslating] = useState(false);
  const [translations, setTranslations] = useState<{
    en?: { text: string; confidence: number };
    zh?: { text: string; confidence: number };
  }>({});

  // Set default translation language based on user's current language
  const getDefaultTranslationLanguage = (): 'en' | 'zh' => {
    const currentLang = i18n.language;
    // If user is using Chinese, default to Chinese translation
    if (currentLang === 'zh' || currentLang === 'zh-CN' || currentLang === 'zh-TW') {
      return 'zh';
    }
    // For English, French, or any other language, default to English
    return 'en';
  };

  const [currentLanguage, setCurrentLanguage] = useState<'en' | 'zh'>(getDefaultTranslationLanguage());
  const [calculatedPosition, setCalculatedPosition] = useState({ x: 0, y: 0, isOnRightSide: true });
  const popupRef = useRef<HTMLDivElement>(null);

  const handleTranslate = () => {
    // Re-translate current language (useful for retry)
    handleTranslateToLanguage(currentLanguage);
  };

  const toggleLanguage = () => {
    const newLanguage = currentLanguage === 'en' ? 'zh' : 'en';
    setCurrentLanguage(newLanguage);

    // Auto-translate to the new language if not already translated
    if (!translations[newLanguage]) {
      handleTranslateToLanguage(newLanguage);
    }
  };

  const handleTranslateToLanguage = async (targetLanguage: 'en' | 'zh') => {
    // Don't check if translation exists - always translate when called
    if (isTranslating) {
      return; // Only prevent if currently translating
    }

    setIsTranslating(true);

    try {
      const result = await translationApi.translate(selectedText, targetLanguage);

      if (result.success && result.translated_text) {
        setTranslations(prev => ({
          ...prev,
          [targetLanguage]: {
            text: result.translated_text!,
            confidence: result.confidence || 1.0
          }
        }));
      } else {
        notifications.show({
          title: t('translation.error'),
          message: result.error || t('translation.failed'),
          color: 'red',
        });
      }
    } catch (error) {
      notifications.show({
        title: t('translation.error'),
        message: t('translation.networkError'),
        color: 'red',
      });
    } finally {
      setIsTranslating(false);
    }
  };

  // Auto-translate when popup opens or when selectedText changes (for replacement)
  useEffect(() => {
    if (selectedText) {
      // Clear previous translations and translate the new text
      setTranslations({});
      // Add small delay to ensure state is cleared before translating
      setTimeout(() => {
        handleTranslateToLanguage(currentLanguage);
      }, 50);
    }
  }, [selectedText, currentLanguage]); // Run when selectedText changes (including replacement)

  // Update translation language if user changes their i18n language setting
  useEffect(() => {
    const newDefaultLanguage = getDefaultTranslationLanguage();
    if (newDefaultLanguage !== currentLanguage) {
      setCurrentLanguage(newDefaultLanguage);
      // Auto-translate to the new language if not already translated
      if (!translations[newDefaultLanguage]) {
        handleTranslateToLanguage(newDefaultLanguage);
      }
    }
  }, [i18n.language]); // Run when i18n language changes



  const getLanguageName = (code: string) => {
    const names = {
      en: 'English',
      zh: '中文',
    };
    return names[code as keyof typeof names] || code;
  };



  // Calculate optimal popup dimensions for readability - with FIXED WIDTH
  const calculatePopupDimensions = () => {
    const currentTranslation = translations[currentLanguage];
    const viewportWidth = window.innerWidth;
    
    // FIXED WIDTH to prevent horizontal position changes - made wider to reduce gap
    const width = 420; // Wider popup to reduce gap between content and popup
    let height = 140; // Base height
    
    if (!currentTranslation) {
      return { width, height };
    }

    const text = currentTranslation.text;
    const textLength = text.length;

    // Only adjust height based on content (width stays fixed)
    if (textLength > 50) {
      height = 160;
    }
    if (textLength > 100) {
      height = 200;
    }
    if (textLength > 150) {
      height = 240;
    }

    // Chinese text needs more height due to character density
    if (currentLanguage === 'zh' && textLength > 30) {
      height += 20;
    }

    // Ensure height fits in viewport (but keep width fixed)
    const maxHeight = window.innerHeight - 40;

    return {
      width: width, // Always fixed width
      height: Math.min(height, maxHeight)
    };
  };

  // Simplified and reliable popup positioning - always right sidebar
  const calculateSmartPosition = () => {
    const { width: popupWidth, height: popupHeight } = calculatePopupDimensions();
    
    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // ABSOLUTELY FIXED HORIZONTAL POSITION: Never changes regardless of anything
    // Position closer to content to reduce gap
    const x = viewportWidth - 430; // Closer to content: 430px from right edge (420px width + 10px margin)
    
    // Y position: Always in the middle of the viewport for optimal readability
    // Center the popup vertically in the viewport regardless of selection position
    const y = (viewportHeight - popupHeight) / 2;

    // Debug logging - check positioning
    console.log('TranslationPopup centered positioning:', {
      'x (fixed)': x,
      'y (centered)': y,
      'viewportWidth': viewportWidth,
      'viewportHeight': viewportHeight,
      'popupWidth': popupWidth,
      'popupHeight': popupHeight
    });

    return { x, y, isOnRightSide: true };
  };

  // Calculate position when popup opens or on resize
  useEffect(() => {
    const smartPosition = calculateSmartPosition();
    setCalculatedPosition(smartPosition);
  }, [position.x, position.y]); // Only when selection position changes

  // Ensure position is calculated immediately on mount with multiple attempts
  useEffect(() => {
    // Multiple calculation attempts to ensure correct initial positioning
    const calculateWithRetry = () => {
      const smartPosition = calculateSmartPosition();
      setCalculatedPosition(smartPosition);
    };

    // Immediate calculation
    calculateWithRetry();
    
    // Backup calculations with delays to handle DOM/dimension loading
    const timer1 = setTimeout(calculateWithRetry, 1);
    const timer2 = setTimeout(calculateWithRetry, 10);
    const timer3 = setTimeout(calculateWithRetry, 50);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []); // Run once on mount

  // Smooth scroll tracking without throttling to prevent flashiness
  useEffect(() => {
    // Skip scroll tracking if fixedPosition is true
    if (fixedPosition) {
      console.log('TranslationPopup: Fixed position mode - skipping scroll tracking');
      return;
    }

    const handleScroll = () => {
      // Direct position update without throttling for smoothness
      const smartPosition = calculateSmartPosition();
      setCalculatedPosition(smartPosition);
    };

    const handleResize = () => {
      const smartPosition = calculateSmartPosition();
      setCalculatedPosition(smartPosition);
    };

    // Add scroll tracking with passive listener - no throttling
    window.addEventListener('scroll', handleScroll, { passive: true });
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      window.removeEventListener('resize', handleResize);
    };
  }, [fixedPosition]);

  const { width: popupWidth, height: popupHeight } = calculatePopupDimensions();

  const popupStyle: React.CSSProperties = {
    position: 'fixed',
    left: calculatedPosition.x,
    top: calculatedPosition.y,
    zIndex: 2500, // Fixed z-index for consistent layering
    width: `${popupWidth}px`,
    height: `${popupHeight}px`,
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: themeColors.surface,
    color: themeColors.textPrimary,
    // PREVENT TEXT SELECTION: Make translation popup content unselectable
    userSelect: 'none',
    WebkitUserSelect: 'none',
    MozUserSelect: 'none',
    msUserSelect: 'none',
    cursor: 'default', // Show default cursor instead of text cursor
    overflow: 'hidden',
    // Professional, clean appearance
    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2), 0 8px 20px rgba(0, 0, 0, 0.1)',
    border: `1px solid ${themeColors.border}`,
    borderRadius: '12px',
    // Add subtle backdrop blur effect if supported
    backdropFilter: 'blur(10px)',
    // Minimal transitions to prevent flickering during scroll
    transition: 'height 0.2s ease, background-color 0.3s ease',
    // Remove top transition to prevent flickering during scroll
  };

  return (
    <Paper
      ref={popupRef}
      shadow="lg"
      p={8}
      style={popupStyle}
      withBorder
      className="translation-popup"
    >
      <Stack gap={4} style={{
        height: '100%',
        flex: 1,
        minHeight: 0,
        overflow: 'hidden',
        // Ensure child elements also inherit unselectable behavior
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
      }}>
        {/* Header with title and controls */}
        <Group justify="space-between" align="center" gap={6} style={{ padding: '0' }}>
          <Text size="xs" fw={600} c="dimmed" style={{
            display: 'flex',
            alignItems: 'center',
            gap: 4,
            // PREVENT HEADER TEXT SELECTION
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
          }}>
            <IconLanguage size={12} />
            Translation
          </Text>
          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            onClick={onClose}
            style={{ width: '20px', height: '20px' }}
          >
            <IconX size={12} />
          </ActionIcon>
        </Group>

        {/* Language and translate controls */}
        <Group justify="space-between" align="center" gap={2}>
          <Group gap={0}>
            <Button
              size="xs"
              variant="light"
              color="blue"
              onClick={handleTranslate}
              disabled={isTranslating || Boolean(translations[currentLanguage])}
              leftSection={
                isTranslating ? (
                  <Loader size={8} />
                ) : translations[currentLanguage] ? (
                  <IconCheck size={8} />
                ) : (
                  <IconLanguage size={8} />
                )
              }
              style={{
                borderTopRightRadius: 0,
                borderBottomRightRadius: 0,
                borderRight: 'none',
                fontSize: '9px',
                height: '18px',
                padding: '0 4px',
                minWidth: 'auto',
              }}
            >
              {isTranslating ? 'Translating...' : translations[currentLanguage] ? 'Done' : 'Translate'}
            </Button>

            {/* Language switch button */}
            <Button
              size="xs"
              variant="light"
              color="blue"
              onClick={toggleLanguage}
              disabled={isTranslating}
              style={{
                borderTopLeftRadius: 0,
                borderBottomLeftRadius: 0,
                fontSize: '9px',
                fontWeight: 600,
                padding: '0 3px',
                height: '18px',
                minWidth: '16px',
              }}
            >
              {currentLanguage === 'en' ? 'EN' : '中'}
            </Button>
          </Group>

        </Group>

        {/* Translation result for current language */}
        {translations[currentLanguage] && (
          <Box style={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            <Text
              size="xs"
              p="sm"
              style={{
                backgroundColor: themeColors.background,
                borderRadius: 'var(--mantine-radius-sm)',
                border: `1px solid ${themeColors.primary}`,
                color: themeColors.textPrimary,
                // Proper text wrapping and overflow handling
                overflowY: 'auto',
                overflowX: 'hidden',
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                whiteSpace: 'pre-wrap',
                lineHeight: 1.4,
                fontSize: '14px',
                flex: 1,
                minHeight: 0,
                maxWidth: '100%',
                // Add smooth transition for content changes
                transition: 'all 0.3s ease',
                // PREVENT TRANSLATION TEXT SELECTION: Critical to avoid user selecting translated content
                userSelect: 'none',
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                cursor: 'default',
              }}
            >
              {translations[currentLanguage]!.text}
            </Text>
          </Box>
        )}

        {/* Help text */}
        {!translations[currentLanguage] && !isTranslating && (
          <Text size="sm" c="dimmed" ta="center" style={{
            padding: '10px 0',
            // PREVENT HELP TEXT SELECTION
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none',
          }}>
            Translating to {getLanguageName(currentLanguage)}...
            <br />
            <Text size="xs" c="dimmed" span style={{
              // PREVENT NESTED TEXT SELECTION
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none',
            }}>
              Click "{currentLanguage === 'en' ? 'EN' : '中文'}" to switch language
            </Text>
          </Text>
        )}
      </Stack>
    </Paper>
  );
}
