import React, { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Stack,
  Card,
  Badge,
  Alert,
  Box,
  <PERSON>readcrumbs,
  Anchor,
  ActionIcon,
  Tooltip,
  Divider,
  Paper,
  SimpleGrid,
  Collapse,
  Modal,
  Loader
} from '@mantine/core';
import {
  IconInfoCircle,
  IconPencil,
  IconEye,
  IconChevronDown,
  IconChevronUp,
  IconArrowLeft,
  IconPlayerPlay,
  IconCopy,
  IconCalendar,
  IconTags,
  IconUsers,
  IconFileText
} from '@tabler/icons-react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useThemeColors } from '../store/useThemeStore';
import { useAuthStore } from '../store/useAuthStore';
import {
  classifiedWritingService,
  type TaskDetailsResponse,
  type TaskEntry
} from '../services/classifiedWritingService';

interface ClassifiedTaskDetailProps {
  representativeId?: string;
  onStartWriting?: (task: TaskEntry) => void;
}

interface TaskMetadataProps {
  task: TaskEntry;
  tacheColor: string;
}

function TaskMetadata({ task, tacheColor }: TaskMetadataProps) {
  const { t } = useTranslation();
  const [expanded, { toggle }] = useDisclosure(false);

  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="space-between" mb="md">
        <Title order={4}>
          {t('classifiedWriting.task.representative')}
        </Title>
        <ActionIcon
          variant="subtle"
          color="gray"
          onClick={toggle}
        >
          {expanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
        </ActionIcon>
      </Group>

      {/* Basic Info */}
      <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="lg" mb="md">
        <Box>
          <Text size="sm" c="dimmed" mb="xs">
            ID
          </Text>
          <Text fw={500} size="sm">
            {task.representative_id}
          </Text>
        </Box>
        <Box>
          <Text size="sm" c="dimmed" mb="xs">
            {t('classifiedWriting.task.duplicates')}
          </Text>
          <Group gap="xs">
            <Text fw={500} size="sm" c={task.is_duplicate_group ? 'orange' : undefined}>
              {task.duplicate_count}
            </Text>
            {task.is_duplicate_group && (
              <Badge size="xs" variant="light" color="orange">
                {t('classifiedWriting.task.duplicates')}
              </Badge>
            )}
          </Group>
        </Box>
        <Box>
          <Text size="sm" c="dimmed" mb="xs">
            {t('classifiedWriting.task.months')}
          </Text>
          <Text fw={500} size="sm">
            {task.month_years?.length || 0}
          </Text>
        </Box>
        <Box>
          <Text size="sm" c="dimmed" mb="xs">
            {t('classifiedWriting.task.combinations')}
          </Text>
          <Text fw={500} size="sm">
            {task.combination_numbers?.length || 0}
          </Text>
        </Box>
      </SimpleGrid>

      {/* Classification Context */}
      {task.classification_context && (
        <Box mb="md">
          <Text size="sm" c="dimmed" mb="xs">
            {t('classifiedWriting.task.context')}
          </Text>
          <Group gap="xs">
            <Badge variant="outline" color={tacheColor}>
              {t('classifiedWriting.tache.title', { number: task.classification_context.tache_number })}
            </Badge>
            <Badge variant="outline" color="blue">
              {task.classification_context.main_topic_name.replace(/_/g, ' ')}
            </Badge>
            <Badge variant="outline" color="gray">
              {task.classification_context.subtopic_name.replace(/_/g, ' ')}
            </Badge>
          </Group>
        </Box>
      )}

      {/* Expanded Details */}
      <Collapse in={expanded}>
        <Divider mb="md" />
        
        {/* Task IDs */}
        {task.task_ids && task.task_ids.length > 0 && (
          <Box mb="md">
            <Text size="sm" c="dimmed" mb="xs">
              {t('classifiedWriting.task.duplicates')} IDs ({task.task_ids.length})
            </Text>
            <Group gap="xs">
              {task.task_ids.slice(0, 10).map((id, index) => (
                <Badge key={index} size="xs" variant="light" color="gray">
                  {id}
                </Badge>
              ))}
              {task.task_ids.length > 10 && (
                <Text size="xs" c="dimmed">
                  +{task.task_ids.length - 10} more
                </Text>
              )}
            </Group>
          </Box>
        )}

        {/* Month Years */}
        {task.month_years && task.month_years.length > 0 && (
          <Box mb="md">
            <Text size="sm" c="dimmed" mb="xs">
              {t('classifiedWriting.task.months')} ({task.month_years.length})
            </Text>
            <Group gap="xs">
              {task.month_years.slice(0, 12).map((monthYear, index) => (
                <Badge key={index} size="xs" variant="light" color="blue">
                  {monthYear}
                </Badge>
              ))}
              {task.month_years.length > 12 && (
                <Text size="xs" c="dimmed">
                  +{task.month_years.length - 12} more
                </Text>
              )}
            </Group>
          </Box>
        )}

        {/* Combination Numbers */}
        {task.combination_numbers && task.combination_numbers.length > 0 && (
          <Box mb="md">
            <Text size="sm" c="dimmed" mb="xs">
              {t('classifiedWriting.task.combinations')} ({task.combination_numbers.length})
            </Text>
            <Group gap="xs">
              {task.combination_numbers.slice(0, 10).map((combo, index) => (
                <Badge key={index} size="xs" variant="light" color="green">
                  {combo}
                </Badge>
              ))}
              {task.combination_numbers.length > 10 && (
                <Text size="xs" c="dimmed">
                  +{task.combination_numbers.length - 10} more
                </Text>
              )}
            </Group>
          </Box>
        )}

        {/* Clean Content */}
        {task.clean_content && task.clean_content !== task.task_content && (
          <Box>
            <Text size="sm" c="dimmed" mb="xs">
              Contenu nettoyé (pour déduplication)
            </Text>
            <Text size="sm" style={{ 
              fontFamily: 'monospace', 
              backgroundColor: 'var(--mantine-color-gray-0)',
              padding: '8px',
              borderRadius: '4px',
              border: '1px solid var(--mantine-color-gray-3)'
            }}>
              {task.clean_content}
            </Text>
          </Box>
        )}
      </Collapse>
    </Paper>
  );
}

export function ClassifiedTaskDetail({ representativeId, onStartWriting }: ClassifiedTaskDetailProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const params = useParams();
  const themeColors = useThemeColors();
  const { isAuthenticated } = useAuthStore();

  // Use representativeId from props or URL params
  const taskId = representativeId || params.representativeId;

  // State management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [taskData, setTaskData] = useState<TaskDetailsResponse | null>(null);
  const [writingModalOpened, { open: openWritingModal, close: closeWritingModal }] = useDisclosure(false);

  // Load task data
  useEffect(() => {
    const loadTaskData = async () => {
      if (!taskId) return;

      try {
        setLoading(true);
        setError(null);
        const data = await classifiedWritingService.getTaskDetails(taskId);
        setTaskData(data);
      } catch (err: any) {
        console.error('Error loading task details:', err);
        setError(err.message || t('classifiedWriting.errors.loadTasks'));
      } finally {
        setLoading(false);
      }
    };

    loadTaskData();
  }, [taskId, t]);

  // Get tâche color
  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = taskData?.task.classification_context 
    ? getTacheColor(taskData.task.classification_context.tache_number)
    : '#fd7e14';

  // Handle copy task content
  const handleCopyContent = async () => {
    if (!taskData?.task.task_content) return;

    try {
      await navigator.clipboard.writeText(taskData.task.task_content);
      notifications.show({
        title: t('common.success'),
        message: 'Contenu copié dans le presse-papiers',
        color: 'green'
      });
    } catch (err) {
      notifications.show({
        title: t('common.error'),
        message: 'Erreur lors de la copie',
        color: 'red'
      });
    }
  };

  // Handle start writing
  const handleStartWriting = () => {
    if (!taskData?.task) return;

    if (onStartWriting) {
      onStartWriting(taskData.task);
    } else {
      // Navigate to writing interface with task context
      navigate('/writing/classified', {
        state: {
          task: taskData.task,
          mode: 'classified'
        }
      });
    }
  };

  // Breadcrumb items
  const getBreadcrumbs = () => {
    const items = [
      { title: t('classifiedWriting.title'), href: '/writing' }
    ];

    if (taskData?.task.classification_context) {
      const context = taskData.task.classification_context;
      items.push(
        {
          title: classifiedWritingService.formatTacheTitle(context.tache_number),
          href: `/writing/classified_tache${context.tache_number}`
        },
        {
          title: context.main_topic_name.replace(/_/g, ' '),
          href: `/writing/classified_tache${context.tache_number}/${encodeURIComponent(context.main_topic_name)}`
        },
        {
          title: context.subtopic_name.replace(/_/g, ' '),
          href: '#' // We don't have subtopic ID here
        }
      );
    }

    items.push({
      title: taskId || 'Task',
      href: `/writing/classified_task/${taskId}`
    });

    return items;
  };

  // Loading state
  if (loading) {
    return (
      <Container size="xl" py="xl">
        <Box ta="center">
          <Loader size="lg" />
          <Text size="sm" c="dimmed" mt="md">
            {t('classifiedWriting.loading.tasks')}
          </Text>
        </Box>
      </Container>
    );
  }

  // Error state
  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          variant="light"
          color="red"
          title={t('common.error')}
          icon={<IconInfoCircle />}
        >
          {error}
        </Alert>
      </Container>
    );
  }

  // No data state
  if (!taskData) {
    return (
      <Container size="xl" py="xl">
        <Alert
          variant="light"
          color="yellow"
          title={t('classifiedWriting.noData.title')}
          icon={<IconInfoCircle />}
        >
          {t('classifiedWriting.noData.message')}
        </Alert>
      </Container>
    );
  }

  const { task } = taskData;

  return (
    <Container size="xl" py="xl">
      {/* Breadcrumbs */}
      <Breadcrumbs mb="lg">
        {getBreadcrumbs().map((item, index) => (
          <Anchor
            key={index}
            component={item.href === '#' ? 'span' : Link as any}
            to={item.href !== '#' ? item.href : undefined}
            size="sm"
            c={index === getBreadcrumbs().length - 1 ? tacheColor : undefined}
          >
            {item.title}
          </Anchor>
        ))}
      </Breadcrumbs>

      {/* Header */}
      <Group justify="space-between" mb="xl">
        <Box>
          <Title order={2} c={tacheColor} mb="xs">
            {t('classifiedWriting.task.representative')}
          </Title>
          <Text c="dimmed">
            {task.classification_context && (
              <>
                {classifiedWritingService.formatTacheTitle(task.classification_context.tache_number)} • {' '}
                {task.classification_context.main_topic_name.replace(/_/g, ' ')} • {' '}
                {task.classification_context.subtopic_name.replace(/_/g, ' ')}
              </>
            )}
          </Text>
        </Box>
        <Group gap="sm">
          <Button
            leftSection={<IconArrowLeft size={16} />}
            variant="light"
            color="gray"
            onClick={() => navigate(-1)}
          >
            {t('common.back')}
          </Button>
          {isAuthenticated && (
            <Button
              leftSection={<IconPencil size={16} />}
              color={tacheColor}
              onClick={handleStartWriting}
            >
              {t('writing.startWriting')}
            </Button>
          )}
        </Group>
      </Group>

      {/* Task Content */}
      <Card withBorder shadow="sm" radius="md" mb="xl">
        <Group justify="space-between" mb="md">
          <Title order={4}>
            {t('classifiedWriting.task.content')}
          </Title>
          <Group gap="xs">
            <Tooltip label="Copier le contenu">
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={handleCopyContent}
              >
                <IconCopy size={16} />
              </ActionIcon>
            </Tooltip>
            {task.classification_context && (
              <Badge variant="light" color={tacheColor}>
                {classifiedWritingService.getWordLimits(task.classification_context.tache_number).min}-
                {classifiedWritingService.getWordLimits(task.classification_context.tache_number).max} mots
              </Badge>
            )}
          </Group>
        </Group>

        <Text size="md" style={{ lineHeight: 1.6 }}>
          {task.task_content}
        </Text>

        {/* Quick Actions */}
        <Divider my="md" />
        <Group justify="space-between">
          <Group gap="xs">
            <Text size="sm" c="dimmed">
              Actions rapides:
            </Text>
            <Button
              size="xs"
              variant="light"
              color={tacheColor}
              leftSection={<IconEye size={14} />}
              onClick={() => {
                // Extract keywords for highlighting
                const keywords = classifiedWritingService.extractKeywords(task.task_content);
              }}
            >
              Analyser
            </Button>
          </Group>
          {isAuthenticated && (
            <Button
              size="sm"
              color={tacheColor}
              leftSection={<IconPlayerPlay size={16} />}
              onClick={handleStartWriting}
            >
              Commencer l'écriture
            </Button>
          )}
        </Group>
      </Card>

      {/* Task Metadata */}
      <TaskMetadata task={task} tacheColor={tacheColor} />

      {/* Writing Modal (if needed) */}
      <Modal
        opened={writingModalOpened}
        onClose={closeWritingModal}
        title={t('writing.startWriting')}
        size="lg"
      >
        <Stack gap="md">
          <Text>
            Vous allez commencer à écrire pour cette tâche classifiée.
            Votre travail sera sauvegardé automatiquement.
          </Text>
          <Group justify="flex-end">
            <Button variant="light" onClick={closeWritingModal}>
              {t('common.cancel')}
            </Button>
            <Button color={tacheColor} onClick={handleStartWriting}>
              {t('common.continue')}
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
}
