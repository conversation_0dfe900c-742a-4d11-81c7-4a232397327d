import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  noIndex?: boolean;
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title = 'Chez-TCFCA - Préparation TCF Canada | Tests Gratuits et Premium',
  description = 'Préparez-vous au TCF Canada avec plus de 1000 questions authentiques. Tests gratuits disponibles. Réussissez votre immigration au Canada avec Chez-TCFCA.',
  keywords = 'TCF Canada, test français, immigration Canada, préparation TCF, français langue étrangère, FLE, tests gratuits, compréhension orale, compréhension écrite, expression orale, expression écrite, Chez-TCFCA',
  image = 'https://chez-tcfcanada.com/android-chrome-512x512.png',
  url = 'https://chez-tcfcanada.com',
  type = 'website',
  noIndex = false
}) => {
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:image" content={image} />
      <meta property="og:site_name" content="Chez-TCFCA" />
      <meta property="og:locale" content="fr_CA" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={url} />
    </Helmet>
  );
};

// Predefined SEO configurations for different pages
export const SEOConfigs = {
  home: {
    title: 'Chez-TCFCA - Préparation TCF Canada | Tests Gratuits et Premium',
    description: 'Préparez-vous au TCF Canada avec plus de 1000 questions authentiques. Tests gratuits disponibles. Réussissez votre immigration au Canada.',
    keywords: 'TCF Canada, test français, immigration Canada, préparation TCF, français langue étrangère, FLE, tests gratuits',
    url: 'https://chez-tcfcanada.com'
  },
  
  reading: {
    title: 'Tests de Compréhension Écrite TCF Canada | Chez-TCFCA',
    description: 'Pratiquez la compréhension écrite pour le TCF Canada. Plus de 300 questions authentiques avec corrections détaillées. Tests gratuits et premium.',
    keywords: 'compréhension écrite, TCF Canada, lecture français, tests gratuits, préparation immigration',
    url: 'https://chez-tcfcanada.com/reading'
  },
  
  listening: {
    title: 'Tests de Compréhension Orale TCF Canada | Chez-TCFCA',
    description: 'Entraînez-vous à la compréhension orale pour le TCF Canada. Audio authentiques avec transcriptions. Tests gratuits et premium disponibles.',
    keywords: 'compréhension orale, écoute français, TCF Canada, audio français, tests gratuits',
    url: 'https://chez-tcfcanada.com/listening'
  },
  
  writing: {
    title: 'Expression Écrite TCF Canada - Exemples et Corrections | Chez-TCFCA',
    description: 'Maîtrisez l\'expression écrite du TCF Canada avec nos exemples corrigés et conseils d\'experts. Tâches 1, 2 et 3 expliquées en détail.',
    keywords: 'expression écrite, rédaction français, TCF Canada, exemples corrigés, tâches écrites',
    url: 'https://chez-tcfcanada.com/writing'
  },
  
  speaking: {
    title: 'Expression Orale TCF Canada - Entraînement et Conseils | Chez-TCFCA',
    description: 'Préparez l\'expression orale du TCF Canada avec nos exercices et conseils. Tâches 1, 2 et 3 avec exemples audio et stratégies.',
    keywords: 'expression orale, oral français, TCF Canada, entraînement oral, conseils speaking',
    url: 'https://chez-tcfcanada.com/speaking'
  },
  
  membership: {
    title: 'Abonnement Premium TCF Canada | Accès Complet | Chez-TCFCA',
    description: 'Débloquez tous les tests TCF Canada avec notre abonnement premium. Plus de 1000 questions, corrections détaillées, et support prioritaire.',
    keywords: 'abonnement premium, TCF Canada payant, accès complet, tests illimités',
    url: 'https://chez-tcfcanada.com/membership'
  },
  
  collection: {
    title: 'Collection de Tests TCF Canada | Bibliothèque Complète | Chez-TCFCA',
    description: 'Explorez notre collection complète de tests TCF Canada organisés par niveau et difficulté. Trouvez les tests adaptés à votre niveau.',
    keywords: 'collection tests, bibliothèque TCF, tests par niveau, organisation tests',
    url: 'https://chez-tcfcanada.com/collection'
  }
};
