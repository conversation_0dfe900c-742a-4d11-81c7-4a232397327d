/**
 * Test panel to verify immediate session invalidation
 * Add this temporarily to test the session monitoring
 */

import React, { useState } from 'react';
import { Paper, Stack, Text, Button, Group, Badge, Code } from '@mantine/core';
import { SessionMonitorIndicator } from './SessionMonitorIndicator';

export function SessionTestPanel() {
  const [showMonitor, setShowMonitor] = useState(true);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev.slice(-4), `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testSessionEndpoint = async () => {
    try {
      const response = await fetch('/api/auth/session', {
        credentials: 'include'
      });
      
      if (response.status === 200) {
        const data = await response.json();
        addTestResult(`✅ Session valid - User: ${data.user?.username || 'Unknown'}`);
      } else if (response.status === 401) {
        const data = await response.json();
        addTestResult(`❌ Session invalid - ${data.message || 'No message'}`);
      } else {
        addTestResult(`⚠️ Unexpected status: ${response.status}`);
      }
    } catch (error) {
      addTestResult(`🔥 Error: ${error}`);
    }
  };

  const simulateApiCall = async () => {
    try {
      const response = await fetch('/api/auth/profile', {
        credentials: 'include'
      });
      
      if (response.status === 200) {
        addTestResult(`✅ Profile access successful`);
      } else if (response.status === 401) {
        const data = await response.json();
        if (data.code === 'SESSION_INVALID') {
          addTestResult(`🔒 Session invalidated! Message: ${data.message}`);
        } else {
          addTestResult(`❌ Auth required: ${data.error}`);
        }
      } else {
        addTestResult(`⚠️ Profile status: ${response.status}`);
      }
    } catch (error) {
      addTestResult(`🔥 Profile error: ${error}`);
    }
  };

  return (
    <>
      <SessionMonitorIndicator show={showMonitor} />
      
      <Paper p="md" withBorder style={{ margin: '1rem', maxWidth: '600px' }}>
        <Stack gap="md">
          <Group justify="space-between" align="center">
            <Text size="lg" fw={600}>Session Invalidation Test Panel</Text>
            <Badge color={showMonitor ? 'green' : 'gray'} variant="light">
              Monitor: {showMonitor ? 'ON' : 'OFF'}
            </Badge>
          </Group>
          
          <Text size="sm" c="dimmed">
            This panel helps test immediate session invalidation. The session monitor checks every 10 seconds.
          </Text>

          <Group>
            <Button 
              size="sm" 
              variant="light" 
              onClick={testSessionEndpoint}
            >
              Test Session Endpoint
            </Button>
            
            <Button 
              size="sm" 
              variant="light" 
              color="blue"
              onClick={simulateApiCall}
            >
              Test Profile API
            </Button>
            
            <Button 
              size="sm" 
              variant="light" 
              color="gray"
              onClick={() => setShowMonitor(!showMonitor)}
            >
              Toggle Monitor
            </Button>
          </Group>

          <Stack gap="xs">
            <Text size="sm" fw={500}>Test Results:</Text>
            {testResults.length === 0 ? (
              <Text size="xs" c="dimmed">No tests run yet</Text>
            ) : (
              testResults.map((result, index) => (
                <Code key={index} block>
                  {result}
                </Code>
              ))
            )}
          </Stack>

          <Paper p="sm" bg="blue.0" style={{ marginTop: '1rem' }}>
            <Text size="sm" fw={500} mb="xs">How to Test:</Text>
            <Stack gap="xs">
              <Text size="xs">1. Open this page in two different browsers</Text>
              <Text size="xs">2. Login with the same account in both browsers</Text>
              <Text size="xs">3. Watch the session monitor - it should detect invalidation within 10 seconds</Text>
              <Text size="xs">4. You should see the security notification appear automatically</Text>
              <Text size="xs">5. No need to click anything - the warning appears immediately!</Text>
            </Stack>
          </Paper>
        </Stack>
      </Paper>
    </>
  );
}
