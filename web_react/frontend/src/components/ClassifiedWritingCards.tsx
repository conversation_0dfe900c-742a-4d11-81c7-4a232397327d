import React, { useState, useEffect } from 'react';
import {
  Card,
  Text,
  Button,
  Group,
  Stack,
  Badge,
  SimpleGrid,
  Loader,
  Alert,
  Box,
  Progress,
  Tooltip,
  ActionIcon,
  Divider
} from '@mantine/core';
import {
  IconPencil,
  IconInfoCircle,
  IconPlayerPlay,
  IconLock,
  IconTags,
  IconFileText,
  IconUsers,
  IconEdit
} from '@tabler/icons-react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { classifiedWritingTranslationService, type TacheTranslations } from '../services/classifiedWritingTranslationService';
import { useAuthStore } from '../store/useAuthStore';
import { useThemeColors } from '../store/useThemeStore';
import { classifiedWritingService, type TacheCard, type CardsResponse } from '../services/classifiedWritingService';

// Translation helpers
const getTacheTitle = (tacheNumber: number): string => {
  switch (tacheNumber) {
    case 1:
      return 'Tâche 1';
    case 2:
      return 'Tâche 2';
    case 3:
      return 'Tâche 3';
    default:
      return `Tâche ${tacheNumber}`;
  }
};

const getTacheDescription = (tacheNumber: number, t: any): string => {
  switch (tacheNumber) {
    case 1:
      return t('classifiedWriting.taches.1.description', 'Personal message writing (60-120 words)');
    case 2:
      return t('classifiedWriting.taches.2.description', 'Informative article writing (120-150 words)');
    case 3:
      return t('classifiedWriting.taches.3.description', 'Argumentative essay writing (120-180 words)');
    default:
      return 'Writing task description';
  }
};

// Helper functions to get translated names using the translation service
const getTranslatedTopicName = (topicName: string, translations: TacheTranslations | undefined, currentLanguage: string): string => {
  return classifiedWritingTranslationService.getTopicTranslation(topicName, currentLanguage, translations);
};

const getTranslatedSubtopicName = (subtopicName: string, translations: TacheTranslations | undefined, currentLanguage: string): string => {
  return classifiedWritingTranslationService.getSubtopicTranslation(subtopicName, currentLanguage, translations);
};

interface ClassifiedWritingCardsProps {
  onCardClick?: (tacheNumber: number) => void;
}

interface TacheCardComponentProps {
  card: TacheCard;
  isAuthenticated: boolean;
  userIsPremium: boolean;
  onCardClick?: (tacheNumber: number) => void;
  translations?: TacheTranslations | null;
}

function TacheCardComponent({ card, isAuthenticated, userIsPremium, onCardClick, translations }: TacheCardComponentProps) {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const themeColors = useThemeColors();

  // Get tâche-specific colors
  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = getTacheColor(card.tache_number);

  // Handle card click
  const handleCardClick = () => {
    if (card.total_tasks === 0) {
      // No tasks available, show message
      return;
    }

    if (!isAuthenticated) {
      navigate('/login', {
        state: {
          message: t('classifiedWriting.auth.loginRequired', 'Vous devez vous connecter pour accéder aux tâches classifiées'),
          from: `/writing`  // Navigate back to writing page since we're integrated
        }
      });
      return;
    }

    if (onCardClick) {
      onCardClick(card.tache_number);
    } else {
      // Navigate to the classified writing test page
      navigate(`/writing/classified_tache${card.tache_number}`);
    }
  };



  return (
    <Card
      withBorder
      shadow="sm"
      radius="md"
      style={{
        height: '100%',
        transition: 'all 0.2s ease',
        backgroundColor: themeColors.surface,
        borderColor: `${tacheColor}30`,
        border: `2px solid ${tacheColor}20`
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-2px)';
        e.currentTarget.style.boxShadow = `0 4px 12px ${tacheColor}20`;
        e.currentTarget.style.borderColor = `${tacheColor}50`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.12)';
        e.currentTarget.style.borderColor = `${tacheColor}30`;
      }}
    >
      <Stack gap="md" style={{ height: '100%', minHeight: '200px' }}>
        {/* Header */}
        <Group justify="space-between" align="flex-start" style={{ minHeight: '60px' }}>
          <Group gap="sm">
            <IconEdit size={24} color={tacheColor} />
            <Box style={{ flex: 1 }}>
              <Text fw={600} size="lg" c={tacheColor}>
                {getTacheTitle(card.tache_number)}
              </Text>
              <Text size="xs" c="dimmed" style={{
                minHeight: '32px',
                display: 'flex',
                alignItems: 'center',
                lineHeight: 1.4
              }}>
                {getTacheDescription(card.tache_number, t)}
              </Text>
            </Box>
          </Group>
        </Group>

        {/* Simplified Statistics */}
        <Stack gap="sm">
          <Group justify="space-between">
            <Group gap="xs">
              <IconFileText size={16} color={themeColors.textSecondary} />
              <Text size="sm" c="dimmed">
                {t('classifiedWriting.card.totalTasks', 'Total tasks')}
              </Text>
            </Group>
            <Text size="sm" fw={500}>
              {card.total_tasks}
            </Text>
          </Group>

          <Group justify="space-between">
            <Group gap="xs">
              <IconUsers size={16} color={themeColors.textSecondary} />
              <Text size="sm" c="dimmed">
                {t('classifiedWriting.card.uniqueTasks', 'Unique tasks')}
              </Text>
            </Group>
            <Text size="sm" fw={500} c={tacheColor}>
              {card.unique_tasks}
            </Text>
          </Group>
        </Stack>

        {/* Action Button */}
        <Box mt="auto">
          {card.total_tasks === 0 ? (
            <Button
              fullWidth
              variant="outline"
              size="sm"
              disabled
              style={{
                borderColor: themeColors.border,
                color: themeColors.textSecondary,
              }}
            >
              {t('classifiedWriting.card.noTasks', 'No tasks available')}
            </Button>
          ) : isAuthenticated && userIsPremium ? (
            <Button
              fullWidth
              color={tacheColor}
              leftSection={<IconPlayerPlay size={16} />}
              size="sm"
              variant="light"
              onClick={handleCardClick}
            >
              {t('classifiedWriting.card.explore', 'Explore')}
            </Button>
          ) : isAuthenticated ? (
            <Button
              fullWidth
              color="orange"
              leftSection={<IconLock size={16} />}
              size="sm"
              variant="outline"
              disabled
            >
              {t('membership.required.button', 'Abonnement requis')}
            </Button>
          ) : (
            <Button
              fullWidth
              color="orange"
              leftSection={<IconLock size={16} />}
              size="sm"
              variant="outline"
              onClick={handleCardClick}
            >
              {t('classifiedWriting.card.loginRequired', 'Login Required')}
            </Button>
          )}
        </Box>
      </Stack>
    </Card>
  );
}

export function ClassifiedWritingCards({ onCardClick }: ClassifiedWritingCardsProps) {
  const { t, i18n } = useTranslation();
  const { isAuthenticated, isMember, isMembershipExpired } = useAuthStore();
  const themeColors = useThemeColors();
  const [cardsData, setCardsData] = useState<CardsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [allTranslations, setAllTranslations] = useState<TacheTranslations | null>(null);

  // Check if user is premium
  const isPremiumUser = isAuthenticated && isMember() && !isMembershipExpired();

  useEffect(() => {
    const loadCardsAndTranslations = async () => {
      try {
        setLoading(true);
        setError(null);

        // Load cards and translations in parallel
        const [cardsData, translationsData] = await Promise.all([
          classifiedWritingService.getCards(),
          classifiedWritingTranslationService.getAllTranslations()
        ]);

        setCardsData(cardsData);
        setAllTranslations(translationsData);

      } catch (err: any) {
        console.error('Error loading classified writing cards or translations:', err);
        setError(err.message || t('classifiedWriting.errors.loadCards'));
      } finally {
        setLoading(false);
      }
    };

    loadCardsAndTranslations();
  }, [t]);

  if (loading) {
    return (
      <Box ta="center" py="xl">
        <Loader size="lg" />
        <Text size="sm" c="dimmed" mt="md">
          {t('classifiedWriting.loading.cards')}
        </Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        variant="light"
        color="red"
        title={t('common.error')}
        icon={<IconInfoCircle />}
      >
        {error}
      </Alert>
    );
  }

  if (!cardsData || cardsData.cards.length === 0) {
    return (
      <Alert
        variant="light"
        color="yellow"
        title={t('classifiedWriting.noData.title')}
        icon={<IconInfoCircle />}
      >
        {t('classifiedWriting.noData.message')}
      </Alert>
    );
  }

  return (
    <Stack gap="lg">
      {/* Info message about practice by tâche */}
      <Alert
        variant="light"
        color="blue"
        icon={<IconInfoCircle size={16} />}
        mb="md"
        style={{
          backgroundColor: themeColors.surface,
          borderColor: themeColors.primary,
          color: themeColors.textPrimary
        }}
      >
        <Text size="sm" style={{ color: themeColors.textPrimary }}>
          {t('classifiedWriting.infoMessage',
            'Practice by tâche contains deduplicated and classified writing tasks from all tests. Each tâche covers specific task types and themes, allowing you to focus on your target writing skills.'
          )}
        </Text>
      </Alert>

      {/* Tâche Cards */}
      <SimpleGrid
        cols={{ base: 1, sm: 2, md: 3 }}
        spacing="lg"
        style={{ alignItems: 'stretch' }}
      >
        {cardsData.cards.map((card) => (
          <TacheCardComponent
            key={card.tache_number}
            card={card}
            isAuthenticated={isAuthenticated}
            userIsPremium={isPremiumUser}
            onCardClick={onCardClick}
            translations={allTranslations}
          />
        ))}
      </SimpleGrid>
    </Stack>
  );
}
