import React, { useState, useEffect } from 'react';
import { Box, Text, Transition } from '@mantine/core';
import { IconArrowUp } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';

interface NavigationHintProps {
  isTestPage: boolean;
  testPageType: string | null;
}

export function NavigationHint({ isTestPage, testPageType }: NavigationHintProps) {
  const [showHint, setShowHint] = useState(false);
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  useEffect(() => {
    if (isTestPage) {
      // Show hint immediately when entering a test page (since nav is hidden by default)
      setShowHint(true);

      // Hide hint after 4 seconds
      const hideTimer = setTimeout(() => {
        setShowHint(false);
      }, 4000);

      return () => {
        clearTimeout(hideTimer);
      };
    } else {
      setShowHint(false);
    }
  }, [isTestPage]);

  if (!isTestPage) return null;

  return (
    <Transition
      mounted={showHint}
      transition="slide-down"
      duration={300}
      timingFunction="ease-out"
    >
      {(styles) => (
        <Box
          style={{
            ...styles,
            position: 'fixed',
            top: '20px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1000,
            background: themeColors.surface,
            border: `1px solid ${themeColors.border}`,
            borderRadius: '12px',
            padding: '12px 20px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
            backdropFilter: 'blur(12px)',
            maxWidth: '320px',
            textAlign: 'center',
          }}
        >
          <Box style={{ display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' }}>
            <IconArrowUp size={16} color={themeColors.textSecondary} />
            <Text size="sm" c={themeColors.textSecondary} fw={500}>
              {t('navigation.hint.moveMouseToTop', 'Move mouse to top to show navigation')}
            </Text>
          </Box>
        </Box>
      )}
    </Transition>
  );
}
