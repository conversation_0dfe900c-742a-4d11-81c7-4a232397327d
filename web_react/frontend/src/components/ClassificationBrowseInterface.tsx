import React, { useState, useEffect } from 'react';
import {
  Container,
  Title,
  Text,
  Button,
  Group,
  Stack,
  Card,
  Badge,
  SimpleGrid,
  Loader,
  Alert,
  Box,
  Accordion,
  TextInput,
  Select,
  Pagination,
  Breadcrumbs,
  Anchor,
  ActionIcon,
  Tooltip,
  Divider,
  Progress,
  Collapse,
  Paper
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconChevronRight,
  IconChevronDown,
  IconChevronUp,
  IconInfoCircle,
  IconTags,
  IconFileText,
  IconUsers,
  IconArrowLeft,
  IconPlayerPlay,
  IconEye
} from '@tabler/icons-react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useDisclosure } from '@mantine/hooks';
import { useThemeColors } from '../store/useThemeStore';
import { useAuthStore } from '../store/useAuthStore';
import {
  classifiedWritingService,
  type TacheOverview,
  type TopicDetails,
  type SubtopicTasksResponse,
  type TaskEntry,
  type SearchResponse,
  type SearchFilters
} from '../services/classifiedWritingService';

interface ClassificationBrowseInterfaceProps {
  tacheNumber?: number;
  topicName?: string;
  subtopicId?: string;
  mode?: 'overview' | 'topic' | 'subtopic' | 'search';
}

interface TopicCardProps {
  topic: any;
  tacheNumber: number;
  onTopicClick: (topicName: string) => void;
}

function TopicCard({ topic, tacheNumber, onTopicClick }: TopicCardProps) {
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const [expanded, { toggle }] = useDisclosure(false);

  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = getTacheColor(tacheNumber);

  return (
    <Card
      withBorder
      shadow="sm"
      radius="md"
      style={{
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        backgroundColor: themeColors.surface,
        borderColor: `${tacheColor}20`
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.borderColor = `${tacheColor}40`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.borderColor = `${tacheColor}20`;
      }}
    >
      <Stack gap="sm">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Box style={{ flex: 1 }}>
            <Group gap="sm" mb="xs">
              <Text fw={600} size="md" c={tacheColor} lineClamp={1}>
                {topic.topic_name.replace(/_/g, ' ')}
              </Text>
              <Badge size="sm" variant="light" color={tacheColor}>
                {topic.unique_tasks}
              </Badge>
            </Group>
            
            {/* Statistics */}
            <Group gap="md" mb="sm">
              <Group gap="xs">
                <IconFileText size={14} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {topic.total_tasks} {t('classifiedWriting.card.totalTasks')}
                </Text>
              </Group>
              <Group gap="xs">
                <IconTags size={14} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {topic.subtopics?.length || 0} {t('classifiedWriting.topic.subtopics')}
                </Text>
              </Group>
            </Group>

            {/* Keywords */}
            {topic.keywords && topic.keywords.length > 0 && (
              <Group gap="xs" mb="sm">
                {topic.keywords.slice(0, 3).map((keyword: string, index: number) => (
                  <Badge key={index} size="xs" variant="outline" color="gray">
                    {keyword}
                  </Badge>
                ))}
                {topic.keywords.length > 3 && (
                  <Text size="xs" c="dimmed">
                    +{topic.keywords.length - 3}
                  </Text>
                )}
              </Group>
            )}
          </Box>

          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              toggle();
            }}
          >
            {expanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </ActionIcon>
        </Group>

        {/* Subtopics Preview */}
        <Collapse in={expanded}>
          <Divider mb="sm" />
          <Stack gap="xs">
            <Text size="xs" fw={500} c="dimmed">
              {t('classifiedWriting.topic.subtopics')}:
            </Text>
            {topic.subtopics && topic.subtopics.length > 0 ? (
              <Stack gap="xs">
                {topic.subtopics.slice(0, 3).map((subtopic: any, index: number) => (
                  <Group key={index} justify="space-between">
                    <Text size="xs" lineClamp={1} style={{ flex: 1 }}>
                      {subtopic.subtopic_name.replace(/_/g, ' ')}
                    </Text>
                    <Badge size="xs" variant="light" color={tacheColor}>
                      {subtopic.unique_task_count}
                    </Badge>
                  </Group>
                ))}
                {topic.subtopics.length > 3 && (
                  <Text size="xs" c="dimmed" ta="center">
                    +{topic.subtopics.length - 3} {t('classifiedWriting.card.moreTopics')}
                  </Text>
                )}
              </Stack>
            ) : (
              <Text size="xs" c="dimmed">
                {t('classifiedWriting.noData.message')}
              </Text>
            )}
          </Stack>
        </Collapse>

        {/* Action Button */}
        <Button
          size="xs"
          variant="light"
          color={tacheColor}
          leftSection={<IconEye size={14} />}
          onClick={(e) => {
            e.stopPropagation();
            onTopicClick(topic.topic_name);
          }}
          fullWidth
        >
          {t('classifiedWriting.card.explore')}
        </Button>
      </Stack>
    </Card>
  );
}

interface SubtopicCardProps {
  subtopic: any;
  tacheNumber: number;
  onSubtopicClick: (subtopicId: string) => void;
}

function SubtopicCard({ subtopic, tacheNumber, onSubtopicClick }: SubtopicCardProps) {
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = getTacheColor(tacheNumber);

  return (
    <Card
      withBorder
      shadow="sm"
      radius="md"
      style={{
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        backgroundColor: themeColors.surface,
        borderColor: `${tacheColor}20`
      }}
      onClick={() => onSubtopicClick(subtopic.id)}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.borderColor = `${tacheColor}40`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.borderColor = `${tacheColor}20`;
      }}
    >
      <Stack gap="sm">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Box style={{ flex: 1 }}>
            <Text fw={600} size="sm" c={tacheColor} lineClamp={2} mb="xs">
              {subtopic.subtopic_name.replace(/_/g, ' ')}
            </Text>
            
            {/* Statistics */}
            <Group gap="md" mb="sm">
              <Group gap="xs">
                <IconFileText size={12} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {subtopic.task_count}
                </Text>
              </Group>
              <Group gap="xs">
                <IconUsers size={12} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {subtopic.unique_task_count} {t('classifiedWriting.card.uniqueTasks')}
                </Text>
              </Group>
            </Group>

            {/* Similarity Score */}
            {subtopic.template_similarity && (
              <Box mb="sm">
                <Group justify="space-between" mb="xs">
                  <Text size="xs" c="dimmed">
                    {t('classifiedWriting.subtopic.similarity')}
                  </Text>
                  <Text size="xs" fw={500}>
                    {(subtopic.template_similarity * 100).toFixed(1)}%
                  </Text>
                </Group>
                <Progress
                  value={subtopic.template_similarity * 100}
                  size="xs"
                  color={tacheColor}
                  radius="xl"
                />
              </Box>
            )}

            {/* Keywords */}
            {subtopic.keywords && subtopic.keywords.length > 0 && (
              <Group gap="xs">
                {subtopic.keywords.slice(0, 2).map((keyword: string, index: number) => (
                  <Badge key={index} size="xs" variant="outline" color="gray">
                    {keyword}
                  </Badge>
                ))}
                {subtopic.keywords.length > 2 && (
                  <Text size="xs" c="dimmed">
                    +{subtopic.keywords.length - 2}
                  </Text>
                )}
              </Group>
            )}
          </Box>

          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
          >
            <IconChevronRight size={14} />
          </ActionIcon>
        </Group>
      </Stack>
    </Card>
  );
}

interface TaskCardProps {
  task: TaskEntry;
  tacheNumber: number;
  onTaskClick: (representativeId: string) => void;
}

function TaskCard({ task, tacheNumber, onTaskClick }: TaskCardProps) {
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = getTacheColor(tacheNumber);

  return (
    <Card
      withBorder
      shadow="sm"
      radius="md"
      style={{
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        backgroundColor: themeColors.surface,
        borderColor: `${tacheColor}20`
      }}
      onClick={() => onTaskClick(task.representative_id)}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = 'translateY(-1px)';
        e.currentTarget.style.borderColor = `${tacheColor}40`;
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = 'translateY(0)';
        e.currentTarget.style.borderColor = `${tacheColor}20`;
      }}
    >
      <Stack gap="sm">
        {/* Header */}
        <Group justify="space-between" align="flex-start">
          <Box style={{ flex: 1 }}>
            <Group gap="xs" mb="xs">
              <Text size="xs" c="dimmed">
                {task.representative_id}
              </Text>
              {task.is_duplicate_group && (
                <Badge size="xs" variant="light" color="orange">
                  {task.duplicate_count} {t('classifiedWriting.task.duplicates')}
                </Badge>
              )}
            </Group>

            {/* Task Content Preview */}
            <Text size="sm" lineClamp={3} mb="sm">
              {task.task_content}
            </Text>

            {/* Metadata */}
            <Group gap="md" mb="sm">
              <Group gap="xs">
                <IconFileText size={12} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {task.month_years?.length || 0} {t('classifiedWriting.task.months')}
                </Text>
              </Group>
              <Group gap="xs">
                <IconTags size={12} color={themeColors.textSecondary} />
                <Text size="xs" c="dimmed">
                  {task.combination_numbers?.length || 0} {t('classifiedWriting.task.combinations')}
                </Text>
              </Group>
            </Group>

            {/* Classification Context */}
            {task.classification_context && (
              <Group gap="xs">
                <Badge size="xs" variant="outline" color={tacheColor}>
                  {task.classification_context.main_topic_name.replace(/_/g, ' ')}
                </Badge>
                <Badge size="xs" variant="outline" color="gray">
                  {task.classification_context.subtopic_name.replace(/_/g, ' ')}
                </Badge>
              </Group>
            )}
          </Box>

          <ActionIcon
            variant="subtle"
            color="gray"
            size="sm"
          >
            <IconPlayerPlay size={14} />
          </ActionIcon>
        </Group>
      </Stack>
    </Card>
  );
}

export function ClassificationBrowseInterface({
  tacheNumber,
  topicName,
  subtopicId,
  mode = 'overview'
}: ClassificationBrowseInterfaceProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const themeColors = useThemeColors();
  const { isAuthenticated } = useAuthStore();

  // State management
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [overviewData, setOverviewData] = useState<TacheOverview | null>(null);
  const [topicData, setTopicData] = useState<TopicDetails | null>(null);
  const [subtopicData, setSubtopicData] = useState<SubtopicTasksResponse | null>(null);
  const [searchData, setSearchData] = useState<SearchResponse | null>(null);

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState('');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [currentPage, setCurrentPage] = useState(1);

  // Load data based on mode
  useEffect(() => {
    const loadData = async () => {
      if (!tacheNumber && mode !== 'search') return;

      try {
        setLoading(true);
        setError(null);

        switch (mode) {
          case 'overview':
            if (tacheNumber) {
              const data = await classifiedWritingService.getTacheOverview(tacheNumber);
              setOverviewData(data);
            }
            break;

          case 'topic':
            if (tacheNumber && topicName) {
              const data = await classifiedWritingService.getTopicDetails(tacheNumber, topicName);
              setTopicData(data);
            }
            break;

          case 'subtopic':
            if (subtopicId) {
              const data = await classifiedWritingService.getSubtopicTasks(subtopicId, currentPage);
              setSubtopicData(data);
            }
            break;

          case 'search':
            if (searchQuery.trim()) {
              const data = await classifiedWritingService.searchTasks(
                searchQuery,
                searchFilters,
                currentPage
              );
              setSearchData(data);
            }
            break;
        }
      } catch (err: any) {
        console.error(`Error loading ${mode} data:`, err);
        setError(err.message || t('classifiedWriting.errors.loadOverview'));
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [mode, tacheNumber, topicName, subtopicId, searchQuery, searchFilters, currentPage, t]);

  // Navigation handlers
  const handleTopicClick = (topicName: string) => {
    navigate(`/writing/classified_tache${tacheNumber}/${encodeURIComponent(topicName)}`);
  };

  const handleSubtopicClick = (subtopicId: string) => {
    navigate(`/writing/classified_subtopic/${encodeURIComponent(encodeURIComponent(subtopicId))}`);
  };

  const handleTaskClick = (representativeId: string) => {
    navigate(`/writing/classified_task/${representativeId}`);
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    setCurrentPage(1);
    // The useEffect will handle the actual search
  };

  // Get tâche color
  const getTacheColor = (tacheNumber: number) => {
    const colors = {
      1: '#fd7e14', // Orange
      2: '#20c997', // Teal
      3: '#6f42c1'  // Purple
    };
    return colors[tacheNumber as keyof typeof colors] || '#fd7e14';
  };

  const tacheColor = tacheNumber ? getTacheColor(tacheNumber) : '#fd7e14';

  // Breadcrumb items
  const getBreadcrumbs = () => {
    const items = [
      { title: t('classifiedWriting.title'), href: '/writing' }
    ];

    if (tacheNumber) {
      items.push({
        title: classifiedWritingService.formatTacheTitle(tacheNumber),
        href: `/writing/classified_tache${tacheNumber}`
      });
    }

    if (topicName) {
      items.push({
        title: topicName.replace(/_/g, ' '),
        href: `/writing/classified_tache${tacheNumber}/${encodeURIComponent(topicName)}`
      });
    }

    if (subtopicData) {
      items.push({
        title: subtopicData.subtopic.subtopic_name.replace(/_/g, ' '),
        href: `/writing/classified_subtopic/${subtopicData.subtopic.id}`
      });
    }

    return items;
  };

  // Loading state
  if (loading) {
    return (
      <Container size="xl" py="xl">
        <Box ta="center">
          <Loader size="lg" />
          <Text size="sm" c="dimmed" mt="md">
            {t('classifiedWriting.loading.overview')}
          </Text>
        </Box>
      </Container>
    );
  }

  // Error state
  if (error) {
    return (
      <Container size="xl" py="xl">
        <Alert
          variant="light"
          color="red"
          title={t('common.error')}
          icon={<IconInfoCircle />}
        >
          {error}
        </Alert>
      </Container>
    );
  }

  // Render based on mode
  switch (mode) {
    case 'overview':
      return (
        <Container size="xl" py="xl">
          {/* Breadcrumbs */}
          <Breadcrumbs mb="lg">
            {getBreadcrumbs().map((item, index) => (
              <Anchor
                key={index}
                component={Link}
                to={item.href}
                size="sm"
                c={index === getBreadcrumbs().length - 1 ? tacheColor : undefined}
              >
                {item.title}
              </Anchor>
            ))}
          </Breadcrumbs>

          {/* Header */}
          <Group justify="space-between" mb="xl">
            <Box>
              <Title order={2} c={tacheColor} mb="xs">
                {classifiedWritingService.formatTacheTitle(tacheNumber!)}
              </Title>
              <Text c="dimmed">
                {classifiedWritingService.getTacheDescription(tacheNumber!)}
              </Text>
            </Box>
            <Button
              leftSection={<IconSearch size={16} />}
              variant="light"
              color={tacheColor}
              onClick={() => navigate('/writing/classified_search')}
            >
              {t('classifiedWriting.search.placeholder')}
            </Button>
          </Group>

          {/* Metadata */}
          {overviewData && (
            <Paper withBorder p="md" radius="md" mb="xl">
              <Title order={4} mb="md">
                {t('classifiedWriting.overview.metadata')}
              </Title>
              <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="lg">
                <Box>
                  <Text size="sm" c="dimmed" mb="xs">
                    {t('classifiedWriting.card.totalTasks')}
                  </Text>
                  <Text fw={700} size="xl" c={tacheColor}>
                    {overviewData.metadata.total_tasks}
                  </Text>
                </Box>
                <Box>
                  <Text size="sm" c="dimmed" mb="xs">
                    {t('classifiedWriting.card.uniqueTasks')}
                  </Text>
                  <Text fw={700} size="xl" c={tacheColor}>
                    {overviewData.metadata.unique_tasks}
                  </Text>
                </Box>
                <Box>
                  <Text size="sm" c="dimmed" mb="xs">
                    {t('classifiedWriting.card.topics')}
                  </Text>
                  <Text fw={700} size="xl" c={tacheColor}>
                    {overviewData.metadata.n_main_topics}
                  </Text>
                </Box>
                <Box>
                  <Text size="sm" c="dimmed" mb="xs">
                    {t('classifiedWriting.overview.method')}
                  </Text>
                  <Text fw={500}>
                    {overviewData.metadata.method || '-'}
                  </Text>
                </Box>
              </SimpleGrid>
            </Paper>
          )}

          {/* Topics */}
          {overviewData && Array.isArray(overviewData.topics) && overviewData.topics.length > 0 ? (
            <>
              <Title order={3} mb="lg">
                {t('classifiedWriting.overview.topics')}
              </Title>
              <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                {Array.isArray(overviewData.topics) && overviewData.topics.map((topic: any) => (
                  <TopicCard
                    key={topic.id}
                    topic={topic}
                    tacheNumber={tacheNumber!}
                    onTopicClick={handleTopicClick}
                  />
                ))}
              </SimpleGrid>
            </>
          ) : (
            <Alert
              variant="light"
              color="yellow"
              title={t('classifiedWriting.noData.title')}
              icon={<IconInfoCircle />}
            >
              {t('classifiedWriting.noData.message')}
            </Alert>
          )}
        </Container>
      );

    case 'topic':
      return (
        <Container size="xl" py="xl">
          {/* Breadcrumbs */}
          <Breadcrumbs mb="lg">
            {getBreadcrumbs().map((item, index) => (
              <Anchor
                key={index}
                component={Link}
                to={item.href}
                size="sm"
                c={index === getBreadcrumbs().length - 1 ? tacheColor : undefined}
              >
                {item.title}
              </Anchor>
            ))}
          </Breadcrumbs>

          {/* Header */}
          {topicData && (
            <>
              <Group justify="space-between" mb="xl">
                <Box>
                  <Title order={2} c={tacheColor} mb="xs">
                    {topicData.topic.topic_name.replace(/_/g, ' ')}
                  </Title>
                  <Text c="dimmed">
                    {topicData.topic.description || classifiedWritingService.getTacheDescription(tacheNumber!)}
                  </Text>
                </Box>
                <Button
                  leftSection={<IconArrowLeft size={16} />}
                  variant="light"
                  color={tacheColor}
                  onClick={() => navigate(`/writing/classified_tache${tacheNumber}`)}
                >
                  {t('classifiedWriting.tache.title', { number: tacheNumber })}
                </Button>
              </Group>

              {/* Topic Metadata */}
              <Paper withBorder p="md" radius="md" mb="xl">
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.card.totalTasks')}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {topicData.topic.total_tasks}
                    </Text>
                  </Box>
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.card.uniqueTasks')}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {topicData.topic.unique_tasks}
                    </Text>
                  </Box>
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.topic.subtopics')}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {Array.isArray(topicData.topic.subtopics) ? topicData.topic.subtopics.length : 0}
                    </Text>
                  </Box>
                </SimpleGrid>

                {/* Keywords */}
                {topicData.topic.keywords && topicData.topic.keywords.length > 0 && (
                  <Box mt="lg">
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.topic.keywords')}
                    </Text>
                    <Group gap="xs">
                      {topicData.topic.keywords.map((keyword, index) => (
                        <Badge key={index} size="sm" variant="outline" color={tacheColor}>
                          {keyword}
                        </Badge>
                      ))}
                    </Group>
                  </Box>
                )}
              </Paper>

              {/* Subtopics */}
              {Array.isArray(topicData.topic.subtopics) && topicData.topic.subtopics.length > 0 ? (
                <>
                  <Title order={3} mb="lg">
                    {t('classifiedWriting.topic.subtopics')}
                  </Title>
                  <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                    {topicData.topic.subtopics.map((subtopic: any) => (
                      <SubtopicCard
                        key={subtopic.id}
                        subtopic={subtopic}
                        tacheNumber={tacheNumber!}
                        onSubtopicClick={handleSubtopicClick}
                      />
                    ))}
                  </SimpleGrid>
                </>
              ) : (
                <Alert
                  variant="light"
                  color="yellow"
                  title={t('classifiedWriting.noData.title')}
                  icon={<IconInfoCircle />}
                >
                  {t('classifiedWriting.noData.message')}
                </Alert>
              )}
            </>
          )}
        </Container>
      );

    case 'subtopic':
      return (
        <Container size="xl" py="xl">
          {/* Breadcrumbs */}
          <Breadcrumbs mb="lg">
            {getBreadcrumbs().map((item, index) => (
              <Anchor
                key={index}
                component={Link}
                to={item.href}
                size="sm"
                c={index === getBreadcrumbs().length - 1 ? tacheColor : undefined}
              >
                {item.title}
              </Anchor>
            ))}
          </Breadcrumbs>

          {/* Header */}
          {subtopicData && (
            <>
              <Group justify="space-between" mb="xl">
                <Box>
                  <Title order={2} c={tacheColor} mb="xs">
                    {subtopicData.subtopic.subtopic_name.replace(/_/g, ' ')}
                  </Title>
                  <Text c="dimmed">
                    {subtopicData.subtopic.main_topic_name.replace(/_/g, ' ')} • {classifiedWritingService.formatTacheTitle(subtopicData.subtopic.tache_number)}
                  </Text>
                </Box>
                <Button
                  leftSection={<IconArrowLeft size={16} />}
                  variant="light"
                  color={tacheColor}
                  onClick={() => navigate(`/writing/classified_tache${subtopicData.subtopic.tache_number}/${encodeURIComponent(subtopicData.subtopic.main_topic_name)}`)}
                >
                  {subtopicData.subtopic.main_topic_name.replace(/_/g, ' ')}
                </Button>
              </Group>

              {/* Subtopic Metadata */}
              <Paper withBorder p="md" radius="md" mb="xl">
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.subtopic.taskCount')}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {subtopicData.subtopic.task_count}
                    </Text>
                  </Box>
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.subtopic.uniqueTaskCount')}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {subtopicData.subtopic.unique_task_count}
                    </Text>
                  </Box>
                  <Box>
                    <Text size="sm" c="dimmed" mb="xs">
                      {t('classifiedWriting.tache.title', { number: '' })}
                    </Text>
                    <Text fw={700} size="xl" c={tacheColor}>
                      {subtopicData.subtopic.tache_number}
                    </Text>
                  </Box>
                </SimpleGrid>
              </Paper>

              {/* Tasks */}
              {subtopicData.tasks.length > 0 ? (
                <>
                  <Group justify="space-between" mb="lg">
                    <Title order={3}>
                      {t('classifiedWriting.search.results')}
                    </Title>
                    <Text size="sm" c="dimmed">
                      {t('classifiedWriting.pagination.showing', {
                        start: (subtopicData.pagination.page - 1) * subtopicData.pagination.limit + 1,
                        end: Math.min(subtopicData.pagination.page * subtopicData.pagination.limit, subtopicData.pagination.total),
                        total: subtopicData.pagination.total
                      })}
                    </Text>
                  </Group>

                  <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg" mb="xl">
                    {subtopicData.tasks.map((task) => (
                      <TaskCard
                        key={task.representative_id}
                        task={task}
                        tacheNumber={subtopicData.subtopic.tache_number}
                        onTaskClick={handleTaskClick}
                      />
                    ))}
                  </SimpleGrid>

                  {/* Pagination */}
                  {subtopicData.pagination.pages > 1 && (
                    <Group justify="center">
                      <Pagination
                        value={currentPage}
                        onChange={setCurrentPage}
                        total={subtopicData.pagination.pages}
                        color={tacheColor}
                      />
                    </Group>
                  )}
                </>
              ) : (
                <Alert
                  variant="light"
                  color="yellow"
                  title={t('classifiedWriting.noData.title')}
                  icon={<IconInfoCircle />}
                >
                  {t('classifiedWriting.noData.message')}
                </Alert>
              )}
            </>
          )}
        </Container>
      );

    case 'search':
      return (
        <Container size="xl" py="xl">
          {/* Header */}
          <Title order={2} mb="xl">
            {t('classifiedWriting.search.placeholder')}
          </Title>

          {/* Search Form */}
          <Paper withBorder p="md" radius="md" mb="xl">
            <Stack gap="md">
              <Group gap="md">
                <TextInput
                  placeholder={t('classifiedWriting.search.placeholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftSection={<IconSearch size={16} />}
                  style={{ flex: 1 }}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleSearch();
                    }
                  }}
                />
                <Button
                  onClick={handleSearch}
                  disabled={!searchQuery.trim()}
                  leftSection={<IconSearch size={16} />}
                >
                  {t('classifiedWriting.search.placeholder')}
                </Button>
              </Group>

              {/* Filters */}
              <Group gap="md">
                <Select
                  placeholder={t('classifiedWriting.search.tacheFilter')}
                  data={[
                    { value: '1', label: 'Tâche 1' },
                    { value: '2', label: 'Tâche 2' },
                    { value: '3', label: 'Tâche 3' }
                  ]}
                  value={searchFilters.tache_number?.toString() || null}
                  onChange={(value) => setSearchFilters(prev => ({
                    ...prev,
                    tache_number: value ? parseInt(value) : undefined
                  }))}
                  clearable
                />
                <TextInput
                  placeholder={t('classifiedWriting.search.topicFilter')}
                  value={searchFilters.topic_name || ''}
                  onChange={(e) => setSearchFilters(prev => ({
                    ...prev,
                    topic_name: e.target.value || undefined
                  }))}
                  leftSection={<IconFilter size={16} />}
                />
              </Group>
            </Stack>
          </Paper>

          {/* Search Results */}
          {searchData && (
            <>
              <Group justify="space-between" mb="lg">
                <Title order={3}>
                  {t('classifiedWriting.search.results')}
                </Title>
                <Text size="sm" c="dimmed">
                  {t('classifiedWriting.pagination.showing', {
                    start: (searchData.pagination.page - 1) * searchData.pagination.limit + 1,
                    end: Math.min(searchData.pagination.page * searchData.pagination.limit, searchData.pagination.total),
                    total: searchData.pagination.total
                  })}
                </Text>
              </Group>

              {searchData.tasks.length > 0 ? (
                <>
                  <SimpleGrid cols={{ base: 1, sm: 2 }} spacing="lg" mb="xl">
                    {searchData.tasks.map((task) => (
                      <TaskCard
                        key={task.representative_id}
                        task={task}
                        tacheNumber={task.classification_context?.tache_number || 1}
                        onTaskClick={handleTaskClick}
                      />
                    ))}
                  </SimpleGrid>

                  {/* Pagination */}
                  {searchData.pagination.pages > 1 && (
                    <Group justify="center">
                      <Pagination
                        value={currentPage}
                        onChange={setCurrentPage}
                        total={searchData.pagination.pages}
                      />
                    </Group>
                  )}
                </>
              ) : (
                <Alert
                  variant="light"
                  color="yellow"
                  title={t('classifiedWriting.search.noResults')}
                  icon={<IconInfoCircle />}
                >
                  {t('classifiedWriting.noData.message')}
                </Alert>
              )}
            </>
          )}
        </Container>
      );

    default:
      return (
        <Container size="xl" py="xl">
          <Alert
            variant="light"
            color="red"
            title={t('common.error')}
            icon={<IconInfoCircle />}
          >
            Invalid mode: {mode}
          </Alert>
        </Container>
      );
  }
}
