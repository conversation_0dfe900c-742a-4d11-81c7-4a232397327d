import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Text, Button as <PERSON>tineButton, Group } from '@mantine/core';
import { IconHighlight, IconLanguage } from '@tabler/icons-react';
import type { HighlightData } from '../hooks/useHighlights';
import { TranslationPopup } from './TranslationPopup';
import { useTranslation } from 'react-i18next';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';

interface UnifiedTextInteractionProps {
  // Content props
  text?: string; // For passage text
  children?: React.ReactNode; // For question/transcript text
  
  // Highlighting props (optional - only for passage text)
  highlights?: HighlightData[];
  onAddHighlight?: (start: number, end: number, text: string) => void;
  onRemoveHighlight?: (highlight: HighlightData) => void;
  
  // Styling props
  fontSize?: string | number;
  lineHeight?: string | number;
  style?: React.CSSProperties;
  className?: string;
  component?: 'span' | 'div';
  size?: string | number;
  fw?: number;
  
  // Behavior props
  mode: 'passage' | 'question' | 'transcript'; // Determines available features
  questionIndex?: number; // For clearing state on question changes
  
  // Pass-through props for Text component
  [key: string]: any;
}

export function UnifiedTextInteraction({
  text,
  children,
  highlights = [],
  onAddHighlight,
  onRemoveHighlight,
  fontSize = 'lg',
  lineHeight = 1.8,
  style,
  className,
  component,
  size,
  fw,
  mode,
  questionIndex,
  ...otherProps
}: UnifiedTextInteractionProps) {
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();
  const [showTranslation, setShowTranslation] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const [translationPosition, setTranslationPosition] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // Custom selection menu state
  const [showSelectionMenu, setShowSelectionMenu] = useState(false);
  const [selectionMenuPosition, setSelectionMenuPosition] = useState({ x: 0, y: 0 });
  const [currentSelection, setCurrentSelection] = useState<{
    text: string;
    range: Range;
    isCrossContainer: boolean; // Track if selection spans multiple containers
  } | null>(null);

  // Clear translation popup when question changes
  useEffect(() => {
    if (showTranslation) {
      setShowTranslation(false);
      setSelectedText('');
      window.getSelection()?.removeAllRanges();
    }
  }, [questionIndex]);



  // Determine available features based on mode
  const canHighlight = mode === 'passage' && onAddHighlight && onRemoveHighlight;
  const canTranslate = true; // All modes support translation
  
  // Generate unique container ID
  const containerId = `unified-text-${mode}-${Math.random().toString(36).substring(2, 11)}`;

  // Close translation popup
  const closeTranslation = useCallback(() => {
    setShowTranslation(false);
    setSelectedText('');
    window.getSelection()?.removeAllRanges();
  }, []);

  // Close selection menu
  const closeSelectionMenu = useCallback(() => {
    setShowSelectionMenu(false);
    setCurrentSelection(null);
  }, []);

  // Smart positioning logic for selection menu - CONSISTENT regardless of translation popup state
  const calculateSelectionMenuPosition = useCallback((range: Range) => {
    const rect = range.getBoundingClientRect();
    const menuWidth = 200;
    const menuHeight = 50;
    const margin = 20;
    const offsetFromSelection = 8;

    // Get viewport dimensions (no scroll offset needed for viewport-relative positioning)
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // CONSISTENT positioning logic - always use the same calculation
    // regardless of whether translation popup is open or not
    let x = rect.left + (rect.width / 2);
    let y = rect.bottom + offsetFromSelection;

    // Adjust for different modes - SAME logic always
    if (mode === 'passage') {
      y = rect.top - menuHeight - offsetFromSelection; // Position above for passage
    }

    // Smart horizontal positioning - SAME logic always
    if (x + menuWidth / 2 > viewportWidth - margin) {
      // Would go off right edge - align to right edge of selection
      x = rect.right - menuWidth / 2;
    }
    if (x - menuWidth / 2 < margin) {
      // Would go off left edge - align to left edge of selection
      x = rect.left + menuWidth / 2;
    }
    // Final horizontal bounds check - SAME logic always
    x = Math.max(margin + menuWidth / 2, Math.min(viewportWidth - margin - menuWidth / 2, x));

    // Smart vertical positioning with fallback
    if (y + menuHeight > viewportHeight - margin) {
      // Would go off bottom - try positioning above
      const aboveY = rect.top - menuHeight - offsetFromSelection;
      if (aboveY >= margin) {
        y = aboveY;
      } else {
        // Not enough space above or below - position at best available spot
        y = Math.max(margin, Math.min(viewportHeight - menuHeight - margin, rect.top - menuHeight / 2));
      }
    }
    if (y < margin) {
      // Would go off top - position below selection
      y = rect.bottom + offsetFromSelection;
    }

    // Check for UI element overlaps and adjust if needed
    // CRITICAL: Do NOT avoid translation popup to ensure consistent positioning
    const avoidUIElements = (x: number, y: number) => {
      const uiSelectors = [
        '.mantine-AppShell-header',
        '.mantine-AppShell-navbar',
        '[data-floating-ui-portal]', // Keep for other floating elements
        '.transcript-controls',
        '.question-navigation',
        '.mantine-Modal-root'
      ];

      for (const selector of uiSelectors) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          // CRITICAL: Skip translation popup to ensure consistent positioning
          if (element.classList.contains('translation-popup')) {
            return; // Skip this element - don't avoid translation popup
          }

          const elementRect = element.getBoundingClientRect();
          const menuRect = {
            left: x - menuWidth / 2,
            top: y,
            right: x + menuWidth / 2,
            bottom: y + menuHeight,
          };

          // Check overlap
          const overlaps = !(
            menuRect.right < elementRect.left ||
            menuRect.left > elementRect.right ||
            menuRect.bottom < elementRect.top ||
            menuRect.top > elementRect.bottom
          );

          if (overlaps) {
            // Try to reposition to avoid overlap
            if (elementRect.bottom + offsetFromSelection + menuHeight < viewportHeight - margin) {
              y = elementRect.bottom + offsetFromSelection;
            } else if (elementRect.top - offsetFromSelection - menuHeight > margin) {
              y = elementRect.top - offsetFromSelection - menuHeight;
            }
          }
        });
      }
      return { x, y };
    };

    const finalPosition = avoidUIElements(x, y);
    return finalPosition;
  }, [mode]);

  // Handle text selection with validation
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      closeSelectionMenu();
      return;
    }

    const selectedText = selection.toString().trim();
    if (!selectedText || selectedText.length < 2) { // Minimum 2 characters
      closeSelectionMenu();
      return;
    }

    // Validate that selection is within our container
    const container = containerRef.current;
    if (!container) {
      closeSelectionMenu();
      return;
    }

    const range = selection.getRangeAt(0);

    // Detect if selection spans multiple containers (cross-container selection)
    // A selection is cross-container if:
    // 1. The common ancestor is not within our container, OR
    // 2. Start and end containers are not both within our container
    const isCrossContainer = !container.contains(range.commonAncestorContainer) ||
                            !(container.contains(range.startContainer) && container.contains(range.endContainer));

    // For cross-container selections, we allow them but only show translate button
    if (!isCrossContainer) {
      // Normal single-container selection - check if it's within our container
      if (!container.contains(range.commonAncestorContainer)) {
        closeSelectionMenu();
        return;
      }
    } else {
      // Cross-container selection - validate that it includes our container
      const includesOurContainer = container.contains(range.startContainer) ||
                                  container.contains(range.endContainer) ||
                                  (range.startContainer.compareDocumentPosition(container) & Node.DOCUMENT_POSITION_CONTAINED_BY) ||
                                  (range.endContainer.compareDocumentPosition(container) & Node.DOCUMENT_POSITION_CONTAINED_BY);

      if (!includesOurContainer) {
        closeSelectionMenu();
        return;
      }
    }

    // Note: Removed the check that prevented highlighting already highlighted text
    // Users should be able to highlight overlapping or adjacent text for better UX

    // Calculate smart position using viewport-relative coordinates
    const position = calculateSelectionMenuPosition(range);

    setCurrentSelection({ text: selectedText, range, isCrossContainer });
    setSelectionMenuPosition(position);
    setShowSelectionMenu(true);
  }, [mode, closeSelectionMenu, canHighlight, text, highlights, calculateSelectionMenuPosition]);

  // Handle text selection events
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(() => {
        handleTextSelection();
      }, 10);
    };

    const handleClickOutside = (e: MouseEvent) => {
      if (!container.contains(e.target as Node)) {
        closeSelectionMenu();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        closeSelectionMenu();
      }
    };

    container.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      container.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleTextSelection, closeSelectionMenu]);

  // Add global mouseup listener for text selection
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      // Small delay to ensure selection is complete
      setTimeout(() => {
        handleTextSelection();
      }, 10);
    };

    document.addEventListener('mouseup', handleGlobalMouseUp);
    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp);
    };
  }, [handleTextSelection]);

  // Handle scroll events to update button position
  useEffect(() => {
    if (!showSelectionMenu || !currentSelection) return;

    const updatePosition = () => {
      const newPosition = calculateSelectionMenuPosition(currentSelection.range);
      setSelectionMenuPosition(newPosition);
    };

    const handleScroll = () => {
      updatePosition();
    };

    const handleResize = () => {
      updatePosition();
    };

    // Add scroll listeners to window and all scrollable parents
    window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [showSelectionMenu, currentSelection, calculateSelectionMenuPosition]);

  // Smart positioning logic for translation popup
  const calculateTranslationPosition = useCallback((range: Range) => {
    const rect = range.getBoundingClientRect();
    const popupWidth = 450;
    const popupHeight = 400;
    const margin = 20;
    const offsetFromSelection = 10;

    // Get viewport dimensions
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // Start with viewport-relative positioning
    let x = rect.left;
    let y = rect.top - popupHeight - offsetFromSelection;

    // Adjust positioning based on mode and selection size
    if (mode === 'passage' && rect.width > 100) {
      x = rect.left + (rect.width / 2) - (popupWidth / 2);
    } else if (mode === 'question') {
      x = rect.left + (rect.width / 2) - (popupWidth / 2);
      y = rect.top - popupHeight - offsetFromSelection;
    } else if (mode === 'transcript') {
      x = Math.max(margin, rect.left - 200);
      y = rect.top - popupHeight - offsetFromSelection;
    }

    // Smart horizontal positioning
    if (x + popupWidth > viewportWidth - margin) {
      x = viewportWidth - popupWidth - margin;
    }
    if (x < margin) {
      x = margin;
    }

    // Smart vertical positioning with fallback
    if (y < margin) {
      // Not enough space above - position below selection
      y = rect.bottom + offsetFromSelection;
      if (y + popupHeight > viewportHeight - margin) {
        // Not enough space below either - position at best available spot
        y = Math.max(margin, Math.min(viewportHeight - popupHeight - margin, rect.top - popupHeight / 2));
      }
    }
    if (y + popupHeight > viewportHeight - margin) {
      y = viewportHeight - popupHeight - margin;
    }

    return { x, y };
  }, [mode]);

  // Handle translation functionality - proper button behavior
  const handleTranslate = useCallback((selectedText: string, selectionRange: Range) => {
    if (!selectedText || selectedText.trim().length === 0) return;

    // Clear text selection immediately to prevent button from lingering
    window.getSelection()?.removeAllRanges();
    
    // Always close the selection menu after translate button is clicked
    closeSelectionMenu();

    // Calculate smart position using viewport-relative coordinates
    const position = calculateTranslationPosition(selectionRange);
    
    // Update the shared translation popup with new text and position
    // This automatically replaces any existing translation in the same popup window
    setSelectedText(selectedText);
    setTranslationPosition(position);
    setShowTranslation(true);
  }, [mode, calculateTranslationPosition, closeSelectionMenu]);

  // Handle scroll events for translation popup
  useEffect(() => {
    if (!showTranslation || !selectedText) return;

    const updateTranslationPosition = () => {
      // Get current selection to recalculate position
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const newPosition = calculateTranslationPosition(range);
        setTranslationPosition(newPosition);
      }
    };

    const handleScroll = () => {
      updateTranslationPosition();
    };

    const handleResize = () => {
      updateTranslationPosition();
    };

    // Add scroll listeners
    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [showTranslation, selectedText, calculateTranslationPosition]);

  // Handle highlighting functionality
  const handleHighlight = useCallback((selectedText: string) => {
    if (!canHighlight || !selectedText || selectedText.trim().length === 0 || !text) return;



    // Simple approach: find the selected text in the original text
    const trimmedSelected = selectedText.trim();

    // Try exact match first
    let startOffset = text.indexOf(trimmedSelected);

    if (startOffset === -1) {
      // Try with the original selected text (including whitespace)
      startOffset = text.indexOf(selectedText);
    }

    if (startOffset === -1) {
      // Try to find by removing extra whitespace
      const normalizedSelected = selectedText.replace(/\s+/g, ' ').trim();
      const normalizedText = text.replace(/\s+/g, ' ');
      const normalizedIndex = normalizedText.indexOf(normalizedSelected);



      if (normalizedIndex !== -1) {
        // Map back to original text position
        let originalIndex = 0;
        let normalizedCount = 0;

        while (originalIndex < text.length && normalizedCount < normalizedIndex) {
          if (text[originalIndex].match(/\s/)) {
            // Skip multiple whitespace in original, count as one in normalized
            while (originalIndex < text.length && text[originalIndex].match(/\s/)) {
              originalIndex++;
            }
            if (normalizedCount < normalizedText.length && normalizedText[normalizedCount] === ' ') {
              normalizedCount++;
            }
          } else {
            if (normalizedCount < normalizedText.length && normalizedText[normalizedCount] === text[originalIndex]) {
              normalizedCount++;
            }
            originalIndex++;
          }
        }
        startOffset = originalIndex;
      }
    }

    if (startOffset === -1) {
      console.error('Could not find selected text in original text');
      return;
    }

    const endOffset = startOffset + selectedText.length;



    // Validate the offsets
    if (startOffset >= 0 && endOffset <= text.length && startOffset < endOffset) {
      onAddHighlight!(startOffset, endOffset, selectedText);
    } else {
      console.error('Invalid highlight offsets:', {
        startOffset,
        endOffset,
        textLength: text.length,
        selectedLength: selectedText.length
      });
    }

    window.getSelection()?.removeAllRanges();
  }, [canHighlight, onAddHighlight, text]);

  // Render highlighted text (for passage mode)
  const renderHighlightedText = useCallback(() => {
    if (mode !== 'passage' || !text) return null;

    if (highlights.length === 0) {
      return text;
    }

    // Sort highlights by start position and merge overlapping ones for display
    // But keep track of original highlights for removal
    const sortedHighlights = [...highlights]
      .sort((a, b) => a.start - b.start)
      .reduce((merged: Array<typeof highlights[0] & { originalHighlights: typeof highlights }>, current) => {
        if (merged.length === 0) {
          return [{ ...current, originalHighlights: [current] }];
        }

        const last = merged[merged.length - 1];

        // Check for overlap or adjacency
        if (current.start <= last.end) {
          // Merge overlapping highlights - extend the last one
          last.end = Math.max(last.end, current.end);
          // Keep the text from the longer highlight
          if (current.end > last.end || current.text.length > last.text.length) {
            last.text = text.slice(last.start, last.end);
          }
          // Add current highlight to the list of original highlights
          last.originalHighlights.push(current);
        } else {
          merged.push({ ...current, originalHighlights: [current] });
        }

        return merged;
      }, []);

    const parts: React.ReactNode[] = [];
    let lastIndex = 0;

    sortedHighlights.forEach((highlight, index) => {
      // Add non-highlighted text before this highlight
      if (highlight.start > lastIndex) {
        const beforeText = text.slice(lastIndex, highlight.start);
        if (beforeText) {
          parts.push(beforeText);
        }
      }

      // Ensure we don't go beyond the text length
      const safeStart = Math.max(0, Math.min(highlight.start, text.length));
      const safeEnd = Math.max(safeStart, Math.min(highlight.end, text.length));

      if (safeStart < safeEnd) {
        const highlightText = text.slice(safeStart, safeEnd);

        parts.push(
          <span
            key={`highlight-${index}-${safeStart}-${safeEnd}`}
            style={{
              backgroundColor: highlight.color || (resolvedTheme === 'dark' ? '#ffd43b' : '#ffeb3b'),
              cursor: 'pointer',
              color: resolvedTheme === 'dark' ? '#000000' : 'inherit',
              // Explicitly preserve all text properties to prevent any changes
              fontWeight: 'inherit',
              fontSize: 'inherit',
              lineHeight: 'inherit',
              fontFamily: 'inherit',
              textDecoration: 'inherit',
              letterSpacing: 'inherit',
              wordSpacing: 'inherit',
              textTransform: 'inherit',
              fontStyle: 'inherit',
              fontVariant: 'inherit',
              // Ensure no layout changes
              padding: '0',
              margin: '0',
              border: 'none',
              outline: 'none',
              boxSizing: 'content-box',
              display: 'inline',
              verticalAlign: 'baseline',
              // Allow text selection through highlighted spans
              userSelect: 'text',
              WebkitUserSelect: 'text',
              MozUserSelect: 'text',
              msUserSelect: 'text',
            }}
            onClick={(e) => {
              // Only remove highlight if user is not selecting text
              const selection = window.getSelection();
              if (!selection || selection.toString().trim().length === 0) {
                // Remove all overlapping highlights at this position
                if ('originalHighlights' in highlight && highlight.originalHighlights) {
                  // Remove all original highlights that were merged into this visual highlight
                  highlight.originalHighlights.forEach(originalHighlight => {
                    onRemoveHighlight!(originalHighlight);
                  });
                } else {
                  // Fallback for non-merged highlights
                  onRemoveHighlight!(highlight);
                }
              }
            }}
            title={
              'originalHighlights' in highlight && highlight.originalHighlights && highlight.originalHighlights.length > 1
                ? `${t('common.clickToRemove')} (${highlight.originalHighlights.length} overlapping highlights)`
                : t('common.clickToRemove')
            }
          >
            {highlightText}
          </span>
        );
      }

      lastIndex = Math.max(lastIndex, safeEnd);
    });

    // Add remaining non-highlighted text
    if (lastIndex < text.length) {
      const remainingText = text.slice(lastIndex);
      if (remainingText) {
        parts.push(remainingText);
      }
    }

    return parts;
  }, [mode, text, highlights, onRemoveHighlight, t, resolvedTheme]);



  // Render content based on mode
  const renderContent = () => {
    if (mode === 'passage' && text) {
      return (
        <Text
          id={containerId}
          size={typeof fontSize === 'number' ? fontSize.toString() : fontSize}
          style={{
            whiteSpace: 'pre',
            lineHeight,
            userSelect: 'text',
            cursor: 'text',
            overflowWrap: 'normal',
            wordBreak: 'normal',
            ...style,
          }}
          className={className}
        >
          {renderHighlightedText()}
        </Text>
      );
    } else {
      // Question or transcript mode
      return (
        <Text
          id={containerId}
          size={typeof size === 'number' ? size.toString() : size}
          fw={fw}
          style={{
            userSelect: 'text',
            cursor: 'text',
            ...(mode === 'transcript' && {
              position: 'relative',
              zIndex: 1,
              pointerEvents: 'auto'
            }),
            ...style,
          }}
          className={className}
          component={component}
          {...otherProps}
        >
          {children}
        </Text>
      );
    }
  };

  return (
    <div
      ref={containerRef}
      id={mode === 'passage' ? 'unified-text-container' : undefined}
      style={{
        // Ensure text selection works properly
        userSelect: 'text',
        WebkitUserSelect: 'text',
        MozUserSelect: 'text',
        msUserSelect: 'text',
      }}
      onMouseUp={(e) => {
        // Allow text selection to work properly
        const selection = window.getSelection();
        if (selection && selection.toString().trim()) {
          e.stopPropagation();
        }
      }}
      onClick={(e) => {
        // Prevent click events from interfering with text selection
        const selection = window.getSelection();
        if (selection && selection.toString().trim().length > 0) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
    >
      {renderContent()}

      {/* Custom Selection Menu - CONSISTENT positioning regardless of translation popup state */}
      {showSelectionMenu && currentSelection && (
        <div
          style={{
            position: 'fixed',
            left: selectionMenuPosition.x,
            top: selectionMenuPosition.y,
            transform: 'translateX(-50%)',
            zIndex: 2600, // CONSISTENT z-index - always the same value
            pointerEvents: 'auto',
          }}
        >
          <Group gap="xs" style={{
            backgroundColor: themeColors.surface,
            border: `1px solid ${themeColors.border}`,
            borderRadius: '8px',
            padding: '6px',
            // CONSISTENT styling - always the same shadow regardless of translation popup state
            boxShadow: resolvedTheme === 'dark'
              ? '0 4px 12px rgba(0, 0, 0, 0.5)'
              : '0 4px 12px rgba(0, 0, 0, 0.15)',
          }}>
            {/* Highlight button for passage mode - only for single-container selections */}
            {canHighlight && !currentSelection.isCrossContainer && (
              <MantineButton
                size="xs"
                leftSection={<IconHighlight size={14} />}
                onClick={() => {
                  if (!currentSelection || !canHighlight) return;
                  handleHighlight(currentSelection.text);
                  setShowSelectionMenu(false);
                  setCurrentSelection(null);
                }}
                styles={{
                  root: {
                    backgroundColor: resolvedTheme === 'dark' ? '#ffd43b' : '#ffd43b',
                    color: resolvedTheme === 'dark' ? '#000000' : 'black',
                    border: resolvedTheme === 'dark' ? '1px solid #fcc419' : '1px solid #fcc419',
                    fontWeight: 600,
                    fontSize: '12px',
                    borderRadius: '6px',
                    '&:hover': {
                      backgroundColor: resolvedTheme === 'dark' ? '#fcc419' : '#fcc419',
                      transform: 'translateY(-1px)',
                    },
                  },
                }}
              >
                {t('common.highlight')}
              </MantineButton>
            )}

            {/* Translate button for all modes - works for both single and cross-container selections */}
            {canTranslate && (
              <MantineButton
                size="xs"
                leftSection={<IconLanguage size={14} />}
                onClick={() => {
                  if (!currentSelection) return;
                  handleTranslate(currentSelection.text, currentSelection.range);
                  setShowSelectionMenu(false);
                  setCurrentSelection(null);
                }}
                styles={{
                  root: {
                    // CONSISTENT styling - always the same appearance regardless of translation popup state
                    backgroundColor: themeColors.primary,
                    color: 'white',
                    border: `1px solid ${themeColors.primaryHover}`,
                    fontWeight: 600,
                    fontSize: '12px',
                    borderRadius: '6px',
                    '&:hover': {
                      backgroundColor: themeColors.primaryHover,
                      transform: 'translateY(-1px)',
                    },
                  },
                }}
              >
                {t('translation.translate')}
              </MantineButton>
            )}
          </Group>
        </div>
      )}

      {/* Translation Popup */}
      {showTranslation && selectedText && (
        <TranslationPopup
          selectedText={selectedText}
          onClose={closeTranslation}
          position={translationPosition}
        />
      )}
    </div>
  );
}
