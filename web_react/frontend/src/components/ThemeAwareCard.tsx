import { Card } from '@mantine/core';
import { useThemeColors } from '../store/useThemeStore';
import React from 'react';

interface ThemeAwareCardProps {
  children?: React.ReactNode;
  style?: React.CSSProperties;
  hoverEffect?: boolean;
  [key: string]: any; // Allow any other Card props
}

export function ThemeAwareCard({
  children,
  style,
  hoverEffect = false,
  ...props
}: ThemeAwareCardProps) {
  const themeColors = useThemeColors();

  const cardStyle: React.CSSProperties = {
    backgroundColor: themeColors.surface,
    borderColor: themeColors.border,
    color: themeColors.textPrimary,
    transition: 'background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1), color 0.2s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
    ...style,
  };

  return (
    <Card
      {...props}
      style={cardStyle}
      withBorder
    >
      {children}
    </Card>
  );
}
