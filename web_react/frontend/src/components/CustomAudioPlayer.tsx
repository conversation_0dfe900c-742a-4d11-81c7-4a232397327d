import React, { useRef, useState, useEffect, useCallback, useImperativeHandle, forwardRef, useMemo } from 'react';
import { Group, Button, Slider, Text, Select, ActionIcon, Progress, Box } from '@mantine/core';
import {
  IconPlayerPlay,
  IconPlayerPause,
  IconVolume,
  IconVolumeOff,
  IconRotateClockwise,
  IconPlayerSkipForward
} from '@tabler/icons-react';
import { useThemeColors, useThemeStore } from '../store/useThemeStore';
import { useTranslation } from 'react-i18next';

interface CustomAudioPlayerProps {
  src: string;
  onEnded?: () => void;
  playbackSpeed?: number;
  onPlaybackSpeedChange?: (speed: number) => void;
  autoPlay?: boolean;
  style?: React.CSSProperties;
  showAutoPlayToggle?: boolean;
  onAutoPlayToggle?: (enabled: boolean) => void;
  autoPlayEnabled?: boolean;
  shouldAutoPlayOnLoad?: boolean;
  isLoading?: boolean; // External loading state for smooth transitions
}

// Interface for the ref object that parent components can use
export interface CustomAudioPlayerRef {
  audioElement: HTMLAudioElement | null;
  play: () => void;
  pause: () => void;
  currentTime: number;
  duration: number;
  setCurrentTime: (time: number) => void;
}

const PLAYBACK_SPEEDS = [
  { value: '0.5', label: '0.5x' },
  { value: '0.75', label: '0.75x' },
  { value: '1', label: '1x' },
  { value: '1.25', label: '1.25x' },
  { value: '1.5', label: '1.5x' },
  { value: '2', label: '2x' }
];

const WAIT_TIME_OPTIONS = [
  { value: '0', label: 'common.waitTime.0' },
  { value: '5', label: 'common.waitTime.5' },
  { value: '10', label: 'common.waitTime.10' },
  { value: '15', label: 'common.waitTime.15' },
  { value: '20', label: 'common.waitTime.20' },
  { value: '25', label: 'common.waitTime.25' },
  { value: '30', label: 'common.waitTime.30' }
];

export const CustomAudioPlayer = forwardRef<CustomAudioPlayerRef, CustomAudioPlayerProps>(({
  src,
  onEnded,
  playbackSpeed = 1,
  onPlaybackSpeedChange,
  autoPlay = false,
  style,
  showAutoPlayToggle = false,
  onAutoPlayToggle,
  autoPlayEnabled = false,
  shouldAutoPlayOnLoad = false,
  isLoading: externalIsLoading = false
}, ref) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [internalIsLoading, setInternalIsLoading] = useState(true);
  const [hasEnded, setHasEnded] = useState(false); // Track if audio has ended
  const [userPaused, setUserPaused] = useState(false); // Track if user manually paused
  const [currentSrc, setCurrentSrc] = useState<string>(''); // Track current audio source
  const [waitTime, setWaitTime] = useState(() => {
    // Load wait time from localStorage, default to 10 seconds
    const savedWaitTime = localStorage.getItem('audioWaitTime');
    return savedWaitTime ? parseInt(savedWaitTime, 10) : 10;
  });
  const isTogglingAutoPlayRef = useRef(false); // Track if we're in the middle of an autoplay toggle
  const isInitialLoadRef = useRef(true); // Track if this is the initial load vs a setting change
  const hasUserInteractedRef = useRef(false); // Track if user has ever interacted with this audio
  const waitTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Track wait timeout

  // Combine external and internal loading states
  const isLoading = externalIsLoading || internalIsLoading;

  // Theme support
  const themeColors = useThemeColors();
  const { resolvedTheme } = useThemeStore();
  const { t } = useTranslation();

  // Expose audio controls to parent component
  useImperativeHandle(ref, () => ({
    audioElement: audioRef.current,
    play: () => {
      if (audioRef.current) {
        audioRef.current.play();
        setIsPlaying(true);
      }
    },
    pause: () => {
      if (audioRef.current) {
        audioRef.current.pause();
        setIsPlaying(false);
      }
    },
    currentTime,
    duration,
    setCurrentTime: (time: number) => {
      if (audioRef.current) {
        audioRef.current.currentTime = time;
        setCurrentTime(time);
      }
    }
  }));



  // Audio event handlers
  const handleLoadedData = useCallback(() => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
      setInternalIsLoading(false);
    }
  }, []);

  const handleTimeUpdate = useCallback(() => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  }, []);

  const handleEnded = useCallback(() => {
    setIsPlaying(false);
    setCurrentTime(0);
    setHasEnded(true); // Mark as ended to prevent autoplay
    setUserPaused(false); // Reset user pause state when audio ends naturally

    // Immediately stop any potential replay during transition
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    if (onEnded) {
      onEnded();
    }
  }, [onEnded]);

  const handleLoadStart = useCallback(() => {
    setInternalIsLoading(true);
  }, []);

  const handleCanPlay = useCallback(() => {
    // This event fires when the audio is ready to play
    // Additional trigger point for autoplay if needed
  }, []);

  // Optimized src change handling - only update when src actually changes
  useEffect(() => {
    // Skip if we're toggling autoplay to prevent any interference
    if (isTogglingAutoPlayRef.current) {
      return;
    }

    // Skip if src hasn't actually changed
    if (currentSrc === src) {
      return;
    }

    setCurrentSrc(src);

    if (audioRef.current) {
      // Only pause if currently playing and src is changing to a different source
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      }

      // Reset states for new audio
      setCurrentTime(0);
      setDuration(0);
      setHasEnded(false);
      setUserPaused(false);
      isInitialLoadRef.current = true; // Mark as initial load for new audio
      hasUserInteractedRef.current = false; // Reset user interaction flag for new audio

      // Set loading state and update src
      if (src) {
        setInternalIsLoading(true);
        // Always update the audio element src when we have a new source
        audioRef.current.src = src;
        // Force reload to ensure proper loading events
        audioRef.current.load();
      } else {
        setInternalIsLoading(false);
        audioRef.current.src = '';
      }
    }
  }, [src, currentSrc, isPlaying]);

  // Memoized control functions to prevent unnecessary re-renders
  const togglePlayPause = useCallback(() => {
    if (!audioRef.current || isLoading) return;

    // Mark that user has interacted with this audio
    hasUserInteractedRef.current = true;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
      setUserPaused(true); // Mark as manually paused
    } else {
      audioRef.current.play().catch(() => {
        // Audio play failed - user interaction may be required
      });
      setIsPlaying(true);
      setUserPaused(false); // Clear manual pause flag
      isInitialLoadRef.current = false; // Clear initial load flag on manual play
    }
  }, [isPlaying, isLoading]);

  const handleSeek = useCallback((value: number) => {
    if (!audioRef.current || isLoading) return;

    const newTime = (value / 100) * duration;
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [duration, isLoading]);

  const handleVolumeChange = useCallback((value: number) => {
    if (!audioRef.current) return;

    const newVolume = value / 100;
    audioRef.current.volume = newVolume;
    setVolume(newVolume);

    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  }, [isMuted]);

  const toggleMute = useCallback(() => {
    if (!audioRef.current) return;

    if (isMuted) {
      audioRef.current.volume = volume;
      setIsMuted(false);
    } else {
      audioRef.current.volume = 0;
      setIsMuted(true);
    }
  }, [isMuted, volume]);

  const handleSpeedChange = useCallback((value: string | null) => {
    if (!value || !audioRef.current) return;

    const speed = parseFloat(value);
    audioRef.current.playbackRate = speed;
    if (onPlaybackSpeedChange) {
      onPlaybackSpeedChange(speed);
    }
  }, [onPlaybackSpeedChange]);

  const skipForward = useCallback(() => {
    if (!audioRef.current || isLoading) return;

    const newTime = Math.min(audioRef.current.currentTime + 10, duration);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [duration, isLoading]);

  const skipBackward = useCallback(() => {
    if (!audioRef.current || isLoading) return;

    const newTime = Math.max(audioRef.current.currentTime - 10, 0);
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  }, [isLoading]);

  const handleWaitTimeChange = useCallback((value: string | null) => {
    if (value === null) return;
    
    const newWaitTime = parseInt(value, 10);
    setWaitTime(newWaitTime);
    localStorage.setItem('audioWaitTime', value);
  }, []);

  // Effects
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.addEventListener('loadedmetadata', handleLoadedData);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('loadstart', handleLoadStart);
    audio.addEventListener('canplay', handleCanPlay);

    return () => {
      // Cleanup: remove event listeners only - don't reset audio position
      if (audio) {
        audio.removeEventListener('loadedmetadata', handleLoadedData);
        audio.removeEventListener('timeupdate', handleTimeUpdate);
        audio.removeEventListener('ended', handleEnded);
        audio.removeEventListener('loadstart', handleLoadStart);
        audio.removeEventListener('canplay', handleCanPlay);
      }
    };
  }, [handleLoadedData, handleTimeUpdate, handleEnded, handleLoadStart, handleCanPlay]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.playbackRate = playbackSpeed;
    }
  }, [playbackSpeed]);

  useEffect(() => {
    if (autoPlay && audioRef.current && !isLoading) {
      audioRef.current.play();
      setIsPlaying(true);
    }
  }, [autoPlay, isLoading]);

  // Fixed auto-play logic for persistent player
  useEffect(() => {
    // Skip entirely if we're in the middle of toggling autoplay
    if (isTogglingAutoPlayRef.current) {
      return;
    }

    // Skip entirely if user has ever interacted with this audio - no more autoplay interference
    if (hasUserInteractedRef.current) {
      return;
    }

    // Only attempt autoplay if conditions are met and avoid interfering with currently playing audio
    // Also only run on initial loads, not on setting changes
    if (shouldAutoPlayOnLoad &&
        src &&
        audioRef.current &&
        !isLoading &&
        duration > 0 &&
        !isPlaying &&
        !hasEnded &&
        !userPaused &&
        isInitialLoadRef.current) { // Only run on initial load, not setting changes

      // Verify that the audio element's src matches the current src prop
      const audioSrc = audioRef.current.src;
      const normalizedSrc = src.startsWith('http') ? src : new URL(src, window.location.origin).href;
      const normalizedAudioSrc = audioSrc.startsWith('http') ? audioSrc : new URL(audioSrc, window.location.origin).href;

      // Only autoplay if the sources match (prevents playing old audio)
      if (normalizedAudioSrc === normalizedSrc) {
        // Clear any existing wait timeout
        if (waitTimeoutRef.current) {
          clearTimeout(waitTimeoutRef.current);
          waitTimeoutRef.current = null;
        }

        // Calculate total delay: base delay (150ms) + user-configured wait time
        const totalDelay = 150 + (waitTime * 1000);
        
        // Small delay to ensure audio is fully loaded and prevent race conditions, plus user wait time
        const timer = setTimeout(() => {
          // Skip if we're now toggling autoplay
          if (isTogglingAutoPlayRef.current) {
            return;
          }

          // Final validation before autoplay - double check that audio is still not playing
          if (audioRef.current &&
              audioRef.current.src &&
              audioRef.current.paused && // Ensure audio is actually paused
              !isPlaying &&
              !hasEnded &&
              !userPaused &&
              duration > 0 &&
              isInitialLoadRef.current) { // Double-check this is still an initial load
            // Reset to beginning for initial autoplay
            audioRef.current.currentTime = 0;
            audioRef.current.play().catch(() => {
              // Audio autoplay failed - user interaction may be required
            });
            setIsPlaying(true);
            // Clear the initial load flag after successful autoplay
            isInitialLoadRef.current = false;
          }
        }, totalDelay);

        waitTimeoutRef.current = timer;
        return () => {
          clearTimeout(timer);
          if (waitTimeoutRef.current === timer) {
            waitTimeoutRef.current = null;
          }
        };
      }
    }
  }, [shouldAutoPlayOnLoad, isLoading, duration, src, hasEnded, userPaused, waitTime]); // Remove isPlaying from dependencies to prevent interference

  // Handle autoplay toggle changes without interfering with currently playing audio
  const prevShouldAutoPlayRef = useRef(shouldAutoPlayOnLoad);
  useEffect(() => {
    // If shouldAutoPlayOnLoad changed, this is a setting change, not an initial load
    if (prevShouldAutoPlayRef.current !== shouldAutoPlayOnLoad) {
      // Mark as user interaction - this prevents future autoplay interference
      hasUserInteractedRef.current = true;

      // Clear the initial load flag since this is a user setting change
      isInitialLoadRef.current = false;

      // Always set the toggle flag when the setting changes to prevent any interference
      isTogglingAutoPlayRef.current = true;

      // Clear the flag after a longer delay to ensure all effects are blocked
      const timer = setTimeout(() => {
        isTogglingAutoPlayRef.current = false;
      }, 2000); // Even longer delay to ensure complete stability

      prevShouldAutoPlayRef.current = shouldAutoPlayOnLoad;
      return () => clearTimeout(timer);
    }

    prevShouldAutoPlayRef.current = shouldAutoPlayOnLoad;
  }, [shouldAutoPlayOnLoad]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
      
      // Clear wait timeout
      if (waitTimeoutRef.current) {
        clearTimeout(waitTimeoutRef.current);
        waitTimeoutRef.current = null;
      }
    };
  }, []);

  // Memoized computed values to prevent unnecessary re-renders
  const progressPercentage = useMemo(() =>
    duration > 0 ? (currentTime / duration) * 100 : 0,
    [currentTime, duration]
  );

  // Memoized format time function
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  return (
    <Box
      style={{
        width: '100%',
        padding: '16px',
        border: `1px solid ${themeColors.border}`,
        borderRadius: '12px',
        backgroundColor: themeColors.surface,
        boxShadow: resolvedTheme === 'dark'
          ? '0 2px 8px rgba(0,0,0,0.3)'
          : '0 2px 8px rgba(0,0,0,0.06)',
        ...style
      }}
    >
      <audio
        ref={audioRef}
        src={src || undefined}
        preload="metadata"
      />
      
      {/* Progress Bar */}
      <Box mb="md">
        <Progress
          value={progressPercentage}
          size="sm"
          radius="xl"
          style={{ cursor: 'pointer' }}
          onClick={(e) => {
            const rect = e.currentTarget.getBoundingClientRect();
            const percent = ((e.clientX - rect.left) / rect.width) * 100;
            handleSeek(percent);
          }}
          styles={{
            root: {
              backgroundColor: themeColors.border,
            },
            section: {
              backgroundColor: themeColors.primary,
            },
          }}
        />
        <Group justify="space-between" mt={4}>
          <Text size="xs" c={themeColors.textSecondary}>{formatTime(currentTime)}</Text>
          <Text size="xs" c={themeColors.textSecondary}>{formatTime(duration)}</Text>
        </Group>
      </Box>

      {/* Main Controls */}
      <Group justify="space-between" align="center">
        {/* Left Side - Play Controls */}
        <Group gap="sm">
          <ActionIcon
            variant="filled"
            size="lg"
            radius="xl"
            onClick={togglePlayPause}
            disabled={isLoading}
            style={{ backgroundColor: themeColors.primary }}
          >
            {isPlaying ? <IconPlayerPause size={20} /> : <IconPlayerPlay size={20} />}
          </ActionIcon>
          
          <ActionIcon
            variant="light"
            size="md"
            onClick={skipBackward}
            disabled={isLoading}
            styles={{
              root: {
                backgroundColor: 'transparent',
                borderColor: themeColors.border,
                color: themeColors.textPrimary,
                '&:hover': {
                  backgroundColor: themeColors.surfaceHover,
                },
              },
            }}
          >
            <IconRotateClockwise size={16} style={{ transform: 'scaleX(-1)' }} />
          </ActionIcon>

          <ActionIcon
            variant="light"
            size="md"
            onClick={skipForward}
            disabled={isLoading}
            styles={{
              root: {
                backgroundColor: 'transparent',
                borderColor: themeColors.border,
                color: themeColors.textPrimary,
                '&:hover': {
                  backgroundColor: themeColors.surfaceHover,
                },
              },
            }}
          >
            <IconPlayerSkipForward size={16} />
          </ActionIcon>
        </Group>

        {/* Center - Speed Control & Wait Time */}
        <Group gap="md">
          <Group gap="xs">
            <Text size="sm" c={themeColors.textSecondary}>{t('common.speed')}:</Text>
            <Select
              data={PLAYBACK_SPEEDS}
              value={playbackSpeed.toString()}
              onChange={handleSpeedChange}
              size="sm"
              w={80}
              disabled={isLoading}
              styles={{
                input: {
                  backgroundColor: themeColors.surface,
                  borderColor: themeColors.border,
                  color: themeColors.textPrimary,
                  fontSize: '12px',
                },
                dropdown: {
                  backgroundColor: themeColors.surface,
                  borderColor: themeColors.border,
                },
                option: {
                  backgroundColor: themeColors.surface,
                  color: themeColors.textPrimary,
                  fontSize: '12px',
                },
              }}
            />
          </Group>
          
          <Group gap="xs">
            <Text size="sm" c={themeColors.textSecondary}>{t('common.audioWaitTime')}:</Text>
            <Select
              data={WAIT_TIME_OPTIONS.map(option => ({
                value: option.value,
                label: t(option.label)
              }))}
              value={waitTime.toString()}
              onChange={handleWaitTimeChange}
              size="sm"
              w={110}
              disabled={isLoading}
              styles={{
                input: {
                  backgroundColor: themeColors.surface,
                  borderColor: themeColors.border,
                  color: themeColors.textPrimary,
                  fontSize: '12px',
                },
                dropdown: {
                  backgroundColor: themeColors.surface,
                  borderColor: themeColors.border,
                },
                option: {
                  backgroundColor: themeColors.surface,
                  color: themeColors.textPrimary,
                  fontSize: '12px',
                },
              }}
            />
          </Group>
        </Group>

        {/* Right Side - Volume & Auto-play */}
        <Group gap="sm">
          {showAutoPlayToggle && (
            <Button
              variant={autoPlayEnabled ? "filled" : "light"}
              size="sm"
              onClick={() => onAutoPlayToggle?.(!autoPlayEnabled)}
              leftSection={<IconPlayerSkipForward size={14} />}
              styles={{
                root: {
                  backgroundColor: autoPlayEnabled ? themeColors.primary : 'transparent',
                  borderColor: themeColors.primary,
                  color: autoPlayEnabled ? 'white' : themeColors.primary,
                  '&:hover': {
                    backgroundColor: autoPlayEnabled ? themeColors.primaryHover : themeColors.surfaceHover,
                  },
                },
              }}
            >
              {t('common.autoPlay')}
            </Button>
          )}
          
          <Group gap={4}>
            <ActionIcon
              variant="light"
              size="sm"
              onClick={toggleMute}
              styles={{
                root: {
                  backgroundColor: 'transparent',
                  borderColor: themeColors.border,
                  color: themeColors.textPrimary,
                  '&:hover': {
                    backgroundColor: themeColors.surfaceHover,
                  },
                },
              }}
            >
              {isMuted || volume === 0 ? <IconVolumeOff size={16} /> : <IconVolume size={16} />}
            </ActionIcon>
            <Slider
              w={60}
              size="sm"
              value={isMuted ? 0 : volume * 100}
              onChange={handleVolumeChange}
              disabled={isLoading}
              styles={{
                track: {
                  backgroundColor: themeColors.border,
                },
                bar: {
                  backgroundColor: themeColors.primary,
                },
                thumb: {
                  backgroundColor: themeColors.primary,
                  borderColor: themeColors.primary,
                },
              }}
            />
          </Group>
        </Group>
      </Group>
    </Box>
  );
});