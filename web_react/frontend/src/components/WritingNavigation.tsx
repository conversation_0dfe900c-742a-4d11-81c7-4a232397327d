import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Stack,
  Text,
  ScrollArea,
  Card,
  ActionIcon,
  Drawer,
  Button,
  Group,
  Badge,
  Tooltip,
  Loader,
  UnstyledButton,
  Collapse,
  useMatches
} from '@mantine/core';
import {
  IconCalendar,
  IconMenu2,
  IconX,
  IconChevronLeft,
  IconChevronRight,
  IconBookmark,
  IconBookmarks
} from '@tabler/icons-react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { useDisclosure, useMediaQuery } from '@mantine/hooks';
import { useThemeColors } from '../store/useThemeStore';
import { writingService, type MonthData } from '../services/writingService';
import { useTranslation } from 'react-i18next';

interface WritingNavigationProps {
  variant?: 'sidebar' | 'compact' | 'mobile';
}

export function WritingNavigation({ variant = 'sidebar' }: WritingNavigationProps = {}) {
  const { monthId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const themeColors = useThemeColors();
  const [writingData, setWritingData] = useState<MonthData[]>([]);
  const [loading, setLoading] = useState(true);
  const [drawerOpened, { open: openDrawer, close: closeDrawer }] = useDisclosure(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true); // Default to collapsed

  // Responsive breakpoints
  const isMobile = useMediaQuery('(max-width: 768px)');
  const isTablet = useMediaQuery('(max-width: 1024px)');

  // Auto-determine variant based on screen size
  const effectiveVariant = isMobile ? 'mobile' : isTablet ? 'compact' : variant;

  // Load writing data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        const data = await writingService.getWritingTestsAsMonthData();
        setWritingData(data);
      } catch (error) {
        console.error('Failed to load writing data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((event: React.KeyboardEvent, monthData: MonthData) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      navigate(`/writing/${monthData.month_year}`);
      if (effectiveVariant === 'mobile') {
        closeDrawer();
      }
    }
  }, [navigate, effectiveVariant, closeDrawer]);

  // Render mobile navigation
  if (effectiveVariant === 'mobile') {
    return (
      <>
        {/* Mobile Navigation Trigger */}
        <Box
          style={{
            position: 'fixed',
            top: '80px',
            right: '16px',
            zIndex: 1000,
            background: themeColors.surface,
            borderRadius: '50%',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            border: `1px solid ${themeColors.border}`
          }}
        >
          <Tooltip label={t('writing.navigation.openMenu')} position="left">
            <ActionIcon
              size="lg"
              variant="subtle"
              color="orange"
              onClick={openDrawer}
              aria-label={t('writing.navigation.openMenu')}
              style={{ borderRadius: '50%' }}
            >
              <IconBookmarks size={20} />
            </ActionIcon>
          </Tooltip>
        </Box>

        {/* Mobile Drawer */}
        <Drawer
          opened={drawerOpened}
          onClose={closeDrawer}
          title={
            <Group gap="sm">
              <IconBookmarks size={20} color={themeColors.writing} />
              <Text fw={600} c={themeColors.writing}>
                {t('writing.navigation.title')}
              </Text>
            </Group>
          }
          position="right"
          size="sm"
          styles={{
            header: {
              backgroundColor: themeColors.surface,
              borderBottom: `1px solid ${themeColors.border}`
            },
            body: {
              backgroundColor: themeColors.background,
              padding: 0
            }
          }}
        >
          <MobileNavigationContent
            writingData={writingData}
            loading={loading}
            currentMonthId={monthId}
            onNavigate={(monthYear) => {
              navigate(`/writing/${monthYear}`);
              closeDrawer();
            }}
            themeColors={themeColors}
            onKeyDown={handleKeyDown}
          />
        </Drawer>
      </>
    );
  }

  // Render compact navigation (for tablets)
  if (effectiveVariant === 'compact') {
    const compactHeight = () => {
      if (loading || writingData.length === 0) return 300;
      const headerHeight = 50;
      const itemHeight = 32; // Smaller for compact
      const padding = 20;
      const maxItems = Math.min(writingData.length, 12);
      return Math.min(headerHeight + (itemHeight * maxItems) + padding, window.innerHeight - 160);
    };

    const compactNavHeight = compactHeight();

    return (
      <Box
        style={{
          position: 'fixed',
          top: '50%',
          left: '16px',
          transform: 'translateY(-50%)', // Center vertically
          zIndex: 100,
          background: themeColors.surface,
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)',
          border: `1px solid ${themeColors.border}`,
          padding: '8px',
          height: compactNavHeight + 'px',
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Modern Collapse/Expand Button */}
        <Tooltip
          label={sidebarCollapsed ? t('writing.navigation.expand') : t('writing.navigation.collapse')}
          position="right"
        >
          <ActionIcon
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            variant="filled"
            color="orange"
            size="md"
            radius="md"
            style={{
              position: 'absolute',
              top: '50%',
              right: '-20px',
              transform: 'translateY(-50%)',
              zIndex: 102,
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
              border: `2px solid ${themeColors.surface}`,
              transition: 'all 0.2s ease'
            }}
            aria-label={sidebarCollapsed ? t('writing.navigation.expand') : t('writing.navigation.collapse')}
          >
            {sidebarCollapsed ? <IconChevronRight size={16} /> : <IconChevronLeft size={16} />}
          </ActionIcon>
        </Tooltip>

        <CompactNavigationContent
          writingData={writingData}
          loading={loading}
          currentMonthId={monthId}
          collapsed={sidebarCollapsed}
          onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
          onNavigate={(monthYear) => navigate(`/writing/${monthYear}`)}
          themeColors={themeColors}
          onKeyDown={handleKeyDown}
        />
      </Box>
    );
  }

  // Calculate dynamic height based on content
  const calculateNavigationHeight = () => {
    if (loading || writingData.length === 0) return 400;

    // Header height + compact month items (no task counts)
    const headerHeight = 60;
    const itemHeight = 40; // Smaller since no task count
    const padding = 24;
    const maxItems = Math.min(writingData.length, 15); // More items can fit

    return Math.min(headerHeight + (itemHeight * maxItems) + padding, window.innerHeight - 160);
  };

  const navHeight = calculateNavigationHeight();

  // Render full sidebar navigation (for desktop)
  return (
    <Box
      style={{
        position: 'fixed',
        top: '50%',
        left: sidebarCollapsed ? '-240px' : '16px',
        transform: 'translateY(-50%)', // Center vertically
        zIndex: 100,
        background: themeColors.surface,
        borderRadius: '16px',
        boxShadow: '0 12px 48px rgba(0, 0, 0, 0.15)',
        border: `1px solid ${themeColors.border}`,
        width: sidebarCollapsed ? '280px' : '320px',
        height: navHeight + 'px',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Modern Collapse/Expand Button */}
      <Tooltip
        label={sidebarCollapsed ? t('writing.navigation.expand') : t('writing.navigation.collapse')}
        position="right"
      >
        <ActionIcon
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          variant="filled"
          color="orange"
          size="lg"
          radius="md"
          style={{
            position: 'absolute',
            top: '50%',
            right: '-22px',
            transform: 'translateY(-50%)',
            zIndex: 102,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            border: `2px solid ${themeColors.surface}`,
            transition: 'all 0.2s ease'
          }}
          aria-label={sidebarCollapsed ? t('writing.navigation.expand') : t('writing.navigation.collapse')}
        >
          {sidebarCollapsed ? <IconChevronRight size={18} /> : <IconChevronLeft size={18} />}
        </ActionIcon>
      </Tooltip>

      <SidebarNavigationContent
        writingData={writingData}
        loading={loading}
        currentMonthId={monthId}
        collapsed={sidebarCollapsed}
        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
        onNavigate={(monthYear) => navigate(`/writing/${monthYear}`)}
        themeColors={themeColors}
        onKeyDown={handleKeyDown}
      />
    </Box>
  );
}

// Mobile Navigation Content Component
interface MobileNavigationContentProps {
  writingData: MonthData[];
  loading: boolean;
  currentMonthId?: string;
  onNavigate: (monthYear: string) => void;
  themeColors: any;
  onKeyDown: (event: React.KeyboardEvent, monthData: MonthData) => void;
}

function MobileNavigationContent({
  writingData,
  loading,
  currentMonthId,
  onNavigate,
  themeColors,
  onKeyDown
}: MobileNavigationContentProps) {
  const { t } = useTranslation();

  if (loading) {
    return (
      <Box
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          minHeight: '200px'
        }}
      >
        <Loader size="md" color="orange" />
        <Text size="sm" c="dimmed" mt="md">
          {t('writing.navigation.loading')}
        </Text>
      </Box>
    );
  }

  return (
    <ScrollArea style={{ height: '100%' }} p="md">
      <Stack gap="xs">
        {writingData.map((monthData) => {
          const formattedName = writingService.formatMonthName(monthData.month_year);
          const isCurrent = currentMonthId === monthData.month_year;

          return (
            <UnstyledButton
              key={monthData.month_year}
              onClick={() => onNavigate(monthData.month_year)}
              onKeyDown={(e) => onKeyDown(e, monthData)}
              style={{
                width: '100%',
                padding: '10px 16px',
                borderRadius: '8px',
                backgroundColor: isCurrent ? `${themeColors.writing}15` : 'transparent',
                border: `1px solid ${isCurrent ? `${themeColors.writing}40` : 'transparent'}`,
                transition: 'all 0.2s ease',
                textAlign: 'left'
              }}
              aria-label={t('writing.navigation.navigateToMonth', { month: formattedName })}
            >
              <Group justify="space-between" align="center">
                <Text
                  size="sm"
                  fw={isCurrent ? 600 : 500}
                  c={isCurrent ? themeColors.writing : themeColors.textPrimary}
                >
                  {formattedName}
                </Text>
                <Group gap="xs">
                  {isCurrent && (
                    <Badge size="xs" color="orange" variant="light">
                      {t('writing.navigation.current')}
                    </Badge>
                  )}
                  <IconCalendar
                    size={16}
                    color={isCurrent ? themeColors.writing : themeColors.textSecondary}
                  />
                </Group>
              </Group>
            </UnstyledButton>
          );
        })}
      </Stack>
    </ScrollArea>
  );
}

// Compact Navigation Content Component (for tablets)
interface CompactNavigationContentProps extends MobileNavigationContentProps {
  collapsed: boolean;
  onToggleCollapse: () => void;
}

function CompactNavigationContent({
  writingData,
  loading,
  currentMonthId,
  collapsed,
  onToggleCollapse,
  onNavigate,
  themeColors,
  onKeyDown
}: CompactNavigationContentProps) {
  const { t } = useTranslation();

  if (loading) {
    return (
      <Box
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          minHeight: '150px'
        }}
      >
        <Loader size="sm" color="orange" />
      </Box>
    );
  }

  return (
    <Stack gap="xs">
      {/* Header */}
      <Group justify="center" p="sm" style={{ borderBottom: `1px solid ${themeColors.border}` }}>
        {!collapsed && (
          <Text size="xs" fw={600} c={themeColors.writing}>
            {t('writing.navigation.title')}
          </Text>
        )}
      </Group>

      <Collapse in={!collapsed}>

        {/* Month list */}
        <ScrollArea style={{ flex: 1 }} p="xs">
          <Stack gap="xs">
            {writingData.map((monthData) => {
              const formattedName = writingService.formatMonthName(monthData.month_year);
              const isCurrent = currentMonthId === monthData.month_year;

              return (
                <UnstyledButton
                  key={monthData.month_year}
                  onClick={() => onNavigate(monthData.month_year)}
                  onKeyDown={(e) => onKeyDown(e, monthData)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    borderRadius: '6px',
                    backgroundColor: isCurrent ? `${themeColors.writing}15` : 'transparent',
                    border: `1px solid ${isCurrent ? `${themeColors.writing}40` : 'transparent'}`,
                    transition: 'all 0.2s ease'
                  }}
                  aria-label={t('writing.navigation.navigateToMonth', { month: formattedName })}
                >
                  <Group justify="space-between" wrap="nowrap">
                    <Text
                      size="xs"
                      fw={isCurrent ? 600 : 500}
                      c={isCurrent ? themeColors.writing : themeColors.textPrimary}
                      truncate
                    >
                      {formattedName}
                    </Text>
                    {isCurrent && (
                      <IconBookmark size={12} color={themeColors.writing} />
                    )}
                  </Group>
                </UnstyledButton>
              );
            })}
          </Stack>
        </ScrollArea>
      </Collapse>
    </Stack>
  );
}

// Sidebar Navigation Content Component (for desktop)
interface SidebarNavigationContentProps extends CompactNavigationContentProps {}

function SidebarNavigationContent({
  writingData,
  loading,
  currentMonthId,
  collapsed,
  onToggleCollapse,
  onNavigate,
  themeColors,
  onKeyDown
}: SidebarNavigationContentProps) {
  const { t } = useTranslation();
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Scroll to current month when data loads or current month changes
  useEffect(() => {
    if (currentMonthId && writingData.length > 0 && scrollAreaRef.current) {
      const currentIndex = writingData.findIndex(month => month.month_year === currentMonthId);
      if (currentIndex !== -1) {
        // Small delay to ensure DOM is ready
        setTimeout(() => {
          const monthElements = scrollAreaRef.current?.querySelectorAll('[data-month-item]');
          if (monthElements && monthElements[currentIndex]) {
            monthElements[currentIndex].scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            });
          }
        }, 100);
      }
    }
  }, [currentMonthId, writingData, collapsed]);

  if (loading) {
    return (
      <Box
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          minHeight: '200px'
        }}
      >
        <Loader size="md" color="orange" />
        <Text size="sm" c="dimmed" mt="md">
          {t('writing.navigation.loading')}
        </Text>
      </Box>
    );
  }

  return (
    <Stack gap={0}>
      {/* Header */}
      <Box
        p="md"
        style={{
          borderBottom: `1px solid ${themeColors.border}`,
          background: `linear-gradient(135deg, ${themeColors.writing}08 0%, ${themeColors.writing}03 100%)`
        }}
      >
        <Group justify="center" align="center">
          {!collapsed && (
            <Group gap="sm">
              <IconBookmarks size={20} color={themeColors.writing} />
              <Text fw={600} c={themeColors.writing}>
                {t('writing.navigation.title')}
              </Text>
            </Group>
          )}
        </Group>
      </Box>

      {/* Month list */}
      <ScrollArea style={{ flex: 1 }} p={collapsed ? "xs" : "md"} ref={scrollAreaRef}>
        <Stack gap="xs">
          {writingData.map((monthData) => {
            const formattedName = writingService.formatMonthName(monthData.month_year);
            const isCurrent = currentMonthId === monthData.month_year;

            return (
              <UnstyledButton
                key={monthData.month_year}
                data-month-item
                onClick={() => onNavigate(monthData.month_year)}
                onKeyDown={(e) => onKeyDown(e, monthData)}
                style={{
                  width: '100%',
                  padding: collapsed ? '6px' : '10px 16px',
                  borderRadius: '8px',
                  backgroundColor: isCurrent ? `${themeColors.writing}15` : 'transparent',
                  border: `1px solid ${isCurrent ? `${themeColors.writing}40` : 'transparent'}`,
                  transition: 'all 0.2s ease'
                }}
                aria-label={t('writing.navigation.navigateToMonth', { month: formattedName })}
              >
                {collapsed ? (
                  <Box style={{ textAlign: 'center' }}>
                    <IconCalendar
                      size={16}
                      color={isCurrent ? themeColors.writing : themeColors.textSecondary}
                    />
                    {isCurrent && (
                      <Box
                        style={{
                          width: '4px',
                          height: '4px',
                          borderRadius: '50%',
                          backgroundColor: themeColors.writing,
                          margin: '4px auto 0'
                        }}
                      />
                    )}
                  </Box>
                ) : (
                  <Group justify="space-between" align="center">
                    <Text
                      size="sm"
                      fw={isCurrent ? 600 : 500}
                      c={isCurrent ? themeColors.writing : themeColors.textPrimary}
                    >
                      {formattedName}
                    </Text>
                    <Group gap="xs">
                      {isCurrent && (
                        <Badge size="xs" color="orange" variant="light">
                          {t('writing.navigation.current')}
                        </Badge>
                      )}
                      <IconCalendar
                        size={16}
                        color={isCurrent ? themeColors.writing : themeColors.textSecondary}
                      />
                    </Group>
                  </Group>
                )}
              </UnstyledButton>
            );
          })}
        </Stack>
      </ScrollArea>
    </Stack>
  );
}
