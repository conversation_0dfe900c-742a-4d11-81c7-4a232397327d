/**
 * Demo component to show improved session invalidation messages
 * This is for testing purposes only
 */

import React from 'react';
import { Button, Stack, Text, Paper, Group, Code, Select } from '@mantine/core';
import { showSessionInvalidatedNotification, showLoginSuccessNotification, showLogoutNotification } from '../utils/sessionUtils';
import { useTranslation } from 'react-i18next';
import { useLanguageStore } from '../store/useLanguageStore';

export function SessionMessageDemo() {
  const { t } = useTranslation();
  const { lang, setLang } = useLanguageStore();

  const demoMessages = [
    {
      label: 'Basic Session Invalid (i18n)',
      message: 'Account accessed from another device',
      messageData: { type: 'device_access' as const }
    },
    {
      label: 'With Timestamp (i18n)',
      message: 'Account accessed from another device at 2025-07-12 22:21:43',
      messageData: {
        type: 'device_access_with_time' as const,
        timestamp: '2025-07-12 22:21:43',
        ip: '*************'
      }
    },
    {
      label: 'Legacy Format (backwards compatibility)',
      message: 'Account accessed from another device at 2025-07-12 22:21:43',
      messageData: undefined
    },
  ];

  return (
    <Paper p="md" withBorder>
      <Stack gap="md">
        <Group justify="space-between" align="center">
          <div>
            <Text size="lg" fw={600}>Session Message Demo (i18n)</Text>
            <Text size="sm" c="dimmed">
              Test the improved session invalidation messages with internationalization
            </Text>
          </div>
          <Select
            label="Language"
            value={lang}
            onChange={(value) => value && setLang(value as any)}
            data={[
              { value: 'en', label: 'English' },
              { value: 'fr', label: 'Français' },
              { value: 'zh', label: '中文' }
            ]}
            w={120}
          />
        </Group>

        <Stack gap="sm">
          <Text fw={500}>Session Invalidation Messages:</Text>
          {demoMessages.map((demo, index) => (
            <Group key={index} justify="space-between">
              <div style={{ flex: 1 }}>
                <Text size="sm" fw={500}>{demo.label}</Text>
                <Code block>{demo.message}</Code>
              </div>
              <Button
                size="xs"
                variant="light"
                color="orange"
                onClick={() => showSessionInvalidatedNotification(demo.message, demo.messageData)}
              >
                Test
              </Button>
            </Group>
          ))}
        </Stack>

        <Stack gap="sm">
          <Text fw={500}>Other Session Messages:</Text>
          <Group>
            <Button
              size="sm"
              variant="light"
              color="green"
              onClick={() => showLoginSuccessNotification()}
            >
              Login Success
            </Button>
            <Button
              size="sm"
              variant="light"
              color="blue"
              onClick={() => showLogoutNotification()}
            >
              Logout Success
            </Button>
          </Group>
        </Stack>

        <Paper p="sm" bg="gray.0" style={{ marginTop: '1rem' }}>
          <Text size="sm" fw={500} mb="xs">Improvements Made:</Text>
          <Stack gap="xs">
            <div>
              <Text size="xs" fw={500} c="red">❌ Before:</Text>
              <Code block>
                • Browser alert with technical IPv6 address
                • 2-second redirect (too fast to read)
                • Bottom-right corner (easy to miss)
                • English only
              </Code>
            </div>
            <div>
              <Text size="xs" fw={500} c="green">✅ After:</Text>
              <Code block>
                • Professional notification with countdown timer
                • 8-second delay (enough time to read)
                • Prominent styling with orange border and glow
                • Clickable to go to login immediately
                • User-friendly "another device" instead of IP
                • Localized timestamp format
                • Full i18n support (English, French, Chinese)
                • Structured message data from backend
              </Code>
            </div>
          </Stack>
        </Paper>
      </Stack>
    </Paper>
  );
}
