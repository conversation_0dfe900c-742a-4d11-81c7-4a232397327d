import React, { useState } from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>, 
  Badge, 
  Group, 
  Card,
  Divider,
  ScrollArea
} from '@mantine/core';
import { IconAlertCircle, IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@tanstack/react-query';
import { api } from '../services/api';

interface ModificationEntry {
  test_id: string;
  question: string;
  original_answer: string;
  new_answer: string;
  modification_date: string;
  reason: string;
  version_type?: string; // 'paid' or 'free'
  note?: string;
}

interface ModificationLog {
  log_info: {
    created_date: string;
    purpose: string;
    total_modifications: number;
  };
  modifications: ModificationEntry[];
  summary: {
    tests_affected: string[];
    questions_modified: number;
    questions_confirmed_unchanged: number;
    most_common_changes: Record<string, number>;
  };
  website_notification: {
    display_message: string;
    affected_tests_count: number;
    last_update: string;
  };
}

interface ModificationNotificationProps {
  section: 'reading' | 'listening';
  className?: string;
}

// Fallback data (used when API is not available yet)
const fallbackReadingModifications: ModificationLog = {
  log_info: {
    created_date: "2025-01-22",
    purpose: "Track modified reading question answers for transparency and user notification",
    total_modifications: 33
  },
  modifications: [
    {
      test_id: "test1",
      question: "Q37",
      original_answer: "A",
      new_answer: "C",
      modification_date: "2025-01-22",
      reason: "LLM analysis revealed incorrect answer",
      version_type: "paid"
    },
    {
      test_id: "test1",
      question: "Q25",
      original_answer: "D",
      new_answer: "C",
      modification_date: "2025-01-22",
      reason: "LLM analysis revealed incorrect answer",
      version_type: "free"
    },
    {
      test_id: "test1",
      question: "Q37",
      original_answer: "D",
      new_answer: "B",
      modification_date: "2025-01-22",
      reason: "LLM analysis revealed incorrect answer",
      version_type: "free"
    },
    {
      test_id: "test2",
      question: "Q11",
      original_answer: "B",
      new_answer: "C",
      modification_date: "2025-01-22",
      reason: "LLM analysis revealed incorrect answer",
      version_type: "paid"
    },
    {
      test_id: "test2",
      question: "Q15",
      original_answer: "B",
      new_answer: "A",
      modification_date: "2025-01-22",
      reason: "LLM analysis revealed incorrect answer",
      version_type: "free"
    }
  ],
  summary: {
    tests_affected: ["test1", "test2"],
    questions_modified: 5,
    questions_confirmed_unchanged: 0,
    most_common_changes: {
      "Q37": 2,
      "Q25": 1,
      "Q11": 1,
      "Q15": 1
    }
  },
  website_notification: {
    display_message: "Reading comprehension test answers have been reviewed and updated through LLM analysis for improved accuracy.",
    affected_tests_count: 2,
    last_update: "2025-01-22"
  }
};

const fallbackListeningModifications: ModificationLog = {
  log_info: {
    created_date: "2024-12-19",
    purpose: "Track modified listening question answers for transparency and user notification",
    total_modifications: 22
  },
  modifications: [
    {
      test_id: "test12",
      question: "Q39",
      original_answer: "C",
      new_answer: "A",
      modification_date: "2024-12-19",
      reason: "Answer review and correction",
      version_type: "paid"
    },
    {
      test_id: "test2",
      question: "Q39",
      original_answer: "C",
      new_answer: "A",
      modification_date: "2024-12-19",
      reason: "Answer review and correction",
      version_type: "paid"
    }
  ],
  summary: {
    tests_affected: ["test2", "test12"],
    questions_modified: 2,
    questions_confirmed_unchanged: 0,
    most_common_changes: {
      "Q39": 2
    }
  },
  website_notification: {
    display_message: "Some listening test answers have been reviewed and updated for accuracy. Please check the modification log for details.",
    affected_tests_count: 2,
    last_update: "2024-12-19"
  }
};

export function ModificationNotification({ section, className }: ModificationNotificationProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { t } = useTranslation();
  
  // Fetch modification data from API with fallback
  const { data: modificationData, isLoading, error } = useQuery({
    queryKey: ['modifications', section],
    queryFn: async () => {
      const response = await api.get(`/modifications/${section}`);
      return response.data as ModificationLog;
    },
    // Cache for 5 minutes to avoid excessive API calls
    staleTime: 5 * 60 * 1000,
    // Retry on failure
    retry: 2
  });
  
  // Use fallback data if API fails
  const finalData = modificationData || (section === 'reading' ? fallbackReadingModifications : fallbackListeningModifications);
  
  // Calculate total modifications by counting actual array length
  const totalModifications = finalData?.modifications?.length || 0;
  const hasModifications = totalModifications > 0;
  
  // Don't show anything if there are no modifications
  if (!hasModifications && !isLoading && !error) {
    return null;
  }
  
  // Show loading state
  if (isLoading) {
    return (
      <Box className={className}>
        <Button
          variant="light"
          color="gray"
          size="sm"
          loading
          disabled
        >
          {t('common.loading')}
        </Button>
      </Box>
    );
  }
  
  // Show error state (but don't crash the page)
  if (error && !modificationData) {
    console.error('Failed to load modifications:', error);
    return null; // Silently fail - modifications are not critical
  }

  const formatTestId = (testId: string, versionType?: string) => {
    const prefix = versionType === 'free' ? 'Test_gr ' : 'Test ';
    return testId.replace('test', prefix);
  };

  return (
    <Box className={className} style={{ position: 'relative', zIndex: 10 }}>
      <Button
        variant="light"
        color="orange"
        size="sm"
        leftSection={<IconAlertCircle size={16} />}
        rightSection={isExpanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          borderRadius: '8px',
          fontWeight: 500,
          textTransform: 'none'
        }}
      >
        {t('modificationNotification.answerUpdates', { count: totalModifications })}
      </Button>

      <Collapse in={isExpanded}>
        <Card 
          shadow="md" 
          padding="md" 
          radius="md" 
          mt="xs"
          style={{ 
            position: 'absolute',
            right: 0,
            top: '100%',
            width: '400px',
            maxWidth: '90vw',
            zIndex: 1000,
            border: '1px solid #e9ecef'
          }}
        >
          <Stack gap="md">
            {/* Header */}
            <Text fw={600} size="md" c="orange" mb="md">
              {t(`modificationNotification.${section}Modifications`)}
            </Text>

            {/* Content */}
            <Text size="sm" c="dimmed" mb="md">
              {t('modificationNotification.summaryMessage')}
            </Text>

            {/* Modification List */}
            <Box>
              <Text fw={500} size="sm" mb="xs">{t('modificationNotification.recentChanges')}</Text>
              <ScrollArea h={200}>
                <Stack gap="xs">
                  {finalData?.modifications
                    ?.sort((a, b) => {
                      // First sort by version type: free tests (test_gr) come before paid tests (test)
                      const aIsFree = a.version_type === 'free';
                      const bIsFree = b.version_type === 'free';
                      if (aIsFree !== bIsFree) {
                        return aIsFree ? -1 : 1; // free tests come first
                      }
                      
                      // Then sort by test number (extract number from test_id)
                      const aNum = parseInt(a.test_id.replace('test', '')) || 0;
                      const bNum = parseInt(b.test_id.replace('test', '')) || 0;
                      if (aNum !== bNum) return aNum - bNum;
                      
                      // Finally sort by question number
                      const aQ = parseInt(a.question.replace('Q', '')) || 0;
                      const bQ = parseInt(b.question.replace('Q', '')) || 0;
                      return aQ - bQ;
                    })
                    .map((mod, index) => (
                    <Card key={index} p="xs" bg="#f8f9fa" radius="sm">
                      <Group justify="space-between" align="center">
                        <Box>
                          <Text size="sm" fw={500}>
                            {formatTestId(mod.test_id, mod.version_type)} {mod.question}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {mod.original_answer} → {mod.new_answer}
                          </Text>
                        </Box>
                      </Group>
                    </Card>
                  )) || []}
                </Stack>
              </ScrollArea>
            </Box>
          </Stack>
        </Card>
      </Collapse>
    </Box>
  );
} 