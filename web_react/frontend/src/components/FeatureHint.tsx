import React, { useState, useEffect } from 'react';
import { Box, Text, Transition, Group } from '@mantine/core';
import { IconNotebook, IconBrain, IconArrowLeft, IconArrowRight } from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useThemeColors } from '../store/useThemeStore';

interface FeatureHintProps {
  showHint: boolean;
  section: 'reading' | 'listening';
}

export function FeatureHint({ showHint, section }: FeatureHintProps) {
  const { t } = useTranslation();
  const themeColors = useThemeColors();

  if (!showHint) return null;

  return (
    <Transition
      mounted={showHint}
      transition="slide-up"
      duration={400}
      timingFunction="ease-out"
    >
      {(styles) => (
        <Box
          style={{
            ...styles,
            position: 'fixed',
            bottom: '80px',
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1000,
            background: themeColors.surface,
            border: `1px solid ${themeColors.border}`,
            borderRadius: '16px',
            padding: '16px 24px',
            boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
            backdropFilter: 'blur(16px)',
            maxWidth: '400px',
            textAlign: 'center',
          }}
        >
          <Text size="sm" fw={600} c={themeColors.textPrimary} mb="xs">
            {t('test.features.hint.title', 'Découvrez nos outils !')}
          </Text>
          
          <Group justify="center" gap="xl" mb="xs">
            <Group gap="xs" align="center">
              <IconBrain size={18} color={themeColors.primary} />
              <Text size="xs" c={themeColors.textSecondary}>
                {t('test.features.hint.analysis', 'Analyse')}
              </Text>
              <IconArrowLeft size={14} color={themeColors.textSecondary} />
            </Group>
            
            <Group gap="xs" align="center">
              <IconArrowRight size={14} color={themeColors.textSecondary} />
              <Text size="xs" c={themeColors.textSecondary}>
                {t('test.features.hint.notebook', 'Notes')}
              </Text>
              <IconNotebook size={18} color={themeColors.primary} />
            </Group>
          </Group>
          
          <Text size="xs" c={themeColors.textSecondary} style={{ opacity: 0.8 }}>
            {t('test.features.hint.description', 'Cliquez sur les boutons sur les côtés pour accéder aux outils')}
          </Text>
        </Box>
      )}
    </Transition>
  );
}
