import {
  <PERSON>dal,
  Group,
  Text,
  Stack,
  Alert,
  <PERSON>,
  Textarea,
  Button
} from '@mantine/core';
import {
  IconHeadphones,
  IconInfoCircle,
  IconId,
  IconUser,
  IconMail,
  IconSend
} from '@tabler/icons-react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '../store/useAuthStore';

interface ContactSupportModalProps {
  opened: boolean;
  onClose: () => void;
  contactForm: any;
  isSubmitting: boolean;
  onSubmit: (values: any) => void;
}

export function ContactSupportModal({
  opened,
  onClose,
  contactForm,
  isSubmitting,
  onSubmit
}: ContactSupportModalProps) {
  const { t } = useTranslation();
  const { isAuthenticated, user } = useAuthStore();

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={
        <Group gap="sm">
          <IconHeadphones size={20} color="#007bff" />
          <Text fw={600}>{t('contact.title')}</Text>
        </Group>
      }
      size="lg"
      radius="md"
      shadow="xl"
    >
      <form onSubmit={contactForm.onSubmit(onSubmit)}>
        <Stack gap="md">
          {/* User Information Display */}
          {isAuthenticated && user && (
            <Alert color="blue" variant="light" radius="md">
              <Group gap="sm" align="flex-start">
                <IconInfoCircle size={20} style={{ marginTop: '2px' }} />
                <Box>
                  <Text size="sm" fw={500} mb="xs">
                    {t('contact.yourInfo')}
                  </Text>
                  <Group gap="lg">
                    <Group gap="xs">
                      <IconId size={16} color="#666" />
                      <Text size="sm" c="dimmed">ID: {user.id}</Text>
                    </Group>
                    <Group gap="xs">
                      <IconUser size={16} color="#666" />
                      <Text size="sm" c="dimmed">{user.username}</Text>
                    </Group>
                    <Group gap="xs">
                      <IconMail size={16} color="#666" />
                      <Text size="sm" c="dimmed">{user.email}</Text>
                    </Group>
                  </Group>
                </Box>
              </Group>
            </Alert>
          )}
          
          <Textarea
            label={t('contact.describeProblem')}
            placeholder={t('contact.placeholder')}
            rows={6}
            {...contactForm.getInputProps('message')}
            required
            styles={{
              input: {
                fontSize: '15px',
              },
              label: {
                fontWeight: 600,
                marginBottom: '8px',
                fontSize: '14px',
              }
            }}
          />

          <Alert color="orange" variant="light" radius="md">
            <Group gap="sm" align="flex-start">
              <Text size="24px" style={{ lineHeight: 1 }}>💡</Text>
              <Box>
                <Text size="sm" fw={500} mb="xs">
                  {t('contact.tips.title')}
                </Text>
                <Text size="sm" c="dimmed">
                  • {t('contact.tips.step1')}<br />
                  • {t('contact.tips.step2')}<br />
                  • {t('contact.tips.step3')}<br />
                  • {t('contact.tips.step4')}
                </Text>
              </Box>
            </Group>
          </Alert>

          <Group justify="flex-end" gap="sm" mt="md">
            <Button 
              variant="outline" 
              onClick={onClose}
              size="md"
              radius="md"
            >
              {t('contact.cancel')}
            </Button>
            <Button
              type="submit"
              loading={isSubmitting}
              leftSection={<IconSend size={16} />}
              size="md"
              radius="md"
              gradient={{ from: 'blue', to: 'cyan', deg: 45 }}
              styles={{
                root: {
                  border: 0,
                }
              }}
            >
              {isSubmitting ? t('contact.sending') : t('contact.send')}
            </Button>
          </Group>
        </Stack>
      </form>
    </Modal>
  );
} 