import { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Textarea,
  Group,
  Text,
  Button,
  Paper,
  Stack,
  Progress,
  Badge,
  Alert,
  ActionIcon,
  Tooltip,
  Modal,
  Card
} from '@mantine/core';
import {
  IconDeviceFloppy,
  IconSend,
  IconAlertCircle,
  IconCheck,
  IconHistory
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useTranslation } from 'react-i18next';
import { testApi } from '../services/api';

interface WritingEditorProps {
  testType: string;
  testIdentifier: string;
  taskNumber: number;
  combinationNumber?: string;
  placeholder?: string;
  minWords?: number; // Optional - will use task-based defaults if not provided
  maxWords?: number; // Optional - will use task-based defaults if not provided
  onSubmissionSaved?: (submissionId: string) => void;
  onSubmissionSubmitted?: (submissionId: string) => void;
  initialContent?: string;
  readOnly?: boolean;
}

interface WritingSubmission {
  id: string;
  content: string;
  word_count: number;
  character_count: number;
  status: string;
  version: number;
  is_final_submission: boolean;
  created_at: string;
  updated_at: string;
  submission_time?: string;
}

export function WritingEditor({
  testType,
  testIdentifier,
  taskNumber,
  combinationNumber,
  placeholder = '',
  minWords,
  maxWords,
  onSubmissionSaved,
  onSubmissionSubmitted,
  initialContent = '',
  readOnly = false
}: WritingEditorProps) {
  // Set word limits based on task number if not provided
  const getWordLimits = (taskNum: number) => {
    switch (taskNum) {
      case 1:
        return { min: 60, max: 120 };
      case 2:
        return { min: 120, max: 150 };
      case 3:
        return { min: 120, max: 180 };
      default:
        return { min: 60, max: 120 };
    }
  };

  const limits = getWordLimits(taskNumber);
  const actualMinWords = minWords ?? limits.min;
  const actualMaxWords = maxWords ?? limits.max;
  const { t } = useTranslation();
  const [content, setContent] = useState(initialContent);
  const [wordCount, setWordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [autoSaveCount, setAutoSaveCount] = useState(0);
  const [startTime] = useState(new Date());
  const [submissions, setSubmissions] = useState<WritingSubmission[]>([]);
  const [hasLoadedInitialContent, setHasLoadedInitialContent] = useState(false);
  const [isLoadingSubmissions, setIsLoadingSubmissions] = useState(true);

  const [historyOpened, { open: openHistory, close: closeHistory }] = useDisclosure(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Calculate content metrics
  const updateContentMetrics = useCallback((text: string) => {
    const words = text.trim() ? text.trim().split(/\s+/).length : 0;
    setWordCount(words);
  }, []);

  // Auto-save functionality
  const autoSave = useCallback(async (text: string) => {
    if (!text.trim() || readOnly) return;

    try {
      setIsSaving(true);
      const writingDuration = Math.floor((new Date().getTime() - startTime.getTime()) / 1000);

      const response = await testApi.saveWritingSubmission({
        test_type: testType,
        test_identifier: testIdentifier,
        task_number: taskNumber,
        combination_number: combinationNumber,
        content: text,
        status: 'draft',
        writing_duration: writingDuration,
        auto_save_count: autoSaveCount + 1
      });

      setAutoSaveCount(prev => prev + 1);
      setLastSaved(new Date());

      if (onSubmissionSaved) {
        onSubmissionSaved(response.submission_id);
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
      // Don't show error to user for auto-save failures, just log it
    } finally {
      setIsSaving(false);
    }
  }, [testType, testIdentifier, taskNumber, combinationNumber, startTime, autoSaveCount, onSubmissionSaved, readOnly]);

  // Handle content change
  const handleContentChange = (value: string) => {
    setContent(value);
    updateContentMetrics(value);

    // Clear existing auto-save timeout
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // Set new auto-save timeout (5 seconds after user stops typing)
    if (!readOnly && value.trim()) {
      autoSaveTimeoutRef.current = setTimeout(() => {
        autoSave(value);
      }, 5000);
    }
  };

  // Manual save
  const handleSave = async () => {
    if (!content.trim()) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.emptyContent'),
        color: 'red'
      });
      return;
    }

    try {
      setIsLoading(true);
      const writingDuration = Math.floor((new Date().getTime() - startTime.getTime()) / 1000);
      
      const response = await testApi.saveWritingSubmission({
        test_type: testType,
        test_identifier: testIdentifier,
        task_number: taskNumber,
        combination_number: combinationNumber,
        content: content,
        status: 'draft',
        writing_duration: writingDuration,
        auto_save_count: autoSaveCount
      });

      setLastSaved(new Date());
      
      notifications.show({
        title: t('writing.editor.saved'),
        message: t('writing.editor.savedMessage'),
        color: 'green',
        icon: <IconCheck size={16} />
      });

      if (onSubmissionSaved) {
        onSubmissionSaved(response.submission_id);
      }
    } catch (error) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.saveError'),
        color: 'red'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Submit final version
  const handleSubmit = async () => {
    if (!content.trim()) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.emptyContent'),
        color: 'red'
      });
      return;
    }

    if (wordCount < actualMinWords) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.minWordsError', { min: actualMinWords, current: wordCount }),
        color: 'red'
      });
      return;
    }

    if (wordCount > actualMaxWords) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.maxWordsError', { max: actualMaxWords, current: wordCount }),
        color: 'red'
      });
      return;
    }

    try {
      setIsLoading(true);
      const writingDuration = Math.floor((new Date().getTime() - startTime.getTime()) / 1000);
      
      const response = await testApi.saveWritingSubmission({
        test_type: testType,
        test_identifier: testIdentifier,
        task_number: taskNumber,
        combination_number: combinationNumber,
        content: content,
        status: 'submitted',
        writing_duration: writingDuration,
        auto_save_count: autoSaveCount
      });

      notifications.show({
        title: t('writing.editor.submitted'),
        message: t('writing.editor.submittedMessage'),
        color: 'green',
        icon: <IconCheck size={16} />
      });

      if (onSubmissionSubmitted) {
        onSubmissionSubmitted(response.submission_id);
      }
    } catch (error) {
      notifications.show({
        title: t('writing.editor.error'),
        message: t('writing.editor.submitError'),
        color: 'red'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load existing submissions
  const loadSubmissions = useCallback(async () => {
    try {
      setIsLoadingSubmissions(true);
      const response = await testApi.getWritingSubmissions(
        testType,
        testIdentifier,
        taskNumber,
        combinationNumber
      );

      setSubmissions(response.submissions);

      // Only load the most recent draft on initial load (when content is empty and hasn't been loaded before)
      if (!initialContent && !content && !hasLoadedInitialContent && response.submissions.length > 0) {
        const latestSubmission = response.submissions[0];
        if (latestSubmission.status === 'draft') {
          setContent(latestSubmission.content);
          updateContentMetrics(latestSubmission.content);
          setHasLoadedInitialContent(true);
        }
      }
    } catch (error) {
      console.error('Failed to load submissions:', error);
    } finally {
      setIsLoadingSubmissions(false);
    }
  }, [testType, testIdentifier, taskNumber, combinationNumber, initialContent, content, hasLoadedInitialContent, updateContentMetrics]);

  // Initialize component - load submissions only once on mount
  useEffect(() => {
    loadSubmissions();
  }, [testType, testIdentifier, taskNumber, combinationNumber]);

  // Update content metrics when content changes
  useEffect(() => {
    updateContentMetrics(content);
  }, [content, updateContentMetrics]);

  // Keyboard shortcut for manual save (Ctrl+S / Cmd+S)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        if (!readOnly && content.trim()) {
          handleSave();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [content, readOnly, handleSave]);

  // Cleanup auto-save timeout
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // Word count progress calculation
  const getWordCountProgress = () => {
    if (wordCount < actualMinWords) {
      return (wordCount / actualMinWords) * 50; // 0-50% for reaching minimum
    } else if (wordCount <= actualMaxWords) {
      return 50 + ((wordCount - actualMinWords) / (actualMaxWords - actualMinWords)) * 50; // 50-100% for optimal range
    } else {
      return 100; // Over maximum
    }
  };

  const getWordCountColor = () => {
    if (wordCount < actualMinWords) return 'red';
    if (wordCount > actualMaxWords) return 'orange';
    return 'green';
  };

  const finalSubmission = submissions.find(s => s.is_final_submission);

  return (
    <Stack gap="md">

      {/* Final Submission Alert */}
      {finalSubmission && (
        <Alert
          icon={<IconCheck size={16} />}
          title={t('writing.editor.finalSubmissionExists')}
          color="green"
        >
          {t('writing.editor.finalSubmissionMessage', {
            date: new Date(finalSubmission.submission_time!).toLocaleString(),
            words: finalSubmission.word_count
          })}
        </Alert>
      )}

      {/* Writing Area */}
      <Paper p="md" withBorder>
        <Stack gap="md">
          {/* Toolbar */}
          <Group justify="space-between">
            <Badge variant="light" color={getWordCountColor()}>
              {wordCount} / {actualMinWords}-{actualMaxWords} {t('writing.editor.words')}
            </Badge>
            
            <Group gap="xs">
              {submissions.length > 0 && !isLoadingSubmissions && (
                <Tooltip label={t('writing.editor.viewHistory')}>
                  <ActionIcon variant="light" onClick={openHistory}>
                    <IconHistory size={16} />
                  </ActionIcon>
                </Tooltip>
              )}

              {isLoadingSubmissions && (
                <Text size="xs" c="blue">
                  {t('writing.editor.loadingContent')}
                </Text>
              )}

              {!isLoadingSubmissions && isSaving && (
                <Text size="xs" c="blue">
                  {t('writing.editor.saving')}...
                </Text>
              )}

              {!isLoadingSubmissions && !isSaving && lastSaved && (
                <Text size="xs" c="green">
                  ✓ {t('writing.editor.lastSaved')}: {lastSaved.toLocaleTimeString()}
                </Text>
              )}

              {!isLoadingSubmissions && !isSaving && !lastSaved && content.trim() && (
                <Text size="xs" c="orange">
                  {t('writing.editor.unsaved')}
                </Text>
              )}
            </Group>
          </Group>

          {/* Progress Bar */}
          <Progress
            value={getWordCountProgress()}
            color={getWordCountColor()}
            size="sm"
            striped={wordCount > actualMaxWords}
          />

          {/* Text Area */}
          <Textarea
            ref={textareaRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            placeholder={isLoadingSubmissions ? t('writing.editor.loadingContent') : (placeholder || t('writing.editor.placeholder'))}
            minRows={12}
            maxRows={20}
            autosize
            disabled={readOnly || !!finalSubmission || isLoadingSubmissions}
            styles={{
              input: {
                fontSize: '16px',
                lineHeight: 1.6,
                opacity: isLoadingSubmissions ? 0.6 : 1
              }
            }}
          />

          {/* Action Buttons */}
          {!readOnly && !finalSubmission && !isLoadingSubmissions && (
            <Group justify="space-between">
              <Group gap="sm">
                {wordCount < actualMinWords && (
                  <Text size="sm" c="red">
                    <IconAlertCircle size={16} style={{ marginRight: 4 }} />
                    {t('writing.editor.needMoreWords', { needed: actualMinWords - wordCount })}
                  </Text>
                )}
                {wordCount > actualMaxWords && (
                  <Text size="sm" c="orange">
                    <IconAlertCircle size={16} style={{ marginRight: 4 }} />
                    {t('writing.editor.tooManyWords', { excess: wordCount - actualMaxWords })}
                  </Text>
                )}
              </Group>

              <Group gap="sm">
                <Button
                  variant="light"
                  leftSection={<IconDeviceFloppy size={16} />}
                  onClick={handleSave}
                  loading={isLoading}
                  disabled={!content.trim()}
                >
                  {t('writing.editor.save')}
                </Button>

                <Button
                  leftSection={<IconSend size={16} />}
                  onClick={handleSubmit}
                  loading={isLoading}
                  disabled={!content.trim() || wordCount < actualMinWords || wordCount > actualMaxWords}
                >
                  {t('writing.editor.submit')}
                </Button>
              </Group>
            </Group>
          )}
        </Stack>
      </Paper>

      {/* Submission History Modal */}
      <Modal
        opened={historyOpened}
        onClose={closeHistory}
        title={t('writing.editor.submissionHistory')}
        size="lg"
      >
        <Stack gap="md">
          {submissions.map((submission) => (
            <Card key={submission.id} withBorder>
              <Group justify="space-between" mb="sm">
                <Group gap="sm">
                  <Badge
                    color={submission.is_final_submission ? 'green' : 'blue'}
                    variant={submission.is_final_submission ? 'filled' : 'light'}
                  >
                    {submission.is_final_submission ? t('writing.editor.final') : t('writing.editor.draft')}
                  </Badge>
                  <Text size="sm" c="dimmed">
                    v{submission.version}
                  </Text>
                </Group>
                <Text size="sm" c="dimmed">
                  {new Date(submission.created_at).toLocaleString()}
                </Text>
              </Group>
              
              <Group gap="md" mb="sm">
                <Text size="sm">
                  <strong>{submission.word_count}</strong> {t('writing.editor.words')}
                </Text>
                {submission.submission_time && (
                  <Text size="sm" c="green">
                    {t('writing.editor.submitted')}: {new Date(submission.submission_time).toLocaleString()}
                  </Text>
                )}
              </Group>
              
              <Text size="sm" style={{ 
                maxHeight: '100px', 
                overflow: 'auto',
                backgroundColor: 'var(--mantine-color-gray-0)',
                padding: '8px',
                borderRadius: '4px'
              }}>
                {submission.content.substring(0, 200)}
                {submission.content.length > 200 && '...'}
              </Text>
            </Card>
          ))}
          
          {submissions.length === 0 && (
            <Text ta="center" c="dimmed">
              {t('writing.editor.noSubmissions')}
            </Text>
          )}
        </Stack>
      </Modal>
    </Stack>
  );
}
