import React, { useState, useCallback, useMemo } from 'react';
import { Box, Alert, Skeleton } from '@mantine/core';
import { useThemeColors } from '../store/useThemeStore';

interface OptimizedImageProps {
  src: string | null;
  alt: string;
  isLoading?: boolean;
  error?: string | null;
  maxWidth?: number;
  maxHeight?: number;
  aspectRatio?: number; // width/height ratio, e.g., 16/9 = 1.78
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  isLoading = false,
  error = null,
  maxWidth = 400,
  maxHeight = 320,
  aspectRatio = 4/3, // Default aspect ratio for TCF images
  style,
  onLoad,
  onError
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const themeColors = useThemeColors();

  // Calculate container dimensions based on aspect ratio
  const containerDimensions = useMemo(() => {
    // Use a more conservative approach for responsive sizing
    const availableWidth = typeof window !== 'undefined' ? Math.min(maxWidth, window.innerWidth - 64) : maxWidth;
    const containerWidth = Math.max(200, availableWidth); // Minimum width of 200px
    const containerHeight = Math.min(containerWidth / aspectRatio, maxHeight);

    return {
      width: containerWidth,
      height: containerHeight
    };
  }, [maxWidth, maxHeight, aspectRatio]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
    setImageError(false);
    onLoad?.();
  }, [onLoad]);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setImageLoaded(false);
    onError?.();
  }, [onError]);

  // Show error state
  if (error || imageError) {
    return (
      <Box
        style={{
          width: containerDimensions.width,
          height: containerDimensions.height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...style
        }}
      >
        <Alert color="red" title="Image Error" style={{ maxWidth: containerDimensions.width }}>
          {error || 'Failed to load image'}
        </Alert>
      </Box>
    );
  }

  // Show loading state or when no src
  if (isLoading || !src) {
    return (
      <Box
        style={{
          width: containerDimensions.width,
          height: containerDimensions.height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...style
        }}
      >
        <Skeleton
          width={containerDimensions.width}
          height={containerDimensions.height}
          radius={8}
        />
      </Box>
    );
  }

  return (
    <Box
      style={{
        width: containerDimensions.width,
        height: containerDimensions.height,
        position: 'relative',
        overflow: 'hidden',
        borderRadius: 8,
        boxShadow: '0 2px 8px rgba(0,0,0,0.07)',
        ...style
      }}
    >
      {/* Loading skeleton overlay */}
      {!imageLoaded && (
        <Skeleton
          width="100%"
          height="100%"
          radius={8}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            zIndex: 1
          }}
        />
      )}
      
      {/* Actual image */}
      <img
        src={src}
        alt={alt}
        onLoad={handleImageLoad}
        onError={handleImageError}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          objectPosition: 'center',
          opacity: imageLoaded ? 1 : 0,
          transition: 'opacity 0.3s ease-in-out',
          position: 'relative',
          zIndex: 2
        }}
      />
    </Box>
  );
};
