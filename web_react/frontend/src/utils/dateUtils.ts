/**
 * Safely formats a date string using user's system locale and timezone
 * @param dateString - The date string to format
 * @param fallback - The fallback text to show if date is invalid
 * @returns Formatted date string or fallback text
 */
export function formatDateSafely(dateString: string | null | undefined, fallback: string = 'Date non disponible'): string {
  if (!dateString) return fallback;
  
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? fallback : date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return fallback;
  }
}

/**
 * Safely formats a date string using user's system locale and timezone with time
 * @param dateString - The date string to format
 * @param fallback - The fallback text to show if date is invalid
 * @returns Formatted date and time string or fallback text
 */
export function formatDateTimeSafely(dateString: string | null | undefined, fallback: string = 'Date non disponible'): string {
  if (!dateString) return fallback;
  
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? fallback : date.toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return fallback;
  }
}

/**
 * Safely checks if a date is in the past
 * @param dateString - The date string to check
 * @returns True if date is in the past, false otherwise
 */
export function isDateExpired(dateString: string | null | undefined): boolean {
  if (!dateString) return false;
  
  try {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date < new Date();
  } catch {
    return false;
  }
}

/**
 * Safely formats a date string using user's system locale and timezone, returns null if invalid
 * @param dateString - The date string to format
 * @returns Formatted date string or null if invalid
 */
export function formatDateOrNull(dateString: string | null | undefined): string | null {
  if (!dateString) return null;
  
  try {
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? null : date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return null;
  }
}

/**
 * Safely formats a date string using the user's system timezone and locale
 * @param dateString - The date string to format
 * @param fallback - The fallback text to show if date is invalid
 * @returns Formatted date string using user's system timezone or fallback text
 */
export function formatDateWithUserTimezone(dateString: string | null | undefined, fallback: string = 'Date non disponible'): string {
  if (!dateString) return fallback;
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return fallback;
    
    // Use user's system locale and timezone (undefined means use system defaults)
    return date.toLocaleDateString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch {
    return fallback;
  }
}

/**
 * Safely formats a date string with time using the user's system timezone and locale
 * @param dateString - The date string to format
 * @param fallback - The fallback text to show if date is invalid
 * @returns Formatted date and time string using user's system timezone or fallback text
 */
export function formatDateTimeWithUserTimezone(dateString: string | null | undefined, fallback: string = 'Date non disponible'): string {
  if (!dateString) return fallback;
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return fallback;
    
    // Use user's system locale and timezone (undefined means use system defaults)
    return date.toLocaleString(undefined, {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return fallback;
  }
} 