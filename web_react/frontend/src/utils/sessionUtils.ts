/**
 * Session management utilities for better user experience
 */

import { notifications } from '@mantine/notifications';
import i18n from '../i18n/config';

export interface SessionInvalidationInfo {
  message: string;
  timestamp?: string;
  deviceInfo?: string;
}

export interface SessionMessageData {
  type: 'device_access' | 'device_access_with_time';
  timestamp?: string;
  ip?: string;
}

/**
 * Parse session invalidation message and extract useful information
 */
export function parseSessionMessage(rawMessage: string, messageData?: SessionMessageData): SessionInvalidationInfo {
  const result: SessionInvalidationInfo = {
    message: i18n.t('auth.session.invalidated.deviceAccess')
  };

  // Use structured data if available (preferred)
  if (messageData) {
    if (messageData.type === 'device_access_with_time' && messageData.timestamp) {
      try {
        // Backend sends UTC time without timezone suffix, so we add 'Z' for proper parsing
        const accessTime = new Date(messageData.timestamp + 'Z');
        // Format in user's local timezone
        result.timestamp = accessTime.toLocaleString(undefined, {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        result.message = i18n.t('auth.session.invalidated.deviceAccessWithTime', {
          time: result.timestamp
        });
      } catch (error) {
        // Fallback to basic message
        result.message = i18n.t('auth.session.invalidated.deviceAccess');
      }
    }
    result.deviceInfo = 'another device';
    return result;
  }

  // Fallback: parse raw message (for backwards compatibility)
  if (!rawMessage) return result;

  // Extract timestamp from raw message
  const timeMatch = rawMessage.match(/at (\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/);
  if (timeMatch) {
    try {
      // Backend sends UTC time, so we add 'Z' for proper parsing
      const accessTime = new Date(timeMatch[1] + 'Z');
      // Format in user's local timezone
      result.timestamp = accessTime.toLocaleString(undefined, {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      result.message = i18n.t('auth.session.invalidated.deviceAccessWithTime', {
        time: result.timestamp
      });
    } catch (error) {
      // Keep default message if parsing fails
    }
  }

  // Extract device/IP info (but keep it user-friendly)
  if (rawMessage.includes('IP ') || rawMessage.includes('another device')) {
    result.deviceInfo = 'another device';
  }

  return result;
}

/**
 * Show a professional session invalidation notification with countdown
 */
export function showSessionInvalidatedNotification(rawMessage?: string, messageData?: SessionMessageData) {
  const info = parseSessionMessage(rawMessage || '', messageData);
  let countdown = 8;

  // Initial notification
  const showNotification = (secondsLeft: number) => {
    const title = `🔒 ${i18n.t('auth.session.invalidated.title')}`;
    const loggedOutMsg = i18n.t('auth.session.invalidated.loggedOut');
    const redirectingMsg = i18n.t('auth.session.invalidated.redirecting', { seconds: secondsLeft });
    const clickMsg = i18n.t('auth.session.invalidated.clickToLogin');

    const message = `${info.message}\n\n${loggedOutMsg}\n${redirectingMsg}\n\n${clickMsg}`;

    // Hide existing notification and show new one (since update doesn't work reliably)
    notifications.hide('session-invalidated');

    notifications.show({
      id: 'session-invalidated',
      title,
      message,
      color: 'orange',
      autoClose: false,
      withCloseButton: true,
      onClick: () => {
        clearInterval(countdownInterval);
        window.location.href = '/login';
      },
      styles: {
        root: {
          cursor: 'pointer',
          border: '2px solid #fd7e14',
          boxShadow: '0 4px 12px rgba(253, 126, 20, 0.3)',
          minWidth: '400px', // Make it wider so it's more noticeable
        },
        title: {
          fontWeight: 600,
          fontSize: '16px'
        },
        description: {
          whiteSpace: 'pre-line',
          fontSize: '14px'
        }
      }
    });
  };

  // Show initial notification
  showNotification(countdown);

  // Update countdown every second
  const countdownInterval = setInterval(() => {
    countdown--;
    if (countdown > 0) {
      showNotification(countdown);
    } else {
      clearInterval(countdownInterval);
      window.location.href = '/login';
    }
  }, 1000);
}

/**
 * Show a notification when user successfully logs in from new device
 */
export function showLoginSuccessNotification() {
  notifications.show({
    id: 'login-success',
    title: `✅ ${i18n.t('auth.session.invalidated.loginSuccess')}`,
    message: i18n.t('auth.session.invalidated.loginSuccessMessage'),
    color: 'green',
    autoClose: 4000,
  });
}

/**
 * Show a notification when user logs out
 */
export function showLogoutNotification() {
  notifications.show({
    id: 'logout-success',
    title: `👋 ${i18n.t('auth.session.invalidated.logoutSuccess')}`,
    message: i18n.t('auth.session.invalidated.logoutSuccessMessage'),
    color: 'blue',
    autoClose: 3000,
  });
}

/**
 * Clear any existing session-related notifications
 */
export function clearSessionNotifications() {
  notifications.hide('session-invalidated');
  notifications.hide('login-success');
  notifications.hide('logout-success');
}
