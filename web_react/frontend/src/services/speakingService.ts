// Speaking Service for handling API-based Task 2 and Task 3 data

// Task 2 Types (scenario-based, organized by months)
export interface Task2Scenario {
  scenario: string;
  example_questions?: string[]; // Optional for non-premium users
  question_count: number;
  partie_number: number;
  month_year: string;
}

export interface Task2Party {
  partie_number: number;
  scenarios: Task2Scenario[];
}

export interface Task2MonthData {
  month_year: string;
  parties: Task2Party[];
  statistics: {
    total_parties: number;
    total_scenarios: number;
    total_questions: number;
  };
}

// Task 3 Types (topic-based discussions)
export interface Task3Question {
  topic: string;
  question: string;
  task_number: number;
  word_count?: number;
  exemplary_answer?: any; // Optional for non-premium users
}

export interface Task3TopicData {
  topic: string;
  questions: Task3Question[];
  statistics: {
    total_tasks: number;
    total_words: number;
    total_paragraphs: number;
    average_words_per_task: number;
  };
}

// API Response Types
export interface SpeakingTestListResponse {
  task2_tests: {
    id: string;
    title: string;
    type: string;
    free: boolean;
  }[];
  task3_tests: {
    id: string;
    title: string;
    type: string;
    free: boolean;
  }[];
  membership_required: boolean;
}

export interface SpeakingTestCardsResponse {
  task2_cards: {
    id: string;
    title: string;
    type: string;
    free: boolean;
    month_year: string;
    total_parties: number;
    total_scenarios: number;
    total_questions: number;
    task_type: string;
  }[];
  task3_cards: {
    id: string;
    title: string;
    type: string;
    free: boolean;
    month_year: string;
    total_parties: number;
    total_scenarios: number;
    total_questions: number;
    task_type: string;
  }[];
  membership_required: boolean;
}

export interface SpeakingTestDataResponse {
  data: any[];
  user_has_access: boolean;
  membership_required: boolean;
}

class SpeakingService {
  private task2Tests: Task2MonthData[] = [];
  private task3Tests: Task3TopicData[] = [];
  private loaded = false;
  private cardsLoaded = false;
  private task2Cards: any[] = [];
  private task3Cards: any[] = [];
  private cardsCache: SpeakingTestCardsResponse | null = null;
  private cacheTimestamp: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Check if cache is valid
  private isCacheValid(): boolean {
    return this.cardsCache !== null && 
           (Date.now() - this.cacheTimestamp) < this.CACHE_DURATION;
  }

  // Load speaking test list from API (lightweight)
  async loadSpeakingTests(): Promise<SpeakingTestListResponse> {
    try {
      const response = await fetch('/api/tests/speaking/list');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error loading speaking tests:', error);
      throw error;
    }
  }

  // Load speaking test cards from API (optimized for card display with caching)
  async loadSpeakingCards(): Promise<SpeakingTestCardsResponse> {
    // Return cached data if valid
    if (this.isCacheValid() && this.cardsCache) {
      this.task2Cards = this.cardsCache.task2_cards || [];
      this.task3Cards = this.cardsCache.task3_cards || [];
      this.cardsLoaded = true;
      return this.cardsCache;
    }

    try {
      const response = await fetch('/api/tests/speaking/cards');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      
      // Cache the response
      this.cardsCache = data;
      this.cacheTimestamp = Date.now();
      
      this.task2Cards = data.task2_cards || [];
      this.task3Cards = data.task3_cards || [];
      this.cardsLoaded = true;

      return data;
    } catch (error) {
      console.error('Error loading speaking cards:', error);
      throw error;
    }
  }

  // Load specific test data from API
  async loadTestData(testId: string): Promise<SpeakingTestDataResponse> {
    try {
      const response = await fetch(`/api/tests/speaking/${testId}/data`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`Error loading test data for ${testId}:`, error);
      throw error;
    }
  }

  // Convert API Task 2 data to frontend format
  private convertTask2Data(testData: any[], testInfo: any): Task2MonthData {
    // Group scenarios by partie_number
    const partiesMap = new Map<number, Task2Scenario[]>();
    
    for (const scenario of testData) {
      const partieNumber = scenario.partie_number || 1;
      if (!partiesMap.has(partieNumber)) {
        partiesMap.set(partieNumber, []);
      }
      
      partiesMap.get(partieNumber)!.push({
        scenario: scenario.scenario || '',
        example_questions: scenario.example_questions || [],
        question_count: scenario.example_questions?.length || 0,
        partie_number: partieNumber,
        month_year: scenario.month_year || testInfo.month_year
      });
    }

    // Convert to parties array
    const parties: Task2Party[] = Array.from(partiesMap.entries())
      .sort(([a], [b]) => a - b)
      .map(([partieNumber, scenarios]) => ({
        partie_number: partieNumber,
        scenarios: scenarios
      }));

    return {
      month_year: testInfo.month_year,
      parties,
      statistics: {
        total_parties: testInfo.total_parties || parties.length,
        total_scenarios: testInfo.total_scenarios || testData.length,
        total_questions: testInfo.total_questions || testData.reduce((sum, s) => sum + (s.example_questions?.length || 0), 0)
      }
    };
  }

  // Convert API Task 3 data to frontend format
  private convertTask3Data(testData: any[], testInfo: any): Task3TopicData {
    const questions: Task3Question[] = testData.map(item => ({
      topic: item.topic || testInfo.month_year,
      question: item.question || '',
      task_number: item.task_number || 1,
      word_count: item.word_count,
      exemplary_answer: item.exemplary_answer
    }));

    const totalWords = questions.reduce((sum, q) => sum + (q.word_count || 0), 0);
    const totalParagraphs = questions.reduce((sum, q) => {
      if (q.exemplary_answer && typeof q.exemplary_answer === 'string') {
        return sum + q.exemplary_answer.split('\n\n').length;
      }
      return sum;
    }, 0);

    return {
      topic: testInfo.month_year,
      questions,
      statistics: {
        total_tasks: testData.length,
        total_words: totalWords,
        total_paragraphs: totalParagraphs,
        average_words_per_task: totalWords > 0 ? Math.round(totalWords / testData.length) : 0
      }
    };
  }

  // Get Task 2 data organized by month (fast loading from cards)
  async loadTask2Data(): Promise<Record<string, Task2MonthData>> {
    // Load cards first for fast display
    if (!this.cardsLoaded) {
      await this.loadSpeakingCards();
    }
    
    const task2Data: Record<string, Task2MonthData> = {};
    
    for (const card of this.task2Cards) {
      // Create Task2MonthData from card info without loading full test data
      const monthData: Task2MonthData = {
        month_year: card.month_year || card.id,
        parties: [], // Will be empty for card display, loaded on demand
        statistics: {
          total_parties: card.total_parties || 0,
          total_scenarios: card.total_scenarios || 0,
          total_questions: card.total_questions || 0
        }
      };
      
      // Use a normalized key for the month
      const monthKey = (card.month_year || card.id).toLowerCase().replace(/[^a-z0-9-]/g, '-');
      task2Data[monthKey] = monthData;
    }
    
    return task2Data;
  }

  // Get Task 3 data organized by topic (fast loading from cards)
  async loadTask3Data(): Promise<Record<string, Task3TopicData>> {
    // Load cards first for fast display
    if (!this.cardsLoaded) {
      await this.loadSpeakingCards();
    }
    
    const task3Data: Record<string, Task3TopicData> = {};
    
    for (const card of this.task3Cards) {
      // Create Task3TopicData from card info without loading full test data
      const topicData: Task3TopicData = {
        topic: card.month_year || card.id,
        questions: [], // Will be empty for card display, loaded on demand
        statistics: {
          total_tasks: card.total_scenarios || 0, // For task 3, scenarios = tasks
          total_words: 0, // Not available in card data
          total_paragraphs: 0, // Not available in card data
          average_words_per_task: 0 // Will be calculated from card data if available
        }
      };
      
      // Use a normalized key for the topic
      const topicKey = (card.month_year || card.id).toLowerCase().replace(/[^a-z0-9-]/g, '-');
      task3Data[topicKey] = topicData;
    }
    
    return task3Data;
  }

  // Get list of available months for Task 2
  async getTask2Months(): Promise<string[]> {
    const data = await this.loadTask2Data();
    return Object.keys(data).sort();
  }

  // Get list of available topics for Task 3  
  async getTask3Topics(): Promise<string[]> {
    const data = await this.loadTask3Data();
    return Object.keys(data).sort();
  }

  // Get specific month data for Task 2 (loads full data on demand)
  async getTask2MonthData(monthId: string): Promise<Task2MonthData | null> {
    // First check if we have card data
    const cardData = await this.loadTask2Data();
    const cardInfo = cardData[monthId];
    
    if (!cardInfo) {
      return null;
    }
    
    // If parties array is empty, we need to load full test data
    if (cardInfo.parties.length === 0) {
      try {
        // Find the original test ID from cards
        const card = this.task2Cards.find(c => 
          (c.month_year || c.id).toLowerCase().replace(/[^a-z0-9-]/g, '-') === monthId
        );
        
        if (card) {
          const testData = await this.loadTestData(card.id);
          const fullData = this.convertTask2Data(testData.data, card);
          
          // Update the cached data
          cardData[monthId] = fullData;
          return fullData;
        }
      } catch (error) {
        console.error(`Error loading full data for ${monthId}:`, error);
      }
    }
    
    return cardInfo;
  }

  // Get specific topic data for Task 3 (loads full data on demand)
  async getTask3TopicData(topicId: string): Promise<Task3TopicData | null> {
    // First check if we have card data
    const cardData = await this.loadTask3Data();
    const cardInfo = cardData[topicId];
    
    if (!cardInfo) {
      return null;
    }
    
    // If questions array is empty, we need to load full test data
    if (cardInfo.questions.length === 0) {
      try {
        // Find the original test ID from cards
        const card = this.task3Cards.find(c => 
          (c.month_year || c.id).toLowerCase().replace(/[^a-z0-9-]/g, '-') === topicId
        );
        
        if (card) {
          const testData = await this.loadTestData(card.id);
          const fullData = this.convertTask3Data(testData.data, card);
          
          // Update the cached data
          cardData[topicId] = fullData;
          return fullData;
        }
      } catch (error) {
        console.error(`Error loading full data for ${topicId}:`, error);
      }
    }
    
    return cardInfo;
  }

  // Get Task 2 statistics
  async getTask2Statistics() {
    const data = await this.loadTask2Data();
    const months = Object.keys(data);
    
    if (months.length === 0) return null;

    const totalMonths = months.length;
    const totalParties = Object.values(data).reduce((sum, month) => sum + month.statistics.total_parties, 0);
    const totalScenarios = Object.values(data).reduce((sum, month) => sum + month.statistics.total_scenarios, 0);
    const totalQuestions = Object.values(data).reduce((sum, month) => sum + month.statistics.total_questions, 0);

    return {
      totalMonths,
      totalParties,
      totalScenarios,
      totalQuestions,
      averagePartiesPerMonth: Math.round(totalParties / totalMonths),
      averageScenariosPerMonth: Math.round(totalScenarios / totalMonths),
      averageQuestionsPerScenario: Math.round(totalQuestions / totalScenarios)
    };
  }

  // Get Task 3 statistics
  async getTask3Statistics() {
    const data = await this.loadTask3Data();
    const topics = Object.keys(data);
    
    if (topics.length === 0) return null;

    const totalTopics = topics.length;
    const totalTasks = Object.values(data).reduce((sum, topic) => sum + topic.statistics.total_tasks, 0);
    const totalWords = Object.values(data).reduce((sum, topic) => sum + topic.statistics.total_words, 0);
    const totalParagraphs = Object.values(data).reduce((sum, topic) => sum + topic.statistics.total_paragraphs, 0);

    return {
      totalTopics,
      totalTasks,
      totalWords,
      totalParagraphs,
      averageTasksPerTopic: Math.round(totalTasks / totalTopics),
      averageWordsPerTask: Math.round(totalWords / totalTasks)
    };
  }

  // Reset data to force reload
  reset(): void {
    this.loaded = false;
    this.cardsLoaded = false;
    this.task2Tests = [];
    this.task3Tests = [];
    this.task2Cards = [];
    this.task3Cards = [];
    this.cardsCache = null;
    this.cacheTimestamp = 0;
  }

  // Force refresh data (bypass cache)
  async forceRefresh(): Promise<void> {
    this.cardsCache = null;
    this.cacheTimestamp = 0;
    this.cardsLoaded = false;
    await this.loadSpeakingCards();
  }
}

export const speakingService = new SpeakingService(); 