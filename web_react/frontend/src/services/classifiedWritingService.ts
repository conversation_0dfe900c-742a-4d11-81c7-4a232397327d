import { api } from './api';

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

export interface ClassificationContext {
  tache_number: number;
  main_topic_name: string;
  subtopic_name: string;
}

export interface TaskEntry {
  representative_id: string;
  task_content: string;
  clean_content?: string;
  is_duplicate_group: boolean;
  duplicate_count: number;
  task_ids: string[];
  month_years: string[];
  combination_numbers: string[];
  metadata?: Record<string, any>;
  classification_context?: ClassificationContext;
}

export interface Subtopic {
  id: string;
  subtopic_name: string;
  subtopic_id?: number;
  keywords: string[];
  task_count: number;
  unique_task_count: number;
  template_similarity?: number;
  description?: string;
  sample_tasks?: TaskEntry[];
  task_entries?: TaskEntry[];  // Add task_entries for direct access
  translations?: {
    en?: string;
    fr?: string;
    zh?: string;
  };
}

export interface MainTopic {
  id: string;
  topic_name: string;
  topic_id: number;
  keywords: string[];
  total_tasks: number;
  unique_tasks: number;
  description?: string;
  subtopics?: Record<string, Subtopic>;  // Subtopics as dictionary
  translations?: {
    en?: string;
    fr?: string;
    zh?: string;
  };
}

export interface ClassificationMetadata {
  tache_number: number;
  checkpoint_name?: string;
  method?: string;
  total_tasks: number;
  unique_tasks: number;
  n_main_topics: number;
  created_timestamp?: string;
  total_modifications?: number;
  description?: string;
  data_version?: string;
}

export interface TacheCard {
  tache_number: number;
  title: string;
  total_tasks: number;
  unique_tasks: number;
  top_topics: Array<{
    name: string;
    total_tasks: number;
  }>;
}

export interface TacheOverview {
  tache_number: number;
  metadata: ClassificationMetadata;
  topics: Record<string, MainTopic>;  // Topics as dictionary, not array
  user_is_premium: boolean;
}

export interface TopicDetails {
  tache_number: number;
  topic: MainTopic;
  user_is_premium: boolean;
}

export interface SubtopicTasksResponse {
  subtopic: {
    id: string;
    subtopic_name: string;
    task_count: number;
    unique_task_count: number;
    main_topic_name: string;
    tache_number: number;
  };
  tasks: TaskEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  user_is_premium: boolean;
}

export interface TaskDetailsResponse {
  task: TaskEntry;
  user_is_premium: boolean;
}

export interface SearchFilters {
  tache_number?: number;
  topic_name?: string;
}

export interface SearchResponse {
  query: string;
  filters: SearchFilters;
  tasks: TaskEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  user_is_premium: boolean;
}

export interface CardsResponse {
  cards: TacheCard[];
  total_unique_tasks: number;
  user_is_premium: boolean;
  membership_required: boolean;
}





// ============================================================================
// SERVICE CLASS
// ============================================================================

class ClassifiedWritingService {
  private cache: Map<string, any> = new Map();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cached data or null if expired/not found
   */
  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const { data, timestamp } = cached;
    if (Date.now() - timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return data;
  }

  /**
   * Set cached data with timestamp
   */
  private setCachedData<T>(key: string, data: T): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Get test cards for all three tâches
   */
  async getCards(): Promise<CardsResponse> {
    const cacheKey = 'classified-writing-cards';
    const cached = this.getCachedData<CardsResponse>(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get('/classified-writing/cards');
      const data = response.data as CardsResponse;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error('Error fetching classified writing cards:', error);
      
      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux tâches classifiées');
      }
      
      throw new Error('Erreur lors du chargement des cartes de tâches classifiées');
    }
  }

  /**
   * Get overview of a specific tâche
   */
  async getTacheOverview(tacheNumber: number): Promise<TacheOverview> {
    if (![1, 2, 3].includes(tacheNumber)) {
      throw new Error('Numéro de tâche invalide. Doit être 1, 2 ou 3.');
    }

    const cacheKey = `tache-overview-${tacheNumber}`;
    const cached = this.getCachedData<TacheOverview>(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`/classified-writing/tache/${tacheNumber}`);
      const data = response.data as TacheOverview;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error(`Error fetching tâche ${tacheNumber} overview:`, error);
      
      if (error.response?.status === 404) {
        throw new Error(`Tâche ${tacheNumber} non trouvée`);
      }
      
      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux détails de la tâche');
      }
      
      throw new Error(`Erreur lors du chargement de la Tâche ${tacheNumber}`);
    }
  }

  /**
   * Get detailed information about a specific topic
   */
  async getTopicDetails(tacheNumber: number, topicName: string): Promise<TopicDetails> {
    if (![1, 2, 3].includes(tacheNumber)) {
      throw new Error('Numéro de tâche invalide. Doit être 1, 2 ou 3.');
    }

    const cacheKey = `topic-details-${tacheNumber}-${topicName}`;
    const cached = this.getCachedData<TopicDetails>(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`/classified-writing/tache/${tacheNumber}/topics/${encodeURIComponent(topicName)}`);
      const data = response.data as TopicDetails;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error(`Error fetching topic ${topicName} details:`, error);
      
      if (error.response?.status === 404) {
        throw new Error(`Sujet "${topicName}" non trouvé dans la Tâche ${tacheNumber}`);
      }
      
      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux détails du sujet');
      }
      
      throw new Error(`Erreur lors du chargement des détails du sujet`);
    }
  }

  /**
   * Get all task entries for a specific subtopic with pagination
   */
  async getSubtopicTasks(
    subtopicId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<SubtopicTasksResponse> {
    const cacheKey = `subtopic-tasks-${subtopicId}-${page}-${limit}`;
    const cached = this.getCachedData<SubtopicTasksResponse>(cacheKey);
    if (cached) return cached;

    try {
      // Double-encode the subtopic ID to handle forward slashes in Flask routing
      const encodedSubtopicId = encodeURIComponent(encodeURIComponent(subtopicId));
      const response = await api.get(`/classified-writing/subtopic/${encodedSubtopicId}/tasks`, {
        params: { page, limit }
      });
      const data = response.data as SubtopicTasksResponse;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error(`Error fetching subtopic ${subtopicId} tasks:`, error);

      if (error.response?.status === 404) {
        throw new Error('Sous-sujet non trouvé');
      }

      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux tâches');
      }

      throw new Error('Erreur lors du chargement des tâches du sous-sujet');
    }
  }

  /**
   * Get detailed information about a specific task entry
   */
  async getTaskDetails(representativeId: string): Promise<TaskDetailsResponse> {
    const cacheKey = `task-details-${representativeId}`;
    const cached = this.getCachedData<TaskDetailsResponse>(cacheKey);
    if (cached) return cached;

    try {
      const response = await api.get(`/classified-writing/task/${representativeId}`);
      const data = response.data as TaskDetailsResponse;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error(`Error fetching task ${representativeId} details:`, error);

      if (error.response?.status === 404) {
        throw new Error('Tâche non trouvée');
      }

      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux détails de la tâche');
      }

      throw new Error('Erreur lors du chargement des détails de la tâche');
    }
  }

  /**
   * Search tasks across all tâches with filters
   */
  async searchTasks(
    query: string,
    filters: SearchFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<SearchResponse> {
    if (!query.trim() && !filters.topic_name) {
      throw new Error('Requête de recherche ou filtre de sujet requis');
    }

    const cacheKey = `search-${query}-${JSON.stringify(filters)}-${page}-${limit}`;
    const cached = this.getCachedData<SearchResponse>(cacheKey);
    if (cached) return cached;

    try {
      const params: any = { q: query, page, limit };

      if (filters.tache_number) {
        params.tache = filters.tache_number;
      }

      if (filters.topic_name) {
        params.topic = filters.topic_name;
      }

      const response = await api.get('/classified-writing/search', { params });
      const data = response.data as SearchResponse;
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error: any) {
      console.error('Error searching tasks:', error);

      if (error.response?.status === 400) {
        throw new Error('Paramètres de recherche invalides');
      }

      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour effectuer une recherche');
      }

      throw new Error('Erreur lors de la recherche de tâches');
    }
  }

  /**
   * Get available tâche numbers
   */
  getAvailableTaches(): number[] {
    return [1, 2, 3];
  }

  /**
   * Format tâche title
   */
  formatTacheTitle(tacheNumber: number): string {
    const titles = {
      1: 'Tâche 1 - Message personnel',
      2: 'Tâche 2 - Article informatif',
      3: 'Tâche 3 - Essai argumentatif'
    };
    return titles[tacheNumber as keyof typeof titles] || `Tâche ${tacheNumber}`;
  }

  /**
   * Get tâche description
   */
  getTacheDescription(tacheNumber: number): string {
    const descriptions = {
      1: 'Rédaction de messages personnels (60-120 mots)',
      2: 'Rédaction d\'articles informatifs (120-150 mots)',
      3: 'Rédaction d\'essais argumentatifs (120-180 mots)'
    };
    return descriptions[tacheNumber as keyof typeof descriptions] || '';
  }

  /**
   * Get word limits for a tâche
   */
  getWordLimits(tacheNumber: number): { min: number; max: number } {
    const limits = {
      1: { min: 60, max: 120 },
      2: { min: 120, max: 150 },
      3: { min: 120, max: 180 }
    };
    return limits[tacheNumber as keyof typeof limits] || { min: 60, max: 120 };
  }

  /**
   * Extract keywords from task content for highlighting
   */
  extractKeywords(taskContent: string, maxKeywords: number = 5): string[] {
    // Simple keyword extraction - remove common French words and extract meaningful terms
    const commonWords = new Set([
      'le', 'la', 'les', 'un', 'une', 'des', 'du', 'de', 'et', 'ou', 'mais', 'donc', 'car',
      'que', 'qui', 'quoi', 'dont', 'où', 'quand', 'comment', 'pourquoi',
      'je', 'tu', 'il', 'elle', 'nous', 'vous', 'ils', 'elles',
      'mon', 'ma', 'mes', 'ton', 'ta', 'tes', 'son', 'sa', 'ses', 'notre', 'votre', 'leur',
      'ce', 'cette', 'ces', 'cet', 'dans', 'sur', 'avec', 'pour', 'par', 'sans', 'sous'
    ]);

    const words = taskContent
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.has(word))
      .slice(0, maxKeywords);

    return [...new Set(words)]; // Remove duplicates
  }

  /**
   * Check if user has access to premium features
   */
  async checkPremiumAccess(): Promise<boolean> {
    try {
      const response = await this.getCards();
      return response.user_is_premium;
    } catch (error) {
      return false;
    }
  }




}

// Export singleton instance
export const classifiedWritingService = new ClassifiedWritingService();
export default classifiedWritingService;
