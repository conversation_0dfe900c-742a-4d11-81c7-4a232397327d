import axios from 'axios';
import type { AxiosError, InternalAxiosRequestConfig } from 'axios';
import { showSessionInvalidatedNotification } from '../utils/sessionUtils';
import type {
  User,
  Test,
  TestHistory,
  NotebookNote,
  Highlight,
  TestData,
  TestsResponse,
  AuthResponse,
  ScoreResult,
  PromoCode,
  PromoUsageLog
} from '../types';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5001/api';

const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to handle auth tokens if needed
api.interceptors.request.use((config: InternalAxiosRequestConfig) => {
  // Add any auth headers here if using JWT tokens
  return config;
});

// Response interceptor to handle errors and session invalidation
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      const errorData = error.response.data as any;

      // Handle session invalidation with user-friendly message
      if (errorData?.code === 'SESSION_INVALID') {
        // Clear any local storage
        localStorage.clear();
        sessionStorage.clear();

        // Show professional session invalidation notification with countdown
        // The notification itself handles the redirect timing
        showSessionInvalidatedNotification(errorData.message, errorData.messageData);
      } else {
        // Handle regular unauthorized access
        const currentPath = window.location.pathname;
        if (currentPath !== '/login' && currentPath !== '/register') {
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

export const authApi = {
  login: async (username: string, password: string): Promise<AuthResponse> => {
    const response = await api.post('/auth/login', { username, password });
    return response.data;
  },
  
  register: async (username: string, email: string, password: string): Promise<AuthResponse> => {
    const response = await api.post('/auth/register', { username, email, password });
    return response.data;
  },
  
  // New Google OAuth methods
  googleCallback: async (accessToken: string, refreshToken: string, user: any): Promise<AuthResponse> => {
    const response = await api.post('/auth/google/callback', { 
      access_token: accessToken,
      refresh_token: refreshToken,
      user 
    });
    return response.data;
  },
  
  verifyToken: async (accessToken: string, refreshToken?: string): Promise<AuthResponse> => {
    const response = await api.post('/auth/verify-token', { 
      access_token: accessToken,
      refresh_token: refreshToken 
    });
    return response.data;
  },
  
  logout: async (): Promise<{ message: string }> => {
    const response = await api.post('/auth/logout');
    return response.data;
  },
  
  getProfile: async (): Promise<{ user: User }> => {
    const response = await api.get('/auth/profile');
    return response.data;
  },
  
  forgotPassword: async (email: string): Promise<{ message: string }> => {
    const response = await api.post('/auth/forgot-password', { email });
    return response.data;
  },
  
  resetPassword: async (token: string, password: string): Promise<{ message: string }> => {
    const response = await api.post(`/auth/reset-password/${token}`, { 
      password: password,
      confirm_password: password 
    });
    return response.data;
  },
  
  changePassword: async (currentPassword: string, newPassword: string): Promise<{ message: string }> => {
    const response = await api.post('/auth/change-password', { 
      current_password: currentPassword, 
      new_password: newPassword 
    });
    return response.data;
  },

  requestProfilePasswordReset: async (): Promise<{ message: string; email: string }> => {
    const response = await api.post('/auth/profile/request-password-reset');
    return response.data;
  },

  verifyEmail: async (email: string, verificationCode: string): Promise<AuthResponse> => {
    const response = await api.post('/auth/verify-email', { 
      email, 
      verification_code: verificationCode 
    });
    return response.data;
  },

  resendVerification: async (email: string): Promise<{ message: string }> => {
    const response = await api.post('/auth/resend-verification', { email });
    return response.data;
  },

  checkPendingRegistration: async (email?: string, username?: string): Promise<{
    has_pending_registration: boolean;
    email?: string;
    username?: string;
    expires_at?: string;
  }> => {
    const response = await api.post('/auth/check-pending-registration', { email, username });
    return response.data;
  },
};

export const testApi = {
  getTests: async (): Promise<TestsResponse> => {
    const response = await api.get('/tests');
    return response.data;
  },
  
  getSectionTests: async (section: string): Promise<{ 
    section: string; 
    tests: Array<{ id: string; title: string; progress?: number; total?: number }>; 
    free_tests: Array<{ id: string; title: string; progress?: number; total?: number }>; 
    group_progress: Record<number, { progress?: number; total: number }>;
  }> => {
    const response = await api.get(`/tests/${section}`);
    return response.data;
  },
  
  getTestData: async (section: string, testId: string, isFree: boolean = false): Promise<TestData> => {
    const response = await api.get(`/tests/${section}/${testId}/data?free=${isFree ? '1' : '0'}`);
    return response.data;
  },

  getTestInfo: async (section: string, testId: string, isFree: boolean = false): Promise<{
    section: string;
    test_id: string;
    free: boolean;
    previous_answers?: Record<string, string | string[]>;
    current_question?: number;
    grading_results?: {
      correct: number[];
      incorrect: number[];
      wrong_details: Array<{
        question: string;
        yourAnswer: string;
        correctAnswer: string;
      }>;
    };
    has_history: boolean;
  }> => {
    const response = await api.get(`/tests/${section}/${testId}?free=${isFree ? '1' : '0'}`);
    return response.data;
  },

  getGroupTestData: async (section: string, groupId: number): Promise<any> => {
    const response = await api.get(`/tests/group/${section}/${groupId}`);
    return response.data;
  },

  getGroupTestInfo: async (section: string, groupId: number): Promise<{
    section: string;
    group_id: number;
    group_data: any[];
    previous_answers?: Record<string, string | string[]>;
    current_question?: number;
    grading_results?: {
      correct: number[];
      incorrect: number[];
      wrong_details: Array<{
        question: string;
        yourAnswer: string;
        correctAnswer: string;
      }>;
    };
    has_history: boolean;
  }> => {
    const response = await api.get(`/tests/group/${section}/${groupId}`);
    return response.data;
  },
  
  saveTestHistory: async (data: {
    section: string;
    test_id: string;
    free: boolean;
    answers: Record<string, string | string[]>;
    current_question?: number;
    score?: number;
    max_score?: number;
    grading_results?: {
      correct: number[];
      incorrect: number[];
      wrong_details: Array<{
        question: string;
        yourAnswer: string;
        correctAnswer: string;
      }>;
    };
  }): Promise<{ message: string }> => {
    const response = await api.post('/tests/history', data);
    return response.data;
  },
  
  getTestHistory: async (): Promise<{ history: TestHistory[] }> => {
    const response = await api.get('/tests/history');
    return response.data;
  },
  
  calculateScore: async (data: {
    section: string;
    test_id: string;
    answers: Record<string, string | string[]>;
    free?: boolean;
  }): Promise<ScoreResult> => {
    const response = await api.post('/tests/calculate-score', data);
    return response.data;
  },
  
  deleteTestHistory: async (section: string, testId: string, isFree: boolean = false): Promise<{
    status: string;
    message: string;
    deleted_records: number;
  }> => {
    const response = await api.delete(`/tests/history/${section}/${testId}?free=${isFree ? '1' : '0'}`);
    return response.data;
  },

  // Writing Submissions API
  saveWritingSubmission: async (data: {
    test_type: string;
    test_identifier: string;
    task_number: number;
    combination_number?: string;
    content: string;
    content_format?: string;
    status?: 'draft' | 'submitted';
    writing_duration?: number;
    auto_save_count?: number;
    metadata?: Record<string, any>;
  }): Promise<{
    status: string;
    submission_id: string;
    word_count: number;
    character_count: number;
    version: number;
  }> => {
    const response = await api.post('/tests/writing/submissions', data);
    return response.data;
  },

  getWritingSubmissions: async (
    testType: string,
    testIdentifier: string,
    taskNumber?: number,
    combinationNumber?: string
  ): Promise<{
    status: string;
    submissions: Array<{
      id: string;
      test_type: string;
      test_identifier: string;
      task_number: number;
      combination_number?: string;
      content: string;
      content_format: string;
      word_count: number;
      character_count: number;
      status: string;
      submission_time?: string;
      writing_duration?: number;
      version: number;
      is_final_submission: boolean;
      created_at: string;
      updated_at: string;
    }>;
  }> => {
    const params = new URLSearchParams();
    if (taskNumber) params.append('task_number', taskNumber.toString());
    if (combinationNumber) params.append('combination_number', combinationNumber);

    const response = await api.get(`/tests/writing/submissions/${testType}/${testIdentifier}?${params}`);
    return response.data;
  },

  getWritingSubmissionHistory: async (
    limit: number = 50,
    offset: number = 0,
    testType?: string
  ): Promise<{
    status: string;
    submissions: Array<{
      id: string;
      test_type: string;
      test_identifier: string;
      task_number: number;
      combination_number?: string;
      word_count: number;
      character_count: number;
      status: string;
      submission_time?: string;
      created_at: string;
      updated_at: string;
      version: number;
      is_final_submission: boolean;
    }>;
    pagination: {
      limit: number;
      offset: number;
      count: number;
    };
  }> => {
    const params = new URLSearchParams();
    params.append('limit', limit.toString());
    params.append('offset', offset.toString());
    if (testType) params.append('test_type', testType);

    const response = await api.get(`/tests/writing/submissions/history?${params}`);
    return response.data;
  },

  getWritingSubmissionById: async (submissionId: string): Promise<{
    status: string;
    submission: {
      id: string;
      test_type: string;
      test_identifier: string;
      task_number: number;
      combination_number?: string;
      content: string;
      content_format: string;
      word_count: number;
      character_count: number;
      status: string;
      submission_time?: string;
      writing_duration?: number;
      version: number;
      is_final_submission: boolean;
      metadata: Record<string, any>;
      created_at: string;
      updated_at: string;
    };
  }> => {
    const response = await api.get(`/tests/writing/submissions/${submissionId}`);
    return response.data;
  },

  deleteWritingSubmission: async (submissionId: string): Promise<{
    status: string;
    message: string;
  }> => {
    const response = await api.delete(`/tests/writing/submissions/${submissionId}`);
    return response.data;
  },

  getWritingSubmissionStats: async (): Promise<{
    status: string;
    stats: {
      total_submissions: number;
      final_submissions: number;
      draft_submissions: number;
      by_test_type: Record<string, {
        count: number;
        final_count: number;
        total_words: number;
      }>;
      total_words_written: number;
      average_word_count: number;
      recent_activity: Array<{
        test_type: string;
        test_identifier: string;
        task_number: number;
        word_count: number;
        status: string;
        created_at: string;
      }>;
    };
  }> => {
    const response = await api.get('/tests/writing/submissions/stats');
    return response.data;
  },

  // Fast list endpoints for initial loading (no test history)
  getReadingTestsList: async (): Promise<{
    section: string;
    tests: Array<{ id: string; title: string; type: string; free: boolean; total: number }>;
    free_tests: Array<{ id: string; title: string; type: string; free: boolean; total: number }>;
    group_tests: Array<{ id: number; title: string; total: number }>;
    membership_required: boolean;
  }> => {
    const response = await api.get('/tests/reading/list');
    return response.data;
  },

  getListeningTestsList: async (): Promise<{
    section: string;
    tests: Array<{ id: string; title: string; type: string; free: boolean; total: number }>;
    free_tests: Array<{ id: string; title: string; type: string; free: boolean; total: number }>;
    group_tests: Array<{ id: number; title: string; total: number }>;
    membership_required: boolean;
  }> => {
    const response = await api.get('/tests/listening/list');
    return response.data;
  },

  // Detailed card endpoints with test history/progress
  getReadingTestCards: async (): Promise<{
    section: string;
    tests: Array<{ id: string; title: string; type: string; free: boolean; total: number; progress?: number; grading?: any }>;
    free_tests: Array<{ id: string; title: string; type: string; free: boolean; total: number; progress?: number; grading?: any }>;
    group_progress: Record<number, { progress?: number; total: number; grading?: any }>;
    membership_required: boolean;
  }> => {
    const response = await api.get('/tests/reading/cards');
    return response.data;
  },

  getListeningTestCards: async (): Promise<{
    section: string;
    tests: Array<{ id: string; title: string; type: string; free: boolean; total: number; progress?: number; grading?: any }>;
    free_tests: Array<{ id: string; title: string; type: string; free: boolean; total: number; progress?: number; grading?: any }>;
    group_progress: Record<number, { progress?: number; total: number; grading?: any }>;
    membership_required: boolean;
  }> => {
    const response = await api.get('/tests/listening/cards');
    return response.data;
  },

  // Get writing test statistics (for cards display)
  getWritingTestCards: async (): Promise<{
    tests: Array<{ id: string; title: string; type: string; statistics: { total_tasks: number; total_combinations: number; has_corrections: boolean } }>
  }> => {
    const response = await api.get('/tests/writing/cards');
    return response.data;
  },

  // Collection Book APIs
  getCollectionBook: async (): Promise<{
    reading_collection: Array<any>;
    listening_collection: Array<any>;
  }> => {
    const response = await api.get('/tests/collection-book');
    return response.data;
  },

  addBookmark: async (questionId: string): Promise<{ success: boolean; message: string; bookmark_id?: string }> => {
    const response = await api.post('/tests/collection-book/bookmark', {
      question_id: questionId
    });
    return response.data;
  },

  removeBookmark: async (questionId: string): Promise<{ success: boolean; message: string }> => {
    const response = await api.delete('/tests/collection-book/bookmark', {
      data: { question_id: questionId }
    });
    return response.data;
  },

  checkBookmarkStatus: async (questionId: string): Promise<{ bookmarked: boolean }> => {
    const response = await api.get(`/tests/collection-book/check/${questionId}`);
    return response.data;
  },

  checkBookmarkStatusBatch: async (questionIds: string[]): Promise<{ bookmarks: Record<string, boolean>; total_checked: number; total_bookmarked: number }> => {
    const response = await api.post('/tests/collection-book/check/batch', {
      question_ids: questionIds
    });
    return response.data;
  },

  addBulkBookmarks: async (data: {
    questions: Array<{ question_id: string }>;
    testType: 'reading' | 'listening';
  }): Promise<{ 
    added_count: number; 
    already_bookmarked: number; 
    errors: string[]; 
    total_processed: number;
  }> => {
    const response = await api.post('/tests/collection-book/bookmark/bulk', data);
    return response.data;
  },
};

export const mockExamApi = {
  // Get all mock exams for a section
  getMockExams: async (section: string): Promise<Array<{
    id: string;
    exam_name: string;
    test_type: string;
    total_questions: number;
    difficulty_breakdown: Record<string, number>;
    created_at: string;
    is_completed: boolean;
    score?: number;
    max_score?: number;
  }>> => {
    const response = await api.get(`/mock-exams/${section}`);
    return response.data;
  },

  // Create a new mock exam
  createMockExam: async (section: string, examName: string): Promise<{
    mock_exam_id: string;
    exam_name: string;
    total_questions: number;
    difficulty_breakdown: Record<string, number>;
    message: string;
  }> => {
    const response = await api.post(`/mock-exams/${section}/create`, {
      exam_name: examName
    });
    return response.data;
  },

  // Delete a mock exam
  deleteMockExam: async (section: string, examId: string): Promise<void> => {
    await api.delete(`/mock-exams/${section}/${examId}`);
  },

  // Get mock exam data for taking the exam
  getMockExamData: async (section: string, examId: string): Promise<{
    exam_info: {
      id: string;
      exam_name: string;
      test_type: string;
      is_completed: boolean;
      score?: number;
      max_score?: number;
    };
    questions: Array<any>;
    total_questions: number;
  }> => {
    const response = await api.get(`/mock-exams/${section}/${examId}/data`);
    return response.data;
  },
};

export const notebookApi = {
  // Save notebook notes to database
  saveNote: async (notes: string): Promise<{ status: string; action: string }> => {
    const response = await api.post('/user/notebook', { notes });
    return response.data;
  },
  
  // Get notebook notes from database  
  getNote: async (): Promise<{ notes: string }> => {
    const response = await api.get('/user/notebook');
    return response.data;
  },
  
  // Get all notebook notes for user with pagination
  getAllNotes: async (page: number = 1, perPage: number = 10): Promise<{ 
    notebooks: Array<{ test_path: string; notes: string; updated_at: string }>; 
    pagination: {
      page: number;
      per_page: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    }
  }> => {
    const response = await api.get(`/user/notebooks?page=${page}&per_page=${perPage}`);
    return response.data;
  },
  
  // Delete notebook notes from database (global notebook)
  deleteNote: async (): Promise<{ status: string; message: string }> => {
    const response = await api.delete('/user/notebook');
    return response.data;
  },

  // Delete specific notebook by test_path
  deleteSpecificNote: async (testPath: string): Promise<{ status: string; message: string }> => {
    const response = await api.delete(`/user/notebook/${encodeURIComponent(testPath)}`);
    return response.data;
  },

  // Save global notebook with a custom name
  saveGlobalNotebookAs: async (name: string, clearGlobal: boolean = true): Promise<{
    status: string;
    message: string;
    notebook_name: string;
    global_cleared: boolean
  }> => {
    const response = await api.post('/user/notebook/save-as', {
      name,
      clear_global: clearGlobal
    });
    return response.data;
  },

  // Update specific notebook content
  updateSpecificNote: async (testPath: string, notes: string): Promise<{
    status: string;
    message: string;
    test_path: string
  }> => {
    const response = await api.put(`/user/notebook/${encodeURIComponent(testPath)}`, { notes });
    return response.data;
  },
};

export const highlightApi = {
  saveHighlight: async (data: {
    section: string;
    test_id: string;
    question_index: number;
    start: number;
    end: number;
    text: string;
    color?: string;
    free?: boolean;
  }): Promise<{ message: string }> => {
    const response = await api.post('/user/highlights/save', data);
    return response.data;
  },
  
  getHighlights: async (
    section: string, 
    testId: string, 
    questionIndex: number, 
    isFree: boolean = false
  ): Promise<{ highlights: Array<{ start: number; end: number; text: string; color?: string }> }> => {
    const response = await api.get('/user/highlights', {
      params: { 
        section, 
        test_id: testId, 
        question_index: questionIndex,
        free: isFree ? '1' : '0'
      },
    });
    return response.data;
  },
  
  removeHighlight: async (data: {
    section: string;
    test_id: string;
    question_index: number;
    start: number;
    end: number;
    free?: boolean;
  }): Promise<{ message: string }> => {
    const response = await api.delete('/user/highlights', { data });
    return response.data;
  },
};

export const paymentsApi = {
  createCheckoutSession: async (plan: string): Promise<{ sessionId: string }> => {
    const response = await api.post('/payments/create-checkout-session', { plan });
    return { sessionId: response.data.session_id };
  },
  
  getMembershipStatus: async (): Promise<{ 
    membership_status: any; 
    user: { 
      id: string; 
      username: string; 
      email: string; 
      membership_type?: string; 
      membership_expires_at?: string; 
    } 
  }> => {
    const response = await api.get('/payments/membership-status');
    return response.data;
  },
  
  redeemPromoCode: async (code: string): Promise<{ message: string; membership_expires_at: string }> => {
    const response = await api.post('/payments/promo/redeem', { promo_code: code });
    return response.data;
  },
};

export const assetsApi = {
  getAssetUrl: (section: string, testId: string, filename: string, isFree: boolean = false): string => {
    const freeParam = isFree ? '?free=1' : '';
    return `${API_URL}/assets/${section}/${testId}/${filename}${freeParam}`;
  },
  
  getCorrectAnswers: async (section: string): Promise<any> => {
    const response = await api.get(`/assets/correct-answers/${section}`);
    return response.data;
  },
  
  getCorrectAnswersByGroup: async (section: string): Promise<any> => {
    const response = await api.get(`/assets/correct-answers/${section}/by-group`);
    return response.data;
  },
};

export const adminApi = {
  getUsers: async (): Promise<{ users: User[] }> => {
    const response = await api.get('/admin/users');
    return response.data;
  },
  
  updateUserMembership: async (userId: string, membership: number, expiresAt?: string): Promise<{ message: string }> => {
    const response = await api.post('/admin/users/membership', {
      user_id: userId,
      membership,
      expires_at: expiresAt,
    });
    return response.data;
  },
  
  getPromoUsageLogs: async (): Promise<{ logs: any[] }> => {
    const response = await api.get('/admin/promo-usage-logs');
    return response.data;
  },
};

export const utilsApi = {
  contactSupport: async (message: string): Promise<{ message: string }> => {
    const response = await api.post('/utils/contact-support', { message });
    return response.data;
  },

  healthCheck: async (): Promise<{ status: string; message: string }> => {
    const response = await api.get('/health');
    return response.data;
  },
};

export const translationApi = {
  translate: async (text: string, targetLanguage: string, sourceLanguage: string = 'fr'): Promise<{
    success: boolean;
    translated_text?: string;
    source_language?: string;
    target_language?: string;
    confidence?: number;
    error?: string;
  }> => {
    const response = await api.post('/translation/translate', {
      text,
      target_language: targetLanguage,
      source_language: sourceLanguage,
    });
    return response.data;
  },

  batchTranslate: async (texts: string[], targetLanguage: string, sourceLanguage: string = 'fr'): Promise<{
    success: boolean;
    translations?: Array<{
      original: string;
      translated: string;
      success: boolean;
      error?: string;
    }>;
    target_language?: string;
    source_language?: string;
    error?: string;
  }> => {
    const response = await api.post('/translation/batch-translate', {
      texts,
      target_language: targetLanguage,
      source_language: sourceLanguage,
    });
    return response.data;
  },

  getSupportedLanguages: async (): Promise<{
    success: boolean;
    languages?: {
      source: string[];
      target: string[];
    };
    language_names?: Record<string, string>;
    error?: string;
  }> => {
    const response = await api.get('/translation/supported-languages');
    return response.data;
  },

  healthCheck: async (): Promise<{
    success: boolean;
    service?: string;
    status?: string;
    configured?: boolean;
    error?: string;
  }> => {
    const response = await api.get('/translation/health');
    return response.data;
  },
};

// Export the main api instance for direct use
export { api };