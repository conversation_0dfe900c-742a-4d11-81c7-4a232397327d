/**
 * Classified Writing Translation Service
 * Provides dedicated translation functionality for classified writing topics and subtopics
 */

import { api } from './api';

export interface TranslationData {
  en?: string;
  fr?: string;
  zh?: string;
}

export interface TacheTranslations {
  topics: Record<string, TranslationData>;
  subtopics: Record<string, TranslationData>;
}

export interface TranslationResponse {
  tache_number: number;
  translations: TacheTranslations;
  metadata: {
    topics_count: number;
    subtopics_count: number;
    total_translations: number;
  };
}

export interface AllTranslationsResponse {
  translations: TacheTranslations;
  metadata: {
    topics_count: number;
    subtopics_count: number;
    total_translations: number;
    taches_included: number[];
  };
}

class ClassifiedWritingTranslationService {
  private cache: Map<string, TacheTranslations> = new Map();
  private allTranslationsCache: TacheTranslations | null = null;
  private cacheTimestamps: Map<string, number> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(key: string): boolean {
    const timestamp = this.cacheTimestamps.get(key);
    if (!timestamp) return false;
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  /**
   * Get translations for a specific tâche
   */
  async getTacheTranslations(tacheNumber: number): Promise<TacheTranslations> {
    const cacheKey = `tache_${tacheNumber}`;
    
    // Check cache first
    if (this.cache.has(cacheKey) && this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    try {
      const response = await api.get<TranslationResponse>(
        `/classified-writing/translations/${tacheNumber}`
      );
      
      const translations = response.data.translations;
      
      // Cache the result
      this.cache.set(cacheKey, translations);
      this.cacheTimestamps.set(cacheKey, Date.now());
      

      
      return translations;
    } catch (error) {
      console.error(`❌ Error fetching translations for tâche ${tacheNumber}:`, error);
      // Return empty translations on error
      return { topics: {}, subtopics: {} };
    }
  }

  /**
   * Get all translations from all tâches
   */
  async getAllTranslations(): Promise<TacheTranslations> {
    const cacheKey = 'all_translations';
    
    // Check cache first
    if (this.allTranslationsCache && this.isCacheValid(cacheKey)) {
      return this.allTranslationsCache;
    }

    try {
      const response = await api.get<AllTranslationsResponse>(
        '/classified-writing/translations/all'
      );
      
      const translations = response.data.translations;
      
      // Cache the result
      this.allTranslationsCache = translations;
      this.cacheTimestamps.set(cacheKey, Date.now());
      

      
      return translations;
    } catch (error) {
      // Return empty translations on error
      return { topics: {}, subtopics: {} };
    }
  }

  /**
   * Get translated topic name
   */
  getTopicTranslation(
    topicKey: string,
    language: string = 'fr',
    translations?: TacheTranslations
  ): string {
    if (!translations) {
      // If no translations provided, return formatted key as fallback
      return this.formatKey(topicKey);
    }

    // Try to find topic translation with different case variations
    let topicTranslations = translations.topics[topicKey];

    if (!topicTranslations) {
      // Try different case variations
      const variations = [
        topicKey,
        topicKey.toLowerCase(),
        topicKey.toUpperCase(),
        // Convert to Title Case
        topicKey.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join('_')
      ];

      for (const variation of variations) {
        if (translations.topics[variation]) {
          topicTranslations = translations.topics[variation];
          break;
        }
      }
    }

    if (!topicTranslations) {
      return this.formatKey(topicKey);
    }

    // Map language codes
    const langKey = language === 'zh-CN' ? 'zh' : language;

    // Try to get translation for current language
    const translated = topicTranslations[langKey as keyof TranslationData];
    if (translated && translated.trim()) {
      return translated;
    }

    // Fallback to English
    if (topicTranslations.en && topicTranslations.en.trim()) {
      return topicTranslations.en;
    }

    // Final fallback to formatted key
    return this.formatKey(topicKey);
  }

  /**
   * Get translated subtopic name
   */
  getSubtopicTranslation(
    subtopicKey: string,
    language: string = 'fr',
    translations?: TacheTranslations
  ): string {
    if (!translations) {
      // If no translations provided, return formatted key as fallback
      return this.formatKey(subtopicKey);
    }

    // Helper function to create case variations, handling forward slashes specially
    const createVariations = (key: string) => {
      const variations = new Set<string>(); // Use Set to avoid duplicates

      // Always add the original key
      variations.add(key);

      // Handle keys with forward slashes by processing each part separately
      if (key.includes('/')) {
        const parts = key.split('/');

        // Basic case variations
        variations.add(key.toLowerCase());
        variations.add(key.toUpperCase());

        // Title case for each part separated by /
        variations.add(parts.map(part =>
          part.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join('_')
        ).join('/'));

        // PascalCase for each part separated by /
        variations.add(parts.map(part =>
          part.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('_')
        ).join('/'));

        // Mixed case variations - first word capitalized, rest lowercase
        variations.add(parts.map(part => {
          const words = part.split('_');
          return words.map((word, index) =>
            index === 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word.toLowerCase()
          ).join('_');
        }).join('/'));

        // Mixed case variations - all words capitalized except last part
        variations.add(parts.map((part, partIndex) => {
          const words = part.split('_');
          return words.map((word, wordIndex) =>
            partIndex === 0 || wordIndex === 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word.toLowerCase()
          ).join('_');
        }).join('/'));

      } else {
        // Regular handling for keys without forward slashes
        variations.add(key.toLowerCase());
        variations.add(key.toUpperCase());

        // Title Case (first letter of each word capitalized)
        variations.add(key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join('_'));

        // PascalCase (all words capitalized)
        variations.add(key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join('_'));

        // Mixed case - first word capitalized, rest lowercase
        const words = key.split('_');
        variations.add(words.map((word, index) =>
          index === 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : word.toLowerCase()
        ).join('_'));
      }

      return Array.from(variations);
    };

    // Try to find subtopic translation with different case variations
    let subtopicTranslations = translations.subtopics[subtopicKey];
    const variations = createVariations(subtopicKey);

    // Add specific known patterns from the database
    if (subtopicKey === 'children_videogames/technologies') {
      variations.push('Children_VideoGames/Technologies');
    }
    if (subtopicKey === 'environment_related') {
      variations.push('Environment_related');
    }

    if (!subtopicTranslations) {
      for (const variation of variations) {
        if (translations.subtopics[variation]) {
          subtopicTranslations = translations.subtopics[variation];
          break;
        }
      }
    }

    if (!subtopicTranslations) {
      return this.formatKey(subtopicKey);
    }

    // Map language codes
    const langKey = language === 'zh-CN' ? 'zh' : language;

    // Try to get translation for current language
    const translated = subtopicTranslations[langKey as keyof TranslationData];
    if (translated && translated.trim()) {
      return translated;
    }

    // Fallback to English
    if (subtopicTranslations.en && subtopicTranslations.en.trim()) {
      return subtopicTranslations.en;
    }

    // Final fallback to formatted key
    return this.formatKey(subtopicKey);
  }

  /**
   * Format a key by replacing underscores with spaces and capitalizing
   */
  private formatKey(key: string): string {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Clear all cached translations
   */
  clearCache(): void {
    this.cache.clear();
    this.allTranslationsCache = null;
    this.cacheTimestamps.clear();
  }

  /**
   * Preload translations for a specific tâche
   */
  async preloadTacheTranslations(tacheNumber: number): Promise<void> {
    try {
      await this.getTacheTranslations(tacheNumber);
    } catch (error) {
      // Silently fail - translations will be loaded on demand
    }
  }

  /**
   * Preload all translations
   */
  async preloadAllTranslations(): Promise<void> {
    try {
      await this.getAllTranslations();
    } catch (error) {
      // Silently fail - translations will be loaded on demand
    }
  }

  /**
   * Check if translations are available for a topic
   */
  hasTopicTranslation(topicKey: string, translations?: TacheTranslations): boolean {
    if (!translations) return false;
    return topicKey in translations.topics;
  }

  /**
   * Check if translations are available for a subtopic
   */
  hasSubtopicTranslation(subtopicKey: string, translations?: TacheTranslations): boolean {
    if (!translations) return false;
    return subtopicKey in translations.subtopics;
  }
}

// Export singleton instance
export const classifiedWritingTranslationService = new ClassifiedWritingTranslationService();
