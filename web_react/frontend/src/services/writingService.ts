import { api } from './api';

// Define interfaces for the data structure from Supabase
export interface Task {
  task_number: number;
  task_content: string;
  correction_content?: string; // Optional for non-premium users
}

export interface Combination {
  combination_number: string;
  tasks: Task[];
}

export interface MonthData {
  month_year: string;
  combinations: Combination[];
}

export interface WritingTestListResponse {
  tests: Array<{
    id: string;
    title: string;
    type: string;
    free: boolean;
    month_year: string;
    total_tasks: number;
    total_combinations: number;
    has_corrections: boolean;
  }>;
  message?: string;
  membership_required?: boolean;
}

export interface WritingTestDataResponse {
  month_year: string;
  task_number: number;
  task_content: string;
  combination_number: string;
  correction_content?: string;
}

class WritingService {
  private cache: Map<string, MonthData> = new Map();
  private allDataCache: WritingTestListResponse | null = null;

  /**
   * Get list of available writing tests with summary data (fast, optimized)
   */
  async getWritingTests(): Promise<WritingTestListResponse> {
    // Check cache first
    if (this.allDataCache) {
      return this.allDataCache;
    }

    try {
      const response = await api.get('/tests/writing/list');
      this.allDataCache = response.data;
      return response.data;
    } catch (error: any) {
      console.error('Error fetching writing tests:', error);
      
      // Handle specific error cases
      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux tests d\'écriture');
      }
      
      throw new Error('Erreur lors du chargement des tests d\'écriture');
    }
  }

  /**
   * Convert writing test list to MonthData format for display cards (fast, no API calls)
   */
  async getWritingTestsAsMonthData(): Promise<MonthData[]> {
    try {
      const testsList = await this.getWritingTests();
      
      // Convert test list to MonthData format without loading full content
      const months: MonthData[] = testsList.tests.map(test => {
        // Create combinations based on actual data
        const tasksPerCombination = test.total_combinations > 0 ? Math.ceil(test.total_tasks / test.total_combinations) : 3;
        
        const combinations: Combination[] = Array.from({ length: test.total_combinations || 1 }, (_, i) => {
          const combinationNumber = (i + 1).toString();
          const tasks: Task[] = Array.from({ length: tasksPerCombination }, (_, j) => ({
            task_number: j + 1,
            task_content: `${test.total_tasks} tâches disponibles - Cliquez pour explorer`,
            correction_content: test.has_corrections ? '✏️ Corrections disponibles pour les membres premium' : undefined
          }));
          
          return {
            combination_number: combinationNumber,
            tasks
          };
        });
        
        return {
          month_year: test.month_year,
          combinations
        };
      });
      
      return months;
    } catch (error) {
      console.error('Error converting writing tests to month data:', error);
      throw error;
    }
  }

  /**
   * Load a specific month's writing data
   */
  async loadMonth(monthId: string): Promise<MonthData> {
    // Check cache first
    if (this.cache.has(monthId)) {
      return this.cache.get(monthId)!;
    }

    try {
      const response = await api.get(`/tests/writing/${monthId}/data`);
      const apiData: WritingTestDataResponse[] = response.data; // It's an array of tasks
      
      // Transform API data to our expected format
      const monthData: MonthData = this.transformApiDataToMonthData(apiData, monthId);
      
      // Cache the result
      this.cache.set(monthId, monthData);
      
      return monthData;
    } catch (error: any) {
      console.error(`Error loading month ${monthId}:`, error);
      
      if (error.response?.status === 401) {
        throw new Error('Connexion requise pour accéder aux données du test');
      }
      
      throw new Error(`Impossible de charger les données du mois ${monthId}`);
    }
  }

  /**
   * Transform API response to our expected MonthData format
   */
  private transformApiDataToMonthData(apiData: WritingTestDataResponse[], monthId: string): MonthData {
    const combinationsMap = new Map<string, Task[]>();
    
    // Group tasks by combination number
    apiData.forEach(taskData => {
      const combNumber = taskData.combination_number;
      
      if (!combinationsMap.has(combNumber)) {
        combinationsMap.set(combNumber, []);
      }
      
      const task: Task = {
        task_number: taskData.task_number,
        task_content: taskData.task_content,
        correction_content: taskData.correction_content || ''
      };
      
      combinationsMap.get(combNumber)!.push(task);
    });
    
    // Convert map to combinations array
    const combinations: Combination[] = Array.from(combinationsMap.entries()).map(([combNumber, tasks]) => ({
      combination_number: combNumber,
      tasks: tasks.sort((a, b) => a.task_number - b.task_number) // Sort tasks by number
    }));
    
    // Sort combinations by number
    combinations.sort((a, b) => parseInt(a.combination_number) - parseInt(b.combination_number));
    
    // Use month_year from the first task, or fallback to monthId if no data
    const month_year = apiData.length > 0 ? apiData[0].month_year : monthId;
    
    return {
      month_year,
      combinations
    };
  }

  /**
   * Load all available writing data (DEPRECATED - use getWritingTestsAsMonthData for list view)
   */
  async loadAllMonths(): Promise<MonthData[]> {
    try {
      const testsList = await this.getWritingTests();
      const months: MonthData[] = [];
      
      for (const test of testsList.tests) {
        try {
          const monthData = await this.loadMonth(test.id); // Use 'id' instead of 'test_id'
          months.push(monthData);
        } catch (error) {
          // Continue loading other months
        }
      }
      
      return months;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get statistics about the writing data
   */
  async getStatistics(): Promise<{
    totalMonths: number;
    totalCombinations: number;
    totalTasks: number;
    monthsWithMostTasks: { month: string; taskCount: number; }[];
  }> {
    try {
      const allData = await this.loadAllMonths();
      
      const totalMonths = allData.length;
      const totalCombinations = allData.reduce((sum, month) => sum + month.combinations.length, 0);
      const totalTasks = allData.reduce((sum, month) => 
        sum + month.combinations.reduce((comboSum, combo) => comboSum + combo.tasks.length, 0), 0
      );

      // Find months with most tasks
      const monthsWithTaskCounts = allData.map(month => ({
        month: month.month_year,
        taskCount: month.combinations.reduce((sum, combo) => sum + combo.tasks.length, 0)
      })).sort((a, b) => b.taskCount - a.taskCount);

      return {
        totalMonths,
        totalCombinations,
        totalTasks,
        monthsWithMostTasks: monthsWithTaskCounts.slice(0, 5) // Top 5
      };
    } catch (error) {
      console.error('Error getting statistics:', error);
      throw error;
    }
  }

  /**
   * Search for tasks containing specific keywords
   */
  async searchTasks(query: string): Promise<{
    month: string;
    combination: string;
    task: Task;
  }[]> {
    const allData = await this.loadAllMonths();
    const results: { month: string; combination: string; task: Task; }[] = [];
    
    const lowerQuery = query.toLowerCase();
    
    for (const monthData of allData) {
      for (const combination of monthData.combinations) {
        for (const task of combination.tasks) {
          if (
            task.task_content.toLowerCase().includes(lowerQuery) ||
            (task.correction_content && task.correction_content.toLowerCase().includes(lowerQuery))
          ) {
            results.push({
              month: monthData.month_year,
              combination: combination.combination_number,
              task
            });
          }
        }
      }
    }
    
    return results;
  }

  /**
   * Get tasks by task number (1, 2, or 3)
   */
  async getTasksByNumber(taskNumber: number): Promise<{
    month: string;
    combination: string;
    task: Task;
  }[]> {
    const allData = await this.loadAllMonths();
    const results: { month: string; combination: string; task: Task; }[] = [];
    
    for (const monthData of allData) {
      for (const combination of monthData.combinations) {
        const task = combination.tasks.find(t => t.task_number === taskNumber);
        if (task) {
          results.push({
            month: monthData.month_year,
            combination: combination.combination_number,
            task
          });
        }
      }
    }
    
    return results;
  }

  /**
   * Clear the cache (useful for refreshing data)
   */
  clearCache(): void {
    this.cache.clear();
    this.allDataCache = null;
  }

  /**
   * Parse month-year string to Date object
   */
  private parseMonthYear(monthYear: string): Date {
    // Handle different formats: "Mai-2025", "janvier-2025", etc.
    const [month, year] = monthYear.split('-');
    
    const monthMap: { [key: string]: number } = {
      'janvier': 0, 'fevrier': 1, 'mars': 2, 'avril': 3, 'mai': 4, 'juin': 5,
      'juillet': 6, 'aout': 7, 'septembre': 8, 'octobre': 9, 'novembre': 10, 'decembre': 11
    };
    
    const monthIndex = monthMap[month.toLowerCase()] ?? 0;
    return new Date(parseInt(year), monthIndex, 1);
  }

  /**
   * Format month name for display
   */
  formatMonthName(monthYear: string): string {
    const [month, year] = monthYear.split('-');
    
    const monthNames: { [key: string]: string } = {
      'janvier': 'Janvier', 'fevrier': 'Février', 'mars': 'Mars', 
      'avril': 'Avril', 'mai': 'Mai', 'juin': 'Juin',
      'juillet': 'Juillet', 'aout': 'Août', 'septembre': 'Septembre', 
      'octobre': 'Octobre', 'novembre': 'Novembre', 'decembre': 'Décembre'
    };
    
    return `${monthNames[month.toLowerCase()] || month} ${year}`;
  }
}

// Export a singleton instance
export const writingService = new WritingService();

// Export the service class for testing
export { WritingService }; 