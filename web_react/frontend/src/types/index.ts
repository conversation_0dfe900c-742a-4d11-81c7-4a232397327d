export interface User {
  id: string;
  username: string;
  email: string;
  membership_type: string; // 'free', 'premium', 'lifetime'
  membership_expires_at?: string;
  used_promo_codes?: string[];
  created_at: string;
  updated_at: string;
  is_active?: boolean;
  email_verified?: boolean;
  first_name?: string;
  last_name?: string;
  last_login?: string;
  profile_data?: string;
}

export interface Test {
  id: string;
  title: string;
  type: 'listening' | 'reading' | 'writing' | 'speaking';
  free?: boolean;
}

export interface TestData {
  questions: Question[];
  passages?: Passage[];
  audio_files?: string[];
}

export interface Question {
  id: string;
  type: 'multiple_choice' | 'text_input' | 'select_multiple';
  question: string;
  options?: string[];
  correct_answer?: string | string[];
  points?: number;
}

export interface Passage {
  id: string;
  title: string;
  content: string;
}

export interface TestHistory {
  id: string;
  user_id: string;
  section: string;
  test_id: string;
  free: boolean;
  answers: Record<string, string | string[]>;
  score?: number;
  max_score?: number;
  correct_count?: number;
  wrong_count?: number;
  timestamp: string;
  percentage?: number;
  time_taken?: number;
  started_at?: string;
  completed_at?: string;
  status?: string;
  metadata?: any;
  current_question?: number;
  grading_results?: GradingResults;
}

export interface GradingResults {
  correct: number[];
  incorrect: number[];
  wrong_details: WrongDetail[];
}

export interface WrongDetail {
  question: string;
  yourAnswer: string;
  correctAnswer: string;
}

export interface Highlight {
  id: string;
  user_id: string;
  section: string;
  test_id: string;
  question_index: number;
  free: boolean;
  highlights: HighlightData[];
  created_at: string;
  updated_at: string;
}

export interface HighlightData {
  start: number;
  end: number;
  text: string;
  color?: string;
}

export interface NotebookNote {
  id: string;
  user_id: string;
  test_path: string;
  notes: string;
  updated_at: string;
}

export interface PromoCode {
  id: string;
  code: string;
  description?: string;
  discount_type?: string;
  discount_value?: number;
  membership_duration: number;
  max_uses: number;
  current_uses: number;
  valid_from?: string;
  valid_until?: string;
  is_active: boolean;
  created_at: string;
  metadata?: any;
}

export interface PromoUsageLog {
  id: string;
  user_id: string;
  username: string;
  promo_code: string;
  used_at: string;
  expires_at: string;
  membership_days: number;
  remaining_uses_after: number;
}

export interface TestsResponse {
  listening: Test[];
  reading: Test[];
  writing: Test[];
  speaking: Test[];
}

export interface AuthResponse {
  user?: User;
  message: string;
  user_id?: string;
  email?: string;
  requires_verification?: boolean;
  email_error?: boolean;
}

export interface ScoreResult {
  score: number;
  max_score: number;
  score_699?: number;
  percent_699?: number;
  correct_count: number;
  wrong_details: WrongDetail[];
}

export interface TestInfo {
  section: string;
  test_id: string;
  free: boolean;
  previous_answers?: Record<string, string | string[]>;
  current_question?: number;
  grading_results?: GradingResults;
  has_history: boolean;
} 