import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import english_translation from './en/translation.json';
import french_translation from './fr/translation.json';
import chinese_translation from './zh/translation.json';

// Get system language preference
const getSystemLanguage = (): string => {
  if (typeof navigator === 'undefined') return 'fr';

  // Get user's preferred languages from browser
  const languages = navigator.languages || [navigator.language];
  const supportedLangs = ['fr', 'en', 'zh'];

  for (const lang of languages) {
    // Check exact match first
    if (supportedLangs.includes(lang)) {
      return lang;
    }

    // Check language code without region (e.g., 'en-US' -> 'en')
    const langCode = lang.split('-')[0];
    if (supportedLangs.includes(langCode)) {
      return langCode;
    }

    // Handle Chinese variants
    if (lang.startsWith('zh')) {
      return 'zh';
    }
  }

  // Default to French if no supported language found
  return 'fr';
};

// Get initial language from localStorage, defaulting to system language if not found
const getInitialLanguage = () => {
  const stored = localStorage.getItem('lang');
  const supportedLangs = ['fr', 'en', 'zh'];
  if (stored && supportedLangs.includes(stored)) {
    return stored;
  }
  const systemLang = getSystemLanguage();
  return systemLang;
};

const initialLang = getInitialLanguage();

i18next.use(initReactI18next).init({
  lng: initialLang, // Use the stored language instead of hardcoded 'en'
  debug: false, // Disable debug logging
  resources: {
    en: { translation: english_translation },
    fr: { translation: french_translation },
    zh: { translation: chinese_translation },
  },
  interpolation: {
    escapeValue: false, // React already escapes values
  },
});

export default i18next;
