<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico?v=2025" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Chez-TCFCA - Test de connaissance du français</title>

    <!-- Enhanced Meta tags for SEO -->
    <meta name="description" content="Préparez-vous au TCF Canada avec Chez-TCFCA. Tests gratuits et premium de compréhension orale, écrite, expression orale et écrite. Plus de 1000 questions authentiques pour réussir votre immigration au Canada." />
    <meta name="keywords" content="TCF Canada, test français, immigration Canada, préparation TCF, français langue étrangère, FLE, tests gratuits, compréhension orale, compréhension écrite, expression orale, expression écrite, Chez-TCFCA" />
    <meta name="author" content="Chez-TCFCA" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="French" />
    <meta name="geo.region" content="CA" />
    <meta name="geo.country" content="Canada" />

    <!-- Enhanced Open Graph meta tags -->
    <meta property="og:title" content="Chez-TCFCA - Préparation TCF Canada | Tests Gratuits et Premium" />
    <meta property="og:description" content="Préparez-vous au TCF Canada avec plus de 1000 questions authentiques. Tests gratuits disponibles. Réussissez votre immigration au Canada avec Chez-TCFCA." />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://chez-tcfcanada.com" />
    <meta property="og:site_name" content="Chez-TCFCA" />
    <meta property="og:locale" content="fr_CA" />
    <meta property="og:image" content="https://chez-tcfcanada.com/android-chrome-512x512.png?v=2025" />
    <meta property="og:image:width" content="512" />
    <meta property="og:image:height" content="512" />
    <meta property="og:image:alt" content="Chez-TCFCA Logo - Préparation TCF Canada" />

    <!-- Twitter Card meta tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Chez-TCFCA - Préparation TCF Canada" />
    <meta name="twitter:description" content="Préparez-vous au TCF Canada avec plus de 1000 questions authentiques. Tests gratuits disponibles." />
    <meta name="twitter:image" content="https://chez-tcfcanada.com/android-chrome-512x512.png?v=2025" />

    <!-- Additional SEO meta tags -->
    <meta name="theme-color" content="#1976D2" />
    <meta name="application-name" content="Chez-TCFCA" />
    <meta name="apple-mobile-web-app-title" content="Chez-TCFCA" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="format-detection" content="telephone=no" />
    
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png?v=2025">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png?v=2025">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png?v=2025">
    <link rel="manifest" href="/site.webmanifest?v=2025">
    
    <!-- JSON-LD Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "EducationalOrganization",
      "name": "Chez-TCFCA",
      "alternateName": "Chez-TCFCA - Préparation TCF Canada",
      "url": "https://chez-tcfcanada.com",
      "logo": "https://chez-tcfcanada.com/android-chrome-512x512.png?v=2025",
      "description": "Plateforme de préparation au TCF Canada avec plus de 1000 questions authentiques. Tests gratuits et premium pour réussir votre immigration au Canada.",
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "CA"
      },
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "availableLanguage": ["French", "English", "Chinese"]
      },
      "offers": [
        {
          "@type": "Offer",
          "name": "Tests Gratuits TCF Canada",
          "description": "Accès gratuit aux tests de préparation TCF Canada",
          "price": "0",
          "priceCurrency": "CAD"
        },
        {
          "@type": "Offer",
          "name": "Abonnement Premium",
          "description": "Accès complet à tous les tests et fonctionnalités premium",
          "priceCurrency": "CAD"
        }
      ],
      "educationalCredentialAwarded": "Préparation TCF Canada",
      "hasCredential": {
        "@type": "EducationalOccupationalCredential",
        "name": "TCF Canada Preparation"
      }
    }
    </script>

    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Initial theme setup to prevent flash -->
    <script>
      // Immediately set theme before any rendering
      (function() {
        try {
          const savedThemeData = localStorage.getItem('tcf-theme-mode');
          let savedMode = 'system';

          if (savedThemeData) {
            try {
              const parsed = JSON.parse(savedThemeData);
              savedMode = parsed.state?.mode || 'system';
            } catch (e) {
              // If parsing fails, treat as direct mode value
              savedMode = savedThemeData;
            }
          }

          const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
          const resolvedTheme = savedMode === 'system' ? systemTheme : savedMode;

          document.documentElement.setAttribute('data-mantine-color-scheme', resolvedTheme);
          if (resolvedTheme === 'dark') {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }

          // Store the resolved theme for immediate access
          window.__INITIAL_THEME__ = resolvedTheme;
        } catch (error) {
          console.warn('Theme initialization error:', error);
          // Fallback to light theme
          document.documentElement.setAttribute('data-mantine-color-scheme', 'light');
          document.documentElement.classList.remove('dark');
        }
      })();
    </script>
    
    <!-- Initial loading screen styles -->
    <style>
      /* Base styles for light mode */
      .initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f7f9fb;
        color: #212529;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        transition: all 0.3s ease;
        font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      }

      .loader-logo {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #007bff;
        transition: color 0.3s ease;
        font-family: inherit;
      }

      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e9ecef;
        border-top: 3px solid #007bff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        transition: border-color 0.3s ease;
      }

      .loader-text {
        margin-top: 1rem;
        font-size: 0.9rem;
        color: #6c757d;
        transition: color 0.3s ease;
        font-family: inherit;
      }

      /* Dark mode styles */
      [data-mantine-color-scheme="dark"] .initial-loader,
      .dark .initial-loader {
        background: #000000;
        color: #f8f9fa;
      }

      [data-mantine-color-scheme="dark"] .loader-logo,
      .dark .loader-logo {
        color: #1e90ff;
      }

      [data-mantine-color-scheme="dark"] .loader-spinner,
      .dark .loader-spinner {
        border-color: #404040;
        border-top-color: #1e90ff;
      }

      [data-mantine-color-scheme="dark"] .loader-text,
      .dark .loader-text {
        color: #adb5bd;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Ensure smooth transitions */
      * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
      }
    </style>
  </head>
  <body>
    <!-- Initial loading screen -->
    <div class="initial-loader">
      <div class="loader-logo">Chez-TCFCA</div>
      <div class="loader-spinner"></div>
      <div class="loader-text">Chargement...</div>
    </div>
    
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Main application script -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
