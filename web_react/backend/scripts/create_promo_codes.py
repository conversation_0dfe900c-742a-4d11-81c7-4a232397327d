#!/usr/bin/env python3
import os
import sys
from datetime import datetime, timezone

# Add the backend directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app import create_app

def create_promo_code(code, max_uses=5, days=3):
    """Create a new promo code in the Supabase database."""
    
    # Create Flask app to get Supabase client
    app = create_app('development')
    
    with app.app_context():
        if not app.supabase:
            print("ERROR: Supabase client not available!")
            return None
        
        # Check if promo code already exists
        existing = app.supabase.table('promo_codes').select('*').eq('code', code.upper()).execute()
        if existing.data:
            print(f"ERROR: Promo code '{code.upper()}' already exists!")
            return None
        
        # Create promo code data
        promo_data = {
            'code': code.upper(),
            'created_at': datetime.now(timezone.utc).isoformat(),
            'is_active': True,
            'max_uses': max_uses,
            'current_uses': 0,
            'membership_duration': days  # Number of days of membership this promo provides
        }
        
        try:
            result = app.supabase.table('promo_codes').insert(promo_data).execute()
            if result.data:
                return result.data[0]
            else:
                print("ERROR: Failed to insert promo code")
                return None
        except Exception as e:
            print(f"ERROR: Database error: {e}")
            return None

def list_promo_codes():
    """List all promo codes in the database."""
    app = create_app('development')
    
    with app.app_context():
        if not app.supabase:
            print("ERROR: Supabase client not available!")
            return
        
        try:
            result = app.supabase.table('promo_codes').select('*').order('created_at', desc=True).execute()
            
            if result.data:
                print(f"\n📋 Found {len(result.data)} promo code(s):")
                print("=" * 80)
                for promo in result.data:
                    status = "🟢 Active" if promo['is_active'] else "🔴 Inactive"
                    uses_left = promo['max_uses'] - promo['current_uses']
                    created = datetime.fromisoformat(promo['created_at'].replace('Z', '+00:00'))
                    
                    print(f"Code: {promo['code']}")
                    print(f"  Status: {status}")
                    print(f"  Uses: {promo['current_uses']}/{promo['max_uses']} (remaining: {uses_left})")
                    print(f"  Days: {promo['membership_duration']} days")
                    print(f"  Created: {created.strftime('%Y-%m-%d %H:%M:%S')}")
                    print("-" * 40)
            else:
                print("No promo codes found.")
                
        except Exception as e:
            print(f"ERROR: Failed to list promo codes: {e}")

def deactivate_promo_code(code):
    """Deactivate a promo code."""
    app = create_app('development')
    
    with app.app_context():
        if not app.supabase:
            print("ERROR: Supabase client not available!")
            return False
        
        try:
            result = app.supabase.table('promo_codes').update({
                'is_active': False
            }).eq('code', code.upper()).execute()
            
            if result.data:
                print(f"✅ Promo code '{code.upper()}' has been deactivated.")
                return True
            else:
                print(f"❌ Promo code '{code.upper()}' not found.")
                return False
                
        except Exception as e:
            print(f"ERROR: Failed to deactivate promo code: {e}")
            return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python create_promo_codes.py <command> [arguments]")
        print("\nCommands:")
        print("  create PROMOCODE [USES] [DAYS]  - Create a new promo code")
        print("  list                            - List all promo codes")
        print("  deactivate PROMOCODE            - Deactivate a promo code")
        print("\nExamples:")
        print("  python create_promo_codes.py create EARLYACCESS 10 7")
        print("  python create_promo_codes.py list")
        print("  python create_promo_codes.py deactivate EARLYACCESS")
        sys.exit(1)
    
    command = sys.argv[1].lower()
    
    if command == 'create':
        if len(sys.argv) < 3:
            print("ERROR: Missing promo code. Usage: python create_promo_codes.py create PROMOCODE [USES] [DAYS]")
            sys.exit(1)
        
        code = sys.argv[2]
        
        # Parse number of uses
        if len(sys.argv) > 3:
            try:
                uses = int(sys.argv[3])
            except ValueError:
                print("ERROR: Invalid number of uses. Must be an integer.")
                sys.exit(1)
        else:
            uses = 5  # Default number of uses
        
        # Parse number of days
        if len(sys.argv) > 4:
            try:
                days = int(sys.argv[4])
            except ValueError:
                print("ERROR: Invalid number of days. Must be an integer.")
                sys.exit(1)
        else:
            days = 3  # Default number of days
        
        # Validate inputs
        if uses <= 0:
            print("ERROR: Number of uses must be greater than 0.")
            sys.exit(1)
        
        if days <= 0:
            print("ERROR: Number of days must be greater than 0.")
            sys.exit(1)
        
        if len(code) < 3:
            print("ERROR: Promo code must be at least 3 characters long.")
            sys.exit(1)
        
        if len(code) > 20:
            print("ERROR: Promo code must be 20 characters or less.")
            sys.exit(1)
        
        # Create the promo code
        try:
            promo = create_promo_code(code, uses, days)
            if promo:
                print(f"\n✅ Promo code created successfully!")
                print(f"📝 Code: {promo['code']}")
                print(f"🔢 Max uses: {promo['max_uses']}")
                print(f"📅 Membership days: {promo['membership_duration']}")
                print(f"🆔 ID: {promo['id']}")
                print(f"📅 Created: {promo['created_at']}")
            else:
                sys.exit(1)
        except Exception as e:
            print(f"ERROR: Failed to create promo code: {e}")
            sys.exit(1)
    
    elif command == 'list':
        list_promo_codes()
    
    elif command == 'deactivate':
        if len(sys.argv) < 3:
            print("ERROR: Missing promo code. Usage: python create_promo_codes.py deactivate PROMOCODE")
            sys.exit(1)
        
        code = sys.argv[2]
        if not deactivate_promo_code(code):
            sys.exit(1)
    
    else:
        print(f"ERROR: Unknown command '{command}'. Use 'create', 'list', or 'deactivate'.")
        sys.exit(1)

if __name__ == "__main__":
    main() 