from flask import Blueprint, jsonify, current_app, request, session
import json
from pathlib import Path
from datetime import datetime, timezone
from . import auth
import unicodedata

def normalize_for_filename(text):
    """Normalize text for cross-platform filename matching"""
    return unicodedata.normalize('NFC', text)

tests_bp = Blueprint('tests', __name__, url_prefix='/api/tests')

# Use the functions from auth module
login_required = auth.login_required
membership_required = auth.membership_required

def check_user_premium_status():
    """Helper function to check if current user has premium membership"""
    if 'user_id' not in session:
        return False
    
    try:
        result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
        user = result.data[0] if result.data else None
        
        if not user or user.get('membership_type', 'free') not in ['premium', 'paid']:
            return False
        
        expires = user.get('membership_expires_at')
        if expires is not None:
            if isinstance(expires, str):
                try:
                    expires_dt = datetime.fromisoformat(expires)
                    if expires_dt.tzinfo is None:
                        expires_dt = expires_dt.replace(tzinfo=timezone.utc)
                except Exception:
                    expires_dt = None
            else:
                expires_dt = expires
            
            # User has premium if no expiry date or expiry is in future
            return expires_dt is None or expires_dt > datetime.now(timezone.utc)
        else:
            # No expiry date means permanent premium
            return True
            
    except Exception:
        return False

def map_section_to_test_type(section):
    """Map frontend section name to database test_type"""
    return section  # They're the same for now

def map_test_type_to_section(test_type):
    """Map database test_type to frontend section"""
    return test_type  # They're the same for now

def query_test_history_by_section(user_id, section, test_id, is_free):
    """Query test history with proper column mapping and free/premium differentiation"""
    # Create effective test_id to differentiate free vs paid tests
    effective_test_id = f"{test_id}_free" if is_free else test_id
    return current_app.supabase.table('test_history').select('*').eq('user_id', user_id).eq('test_type', map_section_to_test_type(section)).eq('test_id', effective_test_id).execute()

def format_test_history_for_frontend(db_record):
    """Convert database test_history record to frontend format"""
    if not db_record:
        return None
    
    # Map database columns to frontend expectations
    formatted = {
        'id': db_record.get('id'),
        'user_id': db_record.get('user_id'),
        'section': map_test_type_to_section(db_record.get('test_type', '')),  # Map test_type back to section
        'test_id': db_record.get('test_id'),
        'free': False,  # Default since this column doesn't exist in DB
        'answers': db_record.get('answers', '{}'),
        'score': db_record.get('score'),
        'max_score': db_record.get('max_score'),
        'correct_count': None,  # This doesn't exist in DB
        'timestamp': db_record.get('created_at'),  # Map created_at to timestamp
        'percentage': db_record.get('percentage'),
        'time_taken': db_record.get('time_taken'),
        'started_at': db_record.get('started_at'),
        'completed_at': db_record.get('completed_at'),
        'status': db_record.get('status'),
        'metadata': db_record.get('metadata')
    }
    
    return formatted

@tests_bp.route('', methods=['GET'])
@tests_bp.route('/', methods=['GET'])
def get_tests():
    """Get list of available tests for all sections - accessible without authentication"""
    
    # Check if user has premium access
    user_is_premium = check_user_premium_status()
    
    tests = {
        'listening': [],
        'reading': [],
        'writing': [],
        'speaking': []
    }
    
    try:
        # Get test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        for test_type in ['reading', 'listening', 'writing', 'speaking']:
            available_tests = service.get_available_tests(test_type, user_is_premium)
            tests[test_type] = available_tests
        
        # Sort tests by ID for consistency
        for section in tests:
            tests[section] = sorted(tests[section], key=lambda t: t.get('id', ''))
            
    except Exception:
        # Fallback to empty lists instead of hardcoded data
        tests = {
            'listening': [],
            'reading': [], 
            'writing': [],
            'speaking': []
        }
    
    return jsonify(tests)

@tests_bp.route('/<section>', methods=['GET'])
@login_required
def get_section_tests(section):
    """Get tests for a specific section with progress - fully migrated to Supabase"""
    user_id = session.get('user_id')
    user_is_premium = check_user_premium_status()
    
    # Get available tests from Supabase using the service
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        available_tests = service.get_available_tests(section, user_is_premium)
        
        # Separate free and premium tests
        free_tests = [t for t in available_tests if t.get('free', False)]
        premium_tests = [t for t in available_tests if not t.get('free', False)]
        
    except Exception:
        return jsonify({'error': f'Failed to load {section} tests'}), 500
    
    # Get progress data using our efficient method
    progress_data = {}
    if user_id and current_app.supabase:
        try:
            result = current_app.supabase.table('test_history').select(
                'test_id, answers, score, max_score, grading_results, status'
            ).eq('user_id', user_id).eq('test_type', section).execute()
            
            for record in result.data:
                stored_test_id = record.get('test_id', '')
                is_free_test = stored_test_id.endswith('_free')
                is_group_test = stored_test_id.startswith('group')
                original_test_id = stored_test_id.replace('_free', '') if is_free_test else stored_test_id
                
                # Create unique key for test identification
                if is_group_test:
                    test_key = original_test_id  # group1, group2, etc.
                else:
                    test_key = f"{original_test_id}{'_free' if is_free_test else ''}"
                
                progress_info = {
                    'test_id': original_test_id,
                    'free': is_free_test,
                    'status': record.get('status', 'in_progress'),
                    'progress': None,
                    'total': record.get('max_score') or 39,  # Handle None values properly
                }
                
                # Calculate progress from answers
                if record.get('answers'):
                    try:
                        answers = json.loads(record['answers']) if isinstance(record['answers'], str) else record['answers']
                        max_questions = record.get('max_score') or 39
                        
                        # Special handling for collection_book which uses UUID keys
                        if stored_test_id == 'collection_book_notebook' and record.get('test_type') == 'collection_book':
                            # For collection book, count all non-empty answers (UUID keys)
                            valid_answers = len([v for _, v in answers.items() if v])
                            progress_info['progress'] = valid_answers
                            progress_info['total'] = len(answers)  # Total questions in collection
                        else:
                            # For regular tests, use numeric key validation
                            valid_answers = len([v for k, v in answers.items() if v and k.isdigit() and 1 <= int(k) <= max_questions])
                            progress_info['progress'] = valid_answers
                    except Exception:
                        pass
                
                # Add grading information if available
                if record.get('score') is not None:
                    grading_info = {
                        'score': record.get('score'),
                        'max_score': record.get('max_score') or 39
                    }
                    
                    if record.get('grading_results'):
                        try:
                            grading_results = json.loads(record['grading_results']) if isinstance(record['grading_results'], str) else record['grading_results']
                            grading_info['correct_count'] = len(grading_results.get('correct', []))
                            grading_info['wrong_count'] = len(grading_results.get('incorrect', []))
                        except Exception:
                            pass
                    
                    progress_info['grading'] = grading_info
                
                progress_data[test_key] = progress_info
        except Exception:
            pass
    
    # Build response with progress data
    tests = []
    free_tests_with_progress = []
    
    # Process premium tests
    for test in premium_tests:
        test_key = test['id']
        progress_info = progress_data.get(test_key, {})
        
        test_data = {
            'id': test['id'],
            'title': test['title'],
            'type': test['type'],
            'free': test['free'],
            'total': 39,
            'progress': progress_info.get('progress'),
        }
        
        if 'grading' in progress_info:
            test_data['grading'] = progress_info['grading']
        
        tests.append(test_data)
    
    # Process free tests
    for test in free_tests:
        test_key = f"{test['id']}_free"
        progress_info = progress_data.get(test_key, {})
        
        test_data = {
            'id': test['id'],
            'title': test['title'] + ' (Gratuit)',
            'type': test['type'],
            'free': test['free'],
            'total': 39,
            'progress': progress_info.get('progress'),
        }
        
        if 'grading' in progress_info:
            test_data['grading'] = progress_info['grading']
        
        free_tests_with_progress.append(test_data)
    
    # Get group progress from Supabase for difficulty-based tests
    group_progress = {}
    if user_is_premium:  # Group tests require membership
        # Updated to support 6 groups (A1, A2, B1, B2, C1, C2)
        for group_id in range(1, 7):
            try:
                # Get group test data from Supabase to determine total questions
                group_data = service.get_group_test_data(section, group_id, user_is_premium)
                if group_data:
                    total = len(group_data)
                    
                    # Get progress data
                    group_key = f'group{group_id}'
                    progress_info = progress_data.get(group_key, {})
                    
                    group_info = {'total': total, 'progress': progress_info.get('progress')}
                    
                    if 'grading' in progress_info:
                        group_info['grading'] = progress_info['grading']
                    
                    group_progress[group_id] = group_info
                else:
                    # Updated fallback totals for 6 groups based on TCF structure
                    # Group 1: Q1-Q4 (4 questions), Group 2: Q5-Q10 (6 questions), etc.
                    tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                    group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
            except Exception:
                # Updated fallback totals for 6 groups
                tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
    else:
        # For non-premium users, show all 6 groups but with no access
        tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
        for group_id in range(1, 7):
            group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
    
    return jsonify({
        'section': section,
        'tests': sorted(tests, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
        'free_tests': sorted(free_tests_with_progress, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
        'group_progress': group_progress
    })

@tests_bp.route('/<section>/<test_id>/data', methods=['GET'])
def get_test_data(section, test_id):
    """Get test data including questions and answers - supports mock exams"""
    # Extract 'free' parameter from query string - handle both '1'/'0' and 'true'/'false' formats
    free_param = request.args.get('free', '0').lower()
    is_free = free_param in ['1', 'true']
    
    # Require authentication for all tests
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401
    
    user_id = session['user_id']
    
    # Check if this is a mock exam - now handles new shorter format: mock_{test_type}_{user_id}_{exam_number}_{timestamp_suffix}
    is_mock_exam = test_id.startswith('mock_')
    
    if is_mock_exam:
        # Handle mock exam data request
        # For new format, the test_id IS the full identifier, no need to extract mock_exam_id
        
        # Verify user owns this mock exam and user has premium access
        user_is_premium = check_user_premium_status()
        if not user_is_premium:
            return jsonify({'error': 'Premium membership required for mock exams'}), 403
            
        try:
            # Get all questions for this mock exam (using test_identifier)
            questions_result = current_app.supabase.table('mock_exams').select('*').eq(
                'test_identifier', test_id  # Use full test_id (e.g., "mock_exam_1")
            ).eq('user_id', user_id).eq('test_type', section).order('question_number', desc=False).execute()
            
            if not questions_result.data:
                return jsonify({'error': 'Mock exam not found or access denied'}), 404
            
            # Sort questions by question_number as integer to ensure proper ordering
            sorted_questions = sorted(questions_result.data, key=lambda q: int(q.get('question_number', 0)))
            
            # Format questions for frontend
            questions_data = []
            for index, q in enumerate(sorted_questions):
                # Merge free and paid content for premium users
                merged_content = {}
                merged_content.update(q.get('free_content', {}))
                merged_content.update(q.get('paid_content', {}))
                
                # Flatten the structure - extract content fields directly to question level
                question_obj = {
                    'question_number': str(index + 1),  # Sequential numbering: 1, 2, 3, etc.
                    'difficulty_group': q['difficulty_group'],
                    'points': q['points'],
                    'metadata': q.get('metadata', {}),
                    'question_data': q.get('question_data', {}),  # Include question_data for asset paths
                }
                
                # Add all content fields directly to the question object (like regular tests)
                question_obj.update(merged_content)
                
                # DO NOT add source test information - not needed for mock exams
                # Users don't need to see "Apparue dans: Test X QY" for mock exam questions
                
                questions_data.append(question_obj)
            
            # Format response similar to regular tests
            return jsonify({
                'test_id': test_id,
                'section': section,
                'questions': questions_data,
                'total_questions': len(questions_data),
                'is_mock_exam': True
            })
            
        except Exception as e:
            return jsonify({'error': f'Failed to load mock exam data: {str(e)}'}), 500
    
    else:
        # Handle regular test data request
        # Check user membership status
        user_is_premium = check_user_premium_status()
        
        # Access control: free tests available to all, premium tests need membership
        if not is_free and not user_is_premium:
            return jsonify({'error': 'Membership required for premium tests'}), 403
            
        # Get test data from Supabase using the service
        try:
            from database.test_data_service import get_test_data_service
            service = get_test_data_service()
            
            test_data = service.get_test_data(
                test_type=section,
                test_identifier=test_id,
                is_free=is_free,
                user_is_premium=user_is_premium
            )
            
            if not test_data:
                return jsonify({'error': 'Test not found'}), 404
            
            return jsonify(test_data)
            
        except Exception as e:
            return jsonify({'error': f'Failed to load test data: {str(e)}'}), 500

@tests_bp.route('/<section>/<test_id>', methods=['GET'])
@login_required
def get_test_info(section, test_id):
    """Get test information including previous answers and current question position - fully migrated to Supabase"""
    user_id = session.get('user_id')
    if not user_id:
        return jsonify({'error': 'User not authenticated'}), 401
    
    # Extract 'free' parameter from query string - handle both '1'/'0' and 'true'/'false' formats
    free_param = request.args.get('free', '0').lower()
    is_free = free_param in ['1', 'true']
    
    # Check if this is a mock exam - now handles new shorter format: mock_{test_type}_{user_id}_{exam_number}_{timestamp_suffix}
    is_mock_exam = test_id.startswith('mock_')
    

    
    if is_mock_exam:
        # Handle mock exam request
        # For new format, the test_id IS the full identifier
        print(f"📊 Mock exam info request: test_id={test_id}")
        
        # Verify user owns this mock exam
        try:
            # Use test_identifier to query mock exams (test_id is the full identifier)
            exam_result = current_app.supabase.table('mock_exams').select('*').eq(
                'test_identifier', test_id  # Use full test_id
            ).eq('user_id', user_id).eq('test_type', section).limit(1).execute()
            
            if not exam_result.data:
                return jsonify({'error': 'Mock exam not found or access denied'}), 404
                
        except Exception as e:
            print(f"Error verifying mock exam: {e}")
            return jsonify({'error': 'Failed to verify mock exam'}), 500
    else:
        # Check user membership status for regular tests
        user_is_premium = check_user_premium_status()
        
        # Access control: free tests available to all, premium tests need membership
        if not is_free and not user_is_premium:
            return jsonify({'error': 'Membership required for premium tests'}), 403
        
        # Check if test exists in Supabase using the service
        try:
            from database.test_data_service import get_test_data_service
            service = get_test_data_service()
            
            # Try to get test data to verify it exists
            test_exists = service.get_test_data(
                test_type=section,
                test_identifier=test_id,
                is_free=is_free,
                user_is_premium=user_is_premium
            )
            
            if not test_exists:
                return jsonify({'error': 'Test not found'}), 404
            
        except Exception as e:
            print(f"Error checking test existence in Supabase: {e}")
            return jsonify({'error': f'Failed to verify test: {str(e)}'}), 500
    
    # For mock exams, the effective test_id is the same as test_id
    # For regular tests, create effective test_id to differentiate free vs paid tests
    effective_test_id = test_id if is_mock_exam else (f"{test_id}_free" if is_free else test_id)

    
    # Get previous answers and current question position from test_history
    try:
        result = current_app.supabase.table('test_history').select('*').eq('user_id', user_id).eq('test_type', section).eq('test_id', effective_test_id).execute()

    except Exception as e:
        print(f"❌ Database query error: {e}")
        result = None
    
    test_history = result.data[0] if result and result.data else None
    previous_answers = None
    current_question = 0  # Default to first question
    grading_results = None  # Add grading results
    
    if test_history:
        
        if test_history.get('answers'):
            try:
                # Parse JSON string to get answers
                previous_answers = json.loads(test_history['answers']) if isinstance(test_history['answers'], str) else test_history['answers']

            except Exception as e:
                print(f"❌ Error parsing answers: {e}")
                previous_answers = None
        
        # Try to get current_question, handle missing column gracefully
        if 'current_question' in test_history and test_history.get('current_question') is not None:
            current_question = test_history['current_question']
        else:
            current_question = 0
        
        # Try to get grading_results, handle missing column gracefully
        if 'grading_results' in test_history and test_history.get('grading_results') is not None:
            try:
                grading_results = json.loads(test_history['grading_results']) if isinstance(test_history['grading_results'], str) else test_history['grading_results']

            except Exception as e:
                grading_results = None
        else:
            grading_results = None
    else:
        grading_results = None
    
    response_data = {
        'section': section,
        'test_id': test_id,
        'free': is_free,
        'previous_answers': previous_answers,
        'current_question': current_question,  # Return saved question position
        'grading_results': grading_results,  # Return saved grading results
        'has_history': test_history is not None
    }
    
    return jsonify(response_data)

@tests_bp.route('/group/<section>/<int:group_id>', methods=['GET'])
@login_required
@membership_required
def get_group_test(section, group_id):
    """Get group test data for difficulty-based practice"""
    try:
        # Check user membership status
        user_is_premium = check_user_premium_status()
        
        if not user_is_premium:
            return jsonify({'error': 'Premium membership required for group tests'}), 403
        
        # Get group test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        group_data = service.get_group_test_data(
            test_type=section,
            group_id=group_id,
            user_is_premium=user_is_premium
        )
        
        if not group_data:
            return jsonify({'error': 'Group test not found'}), 404
        
        # Get user's test history for this group test
        user_id = session['user_id']
        result = current_app.supabase.table('test_history').select('*').eq('user_id', user_id).eq('test_type', section).eq('test_id', f'group{group_id}').execute()
        
        test_history = result.data[0] if result.data else None
        previous_answers = None
        current_question = 0  # Default to first question
        grading_results = None
        
        if test_history and test_history.get('answers'):
            try:
                # Parse JSON string to get answers
                previous_answers = json.loads(test_history['answers']) if isinstance(test_history['answers'], str) else test_history['answers']
            except:
                previous_answers = None
        
        if test_history and test_history.get('current_question') is not None:
            current_question = test_history['current_question']
        
        # Try to get grading_results for group tests
        if test_history and 'grading_results' in test_history and test_history.get('grading_results') is not None:
            try:
                grading_results = json.loads(test_history['grading_results']) if isinstance(test_history['grading_results'], str) else test_history['grading_results']
                print(f"📊 Found saved grading_results for group test: {len(grading_results.get('wrong_details', []))} wrong answers")
            except Exception as e:
                print(f"❌ Error parsing grading_results for group test: {e}")
                grading_results = None
        
        return jsonify({
            'section': section,
            'group_id': group_id,
            'test_id': f'group{group_id}',
            'group_data': group_data,
            'previous_answers': previous_answers,
            'current_question': current_question,
            'grading_results': grading_results,
            'has_history': test_history is not None
        })
        
    except Exception as e:
        print(f"Error loading group test from Supabase: {e}")
        return jsonify({'error': f'Failed to load group test: {str(e)}'}), 500

@tests_bp.route('/history', methods=['POST'])
def save_test_history():
    """Save user's test history"""
    try:
        data = request.get_json()

        # For free tests, skip authentication temporarily for testing
        is_free = data.get('free', False)
        
        if not is_free:
            if 'user_id' not in session:
                return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401
        else:
            # For free tests, allow without login but still try to save if logged in
            if 'user_id' not in session:
                return jsonify({'status': 'success', 'message': 'Free test completed without history save'})
    
        # If Supabase is not available (placeholder credentials), skip history save
        if not current_app.supabase:
            return jsonify({'status': 'success', 'message': 'Test completed without history save'})
            
        user_id = session['user_id']
        
        # Create effective test_id to differentiate free vs paid tests
        effective_test_id = f"{data['test_id']}_free" if is_free else data['test_id']
        
        # Build test history object - only include current_question if we have it and the column exists
        test_history = {
            'user_id': user_id,
            'test_type': data['section'],
            'test_id': effective_test_id,  # Use effective test_id
            'answers': json.dumps(data['answers']),  # Store as JSON string
            'score': data.get('score'),
            'max_score': data.get('max_score'),
            'started_at': datetime.now(timezone.utc).isoformat(),
            'completed_at': datetime.now(timezone.utc).isoformat(),
            'status': 'completed' if data.get('score') is not None else 'in_progress'  # Set status based on whether score is provided
        }
        
        # Try to add current_question if provided, but handle missing column gracefully
        current_question = data.get('current_question')
        if current_question is not None:
            try:
                # First check if the column exists by trying to query it
                current_app.supabase.table('test_history').select('current_question').limit(1).execute()
                # If we get here, the column exists
                test_history['current_question'] = current_question
            except Exception:
                pass  # Column doesn't exist yet
        
        # Try to add grading_results if provided
        grading_results = data.get('grading_results')
        if grading_results is not None:
            try:
                # Check if grading_results column exists and add it
                test_history['grading_results'] = json.dumps(grading_results)
            except Exception:
                pass  # Column doesn't exist yet
        else:
            pass  # No grading_results provided
        
        # Check if history already exists
        result = current_app.supabase.table('test_history').select('id').eq('user_id', user_id).eq('test_type', data['section']).eq('test_id', effective_test_id).execute()
        
        if result.data:
            # Update existing record
            current_app.supabase.table('test_history').update(test_history).eq('id', result.data[0]['id']).execute()
        else:
            # Insert new record
            current_app.supabase.table('test_history').insert(test_history).execute()
        
        return jsonify({'status': 'success'})
        
    except Exception:
        pass  # Log error silently
        # Don't fail the request if history save fails
        return jsonify({'status': 'success', 'message': 'Test completed but history save failed'})

@tests_bp.route('/history', methods=['GET'])
@login_required
def get_test_history():
    """Get user's test history with pagination"""
    try:
        user_id = session['user_id']
        
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 10))
        offset = (page - 1) * limit
        
        # Get filters
        section = request.args.get('section')  # Optional section filter
        status = request.args.get('status')    # Optional status filter
        
        # If Supabase is not available (placeholder credentials), return empty history
        if not current_app.supabase:
            return jsonify({
                'history': [],
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': 0,
                    'pages': 0
                }
            })
        
        # Build query
        query = current_app.supabase.table('test_history').select('*').eq('user_id', user_id)
        
        # Add filters if provided
        if section:
            query = query.eq('test_type', section)
        if status:
            query = query.eq('status', status)
        
        # Get total count for pagination
        count_result = query.execute()
        total_records = len(count_result.data) if count_result.data else 0
        total_pages = (total_records + limit - 1) // limit  # Ceiling division
        
        # Get paginated results
        result = query.order('created_at', desc=True).range(offset, offset + limit - 1).execute()
        
        history = []
        for record in result.data:
            # Extract original test_id and free status from effective_test_id
            stored_test_id = record.get('test_id', '')
            is_free_test = stored_test_id.endswith('_free')
            original_test_id = stored_test_id.replace('_free', '') if is_free_test else stored_test_id
            
            # Format record for frontend compatibility
            formatted_record = {
                'id': record.get('id'),
                'section': record.get('test_type', ''),  # Map test_type to section
                'test_id': original_test_id,  # Use original test_id without _free suffix
                'free': is_free_test,  # Set free flag based on _free suffix
                'answers': record.get('answers', '{}'),  # JSON string
                'score': record.get('score'),
                'max_score': record.get('max_score'),
                'correct_count': None,  # This doesn't exist in DB
                'timestamp': record.get('created_at'),  # Map created_at to timestamp
                'percentage': record.get('percentage'),
                'time_taken': record.get('time_taken'),
                'started_at': record.get('started_at'),
                'completed_at': record.get('completed_at'),
                'status': record.get('status'),
                'metadata': record.get('metadata')
            }
            
            # Try to add grading_results if available
            if 'grading_results' in record and record.get('grading_results') is not None:
                try:
                    grading_results = json.loads(record['grading_results']) if isinstance(record['grading_results'], str) else record['grading_results']
                    formatted_record['grading_results'] = grading_results
                    # Calculate correct_count and wrong_count from grading_results if available
                    if grading_results:
                        formatted_record['correct_count'] = len(grading_results.get('correct', []))
                        formatted_record['wrong_count'] = len(grading_results.get('incorrect', []))
                except Exception as e:
                    print(f"❌ Error parsing grading_results for record {record.get('id')}: {e}")
            
            history.append(formatted_record)
        
        return jsonify({
            'history': history,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total_records,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })
        
    except Exception as e:
        print(f"❌ Error getting test history: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'history': [],
            'pagination': {
                'page': 1,
                'limit': 10,
                'total': 0,
                'pages': 0
            }
        })

@tests_bp.route('/progress/<section>', methods=['GET'])
@login_required  
def get_test_progress(section):
    """Get lightweight test progress data for test cards"""
    try:
        user_id = session['user_id']
        
        # If Supabase is not available, return empty progress
        if not current_app.supabase:
            print("📊 Supabase not available - returning empty progress")
            return jsonify({'progress': {}})
        
        # Get only essential fields for progress display
        result = current_app.supabase.table('test_history').select(
            'test_id, answers, score, max_score, grading_results, status'
        ).eq('user_id', user_id).eq('test_type', section).execute()
        
        progress_data = {}
        
        for record in result.data:
            stored_test_id = record.get('test_id', '')
            is_free_test = stored_test_id.endswith('_free')
            is_group_test = stored_test_id.startswith('group')
            original_test_id = stored_test_id.replace('_free', '') if is_free_test else stored_test_id
            
            # Create unique key for test identification
            if is_group_test:
                test_key = original_test_id  # group1, group2, etc.
            else:
                test_key = f"{original_test_id}{'_free' if is_free_test else ''}"
            
            print(f"📊 Creating test_key: original_test_id={original_test_id}, is_free={is_free_test}, test_key={test_key}")
            print(f"📊 Stored test_id in DB: {stored_test_id}")
            print(f"📊 Record has score: {record.get('score') is not None}")
            print(f"📊 Record has grading_results: {record.get('grading_results') is not None}")
            
            progress_info = {
                'test_id': original_test_id,
                'free': is_free_test,
                'status': record.get('status', 'in_progress'),
                'progress': None,
                'total': record.get('max_score') or 39,  # Handle None values properly
            }
            
            # Calculate progress from answers
            if record.get('answers'):
                try:
                    answers = json.loads(record['answers']) if isinstance(record['answers'], str) else record['answers']
                    # Count valid answers - only questions 1-39 (or 1 to total for group tests)
                    max_questions = record.get('max_score') or 39  # Handle None values properly
                    valid_answers = len([v for k, v in answers.items() if v and k.isdigit() and 1 <= int(k) <= max_questions])
                    progress_info['progress'] = valid_answers
                except:
                    pass
            
            # Add grading information if available
            if record.get('score') is not None:
                grading_info = {
                    'score': record.get('score'),
                    'max_score': record.get('max_score') or 39  # Handle None values properly
                }
                
                # Add grading results for correct/wrong counts
                if record.get('grading_results'):
                    try:
                        grading_results = json.loads(record['grading_results']) if isinstance(record['grading_results'], str) else record['grading_results']
                        grading_info['correct_count'] = len(grading_results.get('correct', []))
                        grading_info['wrong_count'] = len(grading_results.get('incorrect', []))
                        print(f"📊 [Progress API] Added grading info for {test_key}: score={grading_info['score']}, correct={grading_info['correct_count']}, wrong={grading_info['wrong_count']}")
                    except Exception as e:
                        print(f"❌ [Progress API] Error parsing grading_results for {test_key}: {e}")
                        pass
                else:
                    print(f"⚠️  [Progress API] Test {test_key} has score but no grading_results")
                
                progress_info['grading'] = grading_info
            else:
                print(f"📊 [Progress API] Test {test_key} has no score - not completed yet")
            
            progress_data[test_key] = progress_info
        
        return jsonify({'progress': progress_data})
        
    except Exception as e:
        print(f"❌ Error getting test progress: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'progress': {}})

@tests_bp.route('/calculate-score', methods=['POST'])
def calculate_score():
    """Calculate the user's score for a test (reading or listening) using GradingService"""
    try:
        data = request.get_json()
        print(f"📊 Calculate score request: {data}")
        
        section = data['section']
        test_id = data['test_id']
        answers = data['answers']
        is_free = data.get('free', False)
        
        print(f"📊 Section: {section}, Test ID: {test_id}, Free: {is_free}")
        print(f"📊 User answers: {answers}")

        # For free tests, skip authentication temporarily for testing
        # For non-free tests, check authentication and membership
        if not is_free:
            if 'user_id' not in session:
                return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401
                
            user_is_premium = check_user_premium_status()
            if not user_is_premium:
                return jsonify({'error': 'Membership required for premium tests'}), 403

        from database.grading_service import get_grading_service
        grading_service = get_grading_service()
        grading_result = grading_service.grade_test_submission(
            test_type=section,
            test_identifier=test_id,
            user_answers=answers,
            is_free=is_free
        )

        if not grading_result.get('success', False):
            return jsonify({'error': grading_result.get('error', 'Grading failed')}), 500

        # Convert grading service result to expected format
        total_score = grading_result.get('score', 0)
        max_score = grading_result.get('max_score', 0)
        correct_count = grading_result.get('correct_count', 0)

        # Convert detailed results to wrong_details format
        wrong_details = []
        for result_item in grading_result.get('results', []):
            if not result_item.get('is_correct', False) and result_item.get('user_answer'):
                wrong_details.append({
                    'question': result_item.get('question_number'),
                    'yourAnswer': result_item.get('user_answer'),
                    'correctAnswer': result_item.get('correct_answer', '(inconnu)')
                })

        # Calculate score out of 699 (TCF benchmark) for all tests
        tcf_benchmark = 699
        score_699 = int(round((total_score / max_score) * tcf_benchmark)) if max_score > 0 else None
        percent_699 = (score_699 / tcf_benchmark * 100) if score_699 is not None else None

        result = {
            'score': total_score,
            'max_score': max_score,
            'score_699': score_699,
            'percent_699': percent_699,
            'correct_count': correct_count,
            'wrong_details': wrong_details,
            'grade_level': grading_result.get('grade_level')
        }

        print(f"📊 Final score result: {result}")
        print(f"📊 Grading data that should be saved: correct_count={result.get('correct_count')}, wrong_details_count={len(result.get('wrong_details', []))}")
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Error in calculate_score: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Internal server error: {str(e)}'}), 500

@tests_bp.route('/history/<section>/<test_id>', methods=['DELETE'])
@login_required
def delete_test_history(section, test_id):
    """Delete all test history for a specific test (for retaking)"""
    try:
        user_id = session['user_id']
        is_free = request.args.get('free', '0') == '1'
        

        
        # Create effective test_id to differentiate free vs paid tests
        effective_test_id = f"{test_id}_free" if is_free else test_id

        
        # If Supabase is not available, return success (for development)
        if not current_app.supabase:
            return jsonify({'status': 'success', 'message': 'Test history reset (development mode)'})
        
        # Delete all test history records for this specific test
        result = current_app.supabase.table('test_history').delete().eq('user_id', user_id).eq('test_type', section).eq('test_id', effective_test_id).execute()
        
        deleted_count = len(result.data) if result.data else 0

        
        return jsonify({
            'status': 'success',
            'message': f'Test history reset successfully',
            'deleted_records': deleted_count
        })
        
    except Exception as e:
        print(f"❌ Error deleting test history: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to delete test history: {str(e)}'}), 500 

@tests_bp.route('/writing/list', methods=['GET'])
def get_writing_tests():
    """Get list of available writing tests with summary data for cards - optimized for fast loading"""
    
    try:
        # Get writing test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get basic test info first (fast)
        available_tests = service.get_available_tests('writing', user_is_premium=True)
        
        # Get summary statistics for each test (optimized query)
        tests_with_summary = []
        for test in available_tests:
            try:
                # Query summary data directly from database (much faster than loading full content)
                summary_data = service.get_writing_test_summary(test['id'])
                
                test_summary = {
                    'id': test['id'],
                    'title': test['title'],
                    'type': test['type'],
                    'free': test['free'],
                    'month_year': summary_data.get('month_year', test['id']),
                    'total_tasks': summary_data.get('total_tasks', 0),
                    'total_combinations': summary_data.get('total_combinations', 0),
                    'has_corrections': summary_data.get('has_corrections', False)
                }
                tests_with_summary.append(test_summary)
                
            except Exception:
                # Fallback to basic info
                tests_with_summary.append({
                    'id': test['id'],
                    'title': test['title'],
                    'type': test['type'],
                    'free': test['free'],
                    'month_year': test['id'],
                    'total_tasks': 0,
                    'total_combinations': 0,
                    'has_corrections': False
                })
        

        
        # Sort writing tests chronologically (newest first) - same logic as speaking Task 2 tests
        def parse_date_for_sorting(test):
            """Parse test ID to create sortable date key (newest first)"""
            test_id = test['id']
            try:
                # Parse format like "octobre-2023" or "janvier-2025"
                parts = test_id.split('-')
                if len(parts) == 2:
                    month_str, year_str = parts
                    year = int(year_str)
                    
                    # French month mapping
                    month_mapping = {
                        'janvier': 1, 'fevrier': 2, 'mars': 3, 'avril': 4,
                        'mai': 5, 'juin': 6, 'juillet': 7, 'aout': 8,
                        'septembre': 9, 'octobre': 10, 'novembre': 11, 'decembre': 12
                    }
                    
                    month = month_mapping.get(month_str, 1)
                    
                    # Return negative values for descending sort (newest first)
                    return (-year, -month)
            except:
                pass
            
            # Fallback: return very old date for unparseable items
            return (-1900, -1)
        
        # Sort writing tests by date (newest first)
        tests_with_summary.sort(key=parse_date_for_sorting)
        

        
        return jsonify({
            'tests': tests_with_summary,
            'membership_required': False
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to load writing tests: {str(e)}'}), 500

@tests_bp.route('/writing/<test_id>/data', methods=['GET'])
def get_writing_test_data(test_id):
    """Get writing test data - tasks free, corrections premium"""
    
    # Check user membership status for corrections access
    user_is_premium = False
    if 'user_id' in session:
        user_is_premium = check_user_premium_status()
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get writing test data with proper access control
        # Tasks (free_content) are always included
        # Corrections (paid_content) only included for premium users
        test_data = service.get_test_data(
            test_type='writing',
            test_identifier=test_id,
            is_free=False,
            user_is_premium=user_is_premium  # This controls correction access
        )
        
        if not test_data:
            return jsonify({'error': 'Writing test not found'}), 404
        
        # The service already handles the free_content/paid_content separation
        # For writing tests:
        # - free_content contains: task prompts (always available)
        # - paid_content contains: correction examples (premium only)
        
        return jsonify(test_data)
        
    except Exception as e:
        return jsonify({'error': f'Failed to load writing test: {str(e)}'}), 500

@tests_bp.route('/speaking/list', methods=['GET'])
def get_speaking_tests():
    """Get lightweight list of available speaking tests - fast loading without heavy statistics"""
    
    try:
        # Get speaking test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get basic test info only (fast - no heavy database queries)
        available_tests = service.get_available_tests('speaking', user_is_premium=True)  # Always get all tests
        
        # Group tests by task type without heavy statistics
        task2_tests = []
        task3_tests = []
        
        for test in available_tests:
            # Create basic test info without expensive database queries
            basic_test_info = {
                'id': test['id'],
                'title': test['title'],
                'type': test['type'],
                'free': test['free']
            }
            
            # Simple classification based on test ID pattern
            if test['id'] in ['Culture', 'Environnement', 'Économie', 'Éducation', 'Famille', 'Santé', 'Technologie']:
                task3_tests.append(basic_test_info)
            else:
                task2_tests.append(basic_test_info)
        
        # Sort Task 2 tests chronologically (newest first)
        def parse_date_for_sorting(test):
            """Parse test ID to create sortable date key (newest first)"""
            test_id = test['id']
            try:
                # Parse format like "octobre-2023" or "janvier-2025"
                parts = test_id.split('-')
                if len(parts) == 2:
                    month_str, year_str = parts
                    year = int(year_str)
                    
                    # French month mapping
                    month_mapping = {
                        'janvier': 1, 'fevrier': 2, 'mars': 3, 'avril': 4,
                        'mai': 5, 'juin': 6, 'juillet': 7, 'aout': 8,
                        'septembre': 9, 'octobre': 10, 'novembre': 11, 'decembre': 12
                    }
                    
                    month = month_mapping.get(month_str, 1)
                    
                    # Return negative values for descending sort (newest first)
                    return (-year, -month)
            except:
                pass
            
            # Fallback: return very old date for unparseable items
            return (-1900, -1)
        
        # Sort Task 2 tests by date (newest first)
        task2_tests.sort(key=parse_date_for_sorting)
        
        # Sort Task 3 tests alphabetically by title
        task3_tests.sort(key=lambda x: x['title'])
        

        
        return jsonify({
            'task2_tests': task2_tests,
            'task3_tests': task3_tests,
            'membership_required': False  # Basic parties/questions available to all, only sample answers require premium
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to load speaking tests: {str(e)}'}), 500

@tests_bp.route('/speaking/cards', methods=['GET'])
def get_speaking_test_cards():
    """Get speaking test cards with summary statistics - optimized for card display with Unicode normalization"""
    print("=== GET SPEAKING TEST CARDS ENDPOINT CALLED ===")
    
    try:
        # Get speaking test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get basic test info first (fast)
        available_tests = service.get_available_tests('speaking', user_is_premium=True)
        
        # Initialize card lists
        task2_cards = []
        task3_cards = []
        
        # Get proper file paths from configuration with simplified directory names
        base_dir = Path(current_app.config['BASE_DIR'])
        speaking_data_dir = base_dir / 'data' / 'scraped' / 'scraped_speaking'
        
        print(f"📁 Using base directory: {base_dir}")
        print(f"📁 Speaking data directory: {speaking_data_dir}")
        
        # Use simplified directory names (no accent characters)
        task2_dir_name = 'La_tache_2'
        task3_dir_name = 'La_tache_3'
        
        task2_dir = speaking_data_dir / task2_dir_name
        task3_dir = speaking_data_dir / task3_dir_name
        
        print(f"📁 Task 2 directory: {task2_dir}")
        print(f"📁 Task 3 directory: {task3_dir}")
        print(f"📁 Task 2 exists: {task2_dir.exists()}")
        print(f"📁 Task 3 exists: {task3_dir.exists()}")
        
        # Process each test by reading JSON files directly for statistics (efficient approach)
        import json
        
        for test in available_tests:
            test_id = test['id']
            
            try:
                # Try Task 2 (month-based) first - USE CONFIG PATHS
                task2_file = task2_dir / f'{test_id}.json'
                if task2_file.exists():
                    print(f"Reading Task 2 file: {task2_file}")
                    with open(task2_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Get statistics from JSON file (not database - for efficiency)
                    statistics = data.get('statistics', {})
                    
                    test_card = {
                        'id': test_id,
                        'title': test['title'],
                        'type': test['type'],
                        'free': test['free'],
                        'month_year': data.get('month_year', test_id),
                        'total_parties': statistics.get('total_parties', 0),
                        'total_scenarios': statistics.get('total_scenarios', 0),
                        'total_questions': statistics.get('total_questions', 0),
                        'task_type': 'tache2'
                    }
                    task2_cards.append(test_card)
                    print(f"Added Task 2 test: {test_id}")
                    continue
                
                # Try Task 3 (topic-based) - USE CONFIG PATHS
                task3_file = task3_dir / f'{normalize_for_filename(test_id)}.json'
                if task3_file.exists():
                    print(f"📄 Reading Task 3 file: {task3_file}")
                    with open(task3_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Calculate statistics directly from JSON file (not database - for efficiency)
                    taches = data.get('taches', [])
                    total_tasks = len(taches)
                    total_words = sum(tache.get('word_count', 0) for tache in taches)
                    
                    test_card = {
                        'id': test_id,
                        'title': test['title'],
                        'type': test['type'],
                        'free': test['free'],
                        'month_year': data.get('topic', test_id),
                        'total_parties': 0,  # Task 3 doesn't have parties
                        'total_scenarios': total_tasks,
                        'total_questions': total_words,  # Use word count as "questions" for Task 3
                        'task_type': 'tache3'
                    }
                    task3_cards.append(test_card)
                    print(f"Added Task 3 test: {test_id}")
                    continue
                
                # Fallback for tests without JSON files
                print(f"⚠️  No JSON file found for speaking test {test_id}")
                print(f"   Checked Task 2: {task2_file}")
                print(f"   Checked Task 3: {task3_file}")
                
                fallback_card = {
                    'id': test_id,
                    'title': test['title'],
                    'type': test['type'],
                    'free': test['free'],
                    'month_year': test_id,
                    'total_parties': 0,
                    'total_scenarios': 0,
                    'total_questions': 0,
                    'task_type': 'tache2'  # Default to Task 2
                }
                task2_cards.append(fallback_card)
                print(f"⚠️  Added fallback Task 2 test: {test_id}")
                
            except Exception as test_error:
                print(f"❌ Error processing speaking test {test_id}: {test_error}")
                import traceback
                traceback.print_exc()
                # Fallback to basic info
                fallback_card = {
                    'id': test_id,
                    'title': test['title'],
                    'type': test['type'],
                    'free': test['free'],
                    'month_year': test_id,
                    'total_parties': 0,
                    'total_scenarios': 0,
                    'total_questions': 0,
                    'task_type': 'tache2'
                }
                task2_cards.append(fallback_card)
                print(f"❌ Added error fallback Task 2 test: {test_id}")
        
        # Sort Task 2 tests chronologically (newest first)
        def parse_date_for_sorting(test):
            """Parse test ID to create sortable date key (newest first)"""
            test_id = test['id']
            try:
                # Parse format like "octobre-2023" or "janvier-2025"
                parts = test_id.split('-')
                if len(parts) == 2:
                    month_str, year_str = parts
                    year = int(year_str)
                    
                    # French month mapping
                    month_mapping = {
                        'janvier': 1, 'fevrier': 2, 'mars': 3, 'avril': 4,
                        'mai': 5, 'juin': 6, 'juillet': 7, 'aout': 8,
                        'septembre': 9, 'octobre': 10, 'novembre': 11, 'decembre': 12
                    }
                    
                    month = month_mapping.get(month_str, 1)
                    
                    # Return negative values for descending sort (newest first)
                    return (-year, -month)
            except:
                pass
            
            # Fallback: return very old date for unparseable items
            return (9999, 12)
        
        task2_cards.sort(key=parse_date_for_sorting)
        
        # Sort Task 3 tests alphabetically
        task3_cards.sort(key=lambda x: x['month_year'])
        
        print(f"✅ Final result: {len(task2_cards)} Task 2 cards, {len(task3_cards)} Task 3 cards")
        task3_info = [card['id'] + ' (' + str(card.get('total_scenarios', 0)) + ' questions)' for card in task3_cards]
        print(f"📊 Task 3 tests: {task3_info}")
        
        return jsonify({
            'success': True,
            'task2_cards': task2_cards,
            'task3_cards': task3_cards,
            'membership_required': True
        })
        
    except Exception as e:
        print(f"❌ Error in get_speaking_test_cards: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'error': str(e),
            'task2_cards': [],
            'task3_cards': [],
            'membership_required': True
        }), 500

@tests_bp.route('/speaking/<test_id>/data', methods=['GET'])
def get_speaking_test_data(test_id):
    """Get speaking test data - parties/questions free, sample answers premium"""
    
    # Check user membership status for sample answers access
    user_is_premium = False
    if 'user_id' in session:
        user_is_premium = check_user_premium_status()
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get speaking test data with proper access control
        # For speaking tests:
        # - free_content contains: parties, questions, topics (always available)
        # - paid_content contains: example questions, sample answers (premium only)
        test_data = service.get_test_data(
            test_type='speaking',
            test_identifier=test_id,
            is_free=False,
            user_is_premium=user_is_premium  # This controls sample answers access
        )
        
        if not test_data:
            return jsonify({'error': 'Speaking test not found'}), 404
        
        print(f"🎤 Successfully loaded speaking test {test_id} with {len(test_data)} parties")
        print(f"🎤 User premium status: {user_is_premium}")
        
        # Add access control info for frontend
        response_data = {
            'data': test_data,
            'user_has_access': True,  # Basic parties/questions always accessible
            'membership_required': False  # Only sample answers require premium
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error loading speaking test data: {e}")
        return jsonify({'error': f'Failed to load speaking test: {str(e)}'}), 500 

@tests_bp.route('/reading/list', methods=['GET'])
def get_reading_tests():
    """Get list of available reading tests with summary data for cards - optimized for fast loading"""
    print("=== GET READING TESTS LIST ENDPOINT CALLED ===")
    
    try:
        # Get reading test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Check if user has premium access
        user_is_premium = check_user_premium_status()
        
        # Get basic test info first (fast)
        available_tests = service.get_available_tests('reading', user_is_premium)
        
        # Separate free and premium tests
        free_tests = [t for t in available_tests if t.get('free', False)]
        premium_tests = [t for t in available_tests if not t.get('free', False)]
        
        # Build response with basic info only (no test history)
        tests = []
        free_tests_response = []
        
        # Process premium tests
        for test in premium_tests:
            test_data = {
                'id': test['id'],
                'title': test['title'],
                'type': test['type'],
                'free': test['free'],
                'total': 39  # Standard total for reading tests
            }
            tests.append(test_data)
        
        # Process free tests
        for test in free_tests:
            test_data = {
                'id': test['id'],
                'title': test['title'] + ' (Gratuit)',
                'type': test['type'],
                'free': test['free'],
                'total': 39  # Standard total for reading tests
            }
            free_tests_response.append(test_data)
        
        # Group tests info (without progress - just structure)
        group_tests = []
        if user_is_premium:
            # Updated to support 6 groups (A1, A2, B1, B2, C1, C2)
            for group_id in range(1, 7):
                tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}  # TCF difficulty structure
                group_tests.append({
                    'id': group_id,
                    'title': f'Niveau {group_id}',
                    'total': tcf_totals.get(group_id, 5)
                })
        
        print(f"📖 Loaded reading test list: {len(tests)} premium, {len(free_tests_response)} free, {len(group_tests)} groups")
        
        return jsonify({
            'section': 'reading',
            'tests': sorted(tests, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'free_tests': sorted(free_tests_response, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'group_tests': group_tests,
            'membership_required': not user_is_premium
        })
        
    except Exception as e:
        print(f"Error loading reading test list: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to load reading test list: {str(e)}'}), 500

# ============================================================================
# WRITING SUBMISSIONS API ENDPOINTS
# ============================================================================

@tests_bp.route('/writing/submissions', methods=['POST'])
@login_required
def save_writing_submission():
    """Save or update a writing submission"""
    try:
        data = request.get_json()
        user_id = session['user_id']

        # Validate required fields
        required_fields = ['test_type', 'test_identifier', 'task_number', 'content']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400

        # Calculate content metrics
        content = data['content'].strip()
        word_count = len(content.split()) if content else 0
        character_count = len(content)

        # Prepare submission data
        submission_data = {
            'user_id': user_id,
            'test_type': data['test_type'],
            'test_identifier': data['test_identifier'],
            'task_number': data['task_number'],
            'combination_number': data.get('combination_number'),
            'content': content,
            'content_format': data.get('content_format', 'plain_text'),
            'word_count': word_count,
            'character_count': character_count,
            'status': data.get('status', 'draft'),
            'writing_duration': data.get('writing_duration'),
            'auto_save_count': data.get('auto_save_count', 0),
            'metadata': data.get('metadata', {})
        }

        # Set submission time if status is 'submitted'
        if submission_data['status'] == 'submitted':
            submission_data['submission_time'] = datetime.now(timezone.utc).isoformat()
            submission_data['is_final_submission'] = True

        # Check if submission already exists
        query = current_app.supabase.table('writing_submissions').select('id, version').eq(
            'user_id', user_id
        ).eq(
            'test_type', data['test_type']
        ).eq(
            'test_identifier', data['test_identifier']
        ).eq(
            'task_number', data['task_number']
        )

        if data.get('combination_number'):
            query = query.eq('combination_number', data['combination_number'])
        else:
            query = query.is_('combination_number', 'null')

        result = query.execute()

        if result.data:
            # Update existing submission
            existing = result.data[0]
            submission_data['version'] = existing['version'] + 1
            submission_data['previous_version_id'] = existing['id']

            response = current_app.supabase.table('writing_submissions').update(
                submission_data
            ).eq('id', existing['id']).execute()
        else:
            # Create new submission
            response = current_app.supabase.table('writing_submissions').insert(
                submission_data
            ).execute()

        return jsonify({
            'status': 'success',
            'submission_id': response.data[0]['id'],
            'word_count': word_count,
            'character_count': character_count,
            'version': response.data[0]['version']
        })

    except Exception as e:
        print(f"Error saving writing submission: {e}")
        return jsonify({'error': f'Failed to save writing submission: {str(e)}'}), 500

@tests_bp.route('/writing/submissions/<test_type>/<test_identifier>', methods=['GET'])
@login_required
def get_writing_submissions(test_type, test_identifier):
    """Get user's writing submissions for a specific test"""
    try:
        user_id = session['user_id']
        task_number = request.args.get('task_number', type=int)
        combination_number = request.args.get('combination_number')

        # Build query
        query = current_app.supabase.table('writing_submissions').select('*').eq(
            'user_id', user_id
        ).eq(
            'test_type', test_type
        ).eq(
            'test_identifier', test_identifier
        )

        if task_number:
            query = query.eq('task_number', task_number)

        if combination_number:
            query = query.eq('combination_number', combination_number)

        # Order by creation date (newest first)
        query = query.order('created_at', desc=True)

        result = query.execute()

        return jsonify({
            'status': 'success',
            'submissions': result.data
        })

    except Exception as e:
        print(f"Error retrieving writing submissions: {e}")
        return jsonify({'error': f'Failed to retrieve writing submissions: {str(e)}'}), 500

@tests_bp.route('/writing/submissions/history', methods=['GET'])
@login_required
def get_writing_submission_history():
    """Get user's complete writing submission history"""
    try:
        user_id = session['user_id']
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        test_type = request.args.get('test_type')  # Optional filter

        # Build query
        query = current_app.supabase.table('writing_submissions').select(
            'id, test_type, test_identifier, task_number, combination_number, '
            'word_count, character_count, status, submission_time, created_at, '
            'updated_at, version, is_final_submission'
        ).eq('user_id', user_id)

        if test_type:
            query = query.eq('test_type', test_type)

        # Order by creation date (newest first) and apply pagination
        query = query.order('created_at', desc=True).range(offset, offset + limit - 1)

        result = query.execute()

        return jsonify({
            'status': 'success',
            'submissions': result.data,
            'pagination': {
                'limit': limit,
                'offset': offset,
                'count': len(result.data)
            }
        })

    except Exception as e:
        print(f"Error retrieving writing submission history: {e}")
        return jsonify({'error': f'Failed to retrieve writing submission history: {str(e)}'}), 500

@tests_bp.route('/writing/submissions/<submission_id>', methods=['GET'])
@login_required
def get_writing_submission_by_id(submission_id):
    """Get a specific writing submission by ID"""
    try:
        user_id = session['user_id']

        result = current_app.supabase.table('writing_submissions').select('*').eq(
            'id', submission_id
        ).eq(
            'user_id', user_id  # Ensure user can only access their own submissions
        ).execute()

        if not result.data:
            return jsonify({'error': 'Writing submission not found'}), 404

        return jsonify({
            'status': 'success',
            'submission': result.data[0]
        })

    except Exception as e:
        print(f"Error retrieving writing submission: {e}")
        return jsonify({'error': f'Failed to retrieve writing submission: {str(e)}'}), 500

@tests_bp.route('/writing/submissions/<submission_id>', methods=['DELETE'])
@login_required
def delete_writing_submission(submission_id):
    """Delete a writing submission"""
    try:
        user_id = session['user_id']

        # First check if submission exists and belongs to user
        result = current_app.supabase.table('writing_submissions').select('id').eq(
            'id', submission_id
        ).eq(
            'user_id', user_id
        ).execute()

        if not result.data:
            return jsonify({'error': 'Writing submission not found'}), 404

        # Delete the submission
        current_app.supabase.table('writing_submissions').delete().eq(
            'id', submission_id
        ).eq(
            'user_id', user_id
        ).execute()

        return jsonify({
            'status': 'success',
            'message': 'Writing submission deleted successfully'
        })

    except Exception as e:
        print(f"Error deleting writing submission: {e}")
        return jsonify({'error': f'Failed to delete writing submission: {str(e)}'}), 500

@tests_bp.route('/writing/submissions/stats', methods=['GET'])
@login_required
def get_writing_submission_stats():
    """Get user's writing submission statistics"""
    try:
        user_id = session['user_id']

        # Get all submissions for stats
        result = current_app.supabase.table('writing_submissions').select(
            'test_type, status, word_count, character_count, created_at, is_final_submission'
        ).eq('user_id', user_id).execute()

        submissions = result.data

        # Calculate statistics
        stats = {
            'total_submissions': len(submissions),
            'final_submissions': len([s for s in submissions if s.get('is_final_submission')]),
            'draft_submissions': len([s for s in submissions if s.get('status') == 'draft']),
            'by_test_type': {},
            'total_words_written': sum(s.get('word_count', 0) for s in submissions),
            'average_word_count': 0,
            'recent_activity': []
        }

        # Group by test type
        for submission in submissions:
            test_type = submission.get('test_type', 'unknown')
            if test_type not in stats['by_test_type']:
                stats['by_test_type'][test_type] = {
                    'count': 0,
                    'final_count': 0,
                    'total_words': 0
                }

            stats['by_test_type'][test_type]['count'] += 1
            stats['by_test_type'][test_type]['total_words'] += submission.get('word_count', 0)

            if submission.get('is_final_submission'):
                stats['by_test_type'][test_type]['final_count'] += 1

        # Calculate average word count
        if submissions:
            stats['average_word_count'] = stats['total_words_written'] / len(submissions)

        # Recent activity (last 10 submissions)
        recent_submissions = sorted(submissions, key=lambda x: x.get('created_at', ''), reverse=True)[:10]
        stats['recent_activity'] = [
            {
                'test_type': s.get('test_type'),
                'test_identifier': s.get('test_identifier'),
                'task_number': s.get('task_number'),
                'word_count': s.get('word_count'),
                'status': s.get('status'),
                'created_at': s.get('created_at')
            }
            for s in recent_submissions
        ]

        return jsonify({
            'status': 'success',
            'stats': stats
        })

    except Exception as e:
        print(f"Error retrieving writing submission stats: {e}")
        return jsonify({'error': f'Failed to retrieve writing submission stats: {str(e)}'}), 500

@tests_bp.route('/reading/cards', methods=['GET'])
@login_required
def get_reading_test_cards():
    """Get reading test cards with progress and test history - optimized for card display"""
    # Safety check for Supabase client
    if not current_app.supabase:
        print("❌ Supabase client is None in get_reading_test_cards")
        return jsonify({'error': 'Database connection not available', 'code': 'DB_ERROR'}), 503
        
    user_id = session.get('user_id')
    
    # Check if user has premium access
    user_is_premium = check_user_premium_status()
    
    # Initialize progress tracking
    progress_data = {}
    
    try:
        # Get basic test list first
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        available_tests = service.get_available_tests('reading', user_is_premium=True)
        
        # Separate free and premium tests
        free_tests = [t for t in available_tests if t.get('free', False)]
        premium_tests = [t for t in available_tests if not t.get('free', False)]
        
        # Get progress data efficiently
        progress_data = {}
        if user_id and current_app.supabase:
            try:
                result = current_app.supabase.table('test_history').select(
                    'test_id, answers, score, max_score, grading_results, status'
                ).eq('user_id', user_id).eq('test_type', 'reading').execute()
                

                
                for record in result.data:
                    stored_test_id = record.get('test_id', '')
                    is_free_test = stored_test_id.endswith('_free')
                    is_group_test = stored_test_id.startswith('group')
                    original_test_id = stored_test_id.replace('_free', '') if is_free_test else stored_test_id
                    
                    # Create unique key for test identification
                    if is_group_test:
                        test_key = original_test_id  # group1, group2, etc.
                    else:
                        test_key = f"{original_test_id}{'_free' if is_free_test else ''}"
                    
                    progress_info = {
                        'test_id': original_test_id,
                        'free': is_free_test,
                        'status': record.get('status', 'in_progress'),
                        'progress': None,
                        'total': record.get('max_score') or 39,
                    }
                    
                    # Calculate progress from answers
                    if record.get('answers'):
                        try:
                            answers = json.loads(record['answers']) if isinstance(record['answers'], str) else record['answers']
                            max_questions = record.get('max_score') or 39
                            valid_answers = len([v for k, v in answers.items() if v and k.isdigit() and 1 <= int(k) <= max_questions])
                            progress_info['progress'] = valid_answers
                        except Exception as e:
                            print(f"❌ Error calculating progress for {test_key}: {e}")
                    
                    # Add grading information if available
                    if record.get('score') is not None:
                        grading_info = {
                            'score': record.get('score'),
                            'max_score': record.get('max_score') or 39
                        }
                        
                        if record.get('grading_results'):
                            try:
                                grading_results = json.loads(record['grading_results']) if isinstance(record['grading_results'], str) else record['grading_results']
                                grading_info['correct_count'] = len(grading_results.get('correct', []))
                                grading_info['wrong_count'] = len(grading_results.get('incorrect', []))
                            except Exception as e:
                                print(f"❌ Error parsing grading_results for {test_key}: {e}")
                        
                        progress_info['grading'] = grading_info
                    
                    progress_data[test_key] = progress_info
            except Exception as e:
                print(f"Error loading progress data: {e}")
        
        # Build response with progress data
        tests = []
        free_tests_with_progress = []
        
        # Process premium tests
        for test in premium_tests:
            test_key = test['id']
            progress_info = progress_data.get(test_key, {})
            
            test_data = {
                'id': test['id'],
                'title': test['title'],
                'type': test['type'],
                'free': test['free'],
                'total': 39,
                'progress': progress_info.get('progress'),
            }
            
            if 'grading' in progress_info:
                test_data['grading'] = progress_info['grading']
            
            tests.append(test_data)
        
        # Process free tests
        for test in free_tests:
            test_key = f"{test['id']}_free"
            progress_info = progress_data.get(test_key, {})
            
            test_data = {
                'id': test['id'],
                'title': test['title'] + ' (Gratuit)',
                'type': test['type'],
                'free': test['free'],
                'total': 39,
                'progress': progress_info.get('progress'),
            }
            
            if 'grading' in progress_info:
                test_data['grading'] = progress_info['grading']
            
            free_tests_with_progress.append(test_data)
        
        # Get group progress for difficulty-based tests
        group_progress = {}
        if user_is_premium:
            # Updated to support 6 groups (A1, A2, B1, B2, C1, C2)
            for group_id in range(1, 7):
                try:
                    # Get group test data to determine total questions
                    group_data = service.get_group_test_data('reading', group_id, user_is_premium)
                    if group_data:
                        total = len(group_data)
                        
                        # Get progress data
                        group_key = f'group{group_id}'
                        progress_info = progress_data.get(group_key, {})
                        
                        group_info = {'total': total, 'progress': progress_info.get('progress')}
                        
                        if 'grading' in progress_info:
                            group_info['grading'] = progress_info['grading']
                        
                        group_progress[group_id] = group_info
                    else:
                        # Updated fallback totals for 6 groups based on TCF structure
                        tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                        group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
                except Exception as e:
                    print(f"Error loading group {group_id}: {e}")
                    # Updated fallback totals for 6 groups
                    tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                    group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
        else:
            # For non-premium users, show all 6 groups but with no access
            tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
            for group_id in range(1, 7):
                group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
        
        print(f"📖 Loaded reading test cards: {len(tests)} premium, {len(free_tests_with_progress)} free")
        
        return jsonify({
            'section': 'reading',
            'tests': sorted(tests, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'free_tests': sorted(free_tests_with_progress, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'group_progress': group_progress,
            'membership_required': not user_is_premium
        })
        
    except Exception as e:
        print(f"Error loading reading test cards: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to load reading test cards: {str(e)}'}), 500

@tests_bp.route('/listening/list', methods=['GET'])
def get_listening_tests():
    """Get list of available listening tests with summary data for cards - optimized for fast loading"""
    print("=== GET LISTENING TESTS LIST ENDPOINT CALLED ===")
    
    try:
        # Get listening test data from Supabase using the service
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Check if user has premium access
        user_is_premium = check_user_premium_status()
        
        # Get basic test info first (fast)
        available_tests = service.get_available_tests('listening', user_is_premium)
        
        # Separate free and premium tests
        free_tests = [t for t in available_tests if t.get('free', False)]
        premium_tests = [t for t in available_tests if not t.get('free', False)]
        
        # Build response with basic info only (no test history)
        tests = []
        free_tests_response = []
        
        # Process premium tests
        for test in premium_tests:
            test_data = {
                'id': test['id'],
                'title': test['title'],
                'type': test['type'],
                'free': test['free'],
                'total': 39  # Standard total for listening tests
            }
            tests.append(test_data)
        
        # Process free tests
        for test in free_tests:
            test_data = {
                'id': test['id'],
                'title': test['title'] + ' (Gratuit)',
                'type': test['type'],
                'free': test['free'],
                'total': 39  # Standard total for listening tests
            }
            free_tests_response.append(test_data)
        
        # Group tests info (without progress - just structure)
        group_tests = []
        if user_is_premium:
            # Updated to support 6 groups (A1, A2, B1, B2, C1, C2)
            for group_id in range(1, 7):
                tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}  # TCF difficulty structure
                group_tests.append({
                    'id': group_id,
                    'title': f'Niveau {group_id}',
                    'total': tcf_totals.get(group_id, 5)
                })
        
        print(f"🎧 Loaded listening test list: {len(tests)} premium, {len(free_tests_response)} free, {len(group_tests)} groups")
        
        return jsonify({
            'section': 'listening',
            'tests': sorted(tests, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'free_tests': sorted(free_tests_response, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'group_tests': group_tests,
            'membership_required': not user_is_premium
        })
        
    except Exception as e:
        print(f"Error loading listening test list: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to load listening test list: {str(e)}'}), 500

@tests_bp.route('/listening/cards', methods=['GET'])
@login_required
def get_listening_test_cards():
    """Get listening test cards with progress and test history - optimized for card display"""
    # Safety check for Supabase client
    if not current_app.supabase:
        print("❌ Supabase client is None in get_listening_test_cards")
        return jsonify({'error': 'Database connection not available', 'code': 'DB_ERROR'}), 503
        
    user_id = session.get('user_id')
    
    # Check if user has premium access
    user_is_premium = check_user_premium_status()
    
    # Initialize progress tracking
    progress_data = {}
    
    try:
        # Get basic test list first
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        available_tests = service.get_available_tests('listening', user_is_premium)
        
        # Separate free and premium tests
        free_tests = [t for t in available_tests if t.get('free', False)]
        premium_tests = [t for t in available_tests if not t.get('free', False)]
        
        # Get progress data efficiently
        progress_data = {}
        if user_id and current_app.supabase:
            try:
                result = current_app.supabase.table('test_history').select(
                    'test_id, answers, score, max_score, grading_results, status'
                ).eq('user_id', user_id).eq('test_type', 'listening').execute()
                

                
                for record in result.data:
                    stored_test_id = record.get('test_id', '')
                    is_free_test = stored_test_id.endswith('_free')
                    is_group_test = stored_test_id.startswith('group')
                    original_test_id = stored_test_id.replace('_free', '') if is_free_test else stored_test_id
                    
                    # Create unique key for test identification
                    if is_group_test:
                        test_key = original_test_id  # group1, group2, etc.
                    else:
                        test_key = f"{original_test_id}{'_free' if is_free_test else ''}"
                    
                    progress_info = {
                        'test_id': original_test_id,
                        'free': is_free_test,
                        'status': record.get('status', 'in_progress'),
                        'progress': None,
                        'total': record.get('max_score') or 39,
                    }
                    
                    # Calculate progress from answers
                    if record.get('answers'):
                        try:
                            answers = json.loads(record['answers']) if isinstance(record['answers'], str) else record['answers']
                            max_questions = record.get('max_score') or 39
                            valid_answers = len([v for k, v in answers.items() if v and k.isdigit() and 1 <= int(k) <= max_questions])
                            progress_info['progress'] = valid_answers
                        except Exception as e:
                            print(f"❌ Error calculating progress for {test_key}: {e}")
                    
                    # Add grading information if available
                    if record.get('score') is not None:
                        grading_info = {
                            'score': record.get('score'),
                            'max_score': record.get('max_score') or 39
                        }
                        
                        if record.get('grading_results'):
                            try:
                                grading_results = json.loads(record['grading_results']) if isinstance(record['grading_results'], str) else record['grading_results']
                                grading_info['correct_count'] = len(grading_results.get('correct', []))
                                grading_info['wrong_count'] = len(grading_results.get('incorrect', []))
                            except Exception as e:
                                print(f"❌ Error parsing grading_results for {test_key}: {e}")
                        
                        progress_info['grading'] = grading_info
                    
                    progress_data[test_key] = progress_info
            except Exception as e:
                print(f"Error loading progress data: {e}")
        
        # Build response with progress data
        tests = []
        free_tests_with_progress = []
        
        # Process premium tests
        for test in premium_tests:
            test_key = test['id']
            progress_info = progress_data.get(test_key, {})
            
            test_data = {
                'id': test['id'],
                'title': test['title'],
                'type': test['type'],
                'free': test['free'],
                'total': 39,
                'progress': progress_info.get('progress'),
            }
            
            if 'grading' in progress_info:
                test_data['grading'] = progress_info['grading']
            
            tests.append(test_data)
        
        # Process free tests
        for test in free_tests:
            test_key = f"{test['id']}_free"
            progress_info = progress_data.get(test_key, {})
            
            test_data = {
                'id': test['id'],
                'title': test['title'] + ' (Gratuit)',
                'type': test['type'],
                'free': test['free'],
                'total': 39,
                'progress': progress_info.get('progress'),
            }
            
            if 'grading' in progress_info:
                test_data['grading'] = progress_info['grading']
            
            free_tests_with_progress.append(test_data)
        
        # Get group progress for difficulty-based tests
        group_progress = {}
        if user_is_premium:
            # Updated to support 6 groups (A1, A2, B1, B2, C1, C2)
            for group_id in range(1, 7):
                try:
                    # Get group test data to determine total questions
                    group_data = service.get_group_test_data('listening', group_id, user_is_premium)
                    if group_data:
                        total = len(group_data)
                        
                        # Get progress data
                        group_key = f'group{group_id}'
                        progress_info = progress_data.get(group_key, {})
                        
                        group_info = {'total': total, 'progress': progress_info.get('progress')}
                        
                        if 'grading' in progress_info:
                            group_info['grading'] = progress_info['grading']
                        
                        group_progress[group_id] = group_info
                    else:
                        # Updated fallback totals for 6 groups based on TCF structure  
                        tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                        group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
                except Exception as e:
                    print(f"Error loading group {group_id}: {e}")
                    tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
                    group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
        else:
            # For non-premium users, show groups but with no access
            tcf_totals = {1: 4, 2: 6, 3: 9, 4: 10, 5: 6, 6: 4}
            for group_id in range(1, 7):
                group_progress[group_id] = {'total': tcf_totals.get(group_id, 5), 'progress': None}
        
        print(f"🎧 Loaded listening test cards: {len(tests)} premium, {len(free_tests_with_progress)} free")
        
        return jsonify({
            'section': 'listening',
            'tests': sorted(tests, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'free_tests': sorted(free_tests_with_progress, key=lambda t: int(t['id']) if t['id'].isdigit() else 999),
            'group_progress': group_progress,
            'membership_required': not user_is_premium
        })
        
    except Exception as e:
        print(f"Error loading listening test cards: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to load listening test cards: {str(e)}'}), 500 

@tests_bp.route('/collection-book', methods=['GET'])
def get_user_collection_book():
    """Get user's collection book (bookmarked questions) with test history"""
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    
    user_id = session['user_id']
    
    try:
        # Get user's bookmarked questions
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        response = service.supabase.table('collection_book').select('*').eq(
            'user_id', user_id
        ).order('created_at', desc=True).execute()
        
        # Get collection book test history
        test_history = None
        try:
            history_result = current_app.supabase.table('test_history').select('*').eq(
                'user_id', user_id
            ).eq('test_type', 'collection_book').eq('test_id', 'collection_book_notebook').execute()
            
            if history_result.data:
                test_history = history_result.data[0]  # Get the most recent entry
                print(f"📊 Collection book test history found: {test_history.keys()}")
        except Exception as e:
            print(f"📊 No collection book test history found or error: {e}")
            test_history = None
        
        if not response.data:
            result = {
                'reading_collection': [],
                'listening_collection': []
            }
            
            # Include test history even if no bookmarks
            if test_history:
                # Parse previous answers
                if test_history.get('answers'):
                    try:
                        previous_answers = json.loads(test_history['answers']) if isinstance(test_history['answers'], str) else test_history['answers']
                        result['previous_answers'] = previous_answers
                        print(f"📊 Previous collection book answers loaded: {len(previous_answers)} answers")
                    except Exception as e:
                        print(f"❌ Error parsing answers: {e}")
                
                # Parse grading results
                if test_history.get('grading_results'):
                    try:
                        grading_results = json.loads(test_history['grading_results']) if isinstance(test_history['grading_results'], str) else test_history['grading_results']
                        result['grading_results'] = grading_results
                        print(f"📊 Previous collection book grading results loaded: {len(grading_results.get('wrong_details', []))} wrong answers")
                    except Exception as e:
                        print(f"❌ Error parsing grading_results: {e}")
            
            return jsonify(result)
        
        # Separate by test type
        reading_collection = []
        listening_collection = []
        
        for bookmark in response.data:
            test_type = bookmark.get('test_type')
            if test_type == 'reading':
                reading_collection.append(bookmark)
            elif test_type == 'listening':
                listening_collection.append(bookmark)
        
        result = {
            'reading_collection': reading_collection,
            'listening_collection': listening_collection
        }
        
        # Include test history if available
        if test_history:
            # Parse previous answers
            if test_history.get('answers'):
                try:
                    previous_answers = json.loads(test_history['answers']) if isinstance(test_history['answers'], str) else test_history['answers']
                    result['previous_answers'] = previous_answers
                    print(f"📊 Previous collection book answers loaded: {len(previous_answers)} answers")
                except Exception as e:
                    print(f"❌ Error parsing answers: {e}")
            
            # Parse grading results
            if test_history.get('grading_results'):
                try:
                    grading_results = json.loads(test_history['grading_results']) if isinstance(test_history['grading_results'], str) else test_history['grading_results']
                    result['grading_results'] = grading_results
                    print(f"📊 Previous collection book grading results loaded: {len(grading_results.get('wrong_details', []))} wrong answers")
                except Exception as e:
                    print(f"❌ Error parsing grading_results: {e}")
        
        return jsonify(result)
        
    except Exception as e:
        print(f"❌ Error fetching collection book: {e}")
        return jsonify({'error': 'Failed to fetch collection book'}), 500

@tests_bp.route('/collection-book/bookmark', methods=['POST'])
def add_bookmark():
    """Add a question to user's collection book"""
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    
    user_id = session['user_id']
    data = request.get_json()
    
    if not data or 'question_id' not in data:
        return jsonify({'error': 'Question ID required'}), 400
    
    question_id = data['question_id']
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # First, get the complete question data
        question_response = service.supabase.table('test_questions').select('*').eq(
            'id', question_id
        ).execute()
        
        if not question_response.data:
            return jsonify({'error': 'Question not found'}), 404
        
        question = question_response.data[0]
        
        # Only allow reading and listening questions
        if question['test_type'] not in ['reading', 'listening']:
            return jsonify({'error': 'Only reading and listening questions can be bookmarked'}), 400
        
        # Check if already bookmarked
        existing = service.supabase.table('collection_book').select('id').eq(
            'user_id', user_id
        ).eq(
            'question_id', question_id
        ).execute()
        
        if existing.data:
            return jsonify({'error': 'Question already bookmarked'}), 409
        
        # Copy the entire question data to collection_book
        bookmark_record = {
            'user_id': user_id,
            'question_id': question_id,
            'test_type': question['test_type'],
            'test_category': question['test_category'],
            'test_identifier': question['test_identifier'],
            'question_number': question['question_number'],
            'free_content': question.get('free_content', {}),
            'paid_content': question.get('paid_content', {}),
            'content_flags': question.get('content_flags', {}),
            'correct_answer': question.get('correct_answer', {}),
            'question_data': question.get('question_data', {}),
            'metadata': question.get('metadata', {})
        }
        
        result = service.supabase.table('collection_book').insert(bookmark_record).execute()
        
        if result.data:
            return jsonify({
                'success': True, 
                'message': 'Question bookmarked successfully',
                'bookmark_id': result.data[0]['id']
            })
        else:
            return jsonify({'error': 'Failed to bookmark question'}), 500
            
    except Exception as e:
        print(f"❌ Error adding bookmark: {e}")
        # Handle unique constraint violation (already bookmarked)
        if 'unique_user_question' in str(e):
            return jsonify({'error': 'Question already bookmarked'}), 409
        return jsonify({'error': 'Failed to bookmark question'}), 500

@tests_bp.route('/collection-book/bookmark', methods=['DELETE'])
def remove_bookmark():
    """Remove a question from user's collection book"""
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    
    user_id = session['user_id']
    data = request.get_json()
    
    if not data or 'question_id' not in data:
        return jsonify({'error': 'Question ID required'}), 400
    
    question_id = data['question_id']
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Delete the bookmark
        service.supabase.table('collection_book').delete().eq(
            'user_id', user_id
        ).eq(
            'question_id', question_id
        ).execute()
        
        return jsonify({
            'success': True,
            'message': 'Bookmark removed successfully'
        })
        
    except Exception as e:
        print(f"❌ Error removing bookmark: {e}")
        return jsonify({'error': 'Failed to remove bookmark'}), 500

@tests_bp.route('/collection-book/check/<question_id>', methods=['GET'])
def check_bookmark_status(question_id):
    """Check if a question is bookmarked by the user"""
    if 'user_id' not in session:
        return jsonify({'bookmarked': False})
    
    user_id = session['user_id']
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        response = service.supabase.table('collection_book').select('id').eq(
            'user_id', user_id
        ).eq(
            'question_id', question_id
        ).execute()
        
        return jsonify({
            'bookmarked': len(response.data) > 0
        })
        
    except Exception as e:
        print(f"❌ Error checking bookmark status: {e}")
        return jsonify({'bookmarked': False})

@tests_bp.route('/collection-book/check/batch', methods=['POST'])
def check_bookmark_status_batch():
    """Check bookmark status for multiple questions at once - much more efficient"""
    if 'user_id' not in session:
        return jsonify({'bookmarks': {}})
    
    user_id = session['user_id']
    data = request.get_json()
    
    if not data or 'question_ids' not in data:
        return jsonify({'error': 'question_ids array required'}), 400
    
    question_ids = data['question_ids']
    
    if not isinstance(question_ids, list) or len(question_ids) == 0:
        return jsonify({'error': 'question_ids must be a non-empty array'}), 400
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        # Get all bookmarks for this user that match the question IDs in one query
        response = service.supabase.table('collection_book').select('question_id').eq(
            'user_id', user_id
        ).in_('question_id', question_ids).execute()
        
        # Create a set of bookmarked question IDs for fast lookup
        bookmarked_ids = {row['question_id'] for row in response.data}
        
        # Build result mapping each question_id to boolean
        bookmarks = {qid: qid in bookmarked_ids for qid in question_ids}
        
        return jsonify({
            'bookmarks': bookmarks,
            'total_checked': len(question_ids),
            'total_bookmarked': len(bookmarked_ids)
        })
        
    except Exception as e:
        # Return all false on error
        return jsonify({'bookmarks': {qid: False for qid in question_ids}})

@tests_bp.route('/collection-book/bookmark/bulk', methods=['POST'])
def add_bulk_bookmarks():
    """Add multiple questions to user's collection book"""
    if 'user_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401
    
    user_id = session['user_id']
    data = request.get_json()
    
    if not data or 'questions' not in data:
        return jsonify({'error': 'Questions list required'}), 400
    
    questions = data['questions']
    
    if not isinstance(questions, list) or len(questions) == 0:
        return jsonify({'error': 'Questions must be a non-empty list'}), 400
    
    try:
        from database.test_data_service import get_test_data_service
        service = get_test_data_service()
        
        added_count = 0
        already_bookmarked = 0
        errors = []
        
        for question_data in questions:
            question_id = question_data.get('question_id')
            if not question_id:
                errors.append('Missing question_id in question data')
                continue
            
            try:
                # First, get the complete question data
                question_response = service.supabase.table('test_questions').select('*').eq(
                    'id', question_id
                ).execute()
                
                if not question_response.data:
                    errors.append(f'Question {question_id} not found')
                    continue
                
                question = question_response.data[0]
                
                # Only allow reading and listening questions
                if question['test_type'] not in ['reading', 'listening']:
                    errors.append(f'Question {question_id} is not a reading or listening question')
                    continue
                
                # Check if already bookmarked
                existing = service.supabase.table('collection_book').select('id').eq(
                    'user_id', user_id
                ).eq(
                    'question_id', question_id
                ).execute()
                
                if existing.data:
                    already_bookmarked += 1
                    continue
                
                # Copy the entire question data to collection_book
                bookmark_record = {
                    'user_id': user_id,
                    'question_id': question_id,
                    'test_type': question['test_type'],
                    'test_category': question['test_category'],
                    'test_identifier': question['test_identifier'],
                    'question_number': question['question_number'],
                    'free_content': question.get('free_content', {}),
                    'paid_content': question.get('paid_content', {}),
                    'content_flags': question.get('content_flags', {}),
                    'correct_answer': question.get('correct_answer', {}),
                    'question_data': question.get('question_data', {}),
                    'metadata': question.get('metadata', {})
                }
                
                result = service.supabase.table('collection_book').insert(bookmark_record).execute()
                
                if result.data:
                    added_count += 1
                else:
                    errors.append(f'Failed to bookmark question {question_id}')
                    
            except Exception as e:
                if 'unique_user_question' in str(e):
                    already_bookmarked += 1
                else:
                    errors.append(f'Error bookmarking question {question_id}: {str(e)}')
        
        return jsonify({
            'added_count': added_count,
            'already_bookmarked': already_bookmarked,
            'errors': errors,
            'total_processed': len(questions)
        })
        
    except Exception as e:
        print(f"❌ Error adding bulk bookmarks: {e}")
        return jsonify({'error': 'Failed to process bulk bookmarks'}), 500

@tests_bp.route('/writing/generate-template', methods=['POST'])
@login_required
def generate_writing_template():
    """Generate a writing template using AI"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['task_type', 'topic']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        task_type = data['task_type']
        topic = data['topic']
        subtopic = data.get('subtopic', '')
        difficulty_level = data.get('difficulty_level', 'intermediate')
        word_count_target = data.get('word_count_target')
        
        # Set default word counts based on task type
        if word_count_target is None:
            word_count_defaults = {1: 90, 2: 135, 3: 150}
            word_count_target = word_count_defaults.get(task_type, 120)
        
        # Generate template using AI
        try:
            from ..services.template_generator import TemplateGenerator
            generator = TemplateGenerator()
            
            template_data = generator.generate_template({
                'task_type': task_type,
                'topic': topic,
                'subtopic': subtopic,
                'difficulty_level': difficulty_level,
                'word_count_target': word_count_target
            })
            
            return jsonify({
                'status': 'success',
                'template': template_data['template'],
                'structure_guide': template_data.get('structure_guide', ''),
                'key_phrases': template_data.get('key_phrases', []),
                'suggested_vocabulary': template_data.get('suggested_vocabulary', []),
                'word_count_target': word_count_target
            })
            
        except Exception as e:
            print(f"Error generating template: {e}")
            return jsonify({'error': f'Failed to generate template: {str(e)}'}), 500
            
    except Exception as e:
        print(f"Error in generate_writing_template: {e}")
        return jsonify({'error': f'Failed to process request: {str(e)}'}), 500