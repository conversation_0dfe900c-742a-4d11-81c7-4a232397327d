"""
Classified Writing Translations API Blueprint
Provides dedicated endpoints for fetching topic and subtopic translations
"""

from flask import Blueprint, jsonify, current_app
from typing import Dict, Any

# Create blueprint
classified_writing_translations_bp = Blueprint('classified_writing_translations', __name__)

def extract_translations_from_database(tache_number: int) -> Dict[str, Any]:
    """
    Extract all unique translations from the database for a specific tâche

    Args:
        tache_number: The tâche number (1, 2, or 3)

    Returns:
        Dictionary with 'topics' and 'subtopics' translation mappings
    """
    try:
        # Get all test_questions for this tâche
        result = current_app.supabase.table('test_questions').select(
            'question_data'
        ).eq('test_type', 'writing').eq('test_category', 'classified').eq(
            'test_identifier', f'tache_{tache_number}'
        ).execute()

        if not result.data:
            current_app.logger.warning(f"No test questions found for tâche {tache_number}")
            return {'topics': {}, 'subtopics': {}}

        topics_translations = {}
        subtopics_translations = {}

        # Process each task to extract translations
        for record in result.data:
            question_data = record.get('question_data', {})
            classification = question_data.get('classification', {})

            # Extract topic translations
            main_topic = classification.get('main_topic')
            main_topic_translations = classification.get('main_topic_translations', {})
            if main_topic and main_topic_translations:
                topics_translations[main_topic] = main_topic_translations

            # Extract subtopic translations
            subtopic = classification.get('subtopic')
            subtopic_translations = classification.get('subtopic_translations', {})
            if subtopic and subtopic_translations:
                subtopics_translations[subtopic] = subtopic_translations

        current_app.logger.info(f"Extracted translations for tâche {tache_number}: "
                               f"{len(topics_translations)} topics, {len(subtopics_translations)} subtopics")

        return {
            'topics': topics_translations,
            'subtopics': subtopics_translations
        }

    except Exception as e:
        current_app.logger.error(f"Error extracting translations from database for tâche {tache_number}: {e}")
        return {'topics': {}, 'subtopics': {}}

@classified_writing_translations_bp.route('/api/classified-writing/translations/<int:tache_number>', methods=['GET'])
def get_tache_translations(tache_number: int):
    """
    Get all translations for a specific tâche
    
    Args:
        tache_number: The tâche number (1, 2, or 3)
        
    Returns:
        JSON response with topics and subtopics translations
    """
    try:
        # Validate tâche number
        if tache_number not in [1, 2, 3]:
            return jsonify({
                'error': 'Invalid tâche number',
                'message': 'Tâche number must be 1, 2, or 3'
            }), 400
        
        # Extract translations from database
        translations = extract_translations_from_database(tache_number)
        
        # Add metadata
        response = {
            'tache_number': tache_number,
            'translations': translations,
            'metadata': {
                'topics_count': len(translations['topics']),
                'subtopics_count': len(translations['subtopics']),
                'total_translations': len(translations['topics']) + len(translations['subtopics'])
            }
        }
        
        current_app.logger.info(f"Serving translations for tâche {tache_number}: "
                               f"{response['metadata']['total_translations']} total translations")
        
        return jsonify(response)
        
    except Exception as e:
        current_app.logger.error(f"Error in get_tache_translations: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to fetch translations'
        }), 500

@classified_writing_translations_bp.route('/api/classified-writing/translations/all', methods=['GET'])
def get_all_translations():
    """
    Get translations for all tâches combined
    
    Returns:
        JSON response with all topics and subtopics translations from all tâches
    """
    try:
        all_topics = {}
        all_subtopics = {}
        
        # Combine translations from all tâches
        for tache_num in [1, 2, 3]:
            translations = extract_translations_from_database(tache_num)
            all_topics.update(translations['topics'])
            all_subtopics.update(translations['subtopics'])
        
        response = {
            'translations': {
                'topics': all_topics,
                'subtopics': all_subtopics
            },
            'metadata': {
                'topics_count': len(all_topics),
                'subtopics_count': len(all_subtopics),
                'total_translations': len(all_topics) + len(all_subtopics),
                'taches_included': [1, 2, 3]
            }
        }
        

        
        return jsonify(response)
        
    except Exception as e:
        current_app.logger.error(f"Error in get_all_translations: {e}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'Failed to fetch all translations'
        }), 500
