"""
TCF Canada API Blueprints Package

This package contains all the blueprint modules for the TCF Canada API:
- auth: Authentication and user management
- tests: Test management and scoring
- payments: Stripe integration and membership
- user_features: User highlights and notebooks
- assets: File serving and asset management
- admin: Administrative functions
- utils: Utility endpoints (sitemap, robots.txt)
- translation: Azure Translator API integration for French to English/Chinese translation
"""

# This file makes the blueprints directory a Python package 