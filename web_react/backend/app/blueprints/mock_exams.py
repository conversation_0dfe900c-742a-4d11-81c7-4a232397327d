from flask import Blueprint, jsonify, request, session, current_app
import json
import random
from datetime import datetime, timezone
from . import auth
import time
import hashlib

mock_exams_bp = Blueprint('mock_exams', __name__, url_prefix='/api/mock-exams')

# Use the functions from auth module
login_required = auth.login_required
membership_required = auth.membership_required

# Define difficulty groups and their question ranges
DIFFICULTY_GROUPS = {
    'A1': {'range': (1, 4), 'points': 3, 'count': 4},
    'A2': {'range': (5, 10), 'points': 9, 'count': 6}, 
    'B1': {'range': (11, 19), 'points': 15, 'count': 9},
    'B2': {'range': (20, 29), 'points': 21, 'count': 10},
    'C1': {'range': (30, 35), 'points': 26, 'count': 6},
    'C2': {'range': (36, 39), 'points': 33, 'count': 4}
}

def ensure_mock_exam_tables():
    """Create mock exam table if it doesn't exist"""
    # Safety check for Supabase client
    if not current_app.supabase:
        return False

    try:
        # Check if table exists by trying to query it
        result = current_app.supabase.table('mock_exams').select('id').limit(1).execute()
        return True
    except Exception as e:
        error_msg = str(e).lower()

        # Only log if it's not a simple table not found error
        if not ("relation" in error_msg and "does not exist" in error_msg):
            print(f"Mock exam table check failed: {e}")

        return False

def check_user_premium_status():
    """Helper function to check if current user has premium membership"""
    if 'user_id' not in session:
        return False
    
    try:
        result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
        user = result.data[0] if result.data else None
        
        if not user or user.get('membership_type', 'free') not in ['premium', 'paid']:
            return False
        
        expires = user.get('membership_expires_at')
        if expires is not None:
            if isinstance(expires, str):
                try:
                    expires_dt = datetime.fromisoformat(expires)
                    if expires_dt.tzinfo is None:
                        expires_dt = expires_dt.replace(tzinfo=timezone.utc)
                except Exception:
                    expires_dt = None
            else:
                expires_dt = expires
            
            return expires_dt is None or expires_dt > datetime.now(timezone.utc)
        else:
            return True
            
    except Exception as e:
        print(f"Error checking user membership: {e}")
        return False

@mock_exams_bp.route('/<test_type>', methods=['GET'])
@login_required
@membership_required
def get_mock_exams(test_type):
    """Get all mock exams for a user in a specific test type"""

    # Safety check for Supabase client
    if not current_app.supabase:
        return jsonify({'error': 'Database connection not available', 'code': 'DB_ERROR'}), 503

    # Test Supabase connection health
    try:
        health_check = current_app.supabase.table('users').select('id').limit(1).execute()
    except Exception as health_error:
        if "disconnected" in str(health_error).lower():
            return jsonify({
                'error': 'Database connection lost',
                'code': 'CONNECTION_ERROR',
                'message': 'Please refresh the page and try again'
            }), 503
    
    if test_type not in ['reading', 'listening']:
        return jsonify({'error': 'Invalid test type'}), 400

    # Check if table exists
    if not ensure_mock_exam_tables():
        return jsonify({
            'mock_exams': [],
            'max_allowed': 3,
            'current_count': 0,
            'message': 'Mock exam feature not yet set up'
        })

    try:
        user_id = session['user_id']

        # Get unique mock exams (group by test_identifier) with retry logic
        result = None
        max_retries = 3

        for attempt in range(max_retries):
            try:
                result = current_app.supabase.table('mock_exams').select(
                    'test_identifier, exam_name, created_at'
                ).eq('user_id', user_id).eq('test_type', test_type).order('created_at', desc=True).execute()
                break  # Success, exit retry loop

            except Exception as query_error:
                # Check if it's a connection error
                if "disconnected" in str(query_error).lower() or "connection" in str(query_error).lower():
                    if attempt < max_retries - 1:
                        import time
                        time.sleep(1)  # Wait 1 second before retry
                        continue
                    else:
                        # Return empty result instead of crashing
                        return jsonify({
                            'mock_exams': [],
                            'max_allowed': 3,
                            'current_count': 0,
                            'warning': 'Database connection issues - please try again later'
                        })
                else:
                    # Non-connection error, don't retry
                    raise query_error
        
        # Handle case where user has no mock exams
        if not result.data:
            return jsonify({
                'mock_exams': [],
                'max_allowed': 3,
                'current_count': 0,
                'message': 'No mock exams created yet'
            })

        # Group by test_identifier to get unique exams
        unique_exams = {}
        for record in result.data:
            test_id = record['test_identifier']
            if test_id not in unique_exams:
                unique_exams[test_id] = {
                    'id': test_id,
                    'exam_name': record['exam_name'],
                    'created_at': record['created_at']
                }

        # Safety check - if no unique exams found (shouldn't happen after above check)
        if not unique_exams:
            return jsonify({
                'mock_exams': [],
                'max_allowed': 3,
                'current_count': 0,
                'message': 'No valid mock exams found'
            })

        mock_exams = []
        for exam_id, exam_info in unique_exams.items():
            # Get user progress from test_history table
            mock_test_id = exam_id  # Now using test_identifier directly

            try:
                history_result = current_app.supabase.table('test_history').select('*').eq(
                    'user_id', user_id
                ).eq('test_type', test_type).eq('test_id', mock_test_id).execute()
            except Exception as history_error:
                # Continue with empty history if test_history query fails
                history_result = type('obj', (object,), {'data': []})()
                print(f"Test history query failed for exam {mock_test_id}: {history_error}")
            
            # Extract progress info from test_history
            is_completed = False
            score = None
            max_score = None
            completion_time = None
            
            if history_result.data:
                history = history_result.data[0]
                is_completed = history.get('status') == 'completed'
                score = history.get('score')
                max_score = history.get('max_score')
                
                # Calculate completion time if available
                if history.get('started_at') and history.get('completed_at'):
                    from datetime import datetime
                    try:
                        started = datetime.fromisoformat(history['started_at'].replace('Z', '+00:00'))
                        completed = datetime.fromisoformat(history['completed_at'].replace('Z', '+00:00'))
                        completion_time = int((completed - started).total_seconds())
                    except Exception as time_error:
                        completion_time = None
            
            mock_exams.append({
                'id': exam_info['id'],
                'exam_name': exam_info['exam_name'],
                'created_at': exam_info['created_at'],
                'is_completed': is_completed,
                'score': score,
                'max_score': max_score,
                'completion_time': completion_time
            })

        return jsonify({
            'mock_exams': mock_exams,
            'max_allowed': 3,
            'current_count': len(mock_exams)
        })
        
    except Exception as e:
        print(f"Error in get_mock_exams: {e}")
        return jsonify({'error': 'Failed to get mock exams'}), 500

@mock_exams_bp.route('/<test_type>/create', methods=['POST'])
@login_required
@membership_required
def create_mock_exam(test_type):
    """Create a new mock exam by randomly sampling from difficulty-based tests"""
    # Safety check for Supabase client
    if not current_app.supabase:
        return jsonify({'error': 'Database connection not available', 'code': 'DB_ERROR'}), 503
        
    if test_type not in ['reading', 'listening']:
        return jsonify({'error': 'Invalid test type'}), 400
    
    # Check if table exists
    if not ensure_mock_exam_tables():
        return jsonify({'error': 'Mock exam feature not available. Database table needs to be set up.'}), 503
    
    try:
        user_id = session['user_id']
        
        # Check if user already has 3 mock exams
        existing_exams = current_app.supabase.table('mock_exams').select(
            'test_identifier'
        ).eq('user_id', user_id).eq('test_type', test_type).execute()
        
        # Count unique mock exams (test_identifier)
        unique_exams = set()
        if existing_exams.data:
            for exam in existing_exams.data:
                unique_exams.add(exam['test_identifier'])
        
        if len(unique_exams) >= 3:
            return jsonify({'error': 'Maximum of 3 mock exams allowed. Please delete one to create a new one.'}), 400
        
        # Generate unique test_identifier and exam name that includes user and test type
        # Keep it short to fit in 50 character database limit
        timestamp = int(time.time())
        exam_number = len(unique_exams) + 1
        
        # Create shorter unique identifier using hash of user_id to save space
        # Format: mock_{type}_{user_hash}_{number}_{timestamp_suffix}
        user_hash = hashlib.md5(str(user_id).encode()).hexdigest()[:8]  # First 8 chars of MD5 hash
        timestamp_suffix = str(timestamp)[-6:]  # Last 6 digits of timestamp
        test_identifier = f"mock_{test_type}_{user_hash}_{exam_number}_{timestamp_suffix}"
        exam_name = f"Examen Blanc {test_type.title()} #{exam_number}"

        # Sample questions from group tests for each difficulty level
        selected_questions = []
        current_question_number = 1
        total_sampled = 0
        
        # Map difficulty levels to group IDs (A1=group1, A2=group2, etc.)
        difficulty_to_group = {
            'A1': 1, 'A2': 2, 'B1': 3, 'B2': 4, 'C1': 5, 'C2': 6
        }

        def ensure_field_length(value, max_length=50):
            """Ensure a field doesn't exceed the database column limit"""
            if value is None:
                return None
            str_value = str(value)
            if len(str_value) > max_length:
                return str_value[:max_length]
            return str_value
        
        def ensure_safe_fields(question_data):
            """Ensure all string fields in question data are safe for database insertion"""
            safe_data = {}
            for key, value in question_data.items():
                if isinstance(value, str):
                    # Use more conservative limits for different field types
                    if key in ['user_id', 'original_question_id']:
                        safe_data[key] = ensure_field_length(value, 40)  # UUIDs are 36 chars
                    elif key in ['test_identifier', 'exam_name', 'original_test_id']:
                        safe_data[key] = ensure_field_length(value, 50)
                    elif key in ['test_type', 'test_category', 'difficulty_group']:
                        safe_data[key] = ensure_field_length(value, 20)
                    else:
                        safe_data[key] = ensure_field_length(value, 50)
                else:
                    safe_data[key] = value
            return safe_data
        
        for difficulty, config in DIFFICULTY_GROUPS.items():
            start_range, end_range = config['range']
            points = config['points']
            count_needed = config['count']
            group_id = difficulty_to_group[difficulty]
            
            # Get all group test questions for this specific group
            group_questions = current_app.supabase.table('test_questions').select(
                '*'  # Select all columns to copy
            ).eq('test_type', test_type).eq('test_category', 'difficulty').eq(
                'test_identifier', f'group{group_id}'
            ).execute()

            if len(group_questions.data) == 0:
                # Fallback: try to find questions from any group in the question number range
                fallback_questions = current_app.supabase.table('test_questions').select(
                    '*'
                ).eq('test_type', test_type).eq('test_category', 'difficulty').gte(
                    'question_number', start_range
                ).lte('question_number', end_range).execute()

                if fallback_questions.data:
                    group_questions.data = fallback_questions.data
                else:
                    continue  # Skip this difficulty level if no questions found

            if len(group_questions.data) < count_needed:
                # If not enough questions, use what we have
                available_questions = group_questions.data
            else:
                # Randomly sample the required number
                available_questions = random.sample(group_questions.data, count_needed)
            
            # Add selected questions to our mock exam with sequential numbering
            for question in available_questions:
                # Get the question_data and ensure audio/image paths are preserved
                question_data = question.get('question_data', {})
                free_content = question.get('free_content', {})
                
                # Ensure audio/image paths are in question_data (for listening tests)
                if test_type == 'listening':
                    # Copy audio_path from free_content if missing in question_data
                    if not question_data.get('audio_path') and free_content.get('audio_path'):
                        question_data['audio_path'] = free_content['audio_path']
                    
                    # Copy image_path from free_content if missing in question_data 
                    if not question_data.get('image_path') and free_content.get('image_path'):
                        question_data['image_path'] = free_content['image_path']
                
                # Create mock exam question record (similar to test_questions structure)
                mock_question = {
                    'user_id': user_id,
                    'test_type': test_type,
                    'test_category': 'mock_exam',
                    'test_identifier': test_identifier,
                    'exam_name': exam_name,
                    'question_number': current_question_number,  # Sequential: 1, 2, 3, 4, 5, 6, etc.
                    
                    # Copy content fields from original question
                    'free_content': question.get('free_content', {}),
                    'paid_content': question.get('paid_content', {}),
                    'content_flags': question.get('content_flags', {}),
                    'correct_answer': question.get('correct_answer', {}),
                    'answer_metadata': question.get('answer_metadata', {}),
                    'question_data': question_data,  # Use the updated question_data
                    'metadata': question.get('metadata', {}),
                    'analysis': question.get('analysis', {}),  # 🆕 Copy analysis data
                    
                    # Mock exam specific fields - ensure they fit in database constraints
                    'difficulty_group': difficulty,
                    'points': points,
                    'original_question_id': str(question['id']),
                    'original_test_id': str(question.get('test_identifier', f'group{group_id}')),
                    'original_question_number': int(question.get('question_number', 0)) if question.get('question_number') is not None else 0
                }
                
                # Apply field length safety to all string fields
                mock_question = ensure_safe_fields(mock_question)
                
                selected_questions.append(mock_question)
                current_question_number += 1
                total_sampled += 1
        
        if total_sampled == 0:
            return jsonify({'error': 'No questions available for mock exam creation. Please check if group test questions exist in the database.'}), 500
        
        # Insert all questions as individual records
        try:
            result = current_app.supabase.table('mock_exams').insert(selected_questions).execute()
        except Exception as insert_error:
            print(f"Insert error details: {insert_error}")
            raise insert_error  # Re-raise the original error
        
        return jsonify({
            'mock_exam_id': test_identifier,
            'exam_name': exam_name,
            'total_questions': len(result.data),
            'difficulty_breakdown': {difficulty: len([q for q in selected_questions if q['difficulty_group'] == difficulty]) for difficulty in DIFFICULTY_GROUPS.keys()},
            'message': f'Mock exam created successfully with {len(result.data)} questions'
        })

    except Exception as e:
        print(f"Error creating mock exam: {e}")
        return jsonify({'error': 'Failed to create mock exam'}), 500

@mock_exams_bp.route('/<test_type>/<mock_exam_id>', methods=['DELETE'])
@login_required
@membership_required
def delete_mock_exam(test_type, mock_exam_id):
    """Delete a mock exam (all questions with the same test_identifier) and its test history"""
    try:
        user_id = session['user_id']
        
        # First, delete the test history for this mock exam
        history_result = current_app.supabase.table('test_history').delete().eq(
            'user_id', user_id
        ).eq('test_type', test_type).eq('test_id', mock_exam_id).execute()
        
        if history_result.data:
            print(f"Deleted {len(history_result.data)} test history records for mock exam {mock_exam_id}")
        
        # Then delete all questions for this mock exam
        result = current_app.supabase.table('mock_exams').delete().eq(
            'test_identifier', mock_exam_id
        ).eq('user_id', user_id).eq('test_type', test_type).execute()
        
        if not result.data:
            return jsonify({'error': 'Mock exam not found or access denied'}), 404
        
        print(f"Deleted {len(result.data)} questions for mock exam {mock_exam_id}")
        
        return jsonify({'message': 'Mock exam and test history deleted successfully'})
        
    except Exception as e:
        print(f"Error deleting mock exam: {e}")
        return jsonify({'error': 'Failed to delete mock exam'}), 500

@mock_exams_bp.route('/<test_type>/<mock_exam_id>/data', methods=['GET'])
@login_required
@membership_required
def get_mock_exam_data(test_type, mock_exam_id):
    """Get the questions for a specific mock exam"""
    try:
        user_id = session['user_id']
        
        # Get all questions for this mock exam
        questions_result = current_app.supabase.table('mock_exams').select('*').eq(
            'test_identifier', mock_exam_id
        ).eq('user_id', user_id).eq('test_type', test_type).order('question_number').execute()
        
        if not questions_result.data:
            return jsonify({'error': 'Mock exam not found or access denied'}), 404
        
        # Get exam info from first question
        first_question = questions_result.data[0]
        
        # Get progress from test_history table
        mock_test_id = mock_exam_id  # Use test_identifier directly
        history_result = current_app.supabase.table('test_history').select('*').eq(
            'user_id', user_id
        ).eq('test_type', test_type).eq('test_id', mock_test_id).execute()
        
        # Extract progress info
        is_completed = False
        score = None
        max_score = None
        
        if history_result.data:
            history = history_result.data[0]
            is_completed = history.get('status') == 'completed'
            score = history.get('score')
            max_score = history.get('max_score')
        
        # Format questions for frontend
        questions_data = []
        for q in questions_result.data:
            # Merge free and paid content for premium users
            merged_content = {}
            merged_content.update(q.get('free_content', {}))
            merged_content.update(q.get('paid_content', {}))
            
            questions_data.append({
                'question_number': str(q['question_number']),
                'content': merged_content,
                'question_data': q.get('question_data', {}),
                'metadata': q.get('metadata', {}),
                'difficulty_group': q['difficulty_group'],
                'points': q['points']
            })
        
        return jsonify({
            'exam_info': {
                'id': mock_exam_id,
                'exam_name': first_question['exam_name'],
                'test_type': first_question['test_type'],
                'is_completed': is_completed,
                'score': score,
                'max_score': max_score
            },
            'questions': questions_data,
            'total_questions': len(questions_data)
        })
        
    except Exception as e:
        print(f"Error getting mock exam data: {e}")
        return jsonify({'error': 'Failed to get mock exam data'}), 500
