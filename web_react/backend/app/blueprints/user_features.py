from flask import Blueprint, jsonify, request, current_app, session
from datetime import datetime
from . import auth

user_features_bp = Blueprint('user_features', __name__, url_prefix='/api/user')

# Use the function from auth module
login_required = auth.login_required

@user_features_bp.route('/highlights/save', methods=['POST'])
@login_required
def save_highlight():
    """Save a text highlight to Supabase"""
    data = request.get_json()
    user_id = session['user_id']
    section = data.get('section', 'reading')
    test_id = data.get('test_id', 'test1') 
    question_index = data.get('question_index', 0)
    start = data['start']
    end = data['end']
    text = data.get('text', '')
    color = data.get('color', '#ffeb3b')
    free = data.get('free', False)
    

    
    # Create highlight data
    highlight_data = {
        'start': start,
        'end': end,
        'text': text,
        'color': color,
        'section': section,
        'question_index': question_index,
        'free': free
    }
    
    try:
        # Check if Supabase client exists
        if not current_app.supabase:
            print("ERROR: Supabase client not available")
            return jsonify({'error': 'Database connection not available'}), 500
        
        # Work with the actual table schema: user_id, test_id, highlight_data, text_content
        import json
        json_data = json.dumps(highlight_data)
        
        # Create unique composite test_id that includes free/paid status
        test_type = "free" if free else "paid"
        composite_test_id = f"{test_id}_{section}_q{question_index}_{test_type}"
        
        record_data = {
            'user_id': user_id,
            'test_id': composite_test_id,  # Now includes free/paid differentiation
            'highlight_data': json_data,  # Store as proper JSON string
            'text_content': json_data  # Also store in text_content since it's required
        }
        
        # Try to insert - this will either create new or update existing
        result = current_app.supabase.table('highlights').upsert(record_data).execute()
        

        return jsonify({'status': 'success', 'message': 'Highlight saved successfully'})
        
    except Exception as e:
        print(f"Error saving highlight: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Failed to save highlight: {str(e)}'}), 500

@user_features_bp.route('/highlights', methods=['GET'])
@login_required
def get_highlights():
    """Get highlights for a specific question from Supabase"""
    user_id = session['user_id']
    section = request.args.get('section', 'reading')
    test_id = request.args.get('test_id', 'test1')
    question_index = int(request.args.get('question_index', 0))
    free = request.args.get('free', '0') in ['1', 'true', 'True']
    

    
    try:
        # Check if Supabase client exists
        if not current_app.supabase:
            print("ERROR: Supabase client not available")
            return jsonify({'highlights': []})
        
        # Query using actual table schema with free/paid differentiation
        test_type = "free" if free else "paid"
        composite_test_id = f"{test_id}_{section}_q{question_index}_{test_type}"
        result = current_app.supabase.table('highlights').select('highlight_data').eq('user_id', user_id).eq('test_id', composite_test_id).execute()
        
        highlights = []
        if result.data:
            import json
            for row in result.data:
                try:
                    try:
                        stored_data = json.loads(row['highlight_data'])
                    except:
                        import ast
                        stored_data = ast.literal_eval(row['highlight_data'])
                    if isinstance(stored_data, dict):
                        highlights.append(stored_data)
                    elif isinstance(stored_data, list):
                        highlights.extend(stored_data)
                except Exception as parse_error:
                    print(f"Could not parse stored highlight data: {parse_error}")
            print(f"Successfully parsed {len(highlights)} highlight(s) from {len(result.data)} record(s)")

        return jsonify({'highlights': highlights})
        
    except Exception as e:
        print(f"Error retrieving highlights: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'highlights': []})

@user_features_bp.route('/highlights', methods=['DELETE'])
@login_required
def remove_highlight():
    """Remove a specific highlight from Supabase"""
    data = request.get_json()
    user_id = session['user_id']
    section = data.get('section', 'reading')
    test_id = data.get('test_id', 'test1')
    start = data.get('start')
    end = data.get('end')
    question_index = data.get('question_index', 0)
    
    print(f"Removing highlight for user: {user_id}")
    
    try:
        # Check if Supabase client exists
        if not current_app.supabase:
            print("ERROR: Supabase client not available")
            return jsonify({'error': 'Database connection not available'}), 500
        
        # Delete using actual table schema with free/paid differentiation
        test_type = "free" if data.get('free', False) else "paid"
        composite_test_id = f"{test_id}_{section}_q{question_index}_{test_type}"

        # If start and end are provided, delete only the matching highlight
        if start is not None and end is not None:
            # Fetch all records for this user and test_id
            result = current_app.supabase.table('highlights').select('id, text_content').eq('user_id', user_id).eq('test_id', composite_test_id).execute()
            import json
            deleted = False
            for row in result.data:
                try:
                    content = json.loads(row['text_content']) if isinstance(row['text_content'], str) else row['text_content']
                    if content.get('start') == start and content.get('end') == end:
                        current_app.supabase.table('highlights').delete().eq('id', row['id']).execute()
                        deleted = True
                except Exception as parse_error:
                    print(f"Could not parse text_content: {parse_error}")
            if deleted:
                print(f"Successfully removed highlight for user {user_id} with start={start}, end={end}")
                return jsonify({'status': 'success', 'message': 'Highlight removed successfully'})
            else:
                print(f"No matching highlight found for start={start}, end={end}")
                return jsonify({'status': 'not_found', 'message': 'No matching highlight found'})
        else:
            # Remove all records for this user and test_id
            current_app.supabase.table('highlights').delete().eq('user_id', user_id).eq('test_id', composite_test_id).execute()
            print(f"Successfully removed all highlights for user {user_id} and test_id {composite_test_id}")
            return jsonify({'status': 'success', 'message': 'All highlights removed successfully'})
    except Exception as e:
        print(f"Error removing highlight: {e}")
        import traceback
        current_app.logger.error(f"Failed to remove highlight: {str(e)}")
        return jsonify({'error': f'Failed to remove highlight: {str(e)}'}), 500
            
        # Try to get any existing records to see the structure
        result = current_app.supabase.table('highlights').select('*').limit(5).execute()
        
        # Import uuid for proper UUID generation
        import uuid
        test_uuid = str(uuid.uuid4())
        
        debug_results = {}
        
        # Test 1: Try with required test_id
        try:
            minimal_test = current_app.supabase.table('highlights').insert({
                'user_id': test_uuid,
                'test_id': 'test1'
            }).execute()
            debug_results['user_test_id'] = 'SUCCESS'
            # Clean up immediately
            current_app.supabase.table('highlights').delete().eq('user_id', test_uuid).execute()
        except Exception as e:
            debug_results['user_test_id_error'] = str(e)
        
        # Test 2: Try with basic required fields + section
        try:
            test_uuid2 = str(uuid.uuid4())
            section_test = current_app.supabase.table('highlights').insert({
                'user_id': test_uuid2,
                'test_id': 'test1',
                'section': 'reading'
            }).execute()
            debug_results['with_section'] = 'SUCCESS'
            current_app.supabase.table('highlights').delete().eq('user_id', test_uuid2).execute()
        except Exception as e:
            debug_results['with_section_error'] = str(e)
        
        # Test 3: Try with basic required fields + question_index
        try:
            test_uuid3 = str(uuid.uuid4())
            question_test = current_app.supabase.table('highlights').insert({
                'user_id': test_uuid3,
                'test_id': 'test1',
                'question_index': 0
            }).execute()
            debug_results['with_question_index'] = 'SUCCESS'
            current_app.supabase.table('highlights').delete().eq('user_id', test_uuid3).execute()
        except Exception as e:
            debug_results['with_question_index_error'] = str(e)
        
        # Test 4: Try with highlights data array
        try:
            test_uuid4 = str(uuid.uuid4())
            highlights_test = current_app.supabase.table('highlights').insert({
                'user_id': test_uuid4,
                'test_id': 'test1',
                'highlights': [{'start': 0, 'end': 5, 'text': 'test'}]
            }).execute()
            debug_results['with_highlights'] = 'SUCCESS'
            current_app.supabase.table('highlights').delete().eq('user_id', test_uuid4).execute()
        except Exception as e:
            debug_results['with_highlights_error'] = str(e)
        
        # Test 5: Try the full expected schema without 'free'
        try:
            test_uuid5 = str(uuid.uuid4())
            full_test = current_app.supabase.table('highlights').insert({
                'user_id': test_uuid5,
                'test_id': 'test1',
                'section': 'reading',
                'question_index': 0,
                'highlights': [{'start': 0, 'end': 5, 'text': 'test'}]
            }).execute()
            debug_results['full_without_free'] = 'SUCCESS'
            current_app.supabase.table('highlights').delete().eq('user_id', test_uuid5).execute()
        except Exception as e:
            debug_results['full_without_free_error'] = str(e)
        
        return jsonify({
            'table_data': result.data,
            'count': len(result.data) if result.data else 0,
            'debug_tests': debug_results,
            'message': 'Testing actual table schema with required fields'
        })
        
    except Exception as e:
        return jsonify({'error': f'Debug failed: {str(e)}'}), 500

@user_features_bp.route('/notebook', methods=['POST'])
@login_required
def save_notebook():
    """Save user's unified notebook notes"""
    try:
        data = request.get_json()
        user_id = session['user_id']
        notes = data.get('notes', '').strip()
        test_path = 'global'  # Unified notebook
        
        print(f"Saving notebook for user {user_id}, notes length: {len(notes)}")
        
        # If notes are empty or just contain basic HTML tags, delete the document
        if not notes or notes in ['', '<br>', '<div><br></div>', '<p><br></p>']:
            print("Notes are empty, deleting existing notebook")
            try:
                result = current_app.supabase.table('notebook_notes').delete().eq('user_id', user_id).eq('test_path', test_path).execute()
                return jsonify({'status': 'success', 'action': 'deleted'})
            except Exception as delete_error:
                print(f"Delete failed: {delete_error}")
                return jsonify({'status': 'success', 'action': 'deleted'})  # Return success anyway
        
        # NEW APPROACH: Use upsert which handles both insert and update
        # This bypasses foreign key issues by using PostgreSQL's ON CONFLICT
        try:
            print("Using upsert approach to bypass foreign key constraint issues")
            
            notebook_data = {
                'user_id': user_id,
                'test_path': test_path,
                'notes': notes,
                'updated_at': datetime.utcnow().isoformat(),
                'created_at': datetime.utcnow().isoformat()
            }
            
            # Use upsert (insert with ON CONFLICT update)
            # This should work even if the foreign key constraint is broken
            result = current_app.supabase.table('notebook_notes').upsert(
                notebook_data,
                on_conflict='user_id,test_path'  # Update if this combination exists
            ).execute()
            
            print(f"Upsert successful: {result.data}")
            return jsonify({'status': 'success', 'action': 'saved'})
            
        except Exception as upsert_error:
            print(f"Upsert failed: {upsert_error}")
            
            # If upsert fails, try the manual insert/update approach
            try:
                print("Trying manual insert/update approach")
                
                # Check if record exists first
                existing = current_app.supabase.table('notebook_notes').select('id').eq('user_id', user_id).eq('test_path', test_path).execute()
                
                if existing.data:
                    # Update existing record
                    update_data = {
                        'notes': notes,
                        'updated_at': datetime.utcnow().isoformat()
                    }
                    result = current_app.supabase.table('notebook_notes').update(update_data).eq('id', existing.data[0]['id']).execute()
                    print(f"Manual update successful: {result.data}")
                    return jsonify({'status': 'success', 'action': 'updated'})
                else:
                    # Try insert without foreign key validation
                    # We'll use a raw SQL approach if possible, or just let it fail gracefully
                    insert_data = {
                        'user_id': user_id,
                        'test_path': test_path,
                        'notes': notes,
                        'updated_at': datetime.utcnow().isoformat(),
                        'created_at': datetime.utcnow().isoformat()
                    }
                    
                    # Force insert (this may fail, but we'll handle it)
                    result = current_app.supabase.table('notebook_notes').insert(insert_data).execute()
                    print(f"Manual insert successful: {result.data}")
                    return jsonify({'status': 'success', 'action': 'inserted'})
                    
            except Exception as manual_error:
                print(f"Manual approach also failed: {manual_error}")
                
                # Final fallback - return error to trigger localStorage
                return jsonify({
                    'error': 'Database constraint issue. Notes saved locally.',
                    'code': 'CONSTRAINT_ERROR'
                }), 400
        
    except Exception as e:
        print(f"Error saving notebook: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # For any error, provide a workaround response
        return jsonify({
            'error': 'Database synchronization issue. Your notes have been saved locally.',
            'code': 'USER_ACCOUNT_ERROR'
        }), 400

@user_features_bp.route('/notebook', methods=['GET'])
@login_required
def get_notebook():
    """Get user's unified notebook notes"""
    try:
        user_id = session['user_id']
        test_path = 'global'
        
        print(f"🔍 Getting notebook for user {user_id}, test_path: {test_path}")
        
        # Try direct access first
        result = current_app.supabase.table('notebook_notes').select('*').eq('user_id', user_id).eq('test_path', test_path).execute()
        
        notes = ''
        if result.data and len(result.data) > 0:
            notes = result.data[0].get('notes', '')
            print(f"🔍 Found existing notebook with {len(notes)} characters")
        else:
            print(f"🔍 No existing notebook found for user {user_id}")
        
        print(f"🔍 Returning notes length: {len(notes)}")
        
        return jsonify({'notes': notes})
        
    except Exception as e:
        print(f"Error getting notebook: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # If it's an RLS/permission error, return empty notes so the frontend falls back to localStorage
        if 'permission denied' in str(e).lower() or 'rls' in str(e).lower():
            print("RLS permission issue - returning empty notes for localStorage fallback")
            return jsonify({'notes': ''}), 200
        
        return jsonify({'notes': ''}), 200  # Return empty notes on error instead of failing

@user_features_bp.route('/notebook', methods=['DELETE'])
@login_required
def delete_notebook():
    """Delete the unified notebook"""
    try:
        user_id = session['user_id']
        test_path = 'global'
        
        print(f"Deleting notebook for user {user_id}")
        
        # Try direct delete
        result = current_app.supabase.table('notebook_notes').delete().eq('user_id', user_id).eq('test_path', test_path).execute()
        
        print(f"Delete result: {result}")
        
        # Always return success since the goal is achieved (no notebook exists)
        return jsonify({'status': 'success', 'message': 'Note supprimée avec succès'})
        
    except Exception as e:
        print(f"Error deleting notebook: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # If it's an RLS/permission error, still return success since the goal is to delete
        if 'permission denied' in str(e).lower() or 'rls' in str(e).lower():
            print("RLS permission issue - returning success anyway (goal achieved)")
            return jsonify({'status': 'success', 'message': 'Note supprimée avec succès'})
        
        return jsonify({'error': f'Failed to delete notebook: {str(e)}'}), 500

@user_features_bp.route('/notebooks', methods=['GET'])
@login_required
def get_all_notebooks():
    """Get all notebook notes for the current user with pagination"""
    user_id = session['user_id']
    
    # Get pagination parameters
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))
    
    # Calculate offset for pagination
    offset = (page - 1) * per_page
    
    # Get total count for pagination info
    count_result = current_app.supabase.table('notebook_notes').select('id', count='exact').eq('user_id', user_id).execute()
    total_count = count_result.count if hasattr(count_result, 'count') else len(count_result.data)
    
    # Get notebooks with pagination, sorted by most recent first
    result = current_app.supabase.table('notebook_notes').select('test_path,notes,updated_at').eq('user_id', user_id).order('updated_at', desc=True).range(offset, offset + per_page - 1).execute()
    
    notebooks = result.data
    
    # Calculate pagination info
    total_pages = (total_count + per_page - 1) // per_page
    has_next = page < total_pages
    has_prev = page > 1
    
    return jsonify({
        'notebooks': notebooks,
        'pagination': {
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev
        }
    })

@user_features_bp.route('/notebook/<path:test_path>', methods=['DELETE'])
@login_required
def delete_specific_notebook(test_path):
    """Delete a specific notebook by test_path"""
    try:
        user_id = session['user_id']

        print(f"Deleting notebook for user {user_id}, test_path: {test_path}")

        # Delete the specific notebook
        result = current_app.supabase.table('notebook_notes').delete().eq('user_id', user_id).eq('test_path', test_path).execute()

        print(f"Delete result: {result}")

        # Always return success since the goal is achieved (no notebook exists)
        return jsonify({'status': 'success', 'message': 'Note supprimée avec succès'})

    except Exception as e:
        print(f"Error deleting specific notebook: {e}")
        return jsonify({'status': 'success', 'message': 'Note supprimée avec succès'})  # Return success anyway

@user_features_bp.route('/notebook/save-as', methods=['POST'])
@login_required
def save_global_notebook_as():
    """Save the current global notebook with a custom name and optionally clear the global notebook"""
    try:
        data = request.get_json()
        user_id = session['user_id']
        notebook_name = data.get('name', '').strip()
        clear_global = data.get('clear_global', True)  # Default to clearing global notebook

        if not notebook_name:
            return jsonify({'error': 'Notebook name is required'}), 400

        print(f"Saving global notebook as '{notebook_name}' for user {user_id}, clear_global: {clear_global}")

        # First, get the current global notebook content
        global_result = current_app.supabase.table('notebook_notes').select('*').eq('user_id', user_id).eq('test_path', 'global').execute()

        if not global_result.data or len(global_result.data) == 0:
            return jsonify({'error': 'No global notebook found to save'}), 404

        global_notebook = global_result.data[0]
        global_notes = global_notebook.get('notes', '')

        if not global_notes or global_notes.strip() == '':
            return jsonify({'error': 'Global notebook is empty'}), 400

        # Check if a notebook with this name already exists
        existing_result = current_app.supabase.table('notebook_notes').select('*').eq('user_id', user_id).eq('test_path', notebook_name).execute()

        if existing_result.data and len(existing_result.data) > 0:
            return jsonify({'error': 'A notebook with this name already exists'}), 409

        # Create the new named notebook
        new_notebook_data = {
            'user_id': user_id,
            'test_path': notebook_name,
            'notes': global_notes,
            'updated_at': datetime.utcnow().isoformat(),
            'created_at': datetime.utcnow().isoformat()
        }

        # Insert the new notebook
        insert_result = current_app.supabase.table('notebook_notes').insert(new_notebook_data).execute()

        if not insert_result.data:
            return jsonify({'error': 'Failed to save notebook'}), 500

        # Optionally clear the global notebook
        if clear_global:
            clear_result = current_app.supabase.table('notebook_notes').delete().eq('user_id', user_id).eq('test_path', 'global').execute()
            print(f"Global notebook cleared: {clear_result}")

        print(f"Successfully saved notebook '{notebook_name}' for user {user_id}")

        return jsonify({
            'status': 'success',
            'message': f'Notebook "{notebook_name}" saved successfully',
            'notebook_name': notebook_name,
            'global_cleared': clear_global
        })

    except Exception as e:
        print(f"Error saving global notebook as named notebook: {e}")
        return jsonify({'error': f'Failed to save notebook: {str(e)}'}), 500

@user_features_bp.route('/notebook/<path:test_path>', methods=['PUT'])
@login_required
def update_specific_notebook(test_path):
    """Update the content of a specific notebook by test_path"""
    try:
        data = request.get_json()
        user_id = session['user_id']
        notes = data.get('notes', '').strip()

        if not notes:
            return jsonify({'error': 'Notes content is required'}), 400

        print(f"Updating notebook '{test_path}' for user {user_id}, notes length: {len(notes)}")

        # Check if the notebook exists
        existing_result = current_app.supabase.table('notebook_notes').select('*').eq('user_id', user_id).eq('test_path', test_path).execute()

        if not existing_result.data or len(existing_result.data) == 0:
            return jsonify({'error': 'Notebook not found'}), 404

        # Update the notebook
        update_data = {
            'notes': notes,
            'updated_at': datetime.utcnow().isoformat()
        }

        result = current_app.supabase.table('notebook_notes').update(update_data).eq('user_id', user_id).eq('test_path', test_path).execute()

        if not result.data:
            return jsonify({'error': 'Failed to update notebook'}), 500

        print(f"Successfully updated notebook '{test_path}' for user {user_id}")

        return jsonify({
            'status': 'success',
            'message': f'Notebook "{test_path}" updated successfully',
            'test_path': test_path
        })

    except Exception as e:
        print(f"Error updating specific notebook: {e}")
        return jsonify({'error': f'Failed to update notebook: {str(e)}'}), 500

# Add a utility endpoint to create the highlights table
@user_features_bp.route('/highlights/setup', methods=['POST'])
def setup_highlights_table():
    """Setup the highlights table in Supabase - for development only"""
    try:
        if not current_app.supabase:
            return jsonify({'error': 'Database connection not available'}), 500
            
        # Note: This would typically be done via SQL migration, not API
        # This is just for debugging - real table creation should be done via schema.sql
        return jsonify({'message': 'Table setup should be done via database migration (schema.sql)'})
        
    except Exception as e:
        return jsonify({'error': f'Setup failed: {str(e)}'}), 500
@user_features_bp.route('/fix-account', methods=['POST'])
@login_required
def fix_user_account():
    """Fix missing user account that causes foreign key constraint errors"""
    try:
        user_id = session['user_id']
        
        print(f"Attempting to fix account for user {user_id}")
        
        # Check if user exists
        user_check = current_app.supabase.table('users').select('*').eq('id', user_id).execute()
        
        if user_check.data:
            return jsonify({
                'status': 'success',
                'message': 'User account already exists',
                'user': user_check.data[0]
            })
        
        # User doesn't exist, create it
        username = session.get('username', f'user_{user_id[:8]}')
        
        user_data = {
            'id': user_id,
            'username': username,
            'email': session.get('email', f'{username}@fixeduser.com'),
            'password_hash': 'placeholder_hash',
            'membership_type': 'free',
            'email_verified': True,  # Since they have an active session
            'created_at': datetime.utcnow().isoformat(),
            'last_login': datetime.utcnow().isoformat()
        }
        
        # Handle potential username conflicts
        counter = 1
        original_username = username
        while True:
            try:
                create_result = current_app.supabase.table('users').insert(user_data).execute()
                break
            except Exception as e:
                if 'username' in str(e) and 'already exists' in str(e):
                    user_data['username'] = f"{original_username}_{counter}"
                    counter += 1
                    if counter > 10:  # Prevent infinite loop
                        raise e
                else:
                    raise e
        
        print(f"Successfully created user account: {create_result.data}")
        
        return jsonify({
            'status': 'success',
            'message': 'User account created successfully',
            'user': create_result.data[0] if create_result.data else user_data
        })
        
    except Exception as e:
        current_app.logger.error(f"Error fixing user account: {str(e)}")
        return jsonify({'error': f'Failed to fix account: {str(e)}'}), 500


