"""
Media Blueprint for Supabase Storage with Access Control
Handles private bucket access with signed URLs and proper authentication
"""

from flask import Blueprint, jsonify, session, current_app
from datetime import datetime, timezone, timedelta
import logging

media_bp = Blueprint('media', __name__)

# Configure logging
logger = logging.getLogger(__name__)

def construct_media_path(section: str, test_id: str, filename: str) -> str:
    """
    Construct media path for Supabase Storage

    Args:
        section: e.g., 'listening_free', 'listening'
        test_id: e.g., 'test1'
        filename: e.g., 'Q1.mp3', 'Q1.webp'

    Returns:
        Media path for Supabase Storage
    """
    # Map section to asset directory
    if section.endswith('_free'):
        asset_dir = section.replace('_free', '_asset_free')
    else:
        asset_dir = f"{section}_asset"

    # Construct media directory
    media_dir = f"media_{test_id}"

    # Return full path
    return f"{asset_dir}/{media_dir}/{filename}"

def check_user_membership(user_id: str) -> dict:
    """
    Check user's membership status and permissions

    Args:
        user_id: User ID from session

    Returns:
        Dict with user info and permissions
    """
    try:
        # Use regular client for database operations
        supabase = current_app.supabase
        if not supabase:
            logger.error("Supabase client not available")
            return {'error': 'Database connection not available', 'has_premium': False}

        # Add retry logic for database queries
        max_retries = 3
        for attempt in range(max_retries):
            try:

                # Get user data
                result = supabase.table('users').select('*').eq('id', user_id).execute()

                if not result.data:
                    logger.warning(f"User not found: {user_id}")
                    return {'error': 'User not found', 'has_premium': False}

                user = result.data[0]
                break  # Success, exit retry loop

            except Exception as retry_error:
                if attempt == max_retries - 1:  # Last attempt
                    raise retry_error
                logger.warning(f"Database query attempt {attempt + 1} failed: {retry_error}")
                import time
                time.sleep(0.1 * (attempt + 1))  # Exponential backoff

        # Check if user is active
        if not user.get('is_active', False):
            logger.warning(f"Inactive user attempted access: {user.get('username')}")
            return {'error': 'Account inactive', 'has_premium': False, 'user': user}

        # Check membership type
        membership_type = user.get('membership_type', 'free')
        membership_expires_at = user.get('membership_expires_at')

        # Determine if user has premium access
        has_premium = False
        if membership_type in ['premium', 'lifetime']:
            if membership_type == 'lifetime' or membership_expires_at is None:
                has_premium = True
            else:
                # Check if membership has expired
                try:
                    if isinstance(membership_expires_at, str):
                        expires_dt = datetime.fromisoformat(membership_expires_at)
                        if expires_dt.tzinfo is None:
                            expires_dt = expires_dt.replace(tzinfo=timezone.utc)
                    else:
                        expires_dt = membership_expires_at

                    if expires_dt > datetime.now(timezone.utc):
                        has_premium = True
                    else:
                        logger.info(f"User {user.get('username')} membership expired: {expires_dt}")
                        # Update user to free membership
                        supabase.table('users').update({
                            'membership_type': 'free',
                            'membership_expires_at': None
                        }).eq('id', user_id).execute()

                except Exception as e:
                    logger.error(f"Error parsing membership expiration: {e}")

        return {
            'user': user,
            'has_premium': has_premium,
            'membership_type': membership_type,
            'membership_expires_at': membership_expires_at
        }

    except Exception as e:
        logger.error(f"Error checking user membership: {e}")
        return {'error': 'Database error', 'has_premium': False}

@media_bp.route('/media/<section>/<test_id>/<filename>')
def get_media_url(section, test_id, filename):
    """
    Get signed URL for media files with proper access control

    Args:
        section: Test section (e.g., 'listening_free', 'listening')
        test_id: Test identifier (e.g., 'test1')
        filename: Media filename (e.g., 'Q1.mp3', 'Q1.webp')

    Returns:
        JSON response with signed media URL
    """
    try:
        # Check if user is authenticated
        if 'user_id' not in session:
            logger.warning(f"Unauthenticated access attempt for {section}/{test_id}/{filename}")
            return jsonify({'error': 'Authentication required'}), 401

        user_id = session['user_id']
        logger.info(f"Media request from user {user_id}: {section}/{test_id}/{filename}")

        # Check user membership and permissions
        user_info = check_user_membership(user_id)

        if 'error' in user_info:
            logger.error(f"User check failed for {user_id}: {user_info['error']}")
            return jsonify({'error': user_info['error']}), 403

        # Determine if this is free or premium content
        is_free_content = section.endswith('_free')

        # Access control logic
        if not is_free_content and not user_info['has_premium']:
            logger.warning(f"User {user_info['user'].get('username')} attempted to access premium content without membership")
            return jsonify({
                'error': 'Premium membership required',
                'requires_premium': True,
                'user_membership': user_info['membership_type']
            }), 403

        # Construct media path
        media_path = construct_media_path(section, test_id, filename)
        logger.info(f"Constructed media path: {media_path}")

        # Generate signed URL from Supabase Storage
        try:
            # For private bucket signed URLs, we need service role key
            from supabase import create_client
            supabase_url = current_app.config['SUPABASE_URL']
            service_key = current_app.config.get('SUPABASE_SERVICE_KEY')

            if not service_key:
                logger.error("Service key not available for private bucket access")
                return jsonify({'error': 'Storage configuration error'}), 503

            # Create service client for signed URL generation
            service_client = create_client(supabase_url, service_key)

            # Set expiration time (1 hour for media files)
            expires_in = 3600  # 1 hour in seconds



            # Generate signed URL for private bucket
            signed_url_response = service_client.storage.from_('media-files').create_signed_url(
                media_path,
                expires_in
            )



            if signed_url_response and 'signedURL' in signed_url_response:
                signed_url = signed_url_response['signedURL']

                # Calculate expiration timestamp
                expires_at = datetime.now(timezone.utc) + timedelta(seconds=expires_in)



                return jsonify({
                    'success': True,
                    'media_url': signed_url,
                    'expires_at': expires_at.isoformat(),
                    'expires_in': expires_in,
                    'is_free_content': is_free_content,
                    'user_has_premium': user_info['has_premium']
                })
            else:
                logger.error(f"Failed to generate signed URL for {media_path}: {signed_url_response}")
                return jsonify({'error': 'Media file not found'}), 404

        except Exception as e:
            logger.error(f"Error generating signed URL for {media_path}: {e}")
            return jsonify({'error': 'Failed to generate media URL'}), 500

    except Exception as e:
        logger.error(f"Unexpected error in get_media_url: {e}")
        return jsonify({'error': 'Internal server error'}), 500
