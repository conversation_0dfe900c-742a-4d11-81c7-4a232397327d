from flask import Blueprint, jsonify, current_app
import json
import os

modifications_bp = Blueprint('modifications', __name__, url_prefix='/api/modifications')

@modifications_bp.route('/<section>', methods=['GET'])
def get_modifications(section):
    """Get modification logs for a specific section (reading or listening)"""
    try:
        # Validate section
        if section not in ['reading', 'listening']:
            return jsonify({'error': 'Invalid section. Must be "reading" or "listening"'}), 400
        
        # Construct the path to the modification log file
        # Get the absolute path of the current file, then navigate to the data directory
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # Navigate from backend/app/blueprints to data/scraped/scraped_answer
        data_dir = os.path.join(current_dir, '..', '..', '..', '..', 'data', 'scraped', 'scraped_answer')
        log_file = os.path.join(data_dir, f'modified_answer_{section}_log.json')
        
        # Check if file exists
        if not os.path.exists(log_file):
            # Return empty data structure if no modifications exist
            return jsonify({
                "log_info": {
                    "created_date": "",
                    "purpose": f"Track modified {section} question answers for transparency and user notification",
                    "total_modifications": 0
                },
                "modifications": [],
                "summary": {
                    "tests_affected": [],
                    "questions_modified": 0,
                    "questions_confirmed_unchanged": 0,
                    "most_common_changes": {}
                },
                "website_notification": {
                    "display_message": f"No {section} test answer modifications at this time.",
                    "affected_tests_count": 0,
                    "last_update": ""
                }
            })
        
        # Read and return the modification log
        with open(log_file, 'r', encoding='utf-8') as f:
            log_data = json.load(f)
        
        # Ensure the response has the correct structure for the frontend
        if 'website_notification' not in log_data:
            # Generate website notification data if missing
            log_data['website_notification'] = {
                "display_message": f"{section.title()} test answers have been reviewed and updated for improved accuracy.",
                "affected_tests_count": len(log_data.get('summary', {}).get('tests_affected', [])),
                "last_update": log_data.get('log_info', {}).get('created_date', '')
            }
        
        return jsonify(log_data)
        
    except Exception as e:
        current_app.logger.error(f"Error loading {section} modifications: {e}")
        return jsonify({'error': f'Failed to load {section} modifications'}), 500