from flask import Blueprint, jsonify, request, session, current_app
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
from datetime import datetime, timezone, timedelta
import secrets
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import uuid
import jwt
import random
import string
from app.utils.session_manager import SessionManager

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Basic session check
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401

        user_id = session['user_id']

        # If session_token is missing, this might be an old session before our update
        # Let's be more lenient and just check if user exists
        if 'session_token' not in session:
            current_app.logger.warning(f"User {user_id} has session without session_token (legacy session)")

            # Check if user exists and is active
            try:
                result = current_app.supabase.table('users').select('id, is_active').eq('id', user_id).execute()
                if result.data and result.data[0].get('is_active', True):
                    # Allow legacy session but log it
                    current_app.logger.info(f"Allowing legacy session for user {user_id}")
                    return f(*args, **kwargs)
                else:
                    session.clear()
                    return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401
            except Exception as e:
                current_app.logger.error(f"Error checking user {user_id}: {str(e)}")
                session.clear()
                return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401

        # For sessions with session_token, validate properly
        session_token = session['session_token']

        try:
            is_valid, message = SessionManager.validate_session(user_id, session_token)

            if not is_valid:
                # Log the session invalidation for debugging
                current_app.logger.warning(f"Session invalidated for user {user_id}: {message}")

                # Clear invalid session
                session.clear()

                # Handle both old string format and new structured format
                if isinstance(message, dict):
                    response_message = message.get('message', 'Session invalid')
                    message_data = message.get('data', {})
                else:
                    response_message = message
                    message_data = {}

                return jsonify({
                    'error': 'Session invalid',
                    'message': response_message,
                    'messageData': message_data,
                    'code': 'SESSION_INVALID'
                }), 401

        except Exception as e:
            # If session validation fails due to error, log it but don't block user
            current_app.logger.error(f"Session validation error for user {user_id}: {str(e)}")
            current_app.logger.error(f"Allowing request to proceed due to validation error")
            # Continue with the request instead of blocking

        return f(*args, **kwargs)
    return decorated_function

def membership_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Safety check for Supabase client
        if not current_app.supabase:
            print("❌ Supabase client is None in membership_required")
            return jsonify({'error': 'Database connection not available', 'code': 'DB_ERROR'}), 503
            
        result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
        user = result.data[0] if result.data else None
        
        if not user or user.get('membership_type', 'free') not in ['premium', 'paid']:
            return jsonify({'error': 'Membership required', 'code': 'MEMBERSHIP_REQUIRED'}), 403
        
        expires = user.get('membership_expires_at')
        if expires is not None:
            if isinstance(expires, str):
                try:
                    expires_dt = datetime.fromisoformat(expires)
                except Exception:
                    expires_dt = None
            else:
                expires_dt = expires
            if expires_dt and expires_dt < datetime.now(timezone.utc):
                current_app.supabase.table('users').update({
                    'membership_type': 'free', 
                    'membership_expires_at': None
                }).eq('id', user['id']).execute()
                return jsonify({'error': 'Membership expired', 'code': 'MEMBERSHIP_EXPIRED'}), 403
        return f(*args, **kwargs)
    return decorated_function

def generate_verification_code():
    """Generate a 6-digit verification code"""
    return ''.join(random.choices(string.digits, k=6))

def send_verification_email(email, code, username):
    """Send email verification code using SendGrid API"""
    try:
        import requests
        import json
        
        # Get SendGrid API key from environment
        sendgrid_api_key = current_app.config.get('SENDGRID_API_KEY') or current_app.config.get('MAIL_PASSWORD')
        
        print(f"📧 SendGrid API Key set: {bool(sendgrid_api_key)}")
        print(f"📧 API Key preview: {sendgrid_api_key[:20] if sendgrid_api_key else 'None'}...")
        print(f"DEBUG: SENDGRID_API_KEY from config: {current_app.config.get('SENDGRID_API_KEY')[:20] if current_app.config.get('SENDGRID_API_KEY') else 'None'}...")
        print(f"DEBUG: MAIL_PASSWORD from config: {current_app.config.get('MAIL_PASSWORD')[:20] if current_app.config.get('MAIL_PASSWORD') else 'None'}...")
        
        if not sendgrid_api_key:
            print("❌ No SendGrid API key found in SENDGRID_API_KEY or MAIL_PASSWORD")
            return False
        
        # Create professional HTML email content
        html_content = f"""
        <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Chez-TCFCA - Vérification de votre email</title>
            </head>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
                <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #007bff; font-size: 24px; margin: 0;">Chez-TCFCA</h1>
                        <p style="color: #6c757d; font-size: 14px; margin: 5px 0 0 0;">Plateforme de préparation aux tests</p>
                    </div>
                    
                    <h2 style="color: #333; margin-bottom: 20px; font-size: 20px;">Bienvenue {username} !</h2>
                    
                    <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">
                        Merci de vous être inscrit sur TCF Canada. Pour compléter votre inscription et sécuriser votre compte, 
                        veuillez vérifier votre adresse email.
                    </p>
                    
                    <div style="background-color: #e7f3ff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; text-align: center; margin: 25px 0;">
                        <p style="color: #007bff; font-size: 16px; margin: 0 0 10px 0; font-weight: bold;">Votre code de vérification</p>
                        <div style="font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 8px; font-family: 'Courier New', monospace;">
                            {code}
                        </div>
                        <p style="color: #666; font-size: 14px; margin: 10px 0 0 0;">Entrez ce code sur la page de vérification</p>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 25px 0;">
                        <p style="color: #856404; font-size: 14px; margin: 0;">
                            <strong>Important :</strong> Ce code expirera dans 15 minutes pour votre sécurité.
                        </p>
                    </div>
                    
                    <p style="color: #666; font-size: 14px; margin-bottom: 20px;">
                        Si vous n'avez pas créé de compte sur TCF Canada, vous pouvez ignorer cet email en toute sécurité.
                    </p>
                    
                    <div style="background-color: #e8f5e8; border-radius: 4px; padding: 15px; margin: 25px 0;">
                        <p style="color: #2d5a2d; font-size: 14px; margin: 0;">
                            <strong>Pourquoi vérifier votre email ?</strong><br />
                            • Sécurise votre compte<br />
                            • Permet la récupération de mot de passe<br />
                            • Assure la réception des notifications importantes
                        </p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
                    
                    <div style="text-align: center;">
                        <p style="color: #888; font-size: 12px; margin: 0;">
                            © 2025 Chez-TCFCA - Plateforme de préparation aux tests
                        </p>
                        <p style="color: #888; font-size: 12px; margin: 5px 0 0 0;">
                            Cet email a été envoyé automatiquement, veuillez ne pas répondre.
                        </p>
                    </div>
                </div>
            </body>
        </html>
        """
        
        print(f"📧 Sending verification email to: {email}")
        print(f"🔑 Verification code: {code}")
        
        # Prepare SendGrid API request
        headers = {
            'Authorization': f'Bearer {sendgrid_api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "personalizations": [
                {
                    "to": [{"email": email}],
                    "subject": "Chez-TCFCA - Vérifiez votre adresse email"
                }
            ],
            "from": {"email": "<EMAIL>", "name": "Chez-TCFCA"},
            "content": [
                {
                    "type": "text/html",
                    "value": html_content
                }
            ]
        }
        
        print("🔄 Sending email via SendGrid API...")
        
        # Send email via SendGrid API
        try:
            response = requests.post(
                'https://api.sendgrid.com/v3/mail/send',
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            print(f"📡 SendGrid API response status: {response.status_code}")
            
            if response.status_code == 202:
                print("✅ Email sent successfully via SendGrid!")
                return True
            else:
                print(f"❌ SendGrid API error: {response.status_code}")
                print(f"❌ Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Critical error in send_verification_email: {e}")
        return False

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user - stores in pending_registrations until email verification"""
    data = request.get_json()
    username = data.get('username', '').strip()
    email = data.get('email', '').strip()
    password = data.get('password', '')
    
    if not username or not email or not password:
        return jsonify({'error': 'All fields are required'}), 400
    
    if len(password) < 6:
        return jsonify({'error': 'Password must be at least 6 characters'}), 400
    
    # Clean up expired pending registrations first
    try:
        current_app.supabase.rpc('cleanup_expired_pending_registrations', {}).execute()
    except Exception as e:
        print(f"Warning: Could not clean up expired registrations: {e}")
    
    # Check if username exists in users table
    result = current_app.supabase.table('users').select('id').eq('username', username).execute()
    if result.data:
        return jsonify({'error': 'Username already exists'}), 409
    
    # Check if email exists in users table
    result = current_app.supabase.table('users').select('id').eq('email', email).execute()
    if result.data:
        return jsonify({'error': 'Email already exists'}), 409
    
    # Check if username exists in pending registrations
    result = current_app.supabase.table('pending_registrations').select('*').eq('username', username).execute()
    if result.data:
        existing_pending = result.data[0]
        # Check if it's the same email - if so, allow them to continue
        if existing_pending.get('email') == email:
            # Same username and email - update the existing pending registration with new verification code
            verification_code = generate_verification_code()
            verification_expires = datetime.now(timezone.utc) + timedelta(minutes=15)
            
            # Update the existing pending registration
            current_app.supabase.table('pending_registrations').update({
                'password_hash': generate_password_hash(password),  # Update password in case they changed it
                'verification_code': verification_code,
                'verification_expires': verification_expires.isoformat(),
            }).eq('id', existing_pending['id']).execute()
            
            # Send verification email
            if send_verification_email(email, verification_code, username):
                return jsonify({
                    'message': 'Registration updated. Please check your email for verification code.',
                    'email': email,
                    'requires_verification': True
                }), 201
            else:
                return jsonify({
                    'message': 'Registration updated but email sending failed. Please contact support.',
                    'email': email,
                    'requires_verification': True,
                    'email_error': True
                }), 201
        else:
            # Same username but different email - this is a conflict
            return jsonify({'error': 'Username already exists in pending registrations'}), 409

    # Check if email exists in pending registrations (replace if exists)
    result = current_app.supabase.table('pending_registrations').select('*').eq('email', email).execute()
    if result.data:
        existing_pending = result.data[0]
        # If different username, delete the old pending registration
        if existing_pending.get('username') != username:
            current_app.supabase.table('pending_registrations').delete().eq('email', email).execute()
    
    # Hash the password
    hash_pw = generate_password_hash(password)
    
    # Generate verification code
    verification_code = generate_verification_code()
    verification_expires = datetime.now(timezone.utc) + timedelta(minutes=15)
    
    # Insert into pending_registrations table
    try:
        result = current_app.supabase.table('pending_registrations').insert({
            'username': username,
            'email': email,
            'password_hash': hash_pw,
            'verification_code': verification_code,
            'verification_expires': verification_expires.isoformat(),
            'created_at': datetime.utcnow().isoformat()
        }).execute()
        
        pending_registration = result.data[0] if result.data else None
        if not pending_registration:
            return jsonify({'error': 'Failed to create pending registration'}), 500
        
    except Exception as e:
        print(f"Error creating pending registration: {e}")
        return jsonify({'error': 'Registration failed. Please try again.'}), 500
    
    # Send verification email
    if send_verification_email(email, verification_code, username):
        return jsonify({
            'message': 'Registration initiated. Please check your email for verification code.',
            'email': email,
            'requires_verification': True
        }), 201
    else:
        # If email sending fails, still keep the pending registration
        return jsonify({
            'message': 'Registration initiated but email sending failed. Please contact support.',
            'email': email,
            'requires_verification': True,
            'email_error': True
        }), 201

@auth_bp.route('/login', methods=['POST'])
def login():
    """Login user"""
    data = request.get_json()
    username_or_email = data.get('username', '').strip()
    password = data.get('password', '')
    
    if not username_or_email or not password:
        return jsonify({'error': 'Username/email and password required'}), 400
    
    # Try to find user by username first, then by email
    result = current_app.supabase.table('users').select('*').eq('username', username_or_email).execute()
    user = result.data[0] if result.data else None
    
    # If not found by username, try by email
    if not user:
        result = current_app.supabase.table('users').select('*').eq('email', username_or_email).execute()
    user = result.data[0] if result.data else None
    
    if user and check_password_hash(user['password_hash'], password):
        # Check if email is verified
        if not user.get('email_verified', False):
            return jsonify({
                'error': 'Email not verified',
                'code': 'EMAIL_NOT_VERIFIED',
                'email': user['email'],
                'requires_verification': True
            }), 403
        
        # Get client IP address for tracking
        from ..security import get_client_ip

        client_ip = get_client_ip()

        # Create new session (this invalidates any existing sessions)
        try:
            session_token = SessionManager.create_new_session(user['id'], client_ip)

            # Store session info in Flask session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['session_token'] = session_token

        except Exception as e:
            current_app.logger.error(f"Failed to create session for user {user['id']}: {str(e)}")
            return jsonify({'error': 'Login failed. Please try again.'}), 500
        


        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'first_name': user.get('first_name'),
                'last_name': user.get('last_name'),
                'membership_type': user.get('membership_type', 'free'),
                'membership_expires_at': user.get('membership_expires_at'),
                'email_verified': user.get('email_verified', False),
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'last_login': user.get('last_login'),
                'is_active': user.get('is_active', True)
            }
        })
    
    return jsonify({'error': 'Invalid credentials'}), 401

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """Logout user and invalidate session"""
    user_id = session.get('user_id')

    # Invalidate session in database if user is logged in
    if user_id:
        SessionManager.invalidate_session(user_id)

    # Clear Flask session
    session.clear()
    return jsonify({'message': 'Logged out successfully'})

@auth_bp.route('/profile', methods=['GET'])
@login_required
def get_profile():
    """Get user profile"""
    result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
    user = result.data[0] if result.data else None
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    now = datetime.now(timezone.utc)
    expires = user.get('membership_expires_at')
    if isinstance(expires, str):
        try:
            expires_dt = datetime.fromisoformat(expires)
            if expires_dt.tzinfo is None:
                expires_dt = expires_dt.replace(tzinfo=timezone.utc)
            user['membership_expires_at'] = expires_dt.isoformat()
        except Exception:
            user['membership_expires_at'] = None
    
    return jsonify({
        'user': {
            'id': user['id'],
            'username': user['username'],
            'email': user['email'],
            'first_name': user.get('first_name'),
            'last_name': user.get('last_name'),
            'membership_type': user.get('membership_type', 'free'),
            'membership_expires_at': user.get('membership_expires_at'),
            'email_verified': user.get('email_verified', False),
            'created_at': user.get('created_at'),
            'updated_at': user.get('updated_at'),
            'last_login': user.get('last_login'),
            'is_active': user.get('is_active', True)
        }
    })

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    data = request.get_json()
    current_password = data.get('current_password', '').strip()
    new_password = data.get('new_password', '').strip()
    confirm_password = data.get('confirm_password', '').strip()
    
    if not current_password or not new_password:
        return jsonify({'error': 'All fields are required'}), 400
    
    result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
    user = result.data[0] if result.data else None
    
    if not check_password_hash(user['password_hash'], current_password):
        return jsonify({'error': 'Current password is incorrect'}), 400
    
    if len(new_password) < 6:
        return jsonify({'error': 'New password must be at least 6 characters'}), 400
    
    if new_password != confirm_password:
        return jsonify({'error': 'New passwords do not match'}), 400
    
    hashed_password = generate_password_hash(new_password)
    current_app.supabase.table('users').update({
        'password_hash': hashed_password
    }).eq('id', user['id']).execute()
    
    return jsonify({'message': 'Password changed successfully'})

def send_password_reset_email(email, token):
    """Send password reset email using SendGrid API"""
    try:
        import requests
        import json
        
        # Get SendGrid API key from environment
        sendgrid_api_key = current_app.config.get('SENDGRID_API_KEY') or current_app.config.get('MAIL_PASSWORD')
        
        print(f"📧 SendGrid API Key set: {bool(sendgrid_api_key)}")
        print(f"📧 API Key preview: {sendgrid_api_key[:20] if sendgrid_api_key else 'None'}...")
        print(f"DEBUG: SENDGRID_API_KEY from config: {current_app.config.get('SENDGRID_API_KEY')[:20] if current_app.config.get('SENDGRID_API_KEY') else 'None'}...")
        print(f"DEBUG: MAIL_PASSWORD from config: {current_app.config.get('MAIL_PASSWORD')[:20] if current_app.config.get('MAIL_PASSWORD') else 'None'}...")
        
        if not sendgrid_api_key:
            print("❌ No SendGrid API key found for password reset")
            return False
        
        reset_url = f"{current_app.config.get('FRONTEND_URL')}/reset-password/{token}"
        
        # Create professional HTML email content
        html_content = f"""
        <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Chez-TCFCA - Réinitialisation du mot de passe</title>
            </head>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
                <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #007bff; font-size: 24px; margin: 0;">Chez-TCFCA</h1>
                        <p style="color: #6c757d; font-size: 14px; margin: 5px 0 0 0;">Plateforme de préparation aux tests</p>
                    </div>
                    
                    <h2 style="color: #333; margin-bottom: 20px; font-size: 20px;">Réinitialisation du mot de passe</h2>
                    
                    <p style="color: #555; line-height: 1.6; margin-bottom: 15px;">Bonjour,</p>
                    
                    <p style="color: #555; line-height: 1.6; margin-bottom: 25px;">
                        Vous avez demandé la réinitialisation de votre mot de passe pour votre compte TCF Canada. 
                        Cliquez sur le bouton ci-dessous pour créer un nouveau mot de passe.
                    </p>
                    
                    <div style="text-align: center; margin: 30px 0;">
                        <a href="{reset_url}" 
                           style="background-color: #007bff; color: white; padding: 15px 30px; 
                                  text-decoration: none; border-radius: 5px; display: inline-block;
                                  font-weight: bold; font-size: 16px;">
                            Réinitialiser mon mot de passe
                        </a>
                    </div>
                    
                    <p style="color: #666; font-size: 14px; line-height: 1.6; margin-bottom: 15px;">
                        Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :
                    </p>
                    
                    <p style="color: #007bff; font-size: 14px; word-break: break-all; margin-bottom: 25px;">
                        <a href="{reset_url}" style="color: #007bff;">{reset_url}</a>
                    </p>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 25px 0;">
                        <p style="color: #856404; font-size: 14px; margin: 0;">
                            <strong>Important :</strong> Ce lien expirera dans 1 heure pour votre sécurité.
                        </p>
                    </div>
                    
                    <p style="color: #666; font-size: 14px; margin-bottom: 30px;">
                        Si vous n'avez pas demandé cette réinitialisation, vous pouvez ignorer cet email en toute sécurité.
                    </p>
                    
                    <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
                    
                    <div style="text-align: center;">
                        <p style="color: #888; font-size: 12px; margin: 0;">
                            © 2025 Chez-TCFCA - Plateforme de préparation aux tests
                        </p>
                        <p style="color: #888; font-size: 12px; margin: 5px 0 0 0;">
                            Cet email a été envoyé automatiquement, veuillez ne pas répondre.
                        </p>
                    </div>
                </div>
            </body>
        </html>
        """
        
        print(f"📧 Sending password reset email to: {email}")
        print(f"🔗 Reset URL: {reset_url}")
        
        # Prepare SendGrid API request
        headers = {
            'Authorization': f'Bearer {sendgrid_api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "personalizations": [
                {
                    "to": [{"email": email}],
                    "subject": "Chez-TCFCA - Réinitialisation du mot de passe"
                }
            ],
            "from": {"email": "<EMAIL>", "name": "Chez-TCFCA"},
            "content": [
                {
                    "type": "text/html",
                    "value": html_content
                }
            ]
        }
        
        print("🔄 Sending password reset email via SendGrid API...")
        
        # Send email via SendGrid API
        try:
            response = requests.post(
                'https://api.sendgrid.com/v3/mail/send',
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            print(f"📡 SendGrid API response status: {response.status_code}")
            
            if response.status_code == 202:
                print("✅ Password reset email sent successfully via SendGrid!")
                return True
            else:
                print(f"❌ SendGrid API error: {response.status_code}")
                print(f"❌ Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Critical error in send_password_reset_email: {e}")
        return False

@auth_bp.route('/forgot-password', methods=['POST'])
def forgot_password():
    """Request password reset"""
    data = request.get_json()
    email = data.get('email', '').strip()
    
    if not email:
        return jsonify({'error': 'Email is required'}), 400
    
    result = current_app.supabase.table('users').select('*').eq('email', email).execute()
    user = result.data[0] if result.data else None
    
    if user:
        token = secrets.token_urlsafe(32)
        expires = datetime.now(timezone.utc) + timedelta(hours=1)
        
        current_app.supabase.table('users').update({
            'password_reset_token': token,
            'password_reset_expires': expires.isoformat()
        }).eq('id', user['id']).execute()
        
        if send_password_reset_email(email, token):
            return jsonify({'message': 'Password reset email sent'})
        else:
            return jsonify({'error': 'Failed to send email'}), 500
    
    # Don't reveal if email exists for security
    return jsonify({'message': 'If the email exists, a reset link has been sent'})

@auth_bp.route('/reset-password/<token>', methods=['POST'])
def reset_password(token):
    """Reset password with token"""
    result = current_app.supabase.table('users').select('*').eq('password_reset_token', token).execute()
    
    user = None
    if result.data:
        user = result.data[0]
        expires_str = user.get('password_reset_expires')
        if expires_str:
            try:
                expires = datetime.fromisoformat(expires_str)
                if expires < datetime.now(timezone.utc):
                    user = None  # Token expired
            except Exception:
                user = None
    
    if not user:
        return jsonify({'error': 'Invalid or expired reset token'}), 400
    
    data = request.get_json()
    password = data.get('password', '').strip()
    confirm_password = data.get('confirm_password', '').strip()
    
    if not password:
        return jsonify({'error': 'Password is required'}), 400
    
    if len(password) < 6:
        return jsonify({'error': 'Password must be at least 6 characters'}), 400
    
    if password != confirm_password:
        return jsonify({'error': 'Passwords do not match'}), 400
    
    hashed_password = generate_password_hash(password)
    current_app.supabase.table('users').update({
        'password_hash': hashed_password,
        'password_reset_token': None,
        'password_reset_expires': None
    }).eq('id', user['id']).execute()
    
    return jsonify({'message': 'Password reset successful'})

@auth_bp.route('/session', methods=['GET'])
def get_session():
    """Get current session info and validate session token"""
    if 'user_id' not in session or 'session_token' not in session:
        return jsonify({'authenticated': False})

    # Validate session using the same logic as login_required
    user_id = session['user_id']
    session_token = session['session_token']

    is_valid, message = SessionManager.validate_session(user_id, session_token, update_activity=False)

    if not is_valid:
        # Clear invalid session
        session.clear()

        # Handle both old string format and new structured format
        if isinstance(message, dict):
            response_message = message.get('message', 'Session invalid')
            message_data = message.get('data', {})
        else:
            response_message = message
            message_data = {}

        return jsonify({
            'authenticated': False,
            'error': 'Session invalid',
            'message': response_message,
            'messageData': message_data,
            'code': 'SESSION_INVALID'
        }), 401

    # Session is valid, get user info
    result = current_app.supabase.table('users').select('*').eq('id', user_id).execute()
    user = result.data[0] if result.data else None

    if user:
        return jsonify({
            'authenticated': True,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'membership_type': user.get('membership_type', 'free'),
                'membership_expires_at': user.get('membership_expires_at')
            }
        })

    return jsonify({'authenticated': False})

@auth_bp.route('/debug-session', methods=['GET'])
def debug_session():
    """Debug endpoint to check session state"""
    debug_info = {
        'flask_session_keys': list(session.keys()),
        'has_user_id': 'user_id' in session,
        'has_session_token': 'session_token' in session,
        'user_id': session.get('user_id', 'MISSING'),
        'session_token_preview': session.get('session_token', 'MISSING')[:10] + '...' if session.get('session_token') else 'MISSING',
        'username': session.get('username', 'MISSING')
    }

    # If user is logged in, check database state
    if 'user_id' in session:
        try:
            result = current_app.supabase.table('users').select(
                'current_session_token, session_created_at, last_login'
            ).eq('id', session['user_id']).execute()

            if result.data:
                user_data = result.data[0]
                debug_info['database_session_token_preview'] = user_data.get('current_session_token', 'MISSING')[:10] + '...' if user_data.get('current_session_token') else 'MISSING'
                debug_info['database_session_created'] = user_data.get('session_created_at', 'MISSING')
                debug_info['database_last_login'] = user_data.get('last_login', 'MISSING')

                # Check if tokens match
                flask_token = session.get('session_token')
                db_token = user_data.get('current_session_token')
                debug_info['tokens_match'] = flask_token == db_token if flask_token and db_token else False
            else:
                debug_info['database_error'] = 'User not found in database'

        except Exception as e:
            debug_info['database_error'] = str(e)

    return jsonify(debug_info)

# New Supabase Auth endpoints
@auth_bp.route('/google/callback', methods=['POST'])
def google_callback():
    """Handle Google OAuth callback from Supabase"""
    try:
        data = request.get_json()

        access_token = data.get('access_token')
        refresh_token = data.get('refresh_token')
        user_data = data.get('user')

        if not user_data or not access_token:
            return jsonify({'error': 'Missing user data or tokens'}), 400
        
        # Extract user info from Supabase user object
        supabase_user_id = user_data.get('id')
        email = user_data.get('email')
        user_metadata = user_data.get('user_metadata', {})
        
        print(f"🔍 Extracted data - ID: {supabase_user_id}, Email: {email}")
        
        # Check if user already exists in our users table
        result = current_app.supabase.table('users').select('*').eq('email', email).execute()
        
        if result.data:
            # User exists, update their info and log them in
            user = result.data[0]
            
            # Update with latest Google info
            current_app.supabase.table('users').update({
                'supabase_user_id': supabase_user_id,
                'first_name': user_metadata.get('full_name', '').split(' ')[0] if user_metadata.get('full_name') else None,
                'last_name': ' '.join(user_metadata.get('full_name', '').split(' ')[1:]) if user_metadata.get('full_name') and len(user_metadata.get('full_name', '').split(' ')) > 1 else None,
                'avatar_url': user_metadata.get('avatar_url'),
                'last_login': datetime.utcnow().isoformat(),
                'auth_provider': 'google'
            }).eq('id', user['id']).execute()
            
        else:
            # Create new user
            user_id = str(uuid.uuid4())
            username = email.split('@')[0]  # Generate username from email
            
            # Make sure username is unique
            counter = 1
            original_username = username
            while True:
                result = current_app.supabase.table('users').select('id').eq('username', username).execute()
                if not result.data:
                    break
                username = f"{original_username}{counter}"
                counter += 1
            
            # Generate a placeholder password hash for Google OAuth users
            # They won't use password login, but the column requires a value
            placeholder_password_hash = generate_password_hash(f"google_oauth_{supabase_user_id}")
            
            user_data = {
                'id': user_id,
                'supabase_user_id': supabase_user_id,
                'username': username,
                'email': email,
                'password_hash': placeholder_password_hash,  # Required field
                'first_name': user_metadata.get('full_name', '').split(' ')[0] if user_metadata.get('full_name') else None,
                'last_name': ' '.join(user_metadata.get('full_name', '').split(' ')[1:]) if user_metadata.get('full_name') and len(user_metadata.get('full_name', '').split(' ')) > 1 else None,
                'avatar_url': user_metadata.get('avatar_url'),
                'membership_type': 'free',
                'membership_expires_at': None,
                'email_verified': True,  # Google accounts are pre-verified
                'auth_provider': 'google',
                'created_at': datetime.utcnow().isoformat(),
                'last_login': datetime.utcnow().isoformat()
            }
            
            print(f"🔍 Creating new user with data: {user_data}")
            result = current_app.supabase.table('users').insert(user_data).execute()
            print(f"🔍 User creation result: {result}")
            user = result.data[0] if result.data else user_data
        
        # Create proper session with SessionManager (same as traditional login)
        from ..utils.session_manager import SessionManager
        
        try:
            # Get user's IP address
            user_ip = request.headers.get('X-Forwarded-For', request.remote_addr) or 'unknown'
            
            # Create new session token and invalidate any existing sessions
            session_token = SessionManager.create_new_session(user['id'], user_ip)
            
            # Set Flask session with proper session token
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['session_token'] = session_token
            session['supabase_user_id'] = supabase_user_id
            session['access_token'] = access_token
            session['refresh_token'] = refresh_token
            
            print(f"✅ Google OAuth session created for user {user['id']} with token {session_token[:10]}...")
            
        except Exception as session_error:
            print(f"❌ Failed to create session for Google OAuth user: {session_error}")
            return jsonify({'error': 'Session creation failed'}), 500
        
        return jsonify({
            'message': 'Google authentication successful',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'first_name': user.get('first_name'),
                'last_name': user.get('last_name'),
                'avatar_url': user.get('avatar_url'),
                'membership_type': user.get('membership_type', 'free'),
                'membership_expires_at': user.get('membership_expires_at'),
                'email_verified': user.get('email_verified', True),
                'auth_provider': user.get('auth_provider', 'google'),
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'last_login': user.get('last_login'),
                'is_active': user.get('is_active', True)
            }
        })
        
    except Exception as e:
        print(f"Google auth error: {e}")
        return jsonify({'error': 'Google authentication failed'}), 500

@auth_bp.route('/verify-token', methods=['POST'])
def verify_token():
    """Verify Supabase access token and get user info"""
    try:
        data = request.get_json()
        access_token = data.get('access_token')
        
        if not access_token:
            return jsonify({'error': 'Access token required'}), 400
        
        # Use Supabase client to verify the token
        try:
            # Set the auth token for this request
            current_app.supabase.auth.set_session(access_token, data.get('refresh_token', ''))
            
            # Get user from Supabase
            supabase_user = current_app.supabase.auth.get_user()
            
            if not supabase_user.user:
                return jsonify({'error': 'Invalid token'}), 401
            
            # Find user in our database
            result = current_app.supabase.table('users').select('*').eq('supabase_user_id', supabase_user.user.id).execute()
            
            if not result.data:
                # Try by email as fallback
                result = current_app.supabase.table('users').select('*').eq('email', supabase_user.user.email).execute()
            
            if result.data:
                user = result.data[0]
                
                # Set session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['supabase_user_id'] = supabase_user.user.id
                session['access_token'] = access_token
                
                return jsonify({
                    'authenticated': True,
                    'user': {
                        'id': user['id'],
                        'username': user['username'],
                        'email': user['email'],
                        'first_name': user.get('first_name'),
                        'last_name': user.get('last_name'),
                        'avatar_url': user.get('avatar_url'),
                        'membership_type': user.get('membership_type', 'free'),
                        'membership_expires_at': user.get('membership_expires_at'),
                        'email_verified': user.get('email_verified', True),
                        'auth_provider': user.get('auth_provider'),
                        'created_at': user.get('created_at'),
                        'updated_at': user.get('updated_at'),
                        'last_login': user.get('last_login'),
                        'is_active': user.get('is_active', True)
                    }
                })
            else:
                return jsonify({'error': 'User not found in database'}), 404
                
        except Exception as e:
            print(f"Token verification error: {e}")
            return jsonify({'error': 'Token verification failed'}), 401
            
    except Exception as e:
        print(f"Verify token error: {e}")
        return jsonify({'error': 'Token verification failed'}), 500 

@auth_bp.route('/profile/request-password-reset', methods=['POST'])
@login_required
def request_profile_password_reset():
    """Request password reset from profile (email verification)"""
    try:
        # Get the current user's information
        result = current_app.supabase.table('users').select('*').eq('id', session['user_id']).execute()
        user = result.data[0] if result.data else None
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        # Generate a password reset token
        token = secrets.token_urlsafe(32)
        expires = datetime.now(timezone.utc) + timedelta(hours=1)
        
        # Update user with reset token
        current_app.supabase.table('users').update({
            'password_reset_token': token,
            'password_reset_expires': expires.isoformat()
        }).eq('id', user['id']).execute()
        
        # Send email to user's email address
        if send_password_reset_email(user['email'], token):
            return jsonify({
                'message': 'Un email de réinitialisation a été envoyé à votre adresse email',
                'email': user['email']  # Return email for UI feedback
            })
        else:
            return jsonify({'error': 'Erreur lors de l\'envoi de l\'email'}), 500
            
    except Exception as e:
        print(f"Profile password reset request error: {e}")
        return jsonify({'error': 'Erreur lors de la demande de réinitialisation'}), 500 

@auth_bp.route('/verify-email', methods=['POST'])
def verify_email():
    """Verify email with verification code and create actual user account"""
    data = request.get_json()
    email = data.get('email', '').strip()
    verification_code = data.get('verification_code', '').strip()
    
    if not email or not verification_code:
        return jsonify({'error': 'Email and verification code are required'}), 400
    
    # Clean up expired pending registrations first
    try:
        current_app.supabase.rpc('cleanup_expired_pending_registrations', {}).execute()
    except Exception as e:
        print(f"Warning: Could not clean up expired registrations: {e}")
    
    # Find pending registration by email
    result = current_app.supabase.table('pending_registrations').select('*').eq('email', email).execute()
    pending_registration = result.data[0] if result.data else None
    
    if not pending_registration:
        return jsonify({'error': 'No pending registration found for this email'}), 404
    
    # Check verification code
    if pending_registration.get('verification_code') != verification_code:
        return jsonify({'error': 'Invalid verification code'}), 400
    
    # Check expiration
    verification_expires = pending_registration.get('verification_expires')
    if verification_expires:
        try:
            expires_dt = datetime.fromisoformat(verification_expires)
            if expires_dt < datetime.now(timezone.utc):
                # Delete expired pending registration
                current_app.supabase.table('pending_registrations').delete().eq('id', pending_registration['id']).execute()
                return jsonify({'error': 'Verification code has expired. Please register again.'}), 400
        except Exception:
            return jsonify({'error': 'Invalid verification data'}), 400
    
    # Check if user already exists before creating
    existing_user_by_email = current_app.supabase.table('users').select('id, email_verified').eq('email', pending_registration['email']).execute()
    if existing_user_by_email.data:
        existing_user = existing_user_by_email.data[0]
        if existing_user.get('email_verified', False):
            # User already exists and is verified - delete pending registration and redirect to login
            current_app.supabase.table('pending_registrations').delete().eq('id', pending_registration['id']).execute()
            return jsonify({'error': 'Email already verified. Please try logging in instead.'}), 409
        else:
            # User exists but not verified - update their verification status
            current_app.supabase.table('users').update({
                'email_verified': True,
                'email_verification_code': None,
                'email_verification_expires': None,
                'updated_at': datetime.utcnow().isoformat()
            }).eq('email', pending_registration['email']).execute()
            
            # Delete pending registration
            current_app.supabase.table('pending_registrations').delete().eq('id', pending_registration['id']).execute()
            
            return jsonify({
                'message': 'Email verified successfully',
                'user': {
                    'email': pending_registration['email'],
                    'username': pending_registration['username'],
                    'email_verified': True
                }
            })
    
    # Check if username already exists
    existing_user_by_username = current_app.supabase.table('users').select('id').eq('username', pending_registration['username']).execute()
    if existing_user_by_username.data:
        # Username exists - suggest a different one
        return jsonify({'error': f'Username "{pending_registration["username"]}" already exists. Please register again with a different username.'}), 409

    # Create the actual user account
    user_id = str(uuid.uuid4())
    
    # Get client IP address for registration tracking
    from ..security import get_client_ip
    
    client_ip = get_client_ip()
    
    try:
        user_result = current_app.supabase.table('users').insert({
            'id': user_id,
            'username': pending_registration['username'],
            'email': pending_registration['email'],
            'password_hash': pending_registration['password_hash'],
            'membership_type': 'free',
            'membership_expires_at': None,
            'email_verified': True,  # Already verified
            'email_verification_code': None,
            'email_verification_expires': None,
            'registration_ip': client_ip,  # Track registration IP
            'last_ip': client_ip,          # Also set as last IP
            'last_login': datetime.utcnow().isoformat(),  # Set initial login time
            'created_at': datetime.utcnow().isoformat(),
            'updated_at': datetime.utcnow().isoformat()
        }).execute()
        
        user = user_result.data[0] if user_result.data else None
        if not user:
            return jsonify({'error': 'Failed to create user account'}), 500
        

        
        # Delete the pending registration
        current_app.supabase.table('pending_registrations').delete().eq('id', pending_registration['id']).execute()
        
        return jsonify({
            'message': 'Email verified successfully and account created',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'first_name': user.get('first_name'),
                'last_name': user.get('last_name'),
                'membership_type': user.get('membership_type', 'free'),
                'membership_expires_at': user.get('membership_expires_at'),
                'email_verified': True,
                'created_at': user.get('created_at'),
                'updated_at': user.get('updated_at'),
                'is_active': user.get('is_active', True)
            }
        })
        
    except Exception as e:
        print(f"Error creating user account: {e}")
        # Check if it's a duplicate username/email error
        if '23505' in str(e) or 'duplicate key' in str(e):
            if 'username' in str(e):
                return jsonify({'error': 'Username already exists. Please register again with a different username.'}), 409
            elif 'email' in str(e):
                return jsonify({'error': 'Email already exists. Please try logging in instead.'}), 409
        return jsonify({'error': 'Failed to create account. Please try again.'}), 500

@auth_bp.route('/resend-verification', methods=['POST'])
def resend_verification():
    """Resend email verification code for pending registration"""
    data = request.get_json()
    email = data.get('email', '').strip()
    
    if not email:
        return jsonify({'error': 'Email is required'}), 400
    
    # Clean up expired pending registrations first
    try:
        current_app.supabase.rpc('cleanup_expired_pending_registrations', {}).execute()
    except Exception as e:
        print(f"Warning: Could not clean up expired registrations: {e}")
    
    # Find pending registration by email
    result = current_app.supabase.table('pending_registrations').select('*').eq('email', email).execute()
    pending_registration = result.data[0] if result.data else None
    
    if not pending_registration:
        # Check if user already exists and is verified
        user_result = current_app.supabase.table('users').select('email_verified').eq('email', email).execute()
        if user_result.data and user_result.data[0].get('email_verified', False):
            return jsonify({'error': 'Email already verified. Please try logging in.'}), 400
        else:
            return jsonify({'error': 'No pending registration found for this email. Please register again.'}), 404
    
    # Generate new verification code
    verification_code = generate_verification_code()
    verification_expires = datetime.now(timezone.utc) + timedelta(minutes=15)
    
    # Update pending registration with new verification code
    current_app.supabase.table('pending_registrations').update({
        'verification_code': verification_code,
        'verification_expires': verification_expires.isoformat(),
    }).eq('id', pending_registration['id']).execute()
    
    # Send verification email
    if send_verification_email(email, verification_code, pending_registration['username']):
        return jsonify({'message': 'Verification code sent successfully'})
    else:
        return jsonify({'error': 'Failed to send verification email'}), 500

@auth_bp.route('/dev-verify-user', methods=['POST'])
def dev_verify_user():
    """Development-only endpoint to verify users without email verification"""
    if not current_app.config.get('DEBUG'):
        return jsonify({'error': 'This endpoint is only available in development mode'}), 403
    
    data = request.get_json()
    email = data.get('email')
    
    if not email:
        return jsonify({'error': 'Email required'}), 400
    
    try:
        # Update user to mark as verified
        result = current_app.supabase.table('users').update({
            'email_verified': True
        }).eq('email', email).execute()
        
        if result.data:
            return jsonify({'message': 'User verified successfully for development'})
        else:
            return jsonify({'error': 'User not found'}), 404
            
    except Exception as e:
        return jsonify({'error': f'Failed to verify user: {str(e)}'}), 500 

@auth_bp.route('/check-pending-registration', methods=['POST'])
def check_pending_registration():
    """Check if a user has a pending registration"""
    data = request.get_json()
    email = data.get('email', '').strip()
    username = data.get('username', '').strip()
    
    if not email and not username:
        return jsonify({'error': 'Email or username is required'}), 400
    
    # Clean up expired pending registrations first
    try:
        current_app.supabase.rpc('cleanup_expired_pending_registrations', {}).execute()
    except Exception as e:
        print(f"Warning: Could not clean up expired registrations: {e}")
    
    pending_registration = None
    
    # Check by email first
    if email:
        result = current_app.supabase.table('pending_registrations').select('*').eq('email', email).execute()
        if result.data:
            pending_registration = result.data[0]
    
    # If not found by email, check by username
    if not pending_registration and username:
        result = current_app.supabase.table('pending_registrations').select('*').eq('username', username).execute()
        if result.data:
            pending_registration = result.data[0]
    
    if pending_registration:
        # Check if it's expired
        verification_expires = pending_registration.get('verification_expires')
        if verification_expires:
            try:
                expires_dt = datetime.fromisoformat(verification_expires)
                if expires_dt < datetime.now(timezone.utc):
                    # Delete expired pending registration
                    current_app.supabase.table('pending_registrations').delete().eq('id', pending_registration['id']).execute()
                    return jsonify({'has_pending_registration': False})
            except Exception:
                return jsonify({'has_pending_registration': False})
        
        return jsonify({
            'has_pending_registration': True,
            'email': pending_registration['email'],
            'username': pending_registration['username'],
            'expires_at': pending_registration.get('verification_expires')
        })
    
    return jsonify({'has_pending_registration': False}) 