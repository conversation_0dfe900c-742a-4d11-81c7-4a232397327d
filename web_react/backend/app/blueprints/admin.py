from flask import Blueprint, jsonify, request, current_app
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

admin_bp = Blueprint('admin', __name__, url_prefix='/api/admin')

@admin_bp.route('/contact-support', methods=['POST'])
def contact_support():
    """Handle contact support form submissions"""
    try:
        data = request.get_json()
        email = data.get('email')
        problem = data.get('problem')
        
        if not email or not problem:
            return jsonify({'error': 'Veuillez remplir tous les champs.'}), 400
            
        # Create email content
        subject = 'Support Chez-TCFCA - Demande d\'aide'
        body = f"""Nouvelle demande de support reçue:

Email: {email}

Problème:
{problem}

---
Ce message a été envoyé depuis le formulaire de contact de Chez-TCFCA.
"""
        
        # Try using Flask-Mail if configured
        if hasattr(current_app, 'mail'):
            from flask_mail import Message
            mail = current_app.mail
            msg = Message(
                subject,
                recipients=[current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>')],
                body=body,
                reply_to=email,
                sender=('Chez-TCFCA', current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'))
            )
            mail.send(msg)
        else:
            # Fallback to direct SMTP
            smtp_server = current_app.config.get('MAIL_SERVER', 'smtp.gmail.com')
            smtp_port = current_app.config.get('MAIL_PORT', 587)
            smtp_username = current_app.config.get('MAIL_USERNAME')
            smtp_password = current_app.config.get('MAIL_PASSWORD')
            
            if not smtp_username or not smtp_password:
                return jsonify({'error': 'Configuration email manquante. Veuillez contacter l\'administrateur.'}), 500
            
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"Chez-TCFCA <{current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>')}>"
            msg['Reply-To'] = email
            msg['To'] = current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
            
            msg.attach(MIMEText(body, 'plain'))
            
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(smtp_username, smtp_password)
                server.send_message(msg)
        
        return jsonify({'success': True, 'message': 'Votre message a été envoyé avec succès.'})
        
    except Exception as e:
        print(f'Error sending contact email: {e}')
        return jsonify({'error': 'Erreur lors de l\'envoi du message. Veuillez réessayer.'}), 500 