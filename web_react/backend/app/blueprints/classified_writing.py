"""
Classified Writing Tasks API Blueprint

This module provides REST API endpoints for serving classified and deduplicated
writing tasks from the database. It includes endpoints for:
- Fetching test cards for each tâche
- Browsing topics and subtopics
- Searching tasks
- Getting task details
- Retrieving classification metadata
"""

from flask import Blueprint, jsonify, current_app, request, session
from . import auth
import logging
from datetime import datetime, timezone

# Setup logging
logger = logging.getLogger(__name__)

classified_writing_bp = Blueprint('classified_writing', __name__, url_prefix='/api/classified-writing')

@classified_writing_bp.route('/test', methods=['GET'])
def test_connection():
    """Test endpoint to check if Supabase connection is working"""
    try:
        # Simple test query
        result = current_app.supabase.table('test_questions').select('id').limit(1).execute()
        return jsonify({
            'status': 'success',
            'message': 'Supabase connection working',
            'has_data': len(result.data) > 0 if result.data else False
        })
    except Exception as e:
        logger.error(f"Test connection failed: {e}")
        return jsonify({
            'status': 'error',
            'message': f'Supabase connection failed: {str(e)}'
        }), 500

# Use the functions from auth module
login_required = auth.login_required
membership_required = auth.membership_required

def check_user_premium_status():
    """Helper function to check if current user has premium membership"""
    if 'user_id' not in session:
        return False

    try:
        user_id = session['user_id']
        result = current_app.supabase.table('users').select('membership_type, membership_expires_at').eq('id', user_id).execute()

        if not result.data:
            return False

        user = result.data[0]
        membership_type = user.get('membership_type', 'free')

        # Check if user has premium membership (consistent with other parts of the system)
        if membership_type not in ['premium', 'paid']:
            return False

        # Check expiration
        expires = user.get('membership_expires_at')
        if expires is not None:
            if isinstance(expires, str):
                try:
                    expires_dt = datetime.fromisoformat(expires)
                    if expires_dt.tzinfo is None:
                        expires_dt = expires_dt.replace(tzinfo=timezone.utc)
                except Exception:
                    expires_dt = None
            else:
                expires_dt = expires

            # User has premium if no expiry date or expiry is in future
            return expires_dt is None or expires_dt > datetime.now(timezone.utc)
        else:
            # No expiry date means permanent premium
            return True

    except Exception as e:
        logger.error(f"Error checking premium status: {e}")
        return False

@classified_writing_bp.route('/cards', methods=['GET'])
def get_classified_writing_cards():
    """Get test cards for all three tâches with summary statistics"""
    try:
        logger.info("Starting to load classified writing cards")

        # Check if Supabase is available
        if not hasattr(current_app, 'supabase') or current_app.supabase is None:
            logger.error("Supabase client not available")
            raise Exception("Database connection not available")

        logger.info("Supabase client is available")

        # Check user authentication and premium status
        user_is_premium = False
        if 'user_id' in session:
            logger.info(f"User authenticated: {session['user_id']}")
            user_is_premium = check_user_premium_status()
            logger.info(f"User premium status: {user_is_premium}")
        else:
            logger.info("No user authenticated")

        # Get summary statistics for each tâche
        cards = []

        for tache_number in [1, 2, 3]:
            logger.info(f"Processing tâche {tache_number}")

            # Get all classified writing tasks for this tâche
            try:
                tasks_result = current_app.supabase.table('test_questions').select(
                    'question_data'
                ).eq('test_type', 'writing').eq('test_category', 'classified').eq('test_identifier', f'tache_{tache_number}').execute()
                logger.info(f"Database query successful for tâche {tache_number}, found {len(tasks_result.data) if tasks_result.data else 0} tasks")
            except Exception as db_error:
                logger.error(f"Database query failed for tâche {tache_number}: {db_error}")
                raise db_error

            # Always create a card, even if no data
            if not tasks_result.data:
                logger.info(f"No data found for tâche {tache_number}, creating empty card")
                # Create empty card (no data available yet)
                expected_counts = {
                    1: {'total_tasks': 206, 'unique_tasks': 138},
                    2: {'total_tasks': 206, 'unique_tasks': 101},
                    3: {'total_tasks': 206, 'unique_tasks': 132}
                }
                counts = expected_counts.get(tache_number, {'total_tasks': 206, 'unique_tasks': 138})

                card_data = {
                    'tache_number': tache_number,
                    'title': f'Tâche {tache_number}',
                    'total_tasks': counts['total_tasks'],
                    'unique_tasks': counts['unique_tasks'],
                    'top_topics': []
                }
                cards.append(card_data)
                continue

            # Process tasks to get statistics with correct terminology
            # Use authoritative counts from rule-based classification files
            expected_counts = {
                1: {'total_tasks': 206, 'unique_tasks': 138},
                2: {'total_tasks': 206, 'unique_tasks': 101},
                3: {'total_tasks': 206, 'unique_tasks': 132}
            }

            counts = expected_counts.get(tache_number, {'total_tasks': 206, 'unique_tasks': 138})
            total_tasks = counts['total_tasks']
            unique_tasks = counts['unique_tasks']
            topics = {}

            # Build topics list from available data (for display purposes)
            for task in tasks_result.data:
                question_data = task['question_data']
                classification = question_data.get('classification', {})
                main_topic = classification.get('main_topic', 'unknown')

                # Count topics (only track representative tasks for display)
                if not question_data.get('is_duplicate_group', False):
                    if main_topic not in topics:
                        topics[main_topic] = {
                            'name': main_topic,
                            'total_tasks': 0
                        }
                    topics[main_topic]['total_tasks'] += 1

            # Get top 3 topics (simplified)
            sorted_topics = sorted(topics.values(), key=lambda x: x['total_tasks'], reverse=True)
            top_topics = sorted_topics[:3]

            # Simplified card data
            card_data = {
                'tache_number': tache_number,
                'title': f'Tâche {tache_number}',
                'total_tasks': total_tasks,
                'unique_tasks': unique_tasks,
                'top_topics': top_topics
            }

            cards.append(card_data)

        # Calculate total unique tasks across all tâches
        total_unique_tasks = sum(card['unique_tasks'] for card in cards)

        return jsonify({
            'cards': cards,
            'total_unique_tasks': total_unique_tasks,
            'user_is_premium': user_is_premium,
            'membership_required': True  # Classified writing requires premium membership
        })

    except Exception as e:
        logger.error(f"Error loading classified writing cards: {e}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")

        # Return more detailed error information for debugging
        error_details = {
            'error': f'Failed to load classified writing cards: {str(e)}',
            'error_type': str(type(e).__name__),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # In development, include more details
        if current_app.debug:
            error_details['traceback'] = traceback.format_exc()

        return jsonify(error_details), 500

@classified_writing_bp.route('/tache/<int:tache_number>', methods=['GET'])
def get_tache_overview(tache_number):
    """Get overview of a specific tâche with all topics and subtopics"""
    if tache_number not in [1, 2, 3]:
        return jsonify({'error': 'Invalid tâche number. Must be 1, 2, or 3.'}), 400

    try:
        # Check user authentication and premium status
        user_is_premium = False
        if 'user_id' in session:
            user_is_premium = check_user_premium_status()

        # Require premium membership for classified writing
        if not user_is_premium:
            return jsonify({'error': 'Premium membership required to access classified writing tasks'}), 403

        # Get all tasks for this tâche
        tasks_result = current_app.supabase.table('test_questions').select(
            'question_data'
        ).eq('test_type', 'writing').eq('test_category', 'classified').eq('test_identifier', f'tache_{tache_number}').execute()

        if not tasks_result.data:
            # Return empty structure for tâches without data yet
            logger.info(f"No data found for tâche {tache_number}, returning empty structure")
            return jsonify({
                'tache_number': tache_number,
                'title': f'Tâche {tache_number}',
                'total_tasks': 0,
                'unique_tasks': 0,
                'topics': [],
                'message': f'Tâche {tache_number} data is not available yet'
            }), 200

        # Process tasks to build topic/subtopic structure
        topics_dict = {}
        total_tasks = len(tasks_result.data)
        unique_tasks = 0

        for task in tasks_result.data:
            question_data = task['question_data']
            classification = question_data.get('classification', {})
            main_topic = classification.get('main_topic', 'unknown')
            subtopic = classification.get('subtopic', 'unknown')

            # Count unique tasks
            if not question_data.get('is_duplicate_group', False):
                unique_tasks += 1
            else:
                unique_tasks += question_data.get('duplicate_count', 1)

            # Build topic structure
            if main_topic not in topics_dict:
                # Get topic translations from classification
                topic_translations = classification.get('main_topic_translations', {})

                topics_dict[main_topic] = {
                    'id': main_topic,
                    'topic_name': main_topic,
                    'topic_id': hash(main_topic) % 1000,  # Simple hash for ID
                    'keywords': [],
                    'total_tasks': 0,
                    'unique_tasks': 0,
                    'description': '',
                    'subtopics': {},
                    'translations': topic_translations
                }

            if subtopic not in topics_dict[main_topic]['subtopics']:
                # Get subtopic translations from classification
                subtopic_translations = classification.get('subtopic_translations', {})

                topics_dict[main_topic]['subtopics'][subtopic] = {
                    'id': subtopic,  # Use subtopic name as ID for easier lookup
                    'subtopic_name': subtopic,
                    'subtopic_id': hash(subtopic) % 1000,
                    'keywords': [],
                    'task_count': 0,
                    'unique_task_count': 0,
                    'template_similarity': None,
                    'description': '',
                    'task_entries': [],  # Add task entries for direct access
                    'translations': subtopic_translations
                }

            # Add task entry to subtopic
            task_entry = {
                'representative_id': question_data.get('representative_id'),
                'task_content': question_data.get('task_content'),
                'clean_content': question_data.get('clean_content'),
                'is_duplicate_group': question_data.get('is_duplicate_group', False),
                'duplicate_count': question_data.get('duplicate_count', 1),
                'task_ids': question_data.get('task_ids', []),
                'month_years': question_data.get('month_years', []),
                'combination_numbers': question_data.get('combination_numbers', []),
                'metadata': question_data.get('metadata', {})
            }
            topics_dict[main_topic]['subtopics'][subtopic]['task_entries'].append(task_entry)

            # Update counts
            topics_dict[main_topic]['total_tasks'] += 1
            topics_dict[main_topic]['subtopics'][subtopic]['task_count'] += 1

            if not question_data.get('is_duplicate_group', False):
                topics_dict[main_topic]['unique_tasks'] += 1
                topics_dict[main_topic]['subtopics'][subtopic]['unique_task_count'] += 1
            else:
                dup_count = question_data.get('duplicate_count', 1)
                topics_dict[main_topic]['unique_tasks'] += dup_count
                topics_dict[main_topic]['subtopics'][subtopic]['unique_task_count'] += dup_count

        # Keep topics as dictionary for easier frontend access
        # Convert subtopics to dictionaries as well
        for topic_name, topic_data in topics_dict.items():
            # Convert subtopics dict values to dict for easier access
            subtopics_dict = {}
            for subtopic_name, subtopic_data in topic_data['subtopics'].items():
                subtopics_dict[subtopic_name] = subtopic_data
            topic_data['subtopics'] = subtopics_dict

        metadata = {
            'tache_number': tache_number,
            'total_tasks': total_tasks,
            'unique_tasks': unique_tasks,
            'n_main_topics': len(topics_dict),
            'method': 'checkpoint_based',
            'checkpoint_name': 'human_reviewed_v1'
        }

        return jsonify({
            'tache_number': tache_number,
            'metadata': metadata,
            'topics': topics_dict,  # Return as dictionary
            'user_is_premium': user_is_premium
        })

    except Exception as e:
        logger.error(f"Error loading tâche {tache_number} overview: {e}")
        return jsonify({'error': f'Failed to load tâche {tache_number} overview: {str(e)}'}), 500

@classified_writing_bp.route('/tache/<int:tache_number>/topics/<topic_name>', methods=['GET'])
def get_topic_details(tache_number, topic_name):
    """Get detailed information about a specific topic including all subtopics and task counts"""
    if tache_number not in [1, 2, 3]:
        return jsonify({'error': 'Invalid tâche number. Must be 1, 2, or 3.'}), 400

    # URL decode the topic_name to handle special characters
    from urllib.parse import unquote
    topic_name = unquote(topic_name)
    
    try:
        # Check user authentication
        user_is_premium = False
        if 'user_id' in session:
            user_is_premium = check_user_premium_status()

        # Require premium membership for classified writing
        if not user_is_premium:
            return jsonify({'error': 'Premium membership required to access classified writing tasks'}), 403

        # Get topic information
        topic_result = current_app.supabase.table('classified_writing_main_topics').select(
            'id, topic_name, topic_id, keywords, total_tasks, unique_tasks, description'
        ).eq('tache_number', tache_number).eq('topic_name', topic_name).execute()

        # Get topic translations from test_questions
        topic_translations = {}
        translations_result = current_app.supabase.table('test_questions').select(
            'question_data'
        ).eq('test_type', 'writing').eq('test_category', 'classified').eq(
            'test_identifier', f'tache_{tache_number}'
        ).limit(1).execute()

        if translations_result.data:
            classification = translations_result.data[0].get('question_data', {}).get('classification', {})
            if classification.get('main_topic') == topic_name:
                topic_translations = classification.get('main_topic_translations', {})
        
        if not topic_result.data:
            return jsonify({'error': f'Topic "{topic_name}" not found in Tâche {tache_number}'}), 404
        
        topic = topic_result.data[0]
        
        # Get subtopics with task entries
        subtopics_result = current_app.supabase.table('classified_writing_subtopics').select(
            'id, subtopic_name, subtopic_id, keywords, task_count, unique_task_count, template_similarity, description'
        ).eq('main_topic_id', topic['id']).order('task_count', desc=True).execute()
        
        subtopics = []
        for subtopic in subtopics_result.data:
            # Get sample task entries for preview (limit to 3)
            task_entries_result = current_app.supabase.table('classified_writing_task_entries').select(
                'representative_id, task_content, is_duplicate_group, duplicate_count, task_ids, month_years'
            ).eq('subtopic_id', subtopic['id']).limit(3).execute()

            # Get subtopic translations from test_questions
            subtopic_translations = {}
            subtopic_translations_result = current_app.supabase.table('test_questions').select(
                'question_data'
            ).eq('test_type', 'writing').eq('test_category', 'classified').eq(
                'test_identifier', f'tache_{tache_number}'
            ).limit(10).execute()  # Get more records to find matching subtopic

            if subtopic_translations_result.data:
                for record in subtopic_translations_result.data:
                    classification = record.get('question_data', {}).get('classification', {})
                    if (classification.get('main_topic') == topic_name and
                        classification.get('subtopic') == subtopic['subtopic_name']):
                        subtopic_translations = classification.get('subtopic_translations', {})
                        break

            subtopic_data = {
                'id': subtopic['id'],
                'subtopic_name': subtopic['subtopic_name'],
                'subtopic_id': subtopic['subtopic_id'],
                'keywords': subtopic['keywords'],
                'task_count': subtopic['task_count'],
                'unique_task_count': subtopic['unique_task_count'],
                'template_similarity': subtopic['template_similarity'],
                'description': subtopic['description'],
                'sample_tasks': task_entries_result.data if task_entries_result.data else [],
                'translations': subtopic_translations
            }

            subtopics.append(subtopic_data)
        
        return jsonify({
            'tache_number': tache_number,
            'topic': {
                'id': topic['id'],
                'topic_name': topic['topic_name'],
                'topic_id': topic['topic_id'],
                'keywords': topic['keywords'],
                'total_tasks': topic['total_tasks'],
                'unique_tasks': topic['unique_tasks'],
                'description': topic['description'],
                'subtopics': subtopics,
                'translations': topic_translations
            },
            'user_is_premium': user_is_premium
        })
        
    except Exception as e:
        logger.error(f"Error loading topic {topic_name} details: {e}")
        return jsonify({'error': f'Failed to load topic details: {str(e)}'}), 500

@classified_writing_bp.route('/subtopic/<subtopic_id>/tasks', methods=['GET'])
def get_subtopic_tasks(subtopic_id):
    """Get all task entries for a specific subtopic with pagination"""
    try:
        # URL decode the subtopic_id to handle special characters like forward slashes
        from urllib.parse import unquote
        original_subtopic_id = subtopic_id
        subtopic_id = unquote(subtopic_id)

        # Check user authentication and premium status
        user_is_premium = False
        if 'user_id' in session:
            user_is_premium = check_user_premium_status()

        # Require premium membership for classified writing
        if not user_is_premium:
            return jsonify({'error': 'Premium membership required to access classified writing tasks'}), 403

        # Get pagination parameters
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        offset = (page - 1) * limit

        # Get all tasks and filter by subtopic
        tasks_result = current_app.supabase.table('test_questions').select(
            'question_number, question_data'
        ).eq('test_type', 'writing').eq('test_category', 'classified').execute()

        if not tasks_result.data:
            return jsonify({'error': 'No tasks found'}), 404

        # Filter tasks by subtopic and extract task data
        subtopic_tasks = []
        main_topic_name = None
        tache_number = None

        for task in tasks_result.data:
            question_data = task['question_data']
            classification = question_data.get('classification', {})

            if classification.get('subtopic') == subtopic_id:
                subtopic_tasks.append({
                    'representative_id': question_data.get('representative_id'),
                    'task_content': question_data.get('task_content'),
                    'clean_content': question_data.get('clean_content'),
                    'is_duplicate_group': question_data.get('is_duplicate_group', False),
                    'duplicate_count': question_data.get('duplicate_count', 1),
                    'task_ids': question_data.get('task_ids', []),
                    'month_years': question_data.get('month_years', []),
                    'combination_numbers': question_data.get('combination_numbers', []),
                    'metadata': question_data.get('metadata', {})
                })

                # Get topic and tache info from first task
                if main_topic_name is None:
                    main_topic_name = classification.get('main_topic')
                    tache_number = classification.get('tache_number')

        if not subtopic_tasks:
            return jsonify({'error': 'Subtopic not found'}), 404

        # Apply pagination
        total_tasks = len(subtopic_tasks)
        paginated_tasks = subtopic_tasks[offset:offset + limit]

        # Calculate pagination info
        total_pages = (total_tasks + limit - 1) // limit

        return jsonify({
            'subtopic': {
                'id': subtopic_id,
                'subtopic_name': subtopic_id.replace('_', ' '),
                'task_count': total_tasks,
                'unique_task_count': len([t for t in subtopic_tasks if not t['is_duplicate_group']]),
                'main_topic_name': main_topic_name or '',
                'tache_number': tache_number or 0
            },
            'main_topic': main_topic_name or '',
            'tache_number': tache_number or 0,
            'tasks': paginated_tasks,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total_tasks,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            }
        })

    except Exception as e:
        logger.error(f"Error loading subtopic tasks: {e}")
        return jsonify({'error': f'Failed to load subtopic tasks: {str(e)}'}), 500

@classified_writing_bp.route('/task/<representative_id>', methods=['GET'])
def get_task_details(representative_id):
    """Get detailed information about a specific task entry"""
    try:
        # Check user authentication
        user_is_premium = False
        if 'user_id' in session:
            user_is_premium = check_user_premium_status()

        # Get task entry
        task_result = current_app.supabase.table('classified_writing_task_entries').select(
            'representative_id, task_content, clean_content, is_duplicate_group, duplicate_count, '
            'task_ids, month_years, combination_numbers, metadata, subtopic_id'
        ).eq('representative_id', representative_id).execute()

        if not task_result.data:
            return jsonify({'error': 'Task not found'}), 404

        task = task_result.data[0]

        # Get subtopic and topic information
        subtopic_result = current_app.supabase.table('classified_writing_subtopics').select(
            'subtopic_name, main_topic_id'
        ).eq('id', task['subtopic_id']).execute()

        subtopic = subtopic_result.data[0] if subtopic_result.data else {}

        main_topic_result = current_app.supabase.table('classified_writing_main_topics').select(
            'topic_name, tache_number'
        ).eq('id', subtopic.get('main_topic_id')).execute()

        main_topic = main_topic_result.data[0] if main_topic_result.data else {}

        # Enhance task data with classification context
        enhanced_task = {
            **task,
            'classification_context': {
                'tache_number': main_topic.get('tache_number', 0),
                'main_topic_name': main_topic.get('topic_name', ''),
                'subtopic_name': subtopic.get('subtopic_name', '')
            }
        }

        return jsonify({
            'task': enhanced_task,
            'user_is_premium': user_is_premium
        })

    except Exception as e:
        logger.error(f"Error loading task details: {e}")
        return jsonify({'error': f'Failed to load task details: {str(e)}'}), 500

@classified_writing_bp.route('/search', methods=['GET'])
def search_tasks():
    """Search tasks across all tâches with filters"""
    try:
        # Check user authentication
        user_is_premium = False
        if 'user_id' in session:
            user_is_premium = check_user_premium_status()

        # Get search parameters
        query = request.args.get('q', '').strip()
        tache_number = request.args.get('tache', type=int)
        topic_name = request.args.get('topic', '').strip()
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        offset = (page - 1) * limit

        if not query and not topic_name:
            return jsonify({'error': 'Search query or topic filter required'}), 400

        # Build search query
        search_query = current_app.supabase.table('classified_writing_task_entries').select(
            'representative_id, task_content, clean_content, is_duplicate_group, duplicate_count, '
            'task_ids, month_years, combination_numbers, subtopic_id'
        )

        # Apply text search if query provided
        if query:
            # Use PostgreSQL full-text search
            search_query = search_query.text_search('task_content', query, config='french')

        # Apply filters
        if tache_number or topic_name:
            # Need to join with subtopics and main topics for filtering
            # This is a simplified approach - in production, you might want to use a view or more complex query
            pass  # For now, we'll filter in Python after getting results

        # Execute search with pagination
        search_result = search_query.range(offset, offset + limit - 1).execute()

        tasks = search_result.data if search_result.data else []

        # Enhance results with classification context
        enhanced_tasks = []
        for task in tasks:
            # Get classification context
            subtopic_result = current_app.supabase.table('classified_writing_subtopics').select(
                'subtopic_name, main_topic_id'
            ).eq('id', task['subtopic_id']).execute()

            if subtopic_result.data:
                subtopic = subtopic_result.data[0]

                main_topic_result = current_app.supabase.table('classified_writing_main_topics').select(
                    'topic_name, tache_number'
                ).eq('id', subtopic['main_topic_id']).execute()

                if main_topic_result.data:
                    main_topic = main_topic_result.data[0]

                    # Apply filters if specified
                    if tache_number and main_topic['tache_number'] != tache_number:
                        continue
                    if topic_name and main_topic['topic_name'] != topic_name:
                        continue

                    enhanced_task = {
                        **task,
                        'classification_context': {
                            'tache_number': main_topic['tache_number'],
                            'main_topic_name': main_topic['topic_name'],
                            'subtopic_name': subtopic['subtopic_name']
                        }
                    }
                    enhanced_tasks.append(enhanced_task)

        # Get total count (simplified - in production, use a proper count query)
        total_count = len(enhanced_tasks)
        total_pages = (total_count + limit - 1) // limit

        return jsonify({
            'query': query,
            'filters': {
                'tache_number': tache_number,
                'topic_name': topic_name
            },
            'tasks': enhanced_tasks,
            'pagination': {
                'page': page,
                'limit': limit,
                'total': total_count,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            },
            'user_is_premium': user_is_premium
        })

    except Exception as e:
        logger.error(f"Error searching tasks: {e}")
        return jsonify({'error': f'Failed to search tasks: {str(e)}'}), 500
