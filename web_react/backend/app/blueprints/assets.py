from flask import Blueprint, send_from_directory, current_app, jsonify
from pathlib import Path
import re
import os

assets_bp = Blueprint('assets', __name__, url_prefix='/api/assets')

@assets_bp.route('/<section>/<test_id>/<filename>')
def serve_test_asset(section, test_id, filename):
    """Serve test assets (images, audio files)"""
    
    asset_dirs = {
        'listening': 'listening_asset',
        'listening_free': 'listening_asset_free',
    }
    
    asset_dir = asset_dirs.get(section)
    
    if not asset_dir:
        return jsonify({'error': 'Section not found'}), 404
    
    try:
        data_dir = current_app.config['DATA_DIR']
        
        # Check if this is a mock exam - updated to use correct prefix
        if test_id.startswith('mock_'):
            print(f"📁 Detected mock exam: {test_id}")
            
            # Extract question number from filename (e.g., Q1.mp3 -> 1)
            question_match = re.match(r'Q(\d+)\.(mp3|webp|jpg|png)', filename)
            if not question_match:
                print(f"❌ Invalid filename format for mock exam: {filename}")
                return jsonify({'error': f'Invalid filename format: {filename}'}), 404
            
            question_num = int(question_match.group(1))
            file_extension = question_match.group(2)
            print(f"📁 Mock exam question number: {question_num}")
            
            # Get asset path from mock_exams database
            try:
                # Get the specific mock exam question
                mock_question_result = current_app.supabase.table('mock_exams').select('*').eq(
                    'test_identifier', test_id
                ).eq('test_type', section).eq('question_number', question_num).execute()
                
                if not mock_question_result.data:
                    print(f"❌ Mock exam question {question_num} not found in {test_id}")
                    return jsonify({'error': f'Mock exam question not found: {question_num}'}), 404
                
                # Get the question data for this specific question
                question_data = mock_question_result.data[0]
                
                # First check question_data field for asset paths (preferred)
                question_data_field = question_data.get('question_data', {})
                
                # Use the correct field names (audio_path, image_path)
                if file_extension in ['mp3', 'wav']:
                    # Audio file - check question_data first, then merged content
                    asset_path_from_db = question_data_field.get('audio_path', '')
                    if not asset_path_from_db:
                        # Fallback to merged content
                        merged_content = {}
                        merged_content.update(question_data.get('free_content', {}))
                        merged_content.update(question_data.get('paid_content', {}))
                        asset_path_from_db = merged_content.get('audio_path', '')
                    print(f"📁 Using audio path from mock exam: {asset_path_from_db}")
                else:
                    # Image file - check question_data first, then merged content
                    asset_path_from_db = question_data_field.get('image_path', '')
                    if not asset_path_from_db:
                        # Fallback to merged content
                        merged_content = {}
                        merged_content.update(question_data.get('free_content', {}))
                        merged_content.update(question_data.get('paid_content', {}))
                        asset_path_from_db = merged_content.get('image_path', '')
                    print(f"📁 Using image path from mock exam: {asset_path_from_db}")
                
                if not asset_path_from_db:
                    print(f"❌ No asset path found for mock exam {filename}")
                    return jsonify({'error': f'No asset path found for {filename}'}), 404
                
                # Parse asset path (e.g., "listening_asset/media_test12/Q1.mp3")
                path_parts = asset_path_from_db.split('/')
                if len(path_parts) >= 3:
                    source_asset_dir = path_parts[0]  # e.g., "listening_asset"
                    source_media_dir = path_parts[1]  # e.g., "media_test12"  
                    source_filename = path_parts[2]   # e.g., "Q1.mp3"
                    
                    # Build full path to asset
                    asset_path = data_dir / 'assets' / source_asset_dir / source_media_dir
                    full_file_path = asset_path / source_filename
                    
                    print(f"📁 Resolved mock exam asset path: {asset_path}")
                    print(f"📁 Full file path: {full_file_path}")
                else:
                    print(f"❌ Invalid asset path format: {asset_path_from_db}")
                    return jsonify({'error': f'Invalid asset path format: {asset_path_from_db}'}), 404
                
            except Exception as e:
                print(f"❌ Error resolving mock exam asset: {e}")
                import traceback
                traceback.print_exc()
                return jsonify({'error': f'Failed to resolve mock exam asset: {str(e)}'}), 500
                
        else:
            # Regular test - Extract the numeric part from test identifier
            # e.g., "test1" -> "1", "test12" -> "12", "media_test28" -> "28"
            test_num_match = re.match(r'(?:test|media_test)(\d+)', test_id)
            if test_num_match:
                test_num = test_num_match.group(1)
                asset_path = data_dir / 'assets' / asset_dir / f'media_test{test_num}'
            else:
                # Fallback for non-standard test IDs
                asset_path = data_dir / 'assets' / asset_dir / f'media_{test_id}'
            full_file_path = asset_path / filename
        
        print(f"📁 DATA_DIR: {data_dir}")
        print(f"📁 Asset path: {asset_path}")
        print(f"📁 Full file path: {full_file_path}")
        print(f"📁 Asset path exists: {asset_path.exists()}")
        print(f"📁 File exists: {full_file_path.exists()}")
        
        if not asset_path.exists():
            print(f"❌ Asset directory doesn't exist: {asset_path}")
            return jsonify({'error': f'Asset directory not found: {asset_path}'}), 404
        
        if not full_file_path.exists():
            print(f"❌ Asset file doesn't exist: {full_file_path}")
            # List available files in directory for debugging
            available_files = list(asset_path.iterdir()) if asset_path.exists() else []
            print(f"📁 Available files: {[f.name for f in available_files]}")
            return jsonify({'error': f'Asset file not found: {filename}'}), 404
        
        print(f"✅ Serving file: {full_file_path}")
        return send_from_directory(str(asset_path), full_file_path.name)
        
    except Exception as e:
        print(f"❌ Exception in serve_test_asset: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': f'Asset not found: {str(e)}'}), 404

@assets_bp.route('/answers/listening')
def serve_listening_answers():
    """Serve the correct answers for listening tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'listening_correct_answer.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404

@assets_bp.route('/answers/reading')
def serve_reading_answers():
    """Serve the correct answers for reading tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'reading_correct_answer.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404

@assets_bp.route('/answers/listening-groups')
def serve_listening_group_answers():
    """Serve the correct answers for listening group tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'listening_correct_answer_by_group.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404

@assets_bp.route('/answers/reading-groups')
def serve_reading_group_answers():
    """Serve the correct answers for reading group tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'reading_correct_answer_by_group.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404

@assets_bp.route('/answers/listening-free')
def serve_listening_free_answers():
    """Serve the correct answers for free listening tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'listening_correct_answer_free.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404

@assets_bp.route('/answers/reading-free')
def serve_reading_free_answers():
    """Serve the correct answers for free reading tests"""
    try:
        answer_path = current_app.config['DATA_DIR'] / 'scraped' / 'scraped_answer'
        return send_from_directory(str(answer_path), 'reading_correct_answer_free.json')
    except Exception as e:
        return jsonify({'error': f'Answer file not found: {str(e)}'}), 404 

@assets_bp.route('/<path:filename>')
def serve_asset(filename):
    """Serve static assets from the assets directory"""
    try:
        # Construct the full path to the assets directory
        assets_dir = current_app.config.get('ASSETS_DIR')
        if not assets_dir:
            return jsonify({'error': 'Assets directory not configured'}), 500
        
        file_path = os.path.join(assets_dir, filename)
        
        # Security check: ensure the file is within the assets directory
        if not os.path.commonpath([assets_dir, file_path]) == assets_dir:
            return jsonify({'error': 'Invalid file path'}), 400
            
        if not os.path.exists(file_path):
            return jsonify({'error': 'File not found'}), 404
            
        return send_from_directory(assets_dir, filename)
        
    except Exception as e:
        print(f"Error serving asset {filename}: {e}")
        return jsonify({'error': 'Failed to serve asset'}), 500 