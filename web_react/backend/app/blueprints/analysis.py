from flask import Blueprint, request, jsonify, g, current_app, session
from . import auth
import logging

logger = logging.getLogger(__name__)

analysis_bp = Blueprint('analysis', __name__, url_prefix='/api/analysis')

# Use the functions from auth module
login_required = auth.login_required

@analysis_bp.route('/question/<string:section>/<string:test_id>/<int:question_number>', methods=['GET'])
@login_required
def get_question_analysis(section, test_id, question_number):
    """Get analysis data for a specific question"""
    try:
        user_id = session.get('user_id')
        
        # Support both reading and listening analysis
        if section not in ['reading', 'listening']:
            return jsonify({'error': 'Analysis only available for reading and listening tests'}), 400
        
        # Determine test category based on test_id pattern and parameters
        if test_id.startswith('group'):
            test_category = 'difficulty'
        elif test_id.startswith('mock_'):
            test_category = 'mock_exam'
        elif request.args.get('free') == '1':
            test_category = 'free'
        else:
            test_category = 'premium'
        
        # Query for the question's analysis using Supabase
        try:
            # Choose the appropriate table based on test category
            if test_category == 'mock_exam':
                # Query mock_exams table for mock exam analysis
                result = current_app.supabase.table('mock_exams').select('analysis').eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).eq(
                    'question_number', question_number
                ).not_.is_('analysis', 'null').execute()
            else:
                # Query test_questions table for regular test analysis
                result = current_app.supabase.table('test_questions').select('analysis').eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).eq(
                    'question_number', str(question_number)
                ).not_.is_('analysis', 'null').execute()
            
            if not result.data:
                return jsonify({'error': 'Analysis not found for this question'}), 404
            
            analysis_data = result.data[0]['analysis']
            
            # Ensure analysis_data has the expected structure
            if not isinstance(analysis_data, dict):
                return jsonify({'error': 'Invalid analysis data format'}), 500
            
            # Transform analysis data from database format to frontend format
            transformed_analysis = {}
            if 'analysis_en' in analysis_data:
                transformed_analysis['en'] = analysis_data['analysis_en']
            if 'analysis_cn' in analysis_data:
                transformed_analysis['cn'] = analysis_data['analysis_cn']
            if 'analysis_fr' in analysis_data:
                transformed_analysis['fr'] = analysis_data['analysis_fr']
            
            # If no transformed data, try the direct format (en, cn, fr)
            if not transformed_analysis:
                if 'en' in analysis_data:
                    transformed_analysis['en'] = analysis_data['en']
                if 'cn' in analysis_data:
                    transformed_analysis['cn'] = analysis_data['cn']
                if 'fr' in analysis_data:
                    transformed_analysis['fr'] = analysis_data['fr']
            
            # Return the analysis data
            return jsonify({
                'analysis': transformed_analysis,
                'question_info': {
                    'section': section,
                    'test_id': test_id,
                    'question_number': question_number,
                    'test_category': test_category
                }
            })
            
        except Exception as db_error:
            logger.error(f"Database query error for {section}/{test_id}/Q{question_number}: {str(db_error)}")
            return jsonify({'error': 'Database query failed'}), 500
            
    except Exception as e:
        logger.error(f"Error fetching analysis for {section}/{test_id}/Q{question_number}: {str(e)}")
        return jsonify({'error': 'Failed to fetch analysis'}), 500


@analysis_bp.route('/test/<string:section>/<string:test_id>', methods=['GET'])
@login_required
def get_test_analysis(section, test_id):
    """Get analysis data for all questions in a test"""
    try:
        user_id = session.get('user_id')
        
        # Support both reading and listening analysis
        if section not in ['reading', 'listening']:
            return jsonify({'error': 'Analysis only available for reading and listening tests'}), 400
        
        # Determine test category based on test_id pattern and query params
        if test_id.startswith('group'):
            test_category = 'difficulty'
        elif test_id.startswith('mock_'):
            test_category = 'mock_exam'
        elif request.args.get('free') == '1':
            test_category = 'free'
        else:
            test_category = 'premium'
        
        # Query for all questions' analysis in the test using Supabase
        try:
            # Choose the appropriate table based on test category
            if test_category == 'mock_exam':
                # Query mock_exams table for mock exam analysis
                result = current_app.supabase.table('mock_exams').select(
                    'question_number, analysis'
                ).eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).not_.is_('analysis', 'null').order('question_number').execute()
            else:
                # Query test_questions table for regular test analysis
                result = current_app.supabase.table('test_questions').select(
                    'question_number, analysis'
                ).eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).not_.is_('analysis', 'null').order('question_number').execute()
            
            if not result.data:
                return jsonify({'error': 'No analysis found for this test'}), 404
            
            # Format the results
            analysis_by_question = {}
            for row in result.data:
                question_number = row['question_number']
                analysis_data = row['analysis']
                
                # Transform analysis data from database format to frontend format
                transformed_analysis = {}
                if isinstance(analysis_data, dict):
                    if 'analysis_en' in analysis_data:
                        transformed_analysis['en'] = analysis_data['analysis_en']
                    if 'analysis_cn' in analysis_data:
                        transformed_analysis['cn'] = analysis_data['analysis_cn']
                    if 'analysis_fr' in analysis_data:
                        transformed_analysis['fr'] = analysis_data['analysis_fr']
                    
                    # If no transformed data, try the direct format (en, cn, fr)
                    if not transformed_analysis:
                        if 'en' in analysis_data:
                            transformed_analysis['en'] = analysis_data['en']
                        if 'cn' in analysis_data:
                            transformed_analysis['cn'] = analysis_data['cn']
                        if 'fr' in analysis_data:
                            transformed_analysis['fr'] = analysis_data['fr']
                
                analysis_by_question[str(question_number)] = transformed_analysis
            
            return jsonify({
                'analysis': analysis_by_question,
                'test_info': {
                    'section': section,
                    'test_id': test_id,
                    'test_category': test_category,
                    'total_questions': len(analysis_by_question)
                }
            })
            
        except Exception as db_error:
            logger.error(f"Database query error for {section}/{test_id}: {str(db_error)}")
            return jsonify({'error': 'Database query failed'}), 500
            
    except Exception as e:
        logger.error(f"Error fetching test analysis for {section}/{test_id}: {str(e)}")
        return jsonify({'error': 'Failed to fetch test analysis'}), 500


@analysis_bp.route('/available/<string:section>/<string:test_id>', methods=['GET'])
@login_required
def check_analysis_availability(section, test_id):
    """Check which questions have analysis available"""
    try:
        user_id = session.get('user_id')
        
        # Support both reading and listening analysis
        if section not in ['reading', 'listening']:
            return jsonify({'available': False, 'reason': 'Analysis only available for reading and listening tests'})
        
        # Determine test category based on test_id pattern and query params
        if test_id.startswith('group'):
            test_category = 'difficulty'
        elif test_id.startswith('mock_'):
            test_category = 'mock_exam'
        elif request.args.get('free') == '1':
            test_category = 'free'
        else:
            test_category = 'premium'
        
        # Query for available analysis using Supabase
        try:
            # Choose the appropriate table based on test category
            if test_category == 'mock_exam':
                # Query mock_exams table for mock exam analysis availability
                result = current_app.supabase.table('mock_exams').select(
                    'question_number'
                ).eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).not_.is_('analysis', 'null').order('question_number').execute()
            else:
                # Query test_questions table for regular test analysis availability
                result = current_app.supabase.table('test_questions').select(
                    'question_number'
                ).eq(
                    'test_type', section
                ).eq(
                    'test_category', test_category
                ).eq(
                    'test_identifier', test_id
                ).not_.is_('analysis', 'null').order('question_number').execute()
            
            available_questions = [row['question_number'] for row in result.data]
            
            return jsonify({
                'available': len(available_questions) > 0,
                'questions': available_questions,
                'test_info': {
                    'section': section,
                    'test_id': test_id,
                    'test_category': test_category
                }
            })
            
        except Exception as db_error:
            logger.error(f"Database query error for {section}/{test_id}: {str(db_error)}")
            return jsonify({'available': False, 'reason': 'Database query failed'})
            
    except Exception as e:
        logger.error(f"Error checking analysis availability for {section}/{test_id}: {str(e)}")
        return jsonify({'available': False, 'reason': 'Failed to check availability'}) 