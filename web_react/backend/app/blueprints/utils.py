from flask import Blueprint, send_from_directory, current_app, request, jsonify, session
from functools import wraps

def send_support_email(admin_email, subject, message, user_email, username, user_id):
    """Send support email to admin using SendGrid API"""
    try:
        import requests
        import json
        
        # Get SendGrid API key from environment
        sendgrid_api_key = current_app.config.get('SENDGRID_API_KEY') or current_app.config.get('MAIL_PASSWORD')
        
        print(f"📧 SendGrid API Key set for support email: {bool(sendgrid_api_key)}")
        print(f"📧 API Key preview: {sendgrid_api_key[:20] if sendgrid_api_key else 'None'}...")
        
        if not sendgrid_api_key:
            print("❌ No SendGrid API key found for support email")
            return False
        
        # Create professional HTML email content
        html_content = f"""
        <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Chez-TCFCA - Nouvelle demande de support</title>
            </head>
            <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
                <div style="background-color: #f8f9fa; padding: 30px; border-radius: 8px; border: 1px solid #e9ecef;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #007bff; font-size: 24px; margin: 0;">Chez-TCFCA</h1>
                        <p style="color: #6c757d; font-size: 14px; margin: 5px 0 0 0;">Nouvelle demande de support</p>
                    </div>
                    
                    <h2 style="color: #333; margin-bottom: 20px; font-size: 20px;">Demande de support reçue</h2>
                    
                    <div style="background-color: #e7f3ff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 25px 0;">
                        <h3 style="color: #007bff; margin: 0 0 15px 0;">Informations utilisateur</h3>
                        <p style="margin: 5px 0;"><strong>Nom d'utilisateur:</strong> {username}</p>
                        <p style="margin: 5px 0;"><strong>Email:</strong> {user_email}</p>
                        <p style="margin: 5px 0;"><strong>ID utilisateur:</strong> {user_id}</p>
                    </div>
                    
                    <div style="background-color: #fff; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 25px 0;">
                        <h3 style="color: #333; margin: 0 0 15px 0;">Message:</h3>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; white-space: pre-wrap; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6;">
{message}
                        </div>
                    </div>
                    
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 25px 0;">
                        <p style="color: #856404; font-size: 14px; margin: 0;">
                            <strong>Action requise:</strong> Veuillez répondre à cet utilisateur dans les 24-48 heures à l'adresse: {user_email}
                        </p>
                    </div>
                    
                    <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
                    
                    <div style="text-align: center;">
                        <p style="color: #888; font-size: 12px; margin: 0;">
                            © 2025 Chez-TCFCA - Système de support automatique
                        </p>
                        <p style="color: #888; font-size: 12px; margin: 5px 0 0 0;">
                            Cet email a été envoyé automatiquement depuis le formulaire de contact.
                        </p>
                    </div>
                </div>
            </body>
        </html>
        """
        
        print(f"📧 Sending support email to admin: {admin_email}")
        print(f"👤 From user: {username} ({user_email})")
        
        # Prepare SendGrid API request
        headers = {
            'Authorization': f'Bearer {sendgrid_api_key}',
            'Content-Type': 'application/json'
        }
        
        data = {
            "personalizations": [
                {
                    "to": [{"email": admin_email}],
                    "subject": subject
                }
            ],
            "from": {"email": "<EMAIL>", "name": "Chez-TCFCA Support"},
            "reply_to": {"email": user_email, "name": username},
            "content": [
                {
                    "type": "text/html",
                    "value": html_content
                }
            ]
        }
        
        print("🔄 Sending support email via SendGrid API...")
        
        # Send email via SendGrid API
        try:
            response = requests.post(
                'https://api.sendgrid.com/v3/mail/send',
                headers=headers,
                data=json.dumps(data),
                timeout=30
            )
            
            print(f"📡 SendGrid API response status: {response.status_code}")
            
            if response.status_code == 202:
                print("✅ Support email sent successfully via SendGrid!")
                return True
            else:
                print(f"❌ SendGrid API error: {response.status_code}")
                print(f"❌ Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Critical error in send_support_email: {e}")
        return False

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required', 'code': 'AUTH_REQUIRED'}), 401
        return f(*args, **kwargs)
    return decorated_function

# API routes blueprint
utils_bp = Blueprint('utils', __name__, url_prefix='/api/utils')

# Static files blueprint (for root-level files)
static_bp = Blueprint('static_files', __name__)

@static_bp.route('/sitemap.xml')
def sitemap_xml():
    """Serve the sitemap.xml file"""
    try:
        return send_from_directory('static', 'sitemap.xml')
    except Exception:
        # Return a basic sitemap if file doesn't exist
        sitemap_content = '''<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://chez-tcfcanada.com/</loc>
        <changefreq>weekly</changefreq>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://chez-tcfcanada.com/reading</loc>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chez-tcfcanada.com/listening</loc>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chez-tcfcanada.com/writing</loc>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chez-tcfcanada.com/speaking</loc>
        <changefreq>weekly</changefreq>
        <priority>0.9</priority>
    </url>
    <url>
        <loc>https://chez-tcfcanada.com/membership</loc>
        <changefreq>monthly</changefreq>
        <priority>0.8</priority>
    </url>
</urlset>'''
        return sitemap_content, 200, {'Content-Type': 'application/xml'}

@static_bp.route('/robots.txt')
def robots_txt():
    """Serve the robots.txt file"""
    try:
        return send_from_directory('static', 'robots.txt')
    except Exception:
        # Return a basic robots.txt if file doesn't exist
        robots_content = '''User-agent: *
Allow: /

Sitemap: https://chez-tcfcanada.com/sitemap.xml
'''
        return robots_content, 200, {'Content-Type': 'text/plain'}

@utils_bp.route('/contact-support', methods=['POST'])
@login_required
def contact_support():
    """Handle contact support form submissions"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Le message est requis.'}), 400
        
        if len(message) < 10:
            return jsonify({'error': 'Le message doit contenir au moins 10 caractères.'}), 400
            
        # Get user information from session
        user_id = session.get('user_id')
        if not user_id:
            return jsonify({'error': 'Utilisateur non connecté.'}), 401
            
        # Get user from database
        result = current_app.supabase.table('users').select('*').eq('id', user_id).execute()
        user = result.data[0] if result.data else None
        
        if not user:
            return jsonify({'error': 'Utilisateur non trouvé.'}), 404
            
        user_email = user.get('email', 'Email non disponible')
        username = user.get('username', 'Username non disponible')
        
        # Get admin email from environment or use default
        admin_email = current_app.config.get('ADMIN_EMAIL', '<EMAIL>')
        
        # Create email content
        subject = f'Support Chez-TCFCA - Message de {username}'
        
        # Send email to admin using SendGrid
        try:
            print(f"📧 Sending support message to admin: {admin_email}")
            print(f"👤 From user: {username} ({user_email})")
            
            # Send email using SendGrid
            if send_support_email(admin_email, subject, message, user_email, username, user_id):
                return jsonify({
                    'success': True, 
                    'message': 'Votre message a été envoyé avec succès. Nous vous répondrons dans les 24-48 heures.'
                })
            else:
                # Fallback: Log the support request if email fails
                print("=" * 50)
                print("NEW SUPPORT REQUEST (EMAIL FAILED)")
                print("=" * 50)
                print(f"From: {username} ({user_email})")
                print(f"User ID: {user_id}")
                print(f"Subject: {subject}")
                print(f"Message:\n{message}")
                print("=" * 50)
                
                return jsonify({
                    'success': True, 
                    'message': 'Votre message a été reçu. Nous vous répondrons dans les 24-48 heures.'
                })
            
        except Exception as email_error:
            print(f"❌ Error processing support request: {email_error}")
            return jsonify({'error': 'Erreur lors du traitement de la demande. Veuillez réessayer.'}), 500
        
    except Exception as e:
        print(f'Error in contact support: {e}')
        return jsonify({'error': 'Erreur lors du traitement de la demande.'}), 500 