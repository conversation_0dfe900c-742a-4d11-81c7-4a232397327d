"""
Session Management Utility for Single Login Implementation
Prevents multiple people from using the same account simultaneously
"""

import secrets
from datetime import datetime, timedelta
from typing import Tuple, Optional, Dict, Any
from flask import current_app


class SessionManager:
    """Manages user sessions to prevent multiple concurrent logins"""

    GRACE_PERIOD_MINUTES = 5  # Allow 5 minutes grace period for network issues
    STRICT_VALIDATION = True  # Re-enabled after fixing Google OAuth session creation
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate a cryptographically secure session token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def create_new_session(user_id: str, ip_address: str) -> str:
        """
        Create a new session for the user, invalidating any existing sessions
        
        Args:
            user_id: User's UUID
            ip_address: User's IP address
            
        Returns:
            New session token
        """
        supabase = current_app.supabase
        if not supabase:
            raise Exception("Database connection not available")

        session_token = SessionManager.generate_session_token()
        now = datetime.utcnow()

        try:
            # TEMPORARILY DISABLED: Multiple login prevention
            # This allows users to be logged in from multiple devices/browsers
            result = supabase.table('users').update({
                'session_created_at': now.isoformat(),
                'session_last_activity': now.isoformat(),
                'last_ip': ip_address,
                'last_login': now.isoformat()
                # NOTE: Not updating 'current_session_token' to allow multiple sessions
            }).eq('id', user_id).execute()

            if not result.data:
                raise Exception("Failed to create session - database update returned no data")

            # Session created successfully (multiple logins allowed)
            current_app.logger.info(f"✅ New session created for user {user_id} from IP {ip_address}")
            current_app.logger.info(f"   → Multiple login prevention DISABLED - user can login from multiple devices")
            return session_token
            
        except Exception as e:
            current_app.logger.error(f"Failed to create session for user {user_id}: {str(e)}")
            raise
    
    @staticmethod
    def validate_session(user_id: str, session_token: str, update_activity: bool = True) -> Tuple[bool, str]:
        """
        Validate if the current session is still active and valid

        Args:
            user_id: User's UUID
            session_token: Session token to validate
            update_activity: Whether to update last_activity timestamp

        Returns:
            Tuple of (is_valid, message)
        """
        if not user_id or not session_token:
            return False, "Missing session information"

        # TEMPORARILY DISABLED: Multiple login session validation
        # This allows users to be logged in from multiple devices
        return True, "Valid session (multiple login prevention disabled)"

        supabase = current_app.supabase
        if not supabase:
            return False, "Database connection not available"
        
        try:
            # Get user's current session info
            result = supabase.table('users').select(
                'current_session_token, session_created_at, session_last_activity, last_ip'
            ).eq('id', user_id).execute()

            if not result.data:
                current_app.logger.warning(f"User {user_id} not found during session validation")
                return False, "User not found"

            user_data = result.data[0]
            stored_token = user_data.get('current_session_token')

            # If user doesn't have a session token in database, they might have an old session
            if not stored_token:
                current_app.logger.info(f"User {user_id} has no session token in database (legacy or first login)")
                return False, "No active session found"
            
            # Check if user has no active session
            if not stored_token:
                return False, "No active session found"
            
            # CRITICAL CHECK: Tokens must match exactly
            if stored_token != session_token:
                session_created = user_data.get('session_created_at')
                last_ip = user_data.get('last_ip', 'another location')

                # Log the session invalidation for security monitoring
                current_app.logger.warning(f"🔒 Session invalidated for user {user_id}")
                current_app.logger.warning(f"   Expected token: {session_token[:10]}...")
                current_app.logger.warning(f"   Database token: {stored_token[:10] if stored_token else 'None'}...")
                current_app.logger.warning(f"   Reason: Another login from {last_ip}")

                # Create user-friendly message
                if session_created:
                    try:
                        # Parse the timestamp
                        if isinstance(session_created, str):
                            # Handle different timestamp formats
                            session_created = session_created.replace('Z', '+00:00')
                            created_time = datetime.fromisoformat(session_created)
                        else:
                            created_time = session_created

                        # Format time in ISO format for frontend timezone conversion
                        time_str = created_time.strftime('%Y-%m-%d %H:%M:%S')

                        # Create structured message data for i18n
                        message_data = {
                            'type': 'device_access_with_time',
                            'timestamp': time_str,
                            'ip': last_ip if last_ip != 'another location' else None
                        }

                        # Create fallback English message for backwards compatibility
                        message = f"Account accessed from another device at {time_str}"
                    except Exception:
                        # Fallback if timestamp parsing fails
                        message_data = {'type': 'device_access'}
                        message = "Account accessed from another device"
                else:
                    message_data = {'type': 'device_access'}
                    message = "Account accessed from another device"

                # Return structured data for better i18n support
                return False, {
                    'message': message,
                    'data': message_data
                }
            
            # Update last activity if requested
            if update_activity:
                now = datetime.utcnow()
                supabase.table('users').update({
                    'session_last_activity': now.isoformat()
                }).eq('id', user_id).execute()
            
            return True, "Valid session"
            
        except Exception as e:
            current_app.logger.error(f"Session validation error for user {user_id}: {str(e)}")
            return False, "Session validation failed"
    
    @staticmethod
    def invalidate_session(user_id: str) -> bool:
        """
        Invalidate the current session for a user
        
        Args:
            user_id: User's UUID
            
        Returns:
            True if successful, False otherwise
        """
        supabase = current_app.supabase
        if not supabase:
            return False

        try:
            result = supabase.table('users').update({
                'current_session_token': None,
                'session_created_at': None,
                'session_last_activity': None
            }).eq('id', user_id).execute()
            
            current_app.logger.info(f"Session invalidated for user {user_id}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to invalidate session for user {user_id}: {str(e)}")
            return False
    
    @staticmethod
    def get_session_info(user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get session information for a user
        
        Args:
            user_id: User's UUID
            
        Returns:
            Dictionary with session info or None
        """
        supabase = current_app.supabase
        if not supabase:
            return None

        try:
            result = supabase.table('users').select(
                'current_session_token, session_created_at, session_last_activity, last_ip'
            ).eq('id', user_id).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            current_app.logger.error(f"Failed to get session info for user {user_id}: {str(e)}")
            return None
    
    @staticmethod
    def verify_session_invalidation(user_id: str, old_token: str, new_token: str) -> bool:
        """
        Verify that session invalidation worked correctly
        This is a safety check to ensure the database was updated properly

        Args:
            user_id: User's UUID
            old_token: The previous session token that should be invalid
            new_token: The new session token that should be active

        Returns:
            True if invalidation worked correctly, False otherwise
        """
        supabase = current_app.supabase
        if not supabase:
            return False

        try:
            result = supabase.table('users').select('current_session_token').eq('id', user_id).execute()

            if not result.data:
                current_app.logger.error(f"User {user_id} not found during session verification")
                return False

            current_token = result.data[0].get('current_session_token')

            # Verify the new token is active and old token is not
            if current_token == new_token and current_token != old_token:
                current_app.logger.info(f"✅ Session invalidation verified for user {user_id}")
                return True
            else:
                current_app.logger.error(f"❌ Session invalidation FAILED for user {user_id}")
                current_app.logger.error(f"   Expected: {new_token[:10]}...")
                current_app.logger.error(f"   Actual: {current_token[:10] if current_token else 'None'}...")
                return False

        except Exception as e:
            current_app.logger.error(f"Session verification error for user {user_id}: {str(e)}")
            return False

    @staticmethod
    def cleanup_expired_sessions() -> int:
        """
        Clean up sessions that haven't been active for a long time
        This is optional and can be run as a background task
        
        Returns:
            Number of sessions cleaned up
        """
        supabase = current_app.supabase
        if not supabase:
            return 0

        cutoff_time = datetime.utcnow() - timedelta(days=30)  # 30 days inactive

        try:
            result = supabase.table('users').update({
                'current_session_token': None,
                'session_created_at': None,
                'session_last_activity': None
            }).lt('session_last_activity', cutoff_time.isoformat()).execute()
            
            count = len(result.data) if result.data else 0
            current_app.logger.info(f"Cleaned up {count} expired sessions")
            return count
            
        except Exception as e:
            current_app.logger.error(f"Failed to cleanup expired sessions: {str(e)}")
            return 0
