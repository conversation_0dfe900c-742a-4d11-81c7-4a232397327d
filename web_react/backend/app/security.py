"""
Anti-Scraping Security Module for TCF Canada

This module provides comprehensive protection against:
- Bot scraping
- Automated requests
- Rate limiting
- IP blocking
- Honeypot traps
"""

from flask import request, abort, jsonify, g, current_app
from functools import wraps
import time
import re
from collections import defaultdict, deque
from datetime import datetime, timedelta

# Legitimate search engine bots that should NEVER be blocked
LEGITIMATE_SEO_BOTS = [
    'googlebot', 'bingbot', 'slurp', 'duckduckbot', 'baiduspider',
    'yandexbot', 'facebookexternalhit', 'twitterbot', 'linkedinbot',
    'whatsapp', 'telegrambot', 'applebot', 'ia_archiver',
    'msnbot', 'yahoo! slurp', 'sogou', 'exabot', 'facebot',
    'ia_archiver', 'archive.org_bot', 'semrushbot', 'ahrefsbot',
    'mj12bot', 'dotbot', 'rogerbot', 'sistrix', 'xenu',
    'screaming frog', 'siteimprove', 'uptimerobot'
]

# Blocked user agents (malicious bots, scrapers, crawlers)
# Note: Removed generic 'bot', 'spider', 'crawler' to avoid blocking legitimate SEO bots
BLOCKED_USER_AGENTS = [
    'scrapy', 'selenium', 'phantomjs', 'curl', 'wget',
    'python-requests', 'headlesschrome', 'puppeteer',
    'beautifulsoup', 'requests', 'urllib', 'httplib',
    'automation', 'harvest', 'extract', 'scraper',
    'offline', 'download', 'fetch', 'postman',
    'insomnia', 'httpie', 'python-urllib', 'java/',
    'go-http-client', 'okhttp', 'apache-httpclient',
    'libwww-perl', 'lwp-', 'mechanize', 'winhttp'
]

# Rate limiting storage (in production, use Redis)
rate_limits = defaultdict(lambda: deque())
blocked_ips = set()
suspicious_ips = defaultdict(int)

# IP management with timestamps for better control
blocked_ips_with_time = {}  # {ip: {'timestamp': datetime, 'reason': str, 'permanent': bool}}
whitelisted_ips = set()     # Permanently allowed IPs

# Honeypot endpoints that should never be accessed by legitimate users
HONEYPOT_ENDPOINTS = [
    '/admin', '/wp-admin', '/wp-login.php', '/administrator',
    '/xmlrpc.php', '/.env', '/config', '/backup'
    # Note: /robots.txt and /sitemap.xml removed as they are legitimate endpoints
]

def is_legitimate_seo_bot(user_agent):
    """Check if user agent is a legitimate SEO bot that should be allowed"""
    if not user_agent:
        return False

    user_agent_lower = user_agent.lower()
    return any(legitimate_bot in user_agent_lower for legitimate_bot in LEGITIMATE_SEO_BOTS)

def is_bot_user_agent(user_agent):
    """Check if user agent indicates a malicious bot/scraper (but allow legitimate SEO bots)"""
    if not user_agent:
        return True

    # First check if it's a legitimate SEO bot - if so, allow it
    if is_legitimate_seo_bot(user_agent):
        return False

    # Then check if it matches blocked patterns
    user_agent_lower = user_agent.lower()
    return any(blocked in user_agent_lower for blocked in BLOCKED_USER_AGENTS)

def is_suspicious_request():
    """Detect suspicious request patterns"""
    client_ip = get_client_ip()
    
    # Skip suspicious pattern detection for localhost in development
    if current_app.config.get('FLASK_ENV') == 'development' and client_ip in ['127.0.0.1', '::1', 'localhost']:
        return False
    
    # Allow OPTIONS requests (CORS preflight)
    if request.method == 'OPTIONS':
        return False
    
    # Check for missing common headers
    if not request.headers.get('Accept'):
        return True
    
    # Be more lenient with Accept headers - allow common browser headers
    accept_header = request.headers.get('Accept', '')
    valid_accepts = [
        'application/json',
        'text/html',
        'text/plain', 
        '*/*',
        'application/xml',
        'text/xml'
    ]
    
    # Check if any valid accept type is present
    if not any(valid_accept in accept_header for valid_accept in valid_accepts):
        return True
    
    # Check for rapid sequential requests (rate limiting)
    current_time = time.time()
    
    # Clean old requests (older than 1 minute)
    cutoff_time = current_time - 60
    while rate_limits[client_ip] and rate_limits[client_ip][0] < cutoff_time:
        rate_limits[client_ip].popleft()
    
    # Add current request
    rate_limits[client_ip].append(current_time)
    
    # Check if too many requests in the last minute (more lenient in development)
    max_requests = 100 if current_app.config.get('FLASK_ENV') == 'development' else 30
    if len(rate_limits[client_ip]) > max_requests:
        return True
    
    return False

def get_client_ip():
    """Get the real client IP address with fallback handling"""
    try:
        # Check for forwarded headers (for reverse proxies like Cloudflare)
        cf_ip = request.headers.get('CF-Connecting-IP')
        if cf_ip and cf_ip.strip():
            return cf_ip.strip()

        # Check X-Forwarded-For header (can contain multiple IPs)
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for and forwarded_for.strip():
            # Take the first IP (original client)
            first_ip = forwarded_for.split(',')[0].strip()
            if first_ip:
                return first_ip

        # Check X-Real-IP header
        real_ip = request.headers.get('X-Real-IP')
        if real_ip and real_ip.strip():
            return real_ip.strip()

        # Fallback to request.remote_addr
        remote_addr = request.remote_addr
        if remote_addr and remote_addr.strip():
            return remote_addr.strip()

        # Ultimate fallback - should rarely happen
        current_app.logger.warning("Unable to determine client IP address - all methods returned None/empty")
        return "unknown"

    except Exception as e:
        current_app.logger.error(f"Error getting client IP: {str(e)}")
        return "unknown"

def check_honeypot():
    """Check if request is hitting a honeypot endpoint"""
    return any(endpoint in request.path for endpoint in HONEYPOT_ENDPOINTS)

def add_to_whitelist(ip, reason="Admin whitelist"):
    """Add IP to permanent whitelist"""
    whitelisted_ips.add(ip)
    # Remove from blocked list if present
    if ip in blocked_ips:
        blocked_ips.remove(ip)
    if ip in blocked_ips_with_time:
        del blocked_ips_with_time[ip]
    current_app.logger.info(f"Whitelisted IP {ip}: {reason}")

def remove_from_whitelist(ip):
    """Remove IP from whitelist"""
    whitelisted_ips.discard(ip)
    current_app.logger.info(f"Removed IP {ip} from whitelist")

def is_whitelisted(ip):
    """Check if IP is whitelisted"""
    return ip in whitelisted_ips

def block_ip(ip, reason="Suspicious activity", permanent=False, duration_hours=24):
    """Add IP to blocklist with better management"""
    if is_whitelisted(ip):
        current_app.logger.info(f"Skipping block for whitelisted IP {ip}")
        return
    
    blocked_ips.add(ip)
    blocked_ips_with_time[ip] = {
        'timestamp': datetime.now(),
        'reason': reason,
        'permanent': permanent,
        'duration_hours': duration_hours
    }
    current_app.logger.warning(f"Blocked IP {ip}: {reason} ({'permanent' if permanent else f'{duration_hours}h'})")

def unblock_ip(ip, reason="Manual unblock"):
    """Remove IP from blocklist"""
    blocked_ips.discard(ip)
    if ip in blocked_ips_with_time:
        del blocked_ips_with_time[ip]
    if ip in suspicious_ips:
        del suspicious_ips[ip]
    current_app.logger.info(f"Unblocked IP {ip}: {reason}")

def cleanup_expired_blocks():
    """Remove expired IP blocks"""
    current_time = datetime.now()
    expired_ips = []
    
    for ip, block_info in blocked_ips_with_time.items():
        if not block_info['permanent']:
            block_time = block_info['timestamp']
            duration = timedelta(hours=block_info['duration_hours'])
            if current_time - block_time > duration:
                expired_ips.append(ip)
    
    for ip in expired_ips:
        unblock_ip(ip, "Automatic expiry")
    
    return len(expired_ips)

def get_blocked_ips_info():
    """Get detailed information about blocked IPs"""
    cleanup_expired_blocks()  # Clean up first
    
    blocked_list = []
    for ip in blocked_ips:
        info = blocked_ips_with_time.get(ip, {})
        blocked_list.append({
            'ip': ip,
            'timestamp': info.get('timestamp', 'Unknown'),
            'reason': info.get('reason', 'Unknown'),
            'permanent': info.get('permanent', False),
            'duration_hours': info.get('duration_hours', 24)
        })
    
    return {
        'blocked_ips': blocked_list,
        'whitelisted_ips': list(whitelisted_ips),
        'total_blocked': len(blocked_ips),
        'total_whitelisted': len(whitelisted_ips)
    }

def is_ip_blocked(ip):
    """Check if IP is currently blocked (with expiry check)"""
    if is_whitelisted(ip):
        return False
    
    if ip not in blocked_ips:
        return False
    
    # Check if block has expired
    block_info = blocked_ips_with_time.get(ip)
    if block_info and not block_info['permanent']:
        block_time = block_info['timestamp']
        duration = timedelta(hours=block_info['duration_hours'])
        if datetime.now() - block_time > duration:
            unblock_ip(ip, "Automatic expiry")
            return False
    
    return True

def anti_scraping_middleware():
    """Main anti-scraping middleware function"""
    client_ip = get_client_ip()
    user_agent = request.headers.get('User-Agent', '')
    
    # Skip ALL security checks for local development
    if current_app.config.get('FLASK_ENV') == 'development' and client_ip in ['127.0.0.1', '::1', 'localhost']:
        return
    
    # Check if IP is whitelisted (always allow)
    if is_whitelisted(client_ip):
        return
    
    # Check if IP is currently blocked (with automatic expiry)
    if is_ip_blocked(client_ip):
        current_app.logger.warning(f"Blocked IP attempted access: {client_ip}")
        abort(403, description="Access denied")
    
    # Check honeypot endpoints
    if check_honeypot():
        block_ip(client_ip, "Honeypot access", permanent=True)
        abort(404, description="Not found")
    
    # Check if it's a legitimate SEO bot first
    if is_legitimate_seo_bot(user_agent):
        current_app.logger.info(f"Legitimate SEO bot allowed: {user_agent} from {client_ip}")
        return  # Allow legitimate SEO bots to proceed

    # Check for malicious bot user agents
    if is_bot_user_agent(user_agent):
        suspicious_ips[client_ip] += 1
        current_app.logger.warning(f"Malicious bot user agent detected: {user_agent} from {client_ip}")

        if suspicious_ips[client_ip] >= 3:  # Block after 3 suspicious requests
            block_ip(client_ip, f"Malicious bot user agent: {user_agent}", duration_hours=48)

        abort(403, description="Access denied")
    
    # Check for suspicious request patterns
    if is_suspicious_request():
        suspicious_ips[client_ip] += 1
        current_app.logger.warning(f"Suspicious request pattern from {client_ip}")
        
        if suspicious_ips[client_ip] >= 5:  # Block after 5 suspicious requests
            block_ip(client_ip, "Suspicious request patterns", duration_hours=24)
            abort(403, description="Rate limit exceeded")
        
        # Return rate limit error
        return jsonify({
            "error": "Rate limit exceeded",
            "message": "Too many requests. Please slow down.",
            "retry_after": 60
        }), 429

def require_auth(f):
    """Decorator to require authentication for sensitive endpoints"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Add authentication check here if needed
        # For now, just apply anti-scraping
        anti_scraping_middleware()
        return f(*args, **kwargs)
    return decorated_function

def protect_endpoint(f):
    """Decorator to protect specific endpoints with anti-scraping"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        result = anti_scraping_middleware()
        if result:  # If middleware returns a response (error)
            return result
        return f(*args, **kwargs)
    return decorated_function

# Additional security headers
def add_security_headers(response):
    """Add security headers to responses"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    return response 