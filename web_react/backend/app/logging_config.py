"""
Production-ready logging configuration for TCF Canada backend
"""
import logging
import logging.handlers
import os
from pathlib import Path


def setup_logging(app):
    """Configure logging for production use"""
    
    # Get log level from environment or default to INFO for production
    log_level = os.environ.get('LOG_LEVEL', 'INFO').upper()
    
    # Create logs directory if it doesn't exist
    log_dir = Path(__file__).parent.parent / 'logs'
    log_dir.mkdir(exist_ok=True)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure Flask app logger
    if not app.debug:
        # Production logging configuration
        
        # File handler for general application logs
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'app.log',
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        
        # Error file handler for errors and above
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'error.log',
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s'
        ))
        
        # Add handlers to app logger
        app.logger.addHandler(file_handler)
        app.logger.addHandler(error_handler)
        app.logger.setLevel(logging.INFO)
        
        # Configure security logger
        security_logger = logging.getLogger('security')
        security_handler = logging.handlers.RotatingFileHandler(
            log_dir / 'security.log',
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10  # Keep more security logs
        )
        security_handler.setLevel(logging.WARNING)
        security_handler.setFormatter(logging.Formatter(
            '%(asctime)s - SECURITY - %(levelname)s - %(message)s'
        ))
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.WARNING)
        
        # Suppress noisy third-party loggers
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('werkzeug').setLevel(logging.WARNING)
        
    else:
        # Development logging - more verbose
        app.logger.setLevel(logging.DEBUG)
        
        # Console handler for development
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        app.logger.addHandler(console_handler)


def get_security_logger():
    """Get the security logger for security-related events"""
    return logging.getLogger('security')


def log_security_event(message, level=logging.WARNING, **kwargs):
    """Log a security event with additional context"""
    security_logger = get_security_logger()
    
    # Add context information
    context_parts = []
    for key, value in kwargs.items():
        context_parts.append(f"{key}={value}")
    
    if context_parts:
        full_message = f"{message} | {' | '.join(context_parts)}"
    else:
        full_message = message
    
    security_logger.log(level, full_message)


def log_api_access(endpoint, method, user_id=None, ip=None, status_code=None):
    """Log API access for monitoring"""
    app_logger = logging.getLogger('flask.app')
    
    context = {
        'endpoint': endpoint,
        'method': method,
        'user_id': user_id or 'anonymous',
        'ip': ip or 'unknown',
        'status': status_code or 'unknown'
    }
    
    message = f"API Access: {method} {endpoint}"
    context_str = ' | '.join([f"{k}={v}" for k, v in context.items()])
    
    app_logger.info(f"{message} | {context_str}")
