"""
TCF Canada API Configuration

This module contains configuration settings for the Flask application.
"""

import os
from pathlib import Path

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    # Get the directory of this config file
    config_dir = Path(__file__).parent
    # Look for .env file in the backend directory (where this config is)
    env_path = config_dir.parent / '.env'  # backend/.env
    if env_path.exists():
        load_dotenv(env_path)
        print(f"Loaded environment variables from {env_path}")
    else:
        print(f"No .env file found at {env_path}")
except ImportError:
    print("python-dotenv not installed. Install it with: pip install python-dotenv")

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Session Configuration for cross-origin support
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-origin cookies in development
    PERMANENT_SESSION_LIFETIME = 14400  # 4 hour
    
    # Supabase Configuration
    SUPABASE_URL = os.environ.get('SUPABASE_URL', 'https://placeholder.supabase.co')
    SUPABASE_KEY = os.environ.get('SUPABASE_KEY', 'placeholder-key')
    SUPABASE_SERVICE_KEY = os.environ.get('SUPABASE_SERVICE_KEY', 'placeholder-service-key')
    
    # Stripe Configuration
    STRIPE_PUBLISHABLE_KEY = os.environ.get('STRIPE_PUBLISHABLE_KEY', 'pk_test_placeholder')
    STRIPE_SECRET_KEY = os.environ.get('STRIPE_SECRET_KEY', 'sk_test_placeholder')
    STRIPE_WEBHOOK_SECRET = os.environ.get('STRIPE_WEBHOOK_SECRET', 'whsec_placeholder')
    
    # Email Configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER', 'smtp.sendgrid.net')
    MAIL_PORT = int(os.environ.get('MAIL_PORT', '587'))
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME', 'apikey')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER', '<EMAIL>')
    MAIL_USE_TLS = True
    MAIL_USE_SSL = False
    
    # SendGrid Configuration
    SENDGRID_API_KEY = os.environ.get('SENDGRID_API_KEY')
    
    # Admin Configuration
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL', '<EMAIL>')
    
    # Frontend Configuration
    FRONTEND_URL = os.environ.get('FRONTEND_URL', 'https://chez-tcfcanada.com')

    # Azure Translator Configuration
    AZURE_TRANSLATOR_KEY = os.environ.get('AZURE_TRANSLATOR_KEY')
    AZURE_TRANSLATOR_ENDPOINT = os.environ.get('AZURE_TRANSLATOR_ENDPOINT', 'https://api.cognitive.microsofttranslator.com/')
    AZURE_TRANSLATOR_REGION = os.environ.get('AZURE_TRANSLATOR_REGION', 'eastus')
    
    # OpenAI Configuration
    # Force load from .env file to override any shell environment variables
    _openai_key = os.environ.get('OPENAI_API_KEY')
    if _openai_key and _openai_key == 'your-api-key-here':
        # If we get the placeholder, load directly from .env file
        try:
            from dotenv import dotenv_values
            env_vars = dotenv_values(Path(__file__).parent.parent / '.env')
            OPENAI_API_KEY = env_vars.get('OPENAI_API_KEY', _openai_key)
        except Exception:
            OPENAI_API_KEY = _openai_key
    else:
        OPENAI_API_KEY = _openai_key
    
    OPENAI_TRANSLATION_BUDGET = os.environ.get('OPENAI_TRANSLATION_BUDGET', '3.0')

    # File Paths Configuration
    BASE_DIR = Path(__file__).parent.parent.parent.parent  # config.py -> app -> backend -> web_react -> TCF-Canada
    DATA_DIR = BASE_DIR / 'data'
    ASSETS_DIR = {
        'listening': DATA_DIR / 'assets' / 'listening_asset',
        'reading': DATA_DIR / 'assets' / 'reading_asset',
    }
    
    # Testing Configuration
    TESTING = os.environ.get('TESTING', 'false').lower() == 'true'

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    FLASK_ENV = 'development'
    
    # More permissive session settings for development
    SESSION_COOKIE_SECURE = False  # Allow cookies over HTTP in development
    SESSION_COOKIE_SAMESITE = 'Lax'  # Allow cross-origin cookies

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    FLASK_ENV = 'production'
    
    # Override with more secure defaults for production
    SECRET_KEY = os.environ.get('SECRET_KEY')  # Required in production
    
    @classmethod
    def init_app(cls, app):
        """Production-specific initialization"""
        Config.init_app(app)
        
        # Log to stderr
        import logging
        from logging import StreamHandler
        file_handler = StreamHandler()
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    WTF_CSRF_ENABLED = False

# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
} 