"""
TCF Canada API Application Factory

This module creates and configures the Flask application with all blueprints,
database connections, and middleware.
"""

from flask import Flask, g, session, request
from flask_cors import CORS
import os
from .logging_config import setup_logging

def create_app(config_name=None):
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Load configuration
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    from .config import config
    app.config.from_object(config[config_name])

    # Setup logging
    setup_logging(app)

    # Enable CORS for React frontend
    CORS(app, supports_credentials=True, origins=[
        'http://localhost:3000',  # React dev server (old)
        'http://localhost:5173',  # Vite dev server (new)
        'http://localhost:5174',  # Vite dev server (backup)
        'http://localhost:5175',  # Vite dev server (backup)
        'http://localhost:5176',  # Vite dev server (backup)
        'http://localhost:5177',  # Vite dev server (backup)
        'http://localhost:5004',  # Backend dev server
        'https://tcf-canada.site',  # Production frontend
        'https://www.tcf-canada.site',  # Production frontend
        'https://chez-tcfcanada.com',  # New production frontend
        'https://www.chez-tcfcanada.com'  # New production frontend
    ])
    
    # Initialize anti-scraping security
    from .security import anti_scraping_middleware, add_security_headers
    
    # Initialize Supabase
    try:
        from supabase import create_client, Client
    except ImportError:
        try:
            from supabase.client import create_client, Client
        except ImportError as e:
            print(f"❌ Failed to import Supabase: {e}")
            app.supabase = None
            raise ImportError("Supabase library not found. Install with: pip install supabase")
    
    supabase_url = app.config['SUPABASE_URL']
    supabase_key = app.config['SUPABASE_KEY']  # Use anon key for all operations

    print(f"🔍 Supabase URL: {supabase_url}")
    print(f"🔍 Supabase Key: {supabase_key[:20]}..." if supabase_key else "🔍 Supabase Key: None")

    if not supabase_url or not supabase_key:
        print("❌ SUPABASE_URL and SUPABASE_KEY must be set")
        app.supabase = None
        raise ValueError("SUPABASE_URL and SUPABASE_KEY must be set")

    # Only create real Supabase client if not using placeholder values
    if supabase_url != 'https://placeholder.supabase.co' and supabase_key != 'placeholder-key':
        try:
            # Single client with anon key - we'll set user context per request
            app.supabase = create_client(supabase_url, supabase_key)
            
            # Test the connection
            try:
                # Test the connection
                test_result = app.supabase.table('users').select('id').limit(1).execute()
                print("✅ Supabase client created and tested successfully")

            except Exception as test_error:
                print(f"⚠️ Supabase client created but connection test failed: {test_error}")
                print("✅ Supabase client created (connection test failed but client exists)")
                
        except Exception as e:
            print(f"❌ Supabase connection failed: {e}")
            app.supabase = None
            raise Exception(f"Failed to create Supabase client: {e}")
    else:
        print("❌ Using placeholder Supabase configuration - API will not work!")
        app.supabase = None
        raise ValueError("Placeholder Supabase configuration detected")
    
    # Initialize Stripe
    import stripe
    
    stripe_secret_key = app.config.get('STRIPE_SECRET_KEY')
    if stripe_secret_key and stripe_secret_key != 'sk_test_placeholder':
        stripe.api_key = stripe_secret_key
        print("✅ Stripe API key configured successfully")
    else:
        print(f"⚠️ Stripe not initialized - using placeholder key or key not found")
        # Don't set the API key if it's a placeholder to avoid errors
    
    # Set up Flask-Mail if credentials are provided
    if app.config.get('MAIL_USERNAME') and app.config.get('MAIL_PASSWORD'):
        try:
            from flask_mail import Mail
            app.mail = Mail(app)
        except ImportError:
            print("Flask-Mail not installed. Email functionality will use direct SMTP.")
    
    # Register blueprints
    from .blueprints import auth
    from .blueprints import tests
    from .blueprints import assets
    from .blueprints import media
    from .blueprints import payments
    from .blueprints import user_features
    from .blueprints import admin
    from .blueprints import utils
    from .blueprints import mock_exams
    from .blueprints import modifications
    from .blueprints import analysis
    from .blueprints import translation
    from .blueprints import classified_writing
    from .blueprints import classified_writing_translations

    app.register_blueprint(auth.auth_bp)
    app.register_blueprint(tests.tests_bp)
    app.register_blueprint(assets.assets_bp)
    app.register_blueprint(media.media_bp, url_prefix='/api')
    app.register_blueprint(payments.payments_bp)
    app.register_blueprint(user_features.user_features_bp)
    app.register_blueprint(admin.admin_bp)
    app.register_blueprint(utils.utils_bp)
    app.register_blueprint(utils.static_bp)
    app.register_blueprint(mock_exams.mock_exams_bp)
    app.register_blueprint(modifications.modifications_bp)
    app.register_blueprint(analysis.analysis_bp)
    app.register_blueprint(translation.translation_bp)
    app.register_blueprint(classified_writing.classified_writing_bp)
    app.register_blueprint(classified_writing_translations.classified_writing_translations_bp)
    
    # Global request handlers
    @app.before_request
    def apply_security():
        """Apply anti-scraping security checks ONLY to test data endpoints"""
        # Skip security for health checks and static files
        if request.path in ['/api/health', '/api/info', '/sitemap.xml', '/robots.txt']:
            return

        # Log IP capture for monitoring (only for auth endpoints)
        if request.path.startswith('/api/auth/'):
            from .security import get_client_ip
            client_ip = get_client_ip()
            if client_ip == 'unknown':
                app.logger.warning(f"Failed to capture IP for {request.path} - Headers: {dict(request.headers)}")

        # Skip security completely for localhost in development
        client_ip = request.remote_addr or '127.0.0.1'
        if config_name == 'development' and client_ip in ['127.0.0.1', '::1', 'localhost']:
            return
        
        # ONLY protect test data and group test data endpoints
        # This prevents blocking legitimate users while protecting valuable content
        protected_endpoints = [
            # Individual test data endpoints
            '/api/tests/listening',
            '/api/tests/reading', 
            '/api/tests/speaking',
            '/api/tests/writing',
            '/api/tests/mock-exam',
            
            # Group test data endpoints  
            '/api/tests/group',
            '/api/group-tests',
            '/api/assets/group',
            '/api/assets/tests'
        ]
        
        # Check if this is a protected endpoint that needs anti-scraping
        is_protected = any(request.path.startswith(endpoint) for endpoint in protected_endpoints)
        
        if is_protected:
            # Apply anti-scraping middleware ONLY to test data endpoints
            result = anti_scraping_middleware()
            if result:  # If security middleware returns a response (blocked/rate limited)
                return result
        
        # Allow ALL other endpoints through without any security checks
        # This includes: /api/auth/*, /api/payments/*, /api/user/*, /api/admin/*, etc.
    
    @app.before_request
    def load_logged_in_user():
        """Load the logged-in user into g.user"""
        user_id = session.get('user_id')
        if user_id is not None and app.supabase is not None:
            try:
                result = app.supabase.table('users').select('*').eq('id', user_id).execute()
                g.user = result.data[0] if result.data else None
            except Exception as e:
                print(f"Error loading user: {e}")
                g.user = None
        else:
            g.user = None
    
    @app.after_request
    def apply_security_headers(response):
        """Add security headers to all responses"""
        return add_security_headers(response)
    
    # Health check endpoint
    @app.route('/api/health')
    def health_check():
        return {'status': 'healthy', 'message': 'TCF Canada API is running'}
    
    # API info endpoint
    @app.route('/api/info')
    def api_info():
        return {
            'name': 'TCF Canada API',
            'version': '2.0.0',
            'description': 'Refactored API-only backend for TCF Canada with Supabase',
            'database': 'Supabase/PostgreSQL',
            'environment': config_name,
            'endpoints': {
                'auth': '/api/auth/*',
                'tests': '/api/tests/*',
                'assets': '/api/assets/*',
                'payments': '/api/payments/*',
                'user_features': '/api/user/*',
                'admin': '/api/admin/*',
                'utils': '/api/utils/*',
                'modifications': '/api/modifications/*',
                'translation': '/api/translation/*',
                'static': '/sitemap.xml, /robots.txt'
            }
        }

    # IP Management Endpoints for Admins
    @app.route('/api/admin/security/blocked-ips')
    def get_blocked_ips():
        """Get list of all blocked IPs with details"""
        from .security import get_blocked_ips_info
        return get_blocked_ips_info()
    
    @app.route('/api/admin/security/unblock-ip', methods=['POST'])
    def unblock_ip_endpoint():
        """Unblock a specific IP address"""
        from .security import unblock_ip
        data = request.get_json()
        ip = data.get('ip')
        reason = data.get('reason', 'Admin unblock')
        
        if not ip:
            return {'error': 'IP address required'}, 400
        
        unblock_ip(ip, reason)
        return {'success': True, 'message': f'IP {ip} unblocked'}
    
    @app.route('/api/admin/security/block-ip', methods=['POST'])
    def block_ip_endpoint():
        """Block a specific IP address"""
        from .security import block_ip
        data = request.get_json()
        ip = data.get('ip')
        reason = data.get('reason', 'Manual block')
        permanent = data.get('permanent', True)
        duration_hours = data.get('duration_hours', 24)
        
        if not ip:
            return {'error': 'IP address required'}, 400
        
        block_ip(ip, reason, permanent=permanent, duration_hours=duration_hours)
        return {'success': True, 'message': f'IP {ip} blocked'}
    
    @app.route('/api/admin/security/whitelist-ip', methods=['POST'])
    def whitelist_ip_endpoint():
        """Add IP to permanent whitelist"""
        from .security import add_to_whitelist
        data = request.get_json()
        ip = data.get('ip')
        reason = data.get('reason', 'Admin whitelist')
        
        if not ip:
            return {'error': 'IP address required'}, 400
        
        add_to_whitelist(ip, reason)
        return {'success': True, 'message': f'IP {ip} whitelisted'}
    
    @app.route('/api/admin/security/remove-whitelist', methods=['POST'])
    def remove_whitelist_endpoint():
        """Remove IP from whitelist"""
        from .security import remove_from_whitelist
        data = request.get_json()
        ip = data.get('ip')
        
        if not ip:
            return {'error': 'IP address required'}, 400
        
        remove_from_whitelist(ip)
        return {'success': True, 'message': f'IP {ip} removed from whitelist'}
    
    @app.route('/api/admin/security/cleanup-expired')
    def cleanup_expired_blocks_endpoint():
        """Clean up expired IP blocks"""
        from .security import cleanup_expired_blocks
        cleaned = cleanup_expired_blocks()
        return {'success': True, 'cleaned_ips': cleaned}
    
    @app.route('/api/admin/security/my-ip')
    def get_my_ip():
        """Get current user's IP address"""
        from .security import get_client_ip
        client_ip = get_client_ip()
        return {
            'ip': client_ip,
            'user_agent': request.headers.get('User-Agent', ''),
            'headers': dict(request.headers)
        }
    
    # User IP Lookup Endpoints for Admins
    @app.route('/api/admin/users/search')
    def search_users():
        """Search users by email or username and get their IP info"""
        email = request.args.get('email')
        username = request.args.get('username')
        
        try:
            query = app.supabase.table('users').select('id, username, email, last_ip, registration_ip, last_login, banned')
            
            if email:
                query = query.eq('email', email)
            elif username:
                query = query.eq('username', username)
            else:
                return {'error': 'Email or username required'}, 400
            
            result = query.execute()
            
            return {
                'users': result.data,
                'count': len(result.data)
            }
            
        except Exception as e:
            return {'error': str(e)}, 500
    
    @app.route('/api/admin/users/<user_id>/info')
    def get_user_info(user_id):
        """Get detailed user information including IP addresses"""
        try:
            # Get user from database
            result = app.supabase.table('users').select('*').eq('id', user_id).execute()
            
            if not result.data:
                return {'error': 'User not found'}, 404
            
            user = result.data[0]
            
            # Return user info with IP data
            return {
                'user_id': user.get('id'),
                'username': user.get('username'),
                'email': user.get('email'),
                'last_ip': user.get('last_ip', 'Not tracked'),
                'registration_ip': user.get('registration_ip', 'Not tracked'),
                'last_login': user.get('last_login', 'Never'),
                'banned': user.get('banned', False),
                'is_active': user.get('is_active', True),
                'created_at': user.get('created_at'),
                'membership_type': user.get('membership_type', 'free')
            }
            
        except Exception as e:
            return {'error': str(e)}, 500

    return app 