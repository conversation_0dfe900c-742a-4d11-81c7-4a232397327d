"""
Simple Media Service for Supabase Storage
"""

from flask import current_app
from typing import Optional, Dict, Any

class MediaService:
    """Simple service for media file access through Supabase Storage"""

    def __init__(self):
        self.bucket_name = "media-files"

    def get_media_url(self, media_path: str) -> Optional[Dict[str, Any]]:
        """
        Get public URL for media file
        
        Args:
            media_path: Path to media file (e.g., "listening_asset_free/media_test1/Q1.mp3")
        
        Returns:
            Dict with media URL or None if error
        """
        try:
            supabase = current_app.supabase
            if not supabase:
                current_app.logger.error("Supabase client not available")
                return None

            # Get public URL from Supabase Storage
            public_url = supabase.storage.from_(self.bucket_name).get_public_url(media_path)
            
            if public_url:
                return {
                    'success': True,
                    'media_url': public_url,
                    'public': True
                }
            else:
                current_app.logger.error(f"Failed to get public URL for: {media_path}")
                return None
                
        except Exception as e:
            current_app.logger.error(f"Error getting media URL: {e}")
            return None

# Global instance
media_service = MediaService()
