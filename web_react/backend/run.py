#!/usr/bin/env python3
"""
TCF Canada API Server - Entry Point

This is the main entry point for the TCF Canada API server.
Run this file to start the development server.
"""

import os
from dotenv import load_dotenv
from app import create_app

# Load environment variables
load_dotenv()

def main():
    """Main function to run the Flask application"""
    # Create the Flask app
    config_name = os.environ.get('FLASK_ENV', 'development')
    app = create_app(config_name)
    
    # Development server configuration
    port = int(os.environ.get('PORT', 5001))
    debug = config_name == 'development'
    
    print(f"Starting TCF Canada API server on port {port}")
    print(f"Database: Supabase/PostgreSQL")
    print(f"Environment: {config_name}")
    print(f"Debug mode: {debug}")
    print(f"API available at: http://localhost:{port}")
    print(f"Health check: http://localhost:{port}/api/health")
    print(f"API info: http://localhost:{port}/api/info")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug
    )

if __name__ == '__main__':
    main() 