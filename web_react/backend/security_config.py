"""
Security Configuration for TCF Canada

This file contains configurable security settings that can be adjusted based on environment.
"""

import os

class SecurityConfig:
    """Security configuration class"""
    
    # Rate limiting settings
    RATE_LIMIT_REQUESTS_PER_MINUTE = int(os.environ.get('RATE_LIMIT_REQUESTS_PER_MINUTE', 30))
    RATE_LIMIT_WINDOW_SECONDS = int(os.environ.get('RATE_LIMIT_WINDOW_SECONDS', 60))
    
    # Blocking thresholds
    SUSPICIOUS_REQUEST_THRESHOLD = int(os.environ.get('SUSPICIOUS_REQUEST_THRESHOLD', 5))
    BOT_USER_AGENT_THRESHOLD = int(os.environ.get('BOT_USER_AGENT_THRESHOLD', 3))
    
    # Security features toggle
    ENABLE_USER_AGENT_BLOCKING = os.environ.get('ENABLE_USER_AGENT_BLOCKING', 'true').lower() == 'true'
    ENABLE_RATE_LIMITING = os.environ.get('ENABLE_RATE_LIMITING', 'true').lower() == 'true'
    ENABLE_HONEYPOT = os.environ.get('ENABLE_HONEYPOT', 'true').lower() == 'true'
    ENABLE_SECURITY_HEADERS = os.environ.get('ENABLE_SECURITY_HEADERS', 'true').lower() == 'true'
    
    # Skip security for development
    SKIP_SECURITY_FOR_LOCALHOST = os.environ.get('SKIP_SECURITY_FOR_LOCALHOST', 'true').lower() == 'true'
    
    # Additional blocked user agents (can be added via environment)
    ADDITIONAL_BLOCKED_AGENTS = os.environ.get('ADDITIONAL_BLOCKED_AGENTS', '').split(',') if os.environ.get('ADDITIONAL_BLOCKED_AGENTS') else []
    
    # Whitelisted IPs (bypass all security checks)
    WHITELISTED_IPS = os.environ.get('WHITELISTED_IPS', '').split(',') if os.environ.get('WHITELISTED_IPS') else []
    
    # Additional honeypot endpoints
    ADDITIONAL_HONEYPOT_ENDPOINTS = os.environ.get('ADDITIONAL_HONEYPOT_ENDPOINTS', '').split(',') if os.environ.get('ADDITIONAL_HONEYPOT_ENDPOINTS') else []

# Environment-specific configurations
class DevelopmentSecurityConfig(SecurityConfig):
    """Development environment security settings"""
    RATE_LIMIT_REQUESTS_PER_MINUTE = 100  # More lenient for development
    SKIP_SECURITY_FOR_LOCALHOST = True

class ProductionSecurityConfig(SecurityConfig):
    """Production environment security settings"""
    RATE_LIMIT_REQUESTS_PER_MINUTE = 30   # Stricter for production
    SUSPICIOUS_REQUEST_THRESHOLD = 3      # Lower threshold
    BOT_USER_AGENT_THRESHOLD = 2          # Lower threshold
    SKIP_SECURITY_FOR_LOCALHOST = False   # No bypasses in production

class TestingSecurityConfig(SecurityConfig):
    """Testing environment security settings"""
    ENABLE_USER_AGENT_BLOCKING = False    # Disable for testing
    ENABLE_RATE_LIMITING = False          # Disable for testing
    ENABLE_HONEYPOT = False               # Disable for testing
    SKIP_SECURITY_FOR_LOCALHOST = True

# Configuration mapping
security_config = {
    'development': DevelopmentSecurityConfig,
    'production': ProductionSecurityConfig,
    'testing': TestingSecurityConfig,
    'default': SecurityConfig
}

def get_security_config(environment='development'):
    """Get security configuration for the specified environment"""
    return security_config.get(environment, SecurityConfig) 