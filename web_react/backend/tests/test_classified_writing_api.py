#!/usr/bin/env python3
"""
Test suite for Classified Writing API endpoints

This module contains comprehensive tests for the classified writing API,
including authentication, data retrieval, search functionality, and error handling.
"""

import pytest
import json
from unittest.mock import Mock, patch
from flask import Flask
from app import create_app
from app.blueprints.classified_writing import classified_writing_bp

class TestClassifiedWritingAPI:
    """Test suite for classified writing API endpoints."""
    
    @pytest.fixture
    def app(self):
        """Create test Flask application."""
        app = create_app()
        app.config['TESTING'] = True
        return app
    
    @pytest.fixture
    def client(self, app):
        """Create test client."""
        return app.test_client()
    
    @pytest.fixture
    def mock_supabase(self):
        """Mock Supabase client."""
        mock = Mock()
        mock.table.return_value = mock
        mock.select.return_value = mock
        mock.eq.return_value = mock
        mock.order.return_value = mock
        mock.limit.return_value = mock
        mock.range.return_value = mock
        mock.execute.return_value = Mock(data=[], count=0)
        return mock
    
    def test_get_cards_success(self, client, mock_supabase):
        """Test successful retrieval of classified writing cards."""
        # Mock data
        mock_metadata = [
            {
                'total_tasks': 100,
                'unique_tasks': 80,
                'n_main_topics': 5,
                'method': 'checkpoint_based',
                'checkpoint_name': 'human_reviewed_v1'
            }
        ]
        mock_topics = [
            {
                'topic_name': 'recommendation',
                'total_tasks': 30,
                'unique_tasks': 25
            },
            {
                'topic_name': 'description_places',
                'total_tasks': 25,
                'unique_tasks': 20
            }
        ]
        
        with patch('flask.current_app.supabase', mock_supabase):
            # Configure mock responses
            mock_supabase.execute.side_effect = [
                Mock(data=mock_metadata),  # metadata query
                Mock(data=mock_topics),    # topics query for tache 1
                Mock(data=mock_metadata),  # metadata query for tache 2
                Mock(data=mock_topics),    # topics query for tache 2
                Mock(data=mock_metadata),  # metadata query for tache 3
                Mock(data=mock_topics),    # topics query for tache 3
            ]
            
            response = client.get('/api/classified-writing/cards')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'cards' in data
            assert len(data['cards']) == 3
            assert data['user_is_premium'] is False
            assert data['membership_required'] is False
    
    def test_get_tache_overview_success(self, client, mock_supabase):
        """Test successful retrieval of tache overview."""
        mock_metadata = [{
            'tache_number': 1,
            'total_tasks': 100,
            'unique_tasks': 80,
            'n_main_topics': 5,
            'method': 'checkpoint_based'
        }]
        mock_topics = [{
            'id': 'topic-1',
            'topic_name': 'recommendation',
            'topic_id': 1,
            'keywords': ['recommander', 'conseiller'],
            'total_tasks': 30,
            'unique_tasks': 25
        }]
        
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.side_effect = [
                Mock(data=mock_metadata),  # metadata query
                Mock(data=mock_topics),    # topics query
                Mock(data=[])              # subtopics query
            ]
            
            response = client.get('/api/classified-writing/tache/1')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['tache_number'] == 1
            assert 'metadata' in data
            assert 'topics' in data
            assert len(data['topics']) == 1
    
    def test_get_tache_overview_invalid_number(self, client):
        """Test tache overview with invalid tache number."""
        response = client.get('/api/classified-writing/tache/4')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'Invalid tâche number' in data['error']
    
    def test_get_topic_details_success(self, client, mock_supabase):
        """Test successful retrieval of topic details."""
        mock_topic = [{
            'id': 'topic-1',
            'topic_name': 'recommendation',
            'topic_id': 1,
            'keywords': ['recommander', 'conseiller'],
            'total_tasks': 30,
            'unique_tasks': 25
        }]
        mock_subtopics = [{
            'id': 'subtopic-1',
            'subtopic_name': 'recommendation_sport',
            'task_count': 10,
            'unique_task_count': 8
        }]
        
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.side_effect = [
                Mock(data=mock_topic),     # topic query
                Mock(data=mock_subtopics), # subtopics query
                Mock(data=[])              # task entries query
            ]
            
            response = client.get('/api/classified-writing/tache/1/topics/recommendation')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['tache_number'] == 1
            assert data['topic']['topic_name'] == 'recommendation'
    
    def test_get_topic_details_not_found(self, client, mock_supabase):
        """Test topic details with non-existent topic."""
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.return_value = Mock(data=[])
            
            response = client.get('/api/classified-writing/tache/1/topics/nonexistent')
            
            assert response.status_code == 404
            data = json.loads(response.data)
            assert 'error' in data
    
    def test_get_subtopic_tasks_success(self, client, mock_supabase):
        """Test successful retrieval of subtopic tasks."""
        mock_subtopic = [{
            'id': 'subtopic-1',
            'subtopic_name': 'recommendation_sport',
            'task_count': 10,
            'unique_task_count': 8,
            'main_topic_id': 'topic-1'
        }]
        mock_main_topic = [{
            'topic_name': 'recommendation',
            'tache_number': 1
        }]
        mock_tasks = [{
            'representative_id': 'task-1',
            'task_content': 'Recommandez un sport à votre ami.',
            'is_duplicate_group': False,
            'duplicate_count': 1,
            'task_ids': ['task-1'],
            'month_years': ['2024-01'],
            'combination_numbers': ['A']
        }]
        
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.side_effect = [
                Mock(data=mock_subtopic),   # subtopic query
                Mock(data=mock_main_topic), # main topic query
                Mock(data=mock_tasks),      # tasks query
                Mock(count=1)               # count query
            ]
            
            response = client.get('/api/classified-writing/subtopic/subtopic-1/tasks')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'subtopic' in data
            assert 'tasks' in data
            assert 'pagination' in data
            assert len(data['tasks']) == 1
    
    def test_get_task_details_success(self, client, mock_supabase):
        """Test successful retrieval of task details."""
        mock_task = [{
            'representative_id': 'task-1',
            'task_content': 'Recommandez un sport à votre ami.',
            'is_duplicate_group': False,
            'duplicate_count': 1,
            'subtopic_id': 'subtopic-1'
        }]
        mock_subtopic = [{
            'subtopic_name': 'recommendation_sport',
            'main_topic_id': 'topic-1'
        }]
        mock_main_topic = [{
            'topic_name': 'recommendation',
            'tache_number': 1
        }]
        
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.side_effect = [
                Mock(data=mock_task),       # task query
                Mock(data=mock_subtopic),   # subtopic query
                Mock(data=mock_main_topic)  # main topic query
            ]
            
            response = client.get('/api/classified-writing/task/task-1')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'task' in data
            assert data['task']['representative_id'] == 'task-1'
            assert 'classification_context' in data['task']
    
    def test_search_tasks_success(self, client, mock_supabase):
        """Test successful task search."""
        mock_tasks = [{
            'representative_id': 'task-1',
            'task_content': 'Recommandez un sport à votre ami.',
            'subtopic_id': 'subtopic-1'
        }]
        mock_subtopic = [{
            'subtopic_name': 'recommendation_sport',
            'main_topic_id': 'topic-1'
        }]
        mock_main_topic = [{
            'topic_name': 'recommendation',
            'tache_number': 1
        }]
        
        with patch('flask.current_app.supabase', mock_supabase):
            mock_supabase.execute.side_effect = [
                Mock(data=mock_tasks),      # search query
                Mock(data=mock_subtopic),   # subtopic query
                Mock(data=mock_main_topic)  # main topic query
            ]
            
            response = client.get('/api/classified-writing/search?q=sport')
            
            assert response.status_code == 200
            data = json.loads(response.data)
            assert 'query' in data
            assert 'tasks' in data
            assert 'pagination' in data
            assert data['query'] == 'sport'
    
    def test_search_tasks_no_query(self, client):
        """Test search without query parameter."""
        response = client.get('/api/classified-writing/search')
        
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
    
    def test_authentication_required_endpoints(self, client):
        """Test that certain endpoints require authentication."""
        # These endpoints should work without authentication but may have limited data
        endpoints = [
            '/api/classified-writing/cards',
            '/api/classified-writing/tache/1',
            '/api/classified-writing/search?q=test'
        ]
        
        for endpoint in endpoints:
            response = client.get(endpoint)
            # Should not return 401, but may have limited functionality
            assert response.status_code != 401
    
    def test_error_handling(self, client, mock_supabase):
        """Test error handling for database errors."""
        with patch('flask.current_app.supabase', mock_supabase):
            # Simulate database error
            mock_supabase.execute.side_effect = Exception("Database connection error")
            
            response = client.get('/api/classified-writing/cards')
            
            assert response.status_code == 500
            data = json.loads(response.data)
            assert 'error' in data

if __name__ == '__main__':
    pytest.main([__file__, '-v'])
