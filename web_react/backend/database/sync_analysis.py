#!/usr/bin/env python3
"""
TCF Canada Analysis Data Sync Script
Syncs analysis data (en, cn, fr) from reading and listening analysis files to Supabase
"""

import json
import os
import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, List, Optional

# Add the parent directory to the path to import from app
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.config import Config
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AnalysisDataSyncer:
    """Handles syncing of analysis data to Supabase"""
    
    def __init__(self):
        config = Config()
        # Use service key for admin operations that bypass RLS
        self.supabase: Client = create_client(
            config.SUPABASE_URL,
            config.SUPABASE_SERVICE_KEY
        )
        # Use the same path resolution as sync_to_supabase.py
        self.analysis_root = Path(__file__).parent.parent.parent.parent / "data" / "analysis"
        self.analysis_data = {}
        self.group_analysis_data = {}
        
    def load_individual_analysis(self, test_type: str = "reading"):
        """Load analysis data from individual test files (reading or listening)"""
        logger.info(f"📊 Loading {test_type} individual analysis data...")
        
        # Load regular analysis
        regular_dir = self.analysis_root / f"{test_type}_analysis"
        free_dir = self.analysis_root / f"{test_type}_analysis_free"
        
        total_loaded = 0
        
        # Map directory to correct database category
        for category, directory in [("premium", regular_dir), ("free", free_dir)]:
            if not directory.exists():
                logger.warning(f"⚠️  Directory not found: {directory}")
                continue
                
            for json_file in directory.glob("test*.json"):
                test_id = json_file.stem  # e.g., "test1"
                
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if not isinstance(data, list):
                        logger.warning(f"⚠️  Unexpected data format in {json_file}: expected list")
                        continue
                    
                    for idx, question in enumerate(data, 1):
                        # Only process if all required analysis fields exist
                        if all(key in question for key in ['analysis_en', 'analysis_cn', 'analysis_fr']):
                            key = f"{test_type}_{category}_{test_id}_{idx}"
                            self.analysis_data[key] = {
                                'test_type': test_type,
                                'category': category,
                                'test_id': test_id,
                                'question_number': idx,
                                'analysis': {
                                    'analysis_en': question['analysis_en'],
                                    'analysis_cn': question['analysis_cn'],
                                    'analysis_fr': question['analysis_fr']
                                }
                            }
                            total_loaded += 1
                        else:
                            logger.debug(f"⚠️  Missing analysis fields in {test_id} question {idx}")
                            
                except Exception as e:
                    logger.error(f"❌ Error loading {json_file}: {e}")
        
        logger.info(f"✅ Loaded {total_loaded} {test_type} individual analysis records")

    def load_group_analysis(self, test_type: str = "reading"):
        """Load analysis data from group analysis files (reading or listening)"""
        logger.info(f"📊 Loading {test_type} group analysis data...")
        
        group_dir = self.analysis_root / f"{test_type}_analysis_by_group"
        if not group_dir.exists():
            logger.warning(f"⚠️  Group analysis directory not found: {group_dir}")
            return
            
        total_loaded = 0
        
        # Load all group analysis files
        for json_file in group_dir.glob(f"{test_type}_group*_analysis.json"):
            # Extract group number from filename (e.g., "reading_group1_analysis.json" -> "1")
            group_name = json_file.stem  # e.g., "reading_group1_analysis"
            group_number = group_name.replace(f"{test_type}_group", "").replace("_analysis", "")
            
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if not isinstance(data, dict):
                    logger.warning(f"⚠️  Unexpected data format in {json_file}: expected dict")
                    continue
                
                for question_pos, question_data in data.items():
                    # Only process if all required analysis fields exist
                    if all(key in question_data for key in ['analysis_en', 'analysis_cn', 'analysis_fr']):
                        key = f"{test_type}_group{group_number}_{question_pos}"
                        self.group_analysis_data[key] = {
                            'test_type': test_type,
                            'category': 'difficulty',  # Group tests use 'difficulty' category
                            'test_id': f'group{group_number}',
                            'question_number': int(question_pos),
                            'analysis': {
                                'analysis_en': question_data['analysis_en'],
                                'analysis_cn': question_data['analysis_cn'],
                                'analysis_fr': question_data['analysis_fr']
                            }
                        }
                        total_loaded += 1
                    else:
                        logger.debug(f"⚠️  Missing analysis fields in {test_type} group{group_number} question {question_pos}")
                        
            except Exception as e:
                logger.error(f"❌ Error loading {json_file}: {e}")
        
        logger.info(f"✅ Loaded {total_loaded} {test_type} group analysis records")

    def load_all_analysis(self):
        """Load both reading and listening analysis data (individual and group)"""
        # Load reading analysis
        self.load_individual_analysis("reading")
        self.load_group_analysis("reading")
        
        # Load listening analysis  
        self.load_individual_analysis("listening")
        self.load_group_analysis("listening")
        
        total_count = len(self.analysis_data) + len(self.group_analysis_data)
        logger.info(f"📊 Total analysis records loaded: {total_count}")
        logger.info(f"   - Individual tests: {len(self.analysis_data)}")
        logger.info(f"   - Group tests: {len(self.group_analysis_data)}")

    def sync_to_database(self, dry_run: bool = False) -> bool:
        """Sync analysis data to Supabase test_questions table"""
        
        if dry_run:
            logger.info("🔍 DRY RUN MODE - No actual database updates will be performed")
        
        if not self.analysis_data and not self.group_analysis_data:
            logger.error("No analysis data loaded")
            return False
        
        logger.info("✅ Connected to Supabase")
        
        # First, check if the analysis column exists
        try:
            # Try a simple query to test if analysis column exists
            test_result = self.supabase.table('test_questions').select('analysis').limit(1).execute()
            logger.info("✅ Analysis column exists in database")
        except Exception as e:
            if "column" in str(e).lower() and "does not exist" in str(e).lower():
                logger.error("❌ Analysis column does not exist in test_questions table. Please add it first.")
                logger.error("   Run: ALTER TABLE test_questions ADD COLUMN analysis JSONB DEFAULT '{}';")
                return False
            else:
                logger.error(f"❌ Database connection error: {e}")
                return False
        
        updated_count = 0
        not_found_count = 0
        error_count = 0
        
        # Process individual test analysis (reading and listening)
        logger.info("🔄 Syncing individual test analysis...")
        for key, data in self.analysis_data.items():
            test_type = data['test_type']
            category = data['category']
            test_id = data['test_id']
            question_num = data['question_number']
            analysis = data['analysis']
            
            try:
                # Query for the question
                result = self.supabase.table('test_questions').select('id').eq(
                    'test_type', test_type
                ).eq(
                    'test_category', category
                ).eq(
                    'test_identifier', test_id
                ).eq(
                    'question_number', question_num
                ).execute()
                
                if result.data:
                    question_id = result.data[0]['id']
                    
                    if not dry_run:
                        # Update the analysis field
                        update_result = self.supabase.table('test_questions').update({
                            'analysis': analysis
                        }).eq('id', question_id).execute()
                        
                        if update_result.data:
                            updated_count += 1
                            logger.debug(f"✅ Updated {test_type}/{category}/{test_id}/Q{question_num}")
                        else:
                            error_count += 1
                            logger.error(f"❌ Failed to update {test_type}/{category}/{test_id}/Q{question_num}")
                    else:
                        updated_count += 1
                        logger.debug(f"🔍 Would update {test_type}/{category}/{test_id}/Q{question_num}")
                else:
                    not_found_count += 1
                    logger.debug(f"⚠️  No match found for {test_type}/{category}/{test_id}/Q{question_num}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ Error processing {key}: {e}")
        
        # Process group test analysis (reading and listening)
        logger.info("🔄 Syncing group test analysis...")
        for key, data in self.group_analysis_data.items():
            test_type = data['test_type']
            category = data['category']  # 'difficulty'
            group_id = data['test_id']  # e.g., "group1"
            question_num = data['question_number']
            analysis = data['analysis']
            
            try:
                # Query for the group question using the correct schema
                # Group questions have test_category="difficulty" and test_identifier="group1", "group2", etc.
                result = self.supabase.table('test_questions').select('id').eq(
                    'test_type', test_type
                ).eq(
                    'test_category', category  # 'difficulty' for group tests
                ).eq(
                    'test_identifier', group_id  # e.g., "group1"
                ).eq(
                    'question_number', question_num
                ).execute()
                
                if result.data:
                    question_id = result.data[0]['id']
                    
                    if not dry_run:
                        # Update the analysis field
                        update_result = self.supabase.table('test_questions').update({
                            'analysis': analysis
                        }).eq('id', question_id).execute()
                        
                        if update_result.data:
                            updated_count += 1
                            logger.debug(f"✅ Updated {test_type}/{group_id}/Q{question_num}")
                        else:
                            error_count += 1
                            logger.error(f"❌ Failed to update {test_type}/{group_id}/Q{question_num}")
                    else:
                        updated_count += 1
                        logger.debug(f"🔍 Would update {test_type}/{group_id}/Q{question_num}")
                else:
                    not_found_count += 1
                    logger.debug(f"⚠️  No match found for {test_type}/{group_id}/Q{question_num}")
                    
            except Exception as e:
                error_count += 1
                logger.error(f"❌ Error processing {key}: {e}")
        
        # Summary
        logger.info("📊 Analysis sync summary:")
        logger.info(f"   ✅ Updated: {updated_count}")
        logger.info(f"   ⚠️  Not found: {not_found_count}")
        logger.info(f"   ❌ Errors: {error_count}")
        
        if error_count > 0:
            logger.warning("⚠️  Some errors occurred during sync")
            return False
        
        if updated_count > 0:
            logger.info("🎉 Analysis sync completed successfully!")
            return True
        else:
            logger.warning("⚠️  No records were updated")
            return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Sync analysis data to Supabase')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be updated without making changes')
    parser.add_argument('--verbose', '-v', action='store_true', 
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 Starting TCF Canada Analysis Sync...")
    
    # Initialize syncer
    syncer = AnalysisDataSyncer()
    
    # Load analysis data
    logger.info("📁 Loading analysis data...")
    syncer.load_all_analysis()
    
    if not syncer.analysis_data and not syncer.group_analysis_data:
        logger.error("❌ No analysis data found")
        return 1
    
    # Sync to database
    logger.info("🔄 Syncing to database...")
    success = syncer.sync_to_database(dry_run=args.dry_run)
    
    if success:
        logger.info("✅ Analysis sync completed successfully!")
        return 0
    else:
        logger.error("❌ Analysis sync failed!")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 