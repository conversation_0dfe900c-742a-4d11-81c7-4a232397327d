#!/usr/bin/env python3
"""
Media File Migration Script
Uploads all audio files and images from local storage to Supabase Storage
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple
import mimetypes
from supabase import create_client, Client
import json
from datetime import datetime

# Add parent directory to path to import config
sys.path.append(str(Path(__file__).parent.parent))
from app.config import Config

class MediaMigrator:
    def __init__(self, dry_run: bool = False):
        """Initialize the media file migrator"""
        self.config = Config()
        self.dry_run = dry_run
        
        # Use service key for admin operations
        self.supabase: Client = create_client(
            self.config.SUPABASE_URL,
            self.config.SUPABASE_SERVICE_KEY
        )
        
        # Define paths
        self.data_root = Path(__file__).parent.parent.parent.parent / "data"
        self.assets_root = self.data_root / "assets"
        
        # Storage bucket name
        self.bucket_name = "media-files"
        
        # Statistics
        self.stats = {
            'total_files': 0,
            'uploaded': 0,
            'skipped': 0,
            'errors': 0,
            'total_size': 0
        }
        
        # Migration log
        self.migration_log = []

    def ensure_bucket_exists(self) -> bool:
        """Ensure the audio-files bucket exists"""
        try:
            # Try to get bucket info
            buckets = self.supabase.storage.list_buckets()

            # Handle both old and new Supabase client API formats
            bucket_exists = False
            if buckets:
                for bucket in buckets:
                    # Handle both dict format and object format
                    bucket_name = bucket.name if hasattr(bucket, 'name') else bucket.get('name')
                    if bucket_name == self.bucket_name:
                        bucket_exists = True
                        break

            if not bucket_exists:
                print(f"Creating bucket: {self.bucket_name}")
                if not self.dry_run:
                    self.supabase.storage.create_bucket(self.bucket_name, options={"public": False})
                print(f"✅ Bucket {self.bucket_name} created successfully")
            else:
                print(f"✅ Bucket {self.bucket_name} already exists")

            return True
        except Exception as e:
            print(f"❌ Error with bucket: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_media_files(self) -> List[Tuple[Path, str]]:
        """Get all media files (audio and images) and their relative storage paths"""
        media_files = []

        # Define media extensions (audio + images)
        media_extensions = {
            # Audio files
            '.mp3', '.wav', '.m4a', '.ogg', '.webm',
            # Image files
            '.jpg', '.jpeg', '.png', '.webp', '.gif'
        }
        
        # Scan listening asset directories
        listening_dirs = [
            self.assets_root / "listening_asset",
            self.assets_root / "listening_asset_free"
        ]
        
        for listening_dir in listening_dirs:
            if not listening_dir.exists():
                print(f"⚠️ Directory not found: {listening_dir}")
                continue
                
            print(f"📁 Scanning: {listening_dir}")
            
            # Walk through all subdirectories
            for root, dirs, files in os.walk(listening_dir):
                root_path = Path(root)
                
                for file in files:
                    file_path = root_path / file
                    
                    # Check if it's a media file (audio or image)
                    if file_path.suffix.lower() in media_extensions:
                        # Calculate relative path from assets root
                        relative_path = file_path.relative_to(self.assets_root)
                        
                        # Convert to storage path (use forward slashes)
                        storage_path = str(relative_path).replace('\\', '/')
                        
                        media_files.append((file_path, storage_path))
                        
                        # Update stats
                        self.stats['total_files'] += 1
                        self.stats['total_size'] += file_path.stat().st_size
        
        return media_files

    def upload_file(self, local_path: Path, storage_path: str) -> bool:
        """Upload a single file to Supabase Storage"""
        try:
            # Check if file already exists
            try:
                parent_path = str(Path(storage_path).parent)
                if parent_path == '.':
                    parent_path = ''

                existing_files = self.supabase.storage.from_(self.bucket_name).list(path=parent_path)

                # Handle both old and new API formats
                file_exists = False
                filename = Path(storage_path).name

                for f in existing_files:
                    # Handle both dict format and object format
                    file_name = f.name if hasattr(f, 'name') else f.get('name')
                    if file_name == filename:
                        file_exists = True
                        break

                if file_exists:
                    print(f"⏭️ Skipping (already exists): {storage_path}")
                    self.stats['skipped'] += 1
                    return True

            except Exception as e:
                # If listing fails, assume file doesn't exist
                print(f"⚠️ Could not check if file exists: {e}")
                pass
            
            if self.dry_run:
                print(f"🔍 [DRY RUN] Would upload: {local_path} -> {storage_path}")
                self.stats['uploaded'] += 1
                return True
            
            # Read file content
            with open(local_path, 'rb') as f:
                file_content = f.read()
            
            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(str(local_path))
            if not mime_type:
                # Default based on file extension
                ext = local_path.suffix.lower()
                if ext in ['.mp3', '.wav', '.m4a', '.ogg']:
                    mime_type = 'audio/mpeg'
                elif ext in ['.jpg', '.jpeg']:
                    mime_type = 'image/jpeg'
                elif ext == '.png':
                    mime_type = 'image/png'
                elif ext == '.webp':
                    mime_type = 'image/webp'
                else:
                    mime_type = 'application/octet-stream'
            
            # Upload to Supabase Storage
            result = self.supabase.storage.from_(self.bucket_name).upload(
                path=storage_path,
                file=file_content,
                file_options={
                    "content-type": mime_type,
                    "cache-control": "3600"  # Cache for 1 hour
                }
            )
            
            print(f"✅ Uploaded: {storage_path} ({len(file_content)} bytes)")
            self.stats['uploaded'] += 1
            
            # Log successful upload
            self.migration_log.append({
                'local_path': str(local_path),
                'storage_path': storage_path,
                'size': len(file_content),
                'mime_type': mime_type,
                'status': 'success',
                'timestamp': datetime.now().isoformat()
            })
            
            return True
            
        except Exception as e:
            print(f"❌ Error uploading {storage_path}: {e}")
            self.stats['errors'] += 1
            
            # Log error
            self.migration_log.append({
                'local_path': str(local_path),
                'storage_path': storage_path,
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
            
            return False

    def save_migration_log(self):
        """Save migration log to file"""
        log_file = Path(__file__).parent / f"audio_migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        log_data = {
            'migration_stats': self.stats,
            'migration_log': self.migration_log,
            'timestamp': datetime.now().isoformat(),
            'dry_run': self.dry_run
        }
        
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
        
        print(f"📝 Migration log saved to: {log_file}")

    def run_migration(self):
        """Run the complete migration process"""
        print("🚀 Starting Media File Migration to Supabase Storage")
        print(f"📊 Mode: {'DRY RUN' if self.dry_run else 'LIVE MIGRATION'}")
        print("=" * 60)

        # Step 1: Ensure bucket exists
        if not self.ensure_bucket_exists():
            print("❌ Failed to create/verify bucket. Aborting.")
            return False

        # Step 2: Get all media files
        print("\n📁 Scanning for media files (audio + images)...")
        media_files = self.get_media_files()

        if not media_files:
            print("⚠️ No media files found!")
            return False

        # Categorize files
        audio_count = sum(1 for path, _ in media_files if path.suffix.lower() in {'.mp3', '.wav', '.m4a', '.ogg', '.webm'})
        image_count = len(media_files) - audio_count

        print(f"📊 Found {len(media_files)} media files ({self.stats['total_size'] / 1024 / 1024:.2f} MB total)")
        print(f"   🎵 Audio files: {audio_count}")
        print(f"   🖼️ Image files: {image_count}")

        # Step 3: Upload files
        print(f"\n⬆️ {'Simulating upload of' if self.dry_run else 'Uploading'} media files...")

        for i, (local_path, storage_path) in enumerate(media_files, 1):
            file_type = "🎵" if local_path.suffix.lower() in {'.mp3', '.wav', '.m4a', '.ogg', '.webm'} else "🖼️"
            print(f"[{i}/{len(media_files)}] {file_type} Processing: {storage_path}")
            self.upload_file(local_path, storage_path)
        
        # Step 4: Print summary
        print("\n" + "=" * 60)
        print("📊 MIGRATION SUMMARY")
        print("=" * 60)
        print(f"Total files found: {self.stats['total_files']}")
        print(f"Successfully uploaded: {self.stats['uploaded']}")
        print(f"Skipped (already exist): {self.stats['skipped']}")
        print(f"Errors: {self.stats['errors']}")
        print(f"Total size: {self.stats['total_size'] / 1024 / 1024:.2f} MB")
        
        # Step 5: Save log
        self.save_migration_log()
        
        return self.stats['errors'] == 0

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Migrate audio files to Supabase Storage')
    parser.add_argument('--dry-run', action='store_true', help='Simulate migration without uploading')
    parser.add_argument('--force', action='store_true', help='Skip confirmation prompt')

    args = parser.parse_args()

    if not args.force and not args.dry_run:
        response = input("This will upload media files (audio + images) to Supabase Storage. Continue? (y/N): ")
        if response.lower() != 'y':
            print("Migration cancelled.")
            return
    
    migrator = MediaMigrator(dry_run=args.dry_run)
    success = migrator.run_migration()
    
    if success:
        print("✅ Migration completed successfully!")
    else:
        print("❌ Migration completed with errors. Check the log file for details.")
        sys.exit(1)

if __name__ == "__main__":
    main()
