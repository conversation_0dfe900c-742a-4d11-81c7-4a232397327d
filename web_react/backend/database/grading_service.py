#!/usr/bin/env python3
"""
Grading Service for TCF Canada Tests

This service handles the grading of test submissions including:
- Reading and listening tests (regular, free, group, mock exams)
- Secure answer retrieval and validation
- Score calculation and feedback generation
"""

import logging
from typing import Dict, Any, Tuple, List

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define difficulty groups and their question ranges with points
DIFFICULTY_GROUPS = {
    'A1': {'range': (1, 4), 'points': 3, 'count': 4},
    'A2': {'range': (5, 10), 'points': 9, 'count': 6},
    'B1': {'range': (11, 19), 'points': 15, 'count': 9},
    'B2': {'range': (20, 29), 'points': 21, 'count': 10},
    'C1': {'range': (30, 35), 'points': 26, 'count': 6},
    'C2': {'range': (36, 39), 'points': 33, 'count': 4}
}

def get_question_points(question_number: int) -> int:
    """Get points for a question based on its number (1-39)"""
    for difficulty, config in DIFFICULTY_GROUPS.items():
        start, end = config['range']
        if start <= question_number <= end:
            return config['points']
    return 1  # Default fallback

class GradingService:
    """Service for grading test submissions with secure answer access"""
    
    def __init__(self, supabase_client):
        self.supabase = supabase_client

    def grade_test_submission(self, test_type: str, test_identifier: str, 
                            user_answers: Dict[str, Any], is_free: bool = False) -> Dict[str, Any]:
        """
        Grade a test submission and return detailed results
        
        Args:
            test_type: 'reading', 'listening', etc.
            test_identifier: Test ID (e.g., '1', 'group1', 'mock_exam_1', 'collection_book_notebook')
            user_answers: Dict mapping question numbers (as strings) to user answers
            is_free: Whether this is a free test version
            
        Returns:
            Dict with grading results including score, feedback, and detailed breakdown
        """
        
        try:
            # Special handling for collection book
            if test_identifier == 'collection_book_notebook':
                return self._grade_collection_book(test_type, test_identifier, user_answers)
            # Route to appropriate grading method based on test type
            elif test_identifier.startswith('group'):
                return self._grade_group_test(test_type, test_identifier, user_answers)
            elif test_identifier.startswith('mock_'):
                return self._grade_mock_exam(test_type, test_identifier, user_answers)
            else:
                return self._grade_regular_test(test_type, test_identifier, user_answers, is_free)
            
        except Exception as e:
            logger.error(f"Error grading test {test_type}/{test_identifier}: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }

    def _grade_collection_book(self, test_type: str, test_identifier: str, user_answers: Dict[str, Any]) -> Dict[str, Any]:
        """Grade collection book questions using UUID keys"""
        from flask import session
        user_id = session.get('user_id')
        if not user_id:
            return {
                'success': False,
                'error': 'Authentication required',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }
        
        try:
            # Get collection book questions with their correct answers
            response = self.supabase.table('collection_book').select('*').eq(
                'user_id', user_id
            ).execute()
            
            if not response.data:
                return {
                    'success': False,
                    'error': 'No collection book questions found',
                    'total_questions': 0,
                    'correct_count': 0,
                    'score_percentage': 0
                }
            
            # Grade each question using UUID keys
            results = []
            correct_count = 0
            total_questions = len(user_answers)  # Only grade answered questions
            
            for bookmark in response.data:
                question_id = bookmark['question_id']
                user_answer = user_answers.get(question_id, '')
                
                # Skip if user didn't answer this question
                if not user_answer:
                    continue
                
                # Get correct answer from bookmark data
                correct_answer_data = bookmark.get('correct_answer', {})
                
                is_correct, explanation = self._grade_single_question(
                    bookmark['test_type'], correct_answer_data, user_answer
                )
                
                if is_correct:
                    correct_count += 1
                
                results.append({
                    'question_number': question_id,  # Use UUID for consistency with answers
                    'user_answer': user_answer,
                    'correct_answer': self._extract_answer_text(correct_answer_data),
                    'is_correct': is_correct,
                    'explanation': explanation,
                })
            
            # Simple scoring: 1 point per correct answer
            score = correct_count
            max_score = total_questions
            score_percentage = (correct_count / total_questions * 100) if total_questions > 0 else 0
            
            return {
                'success': True,
                'test_type': test_type,
                'test_identifier': test_identifier,
                'total_questions': total_questions,
                'correct_count': correct_count,
                'score': score,
                'max_score': max_score,
                'score_percentage': round(score_percentage, 2),
                'results': results,
                'grade_level': self._determine_grade_level(score_percentage)
            }
            
        except Exception as e:
            logger.error(f"Error grading collection book: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': f'Failed to grade collection book: {str(e)}',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }

    def _grade_mock_exam(self, test_type: str, test_identifier: str, user_answers: Dict[str, Any]) -> Dict[str, Any]:
        from flask import session
        user_id = session.get('user_id')
        if not user_id:
            return {
                'success': False,
                'error': 'Authentication required',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }
        # Query mock_exams table for this user and test
        response = self.supabase.table('mock_exams').select('*').eq(
            'test_identifier', test_identifier
        ).eq('user_id', user_id).eq('test_type', test_type).order('question_number').execute()
        questions = response.data or []
        if not questions:
            return {
                'success': False,
                'error': 'Mock exam questions not found',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }
        results = []
        correct_count = 0
        total_questions = len(questions)
        total_score = 0
        max_score = sum(q.get('points', 1) for q in questions)
        for question in questions:
            question_num = str(question['question_number'])
            user_answer = user_answers.get(question_num, '')
            correct_answer_data = question.get('correct_answer', {})
            is_correct, explanation = self._grade_single_question(
                test_type, correct_answer_data, user_answer
            )
            if is_correct:
                correct_count += 1
                total_score += question.get('points', 1)
            results.append({
                'question_number': question_num,
                'user_answer': user_answer,
                'correct_answer': self._extract_answer_text(correct_answer_data),
                'is_correct': is_correct,
                'explanation': explanation,
            })
        score_percentage = (total_score / max_score * 100) if max_score > 0 else 0
        return {
            'success': True,
            'test_type': test_type,
            'test_identifier': test_identifier,
            'total_questions': total_questions,
            'correct_count': correct_count,
            'score': total_score,
            'max_score': max_score,
            'score_percentage': round(score_percentage, 2),
            'results': results,
            'grade_level': self._determine_grade_level(score_percentage)
        }
    
    def _get_questions_with_answers(self, test_type: str, test_identifier: str, is_free: bool = False) -> List[Dict[str, Any]]:
        """Get questions with their correct answers using unified logic"""
        try:
            # Use the same category determination logic as test data service
            category = None
            final_test_identifier = test_identifier
            
            if test_type in ['reading', 'listening']:
                if is_free:
                    category = 'free'
                    # Add test prefix for free tests if needed
                    if not test_identifier.startswith('test'):
                        final_test_identifier = f'test{test_identifier}'
                elif str(test_identifier).startswith('group'):
                    category = 'difficulty'
                    # Group tests already have the right identifier
                else:
                    category = 'premium'
                    # Add test prefix for premium tests if needed
                    if not test_identifier.startswith('test'):
                        final_test_identifier = f'test{test_identifier}'
            elif test_type == 'writing':
                category = 'month'
            elif test_type == 'speaking':
                # Speaking tests have multiple categories, but for grading we'd need specific logic
                if 'tache2' in test_identifier or any(month in test_identifier for month in ['janvier', 'fevrier', 'mars', 'avril', 'mai', 'juin', 'juillet', 'aout', 'septembre', 'octobre', 'novembre', 'decembre']):
                    category = 'tache2_month'
                else:
                    category = 'tache3_topic'
            else:
                logger.error(f"Unknown test type: {test_type}")
                return []
            
            # Query database for questions with correct answers
            response = self.supabase.table('test_questions').select(
                'question_number, correct_answer'
            ).eq(
                'test_type', test_type
            ).eq(
                'test_category', category
            ).eq(
                'test_identifier', final_test_identifier
            ).execute()
            
            if not response.data:
                logger.warning(f"No questions found for {test_type}/{category}/{final_test_identifier}")
                return []
            
            # Sort by question_number and return
            questions = sorted(response.data, key=lambda x: int(x.get('question_number', '0') or '0'))
            logger.info(f"Retrieved {len(questions)} questions with answers for {test_type}/{final_test_identifier}")
            return questions
            
        except Exception as e:
            logger.error(f"Error retrieving questions for {test_type}/{test_identifier}: {e}")
            import traceback
            traceback.print_exc()
            return []

    def _extract_answer_text(self, correct_answer_data: Dict[str, Any]) -> str:
        """Extract the answer text from correct_answer data"""
        if isinstance(correct_answer_data, dict):
            # Handle nested formats: {"answer": "A"}, {"correct_answer": "A"}, etc.
            return str(
                correct_answer_data.get('answer') or 
                correct_answer_data.get('correct_answer') or
                correct_answer_data.get('correctAnswer') or
                ''
            )
        elif isinstance(correct_answer_data, str):
            return correct_answer_data.strip()
        else:
            return str(correct_answer_data) if correct_answer_data else ''
    
    def _grade_single_question(self, test_type: str, correct_answer_data: Dict[str, Any], 
                              user_answer: str) -> Tuple[bool, str]:
        """Grade a single question"""
        try:
            correct_answer = self._extract_answer_text(correct_answer_data)
            
            if test_type in ['reading', 'listening']:
                # Multiple choice - exact match required
                is_correct = str(user_answer).strip().upper() == str(correct_answer).strip().upper()
                explanation = f"Correct answer: {correct_answer}" if not is_correct else "Correct!"
                
            elif test_type == 'writing':
                # Writing tasks don't have single correct answers
                # This would require more sophisticated grading (AI/human review)
                is_correct = False  # Placeholder
                explanation = "Writing tasks require manual grading"
                
            elif test_type == 'speaking':
                # Speaking tasks don't have single correct answers
                # This would require more sophisticated grading (AI/human review)
                is_correct = False  # Placeholder
                explanation = "Speaking tasks require manual grading"
                
            else:
                is_correct = False
                explanation = "Unknown test type"
            
            return is_correct, explanation
            
        except Exception as e:
            logger.error(f"Error grading question: {e}")
            return False, "Error during grading"
    
    def _determine_grade_level(self, score_percentage: float) -> str:
        """Determine TCF grade level based on score percentage"""
        if score_percentage >= 90:
            return "C2"
        elif score_percentage >= 80:
            return "C1"  
        elif score_percentage >= 70:
            return "B2"
        elif score_percentage >= 60:
            return "B1"
        elif score_percentage >= 50:
            return "A2"
        elif score_percentage >= 40:
            return "A1"
        else:
            return "Below A1"
    
    def get_question_feedback(self, test_type: str, test_identifier: str, 
                            question_number: str, user_answer: str, is_free: bool = False) -> Dict[str, Any]:
        """Get detailed feedback for a single question (for immediate feedback)"""
        try:
            # Get the question using the same unified logic
            questions = self._get_questions_with_answers(test_type, test_identifier, is_free)
            
            # Find the specific question
            target_question = None
            for question in questions:
                if str(question.get('question_number', '')) == str(question_number):
                    target_question = question
                    break
            
            if not target_question:
                return {'success': False, 'error': 'Question not found'}
            
            correct_answer_data = target_question.get('correct_answer', {})
            
            if not correct_answer_data:
                return {'success': False, 'error': 'No correct answer available'}
            
            is_correct, explanation = self._grade_single_question(
                test_type, correct_answer_data, user_answer
            )
            
            return {
                'success': True,
                'question_number': question_number,
                'user_answer': user_answer,
                'correct_answer': self._extract_answer_text(correct_answer_data),
                'is_correct': is_correct,
                'explanation': explanation,
                'metadata': target_question.get('metadata', {})
            }
            
        except Exception as e:
            logger.error(f"Error getting question feedback: {e}")
            return {'success': False, 'error': str(e)}

    def _grade_regular_test(self, test_type: str, test_identifier: str, user_answers: Dict[str, Any], is_free: bool) -> Dict[str, Any]:
        """Grade a regular test (premium or free)"""
        try:
            # Use unified approach: get questions using the same logic as test data service
            questions = self._get_questions_with_answers(test_type, test_identifier, is_free)
            
            if not questions:
                return {
                    'success': False,
                    'error': f'No questions found for {test_type}/{test_identifier} (free: {is_free})',
                    'total_questions': 0,
                    'correct_count': 0,
                    'score_percentage': 0
                }
            
            # Grade each question
            results = []
            correct_count = 0
            total_questions = len(questions)
            total_score = 0

            # Determine if we should use weighted scoring (only for reading/listening tests)
            use_weighted_scoring = test_type in ['reading', 'listening']

            if use_weighted_scoring:
                # Calculate max score using weighted points
                max_score = sum(get_question_points(int(q.get('question_number', 1))) for q in questions)
            else:
                # Simple scoring: 1 point per question
                max_score = total_questions

            for question in questions:
                question_num = str(question.get('question_number', ''))
                user_answer = user_answers.get(question_num, '')
                correct_answer_data = question.get('correct_answer', {})

                is_correct, explanation = self._grade_single_question(
                    test_type, correct_answer_data, user_answer
                )

                if is_correct:
                    correct_count += 1
                    if use_weighted_scoring:
                        # Add weighted points for correct answer
                        question_points = get_question_points(int(question_num))
                        total_score += question_points
                    else:
                        # Simple scoring: 1 point per correct answer
                        total_score += 1

                results.append({
                    'question_number': question_num,
                    'user_answer': user_answer,
                    'correct_answer': self._extract_answer_text(correct_answer_data),
                    'is_correct': is_correct,
                    'explanation': explanation,
                })

            # Set final score
            score = total_score
            score_percentage = (total_score / max_score * 100) if max_score > 0 else 0
            
            return {
                'success': True,
                'test_type': test_type,
                'test_identifier': test_identifier,
                'total_questions': total_questions,
                'correct_count': correct_count,
                'score': score,
                'max_score': max_score,
                'score_percentage': round(score_percentage, 2),
                'results': results,
                'grade_level': self._determine_grade_level(score_percentage)
            }
            
        except Exception as e:
            print(f"❌ Error grading regular test: {e}")
            return {
                'success': False,
                'error': f'Failed to grade regular test: {str(e)}',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }
    
    def _grade_group_test(self, test_type: str, test_identifier: str, user_answers: Dict[str, Any]) -> Dict[str, Any]:
        """Grade a group test (difficulty-based)"""
        try:
            # Use unified approach: get questions using the same logic as test data service
            questions = self._get_questions_with_answers(test_type, test_identifier, is_free=False)
            
            if not questions:
                return {
                    'success': False,
                    'error': f'No questions found for {test_type}/{test_identifier}',
                    'total_questions': 0,
                    'correct_count': 0,
                    'score_percentage': 0
                }
            
            # Grade each question
            results = []
            correct_count = 0
            total_questions = len(questions)
            
            for question in questions:
                question_num = str(question.get('question_number', ''))
                user_answer = user_answers.get(question_num, '')
                correct_answer_data = question.get('correct_answer', {})
                
                is_correct, explanation = self._grade_single_question(
                    test_type, correct_answer_data, user_answer
                )
                
                if is_correct:
                    correct_count += 1
                
                results.append({
                    'question_number': question_num,
                    'user_answer': user_answer,
                    'correct_answer': self._extract_answer_text(correct_answer_data),
                    'is_correct': is_correct,
                    'explanation': explanation,
                })
            
            # Simple scoring: 1 point per correct answer
            score = correct_count
            max_score = total_questions
            score_percentage = (correct_count / total_questions * 100) if total_questions > 0 else 0
            
            return {
                'success': True,
                'test_type': test_type,
                'test_identifier': test_identifier,
                'total_questions': total_questions,
                'correct_count': correct_count,
                'score': score,
                'max_score': max_score,
                'score_percentage': round(score_percentage, 2),
                'results': results,
                'grade_level': self._determine_grade_level(score_percentage)
            }
            
        except Exception as e:
            print(f"❌ Error grading group test: {e}")
            return {
                'success': False,
                'error': f'Failed to grade group test: {str(e)}',
                'total_questions': 0,
                'correct_count': 0,
                'score_percentage': 0
            }

# Convenience function for Flask routes
def get_grading_service():
    """Get grading service instance with current app's Supabase client"""
    from flask import current_app
    return GradingService(current_app.supabase) 