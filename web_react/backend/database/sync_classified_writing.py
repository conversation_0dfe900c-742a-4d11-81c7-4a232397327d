#!/usr/bin/env python3
"""
Classified Writing Data Sync Script

This script syncs classified writing data from reviewed_classification JSON files
into the test_questions table using the same structure as other test types.

Usage:
    python sync_classified_writing.py [--tache 1|2|3] [--dry-run] [--verbose]
"""

import json
import os
import sys
import argparse
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging

# Add the parent directory to the path to import from app
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.config import Config
from supabase import create_client, Client

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ClassifiedWritingSyncer:
    """Syncs classified writing data from JSON files to test_questions table."""

    def __init__(self, dry_run: bool = False, verbose: bool = False):
        self.dry_run = dry_run
        self.verbose = verbose

        # Initialize Supabase client
        config = Config()
        self.supabase: Client = create_client(
            config.SUPABASE_URL,
            config.SUPABASE_SERVICE_KEY  # Use service key for admin operations
        )

        # Paths
        self.workspace_root = Path(__file__).parent.parent.parent.parent
        self.data_dir = self.workspace_root / 'data' / 'classified' / 'writing' / 'reviewed_classification'

        # Stats
        self.stats = {
            'total_processed': 0,
            'total_inserted': 0,
            'total_updated': 0,
            'total_skipped': 0,
            'errors': 0
        }

        logger.info(f"Data directory: {self.data_dir}")
        logger.info(f"Dry run mode: {dry_run}")
        logger.info(f"Verbose mode: {verbose}")
    
    def generate_data_hash(self, data: Dict[str, Any]) -> str:
        """Generate hash for data to detect changes."""
        data_str = json.dumps(data, sort_keys=True, ensure_ascii=False)
        return hashlib.sha256(data_str.encode('utf-8')).hexdigest()
    
    def load_classification_data(self, tache_number: int) -> Dict[str, Any]:
        """Load classification data from JSON file."""
        file_path = self.data_dir / f'tache_{tache_number}.json'

        if not file_path.exists():
            raise FileNotFoundError(f"Classification file not found: {file_path}")

        logger.info(f"Loading classification data from: {file_path}")

        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        logger.info(f"Loaded data for Tâche {tache_number}: {data['classification']['total_tasks']} total tasks")
        return data

    def create_test_question_entry(self, task_entry: Dict[str, Any], topic_name: str,
                                 subtopic_name: str, tache_number: int, topic_data: Dict[str, Any] = None,
                                 subtopic_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Create a test_questions table entry from a classified task."""

        # Generate unique test_id based on representative_id
        test_id = f"classified_writing_tache_{tache_number}_{task_entry['representative_id']}"

        # Log translation info if available
        if topic_data and self.verbose:
            logger.info(f"    Topic translations: EN='{topic_data.get('name_en', topic_name)}', "
                       f"FR='{topic_data.get('name_fr', topic_name)}', "
                       f"ZH='{topic_data.get('name_zh', topic_name)}'")
        if subtopic_data and self.verbose:
            logger.info(f"    Subtopic translations: EN='{subtopic_data.get('name_en', subtopic_name)}', "
                       f"FR='{subtopic_data.get('name_fr', subtopic_name)}', "
                       f"ZH='{subtopic_data.get('name_zh', subtopic_name)}'")

        # Create the question data structure
        question_data = {
            'task_content': task_entry['task_content'],
            'clean_content': task_entry.get('clean_content', ''),
            'is_duplicate_group': task_entry.get('is_duplicate_group', False),
            'duplicate_count': task_entry.get('duplicate_count', 1),
            'task_ids': task_entry.get('task_ids', []),
            'month_years': task_entry.get('month_years', []),
            'combination_numbers': task_entry.get('combination_numbers', []),
            'representative_id': task_entry['representative_id'],
            'classification': {
                'main_topic': topic_name,
                'subtopic': subtopic_name,
                'tache_number': tache_number,
                # Add translated topic names
                'main_topic_translations': {
                    'en': topic_data.get('name_en', topic_name) if topic_data else topic_name,
                    'fr': topic_data.get('name_fr', topic_name) if topic_data else topic_name,
                    'zh': topic_data.get('name_zh', topic_name) if topic_data else topic_name
                },
                # Add translated subtopic names
                'subtopic_translations': {
                    'en': subtopic_data.get('name_en', subtopic_name) if subtopic_data else subtopic_name,
                    'fr': subtopic_data.get('name_fr', subtopic_name) if subtopic_data else subtopic_name,
                    'zh': subtopic_data.get('name_zh', subtopic_name) if subtopic_data else subtopic_name
                }
            },
            'metadata': task_entry.get('metadata', {})
        }

        # Word limits for each tâche
        word_limits = {
            1: {'min': 60, 'max': 120},
            2: {'min': 120, 'max': 150},
            3: {'min': 120, 'max': 180}
        }

        # Create metadata for the test_questions table
        metadata = {
            'difficulty': 'intermediate',
            'tags': [f'tache_{tache_number}', topic_name, subtopic_name, 'classified', 'writing'],
            'word_limits': word_limits.get(tache_number, {'min': 60, 'max': 120}),
            'time_limit': 60,  # 60 minutes for writing tasks
            'points': 25,  # Standard points for writing tasks
        }

        return {
            'test_type': 'writing',
            'test_category': 'classified',
            'test_identifier': f'tache_{tache_number}',
            'question_number': task_entry['representative_id'],
            'question_data': question_data,
            'free_content': question_data,  # All content is free for browsing
            'paid_content': {},  # No paid content for classified writing
            'content_flags': {},
            'correct_answer': {},  # No correct answer for writing tasks
            'metadata': metadata
        }
    
    def sync_task_to_supabase(self, task_entry: Dict[str, Any], topic_name: str,
                             subtopic_name: str, tache_number: int, topic_data: Dict[str, Any] = None,
                             subtopic_data: Dict[str, Any] = None) -> bool:
        """Sync a single task to the test_questions table in Supabase."""
        try:
            # Create the test question entry
            test_question = self.create_test_question_entry(
                task_entry, topic_name, subtopic_name, tache_number, topic_data, subtopic_data
            )

            # Generate a data hash for change detection
            data_hash = self.generate_data_hash(test_question)
            test_question['data_hash'] = data_hash

            # Check if the question already exists
            existing = self.supabase.table('test_questions').select('id, data_hash').eq(
                'test_type', test_question['test_type']
            ).eq('test_category', test_question['test_category']).eq(
                'test_identifier', test_question['test_identifier']
            ).eq('question_number', test_question['question_number']).execute()

            if existing.data:
                # Question exists, check if it needs updating
                if existing.data[0]['data_hash'] != data_hash:
                    if not self.dry_run:
                        self.supabase.table('test_questions').update(
                            test_question
                        ).eq('id', existing.data[0]['id']).execute()

                    self.stats['total_updated'] += 1
                    if self.verbose:
                        logger.info(f"Updated task: {test_question['question_number']}")
                else:
                    self.stats['total_skipped'] += 1
                    if self.verbose:
                        logger.info(f"Skipped unchanged task: {test_question['question_number']}")
            else:
                # New question, insert it
                if not self.dry_run:
                    self.supabase.table('test_questions').insert(
                        test_question
                    ).execute()

                self.stats['total_inserted'] += 1
                if self.verbose:
                    logger.info(f"Inserted new task: {test_question['question_number']}")

            self.stats['total_processed'] += 1
            return True

        except Exception as e:
            logger.error(f"Error syncing task {task_entry.get('representative_id')}: {e}")
            self.stats['errors'] += 1
            return False
    
    def sync_tache(self, tache_number: int) -> bool:
        """Sync all tasks for a specific tâche."""
        logger.info(f"Starting sync for Tâche {tache_number}")

        try:
            # Load classification data
            data = self.load_classification_data(tache_number)

            # Process all topics and subtopics
            main_topics = data['classification']['main_topics']

            for topic_name, topic_data in main_topics.items():
                logger.info(f"Processing topic: {topic_name}")

                subtopics = topic_data.get('subtopics', {})
                for subtopic_name, subtopic_data in subtopics.items():
                    if self.verbose:
                        logger.info(f"  Processing subtopic: {subtopic_name}")

                    task_entries = subtopic_data.get('task_entries', [])
                    for task_entry in task_entries:
                        success = self.sync_task_to_supabase(
                            task_entry, topic_name, subtopic_name, tache_number, topic_data, subtopic_data
                        )
                        if not success:
                            logger.warning(f"Failed to sync task: {task_entry.get('representative_id')}")

            logger.info(f"Completed sync for Tâche {tache_number}")
            return True

        except Exception as e:
            logger.error(f"Error syncing Tâche {tache_number}: {e}")
            return False
    
    def sync_all(self) -> bool:
        """Sync all tâches (1, 2, 3)."""
        logger.info("Starting sync for all tâches")

        success = True
        for tache_number in [1, 2, 3]:
            if not self.sync_tache(tache_number):
                success = False

        # Print final statistics
        logger.info("=== SYNC STATISTICS ===")
        logger.info(f"Total processed: {self.stats['total_processed']}")
        logger.info(f"Total inserted: {self.stats['total_inserted']}")
        logger.info(f"Total updated: {self.stats['total_updated']}")
        logger.info(f"Total skipped: {self.stats['total_skipped']}")
        logger.info(f"Errors: {self.stats['errors']}")

        return success
    


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Sync classified writing data to test_questions table')
    parser.add_argument('--tache', type=int, choices=[1, 2, 3],
                       help='Sync specific tâche only (1, 2, or 3)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Perform a dry run without making database changes')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set up logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Create syncer
    syncer = ClassifiedWritingSyncer(dry_run=args.dry_run, verbose=args.verbose)

    try:
        # Sync data
        if args.tache:
            success = syncer.sync_tache(args.tache)
        else:
            success = syncer.sync_all()

        if success:
            logger.info("Sync completed successfully!")
            sys.exit(0)
        else:
            logger.error("Sync completed with errors!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"Sync failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
