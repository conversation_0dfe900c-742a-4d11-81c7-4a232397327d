#!/usr/bin/env python3
"""
Complete TCF Canada test data sync script with enhanced group test support
This script syncs all test types including regular tests and difficulty-based group tests
with proper asset path management and complete transcript extraction
"""

import json
import os
import sys
import hashlib
import argparse
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging

# Add the parent directory to the path to import from app
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.config import Config
from supabase import create_client, Client

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sync_to_supabase.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ComprehensiveTestDataSyncer:
    def __init__(self, force_update=False):
        """Initialize the syncer with Supabase client"""
        config = Config()
        # Use service key for admin operations that bypass RLS
        self.supabase: Client = create_client(
            config.SUPABASE_URL,
            config.SUPABASE_SERVICE_KEY  # Use service key instead of regular key
        )
        self.data_root = Path(__file__).parent.parent.parent.parent / "data" / "scraped"
        self.deduplicated_root = Path(__file__).parent.parent.parent.parent / "data" / "deduplicated"
        self.answer_root = self.data_root / "scraped_answer"
        self.force_update = force_update  # Force local files to override database
        self.stats = {
            'total_processed': 0,
            'total_inserted': 0,
            'total_updated': 0,
            'total_skipped': 0,
            'errors': 0
        }

        # Load all answer data
        self.answers = self._load_all_answers()
        self.group_answers = self._load_group_answers()

    def _load_all_answers(self) -> Dict[str, Dict]:
        """Load all correct answers from separate JSON files"""
        answers = {}
        
        if not self.answer_root.exists():
            logger.warning("❌ Answer directory not found")
            return answers
            
        answer_files = {
            'reading_free': 'reading_correct_answer_free.json',
            'reading_premium': 'reading_correct_answer.json',
            'listening_free': 'listening_correct_answer_free.json', 
            'listening_premium': 'listening_correct_answer.json',
        }
        
        for key, filename in answer_files.items():
            file_path = self.answer_root / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        answers[key] = json.load(f)
                    logger.info(f"✅ Loaded {key} answers: {len(answers[key])} items")
                except Exception as e:
                    logger.error(f"❌ Error loading {filename}: {e}")
                    answers[key] = {}
            else:
                logger.warning(f"⚠️ Answer file not found: {filename}")
                answers[key] = {}
        
        return answers

    def _load_group_answers(self) -> Dict[str, Dict]:
        """Load group test answers"""
        group_answers = {}
        
        for section in ['reading', 'listening']:
            answer_file = self.answer_root / f"{section}_correct_answer_by_group.json"
            if answer_file.exists():
                try:
                    with open(answer_file, 'r', encoding='utf-8') as f:
                        group_answers[section] = json.load(f)
                    logger.info(f"✅ Loaded {section} group answers: {len(group_answers[section])} groups")
                except Exception as e:
                    logger.error(f"❌ Error loading {answer_file}: {e}")
                    group_answers[section] = {}
            else:
                logger.warning(f"⚠️ Group answer file not found: {answer_file}")
                group_answers[section] = {}
        
        return group_answers

    def _get_correct_answer(self, test_type: str, is_free: bool, test_identifier: str, question_number: str) -> Dict[str, Any]:
        """Get correct answer for a specific question"""
        if test_type not in ['reading', 'listening']:
            return {}  # Writing/speaking don't have predefined correct answers

        # Check if this is a group test (test_identifier starts with 'group')
        if test_identifier.startswith('group'):
            # Handle group tests
            group_answers_data = self.group_answers.get(test_type, {})
            if test_identifier in group_answers_data:
                group_test_answers = group_answers_data[test_identifier]
                if question_number in group_test_answers:
                    return {'answer': group_test_answers[question_number]}
            logger.debug(f"No group answer found for {test_type}/{test_identifier}#{question_number}")
            return {}
        else:
            # Handle regular tests
            answer_key = f"{test_type}_{'free' if is_free else 'premium'}"
            answers_data = self.answers.get(answer_key, {})

            # Try to find the answer by test identifier and question
            if test_identifier in answers_data:
                test_answers = answers_data[test_identifier]
                if question_number in test_answers:
                    return {'answer': test_answers[question_number]}

            logger.debug(f"No answer found for {test_type}/{test_identifier}#{question_number}")
            return {}

    def generate_data_hash(self, free_content: Dict, paid_content: Dict) -> str:
        """Generate a hash for both free and paid content to detect changes"""
        combined_data = {'free': free_content, 'paid': paid_content}
        data_str = json.dumps(combined_data, sort_keys=True)
        return hashlib.sha256(data_str.encode()).hexdigest()

    def separate_writing_content(self, question: Dict[str, Any]) -> Tuple[Dict, Dict, Dict]:
        """Separate writing test content into free and paid parts"""
        # Free content (available to all users)
        free_content = {
            'month_year': question.get('month_year'),
            'combination_number': question.get('combination_number'),
            'task_number': question.get('task_number'),
            'task_content': question.get('task_content'),  # The actual task/prompt
        }
        
        # Paid content (premium users only)
        paid_content = {
            'correction_content': question.get('correction_content'),  # Example answer
        }
        
        # Content flags for tracking
        content_flags = {
            'has_paid_content': bool(question.get('correction_content')),
            'paid_fields': ['correction_content'] if question.get('correction_content') else []
        }
        
        # Remove None values
        free_content = {k: v for k, v in free_content.items() if v is not None}
        paid_content = {k: v for k, v in paid_content.items() if v is not None}
        
        return free_content, paid_content, content_flags

    def separate_speaking_content(self, question: Dict[str, Any], task_type: str) -> Tuple[Dict, Dict, Dict]:
        """Separate speaking test content into free and paid parts"""
        if task_type == 'tache2_month':
            # Task 2: Scenario-based
            free_content = {
                'month_year': question.get('month_year'),
                'partie_number': question.get('partie_number'),
                'scenario': question.get('scenario'),  # The scenario prompt
            }
            
            paid_content = {
                'example_questions': question.get('example_questions', []),  # Example questions
            }
            
            paid_fields = ['example_questions'] if question.get('example_questions') else []
            
        else:  # tache3_topic
            # Task 3: Topic-based
            free_content = {
                'topic': question.get('topic'),
                'task_number': question.get('task_number'),
                'question': question.get('question'),  # The question/prompt
                'word_count': question.get('word_count'),
            }
            
            paid_content = {
                'exemplary_answer': question.get('exemplary_answer', {}),  # Example answer
            }
            
            paid_fields = ['exemplary_answer'] if question.get('exemplary_answer') else []
        
        content_flags = {
            'has_paid_content': bool(paid_content and any(paid_content.values())),
            'paid_fields': paid_fields
        }
        
        # Remove None values
        free_content = {k: v for k, v in free_content.items() if v is not None}
        paid_content = {k: v for k, v in paid_content.items() if v is not None}
        
        return free_content, paid_content, content_flags

    def sync_writing_tests(self) -> None:
        """Sync writing tests with content separation"""
        logger.info("🔄 Syncing writing tests with content separation...")
        
        writing_dir = self.data_root / "scraped_writing"
        if not writing_dir.exists():
            logger.warning("❌ Writing directory not found")
            return
        
        for json_file in writing_dir.glob("*.json"):
            if json_file.name == "all_expression_ecrite.json":
                continue  # Skip the combined file
                
            try:
                month_year = json_file.stem  # e.g., "janvier-2025"
                logger.info(f"  📝 Processing writing {month_year}")
                
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Extract combinations
                combinations = data.get('combinations', [])
                for combination in combinations:
                    combination_num = combination.get('combination_number')
                    tasks = combination.get('tasks', [])
                    
                    for task in tasks:
                        task_num = task.get('task_number')
                        question_num = f"{combination_num}_{task_num}"
                        
                        # Prepare task data for content separation
                        task_data = {
                            'month_year': data.get('month_year'),
                            'combination_number': combination_num,
                            'task_number': task_num,
                            'task_content': task.get('task_content'),
                            'correction_content': task.get('correction_content')
                        }
                        
                        # Separate content
                        free_content, paid_content, content_flags = self.separate_writing_content(task_data)
                        
                        self._upsert_question_improved(
                            test_type="writing",
                            test_category="month",
                            test_identifier=month_year,
                            question_number=question_num,
                            free_content=free_content,
                            paid_content=paid_content,
                            content_flags=content_flags
                        )
                        
            except Exception as e:
                logger.error(f"❌ Error processing {json_file}: {e}")
                self.stats['errors'] += 1

    def _upsert_question_improved(self, test_type: str, test_category: str, test_identifier: str,
                                 question_number: Any, free_content: Dict[str, Any],
                                 paid_content: Dict[str, Any], content_flags: Dict[str, Any],
                                 correct_answer: Optional[Dict[str, Any]] = None,
                                 metadata: Optional[Dict[str, Any]] = None) -> None:
        """Insert or update a question with current database schema (local files always override)"""
        try:
            # Combine all content into question_data (do NOT include correct_answer here)
            question_data = {**free_content, **paid_content}

            # Include all data in hash calculation (including correct_answer separately)
            content_for_hash = {
                'question_data': question_data,
                'correct_answer': correct_answer or {},
                'metadata': metadata or {}
            }
            data_hash = self.generate_data_hash(content_for_hash, {})

            # Check if question already exists
            existing = self.supabase.table('test_questions').select('id, data_hash').eq(
                'test_type', test_type
            ).eq(
                'test_category', test_category
            ).eq(
                'test_identifier', test_identifier
            ).eq(
                'question_number', question_number
            ).execute()

            # Create record with complete database schema (including all columns)
            record = {
                'test_type': test_type,
                'test_category': test_category,
                'test_identifier': test_identifier,
                'question_number': question_number,
                'question_data': question_data,
                'free_content': free_content,
                'paid_content': paid_content,
                'content_flags': content_flags,
                'correct_answer': correct_answer or {},
                'metadata': metadata or {},
                'data_hash': data_hash
            }

            if existing.data:
                # Check if we should force update or only update when data changed
                should_update = self.force_update or (existing.data[0]['data_hash'] != data_hash)

                if should_update:
                    self.supabase.table('test_questions').update(record).eq(
                        'id', existing.data[0]['id']
                    ).execute()
                    self.stats['total_updated'] += 1
                    update_reason = "force override" if self.force_update else "data changed"
                    logger.debug(f"  ✏️  Updated {test_type}/{test_category}/{test_identifier}#{question_number} ({update_reason})")
                else:
                    self.stats['total_skipped'] += 1
                    logger.debug(f"  ⏭️  Skipped {test_type}/{test_category}/{test_identifier}#{question_number} (no changes)")
            else:
                # Insert new record
                self.supabase.table('test_questions').insert(record).execute()
                self.stats['total_inserted'] += 1
                logger.debug(f"  ➕ Inserted {test_type}/{test_category}/{test_identifier}#{question_number}")

            self.stats['total_processed'] += 1

        except Exception as e:
            logger.error(f"❌ Error upserting question {test_type}/{test_identifier}#{question_number}: {e}")
            self.stats['errors'] += 1

    def sync_reading_tests(self) -> None:
        """Sync reading tests with answers"""
        logger.info("🔄 Syncing reading tests with answers...")
        
        test_types = [
            ("free", self.data_root / "scraped_reading_cleaned_free", True),
            ("premium", self.data_root / "scraped_reading_cleaned", False)
        ]
        
        for category, reading_dir, is_free in test_types:
            if not reading_dir.exists():
                logger.warning(f"❌ Reading {category} directory not found")
                continue
                
            for json_file in reading_dir.glob("*.json"):
                try:
                    test_identifier = json_file.stem
                    logger.info(f"  📚 Processing reading {category} {test_identifier}")
                    
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Handle both array format and object with 'questions' key
                    if isinstance(data, list):
                        questions = data
                    else:
                        questions = data.get('questions', [])
                    
                    for question in questions:
                        question_num = str(question.get('question_number', ''))
                        
                        # Get correct answer
                        correct_answer = self._get_correct_answer('reading', is_free, test_identifier, question_num)
                        
                        # All reading content is free (no paid separation needed)
                        free_content = {k: v for k, v in question.items() if k != 'question_number'}
                        paid_content = {}
                        content_flags = {'has_paid_content': False, 'paid_fields': []}
                        
                        self._upsert_question_improved(
                            test_type="reading",
                            test_category=category,
                            test_identifier=test_identifier,
                            question_number=question_num,
                            free_content=free_content,
                            paid_content=paid_content,
                            content_flags=content_flags,
                            correct_answer=correct_answer
                        )
                        
                except Exception as e:
                    logger.error(f"❌ Error processing {json_file}: {e}")
                    self.stats['errors'] += 1

    def sync_listening_tests(self) -> None:
        """Sync listening tests with answers"""
        logger.info("🔄 Syncing listening tests with answers...")
        
        test_types = [
            ("free", self.data_root / "scraped_listening_cleaned_free", True),
            ("premium", self.data_root / "scraped_listening_cleaned", False)
        ]
        
        for category, listening_dir, is_free in test_types:
            if not listening_dir.exists():
                logger.warning(f"❌ Listening {category} directory not found")
                continue
                
            for json_file in listening_dir.glob("*.json"):
                try:
                    test_identifier = json_file.stem
                    logger.info(f"  🎧 Processing listening {category} {test_identifier}")
                    
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Handle both array format and object with 'questions' key
                    if isinstance(data, list):
                        questions = data
                    else:
                        questions = data.get('questions', [])
                    
                    for question in questions:
                        question_num = str(question.get('question_number', ''))
                        
                        # Get correct answer
                        correct_answer = self._get_correct_answer('listening', is_free, test_identifier, question_num)
                        
                        # All listening content is free (no paid separation needed)
                        free_content = {k: v for k, v in question.items() if k != 'question_number'}
                        paid_content = {}
                        content_flags = {'has_paid_content': False, 'paid_fields': []}
                        
                        self._upsert_question_improved(
                            test_type="listening",
                            test_category=category,
                            test_identifier=test_identifier,
                            question_number=question_num,
                            free_content=free_content,
                            paid_content=paid_content,
                            content_flags=content_flags,
                            correct_answer=correct_answer
                        )
                        
                except Exception as e:
                    logger.error(f"❌ Error processing {json_file}: {e}")
                    self.stats['errors'] += 1

    def sync_speaking_tests(self) -> None:
        """Sync speaking tests with content separation"""
        logger.info("🔄 Syncing speaking tests with content separation...")
        
        # Speaking tests are organized into two types:
        # 1. Task 2 (tache2) - Scenario-based, organized by month
        # 2. Task 3 (tache3) - Topic-based discussions
        
        speaking_dirs = [
            ("tache2_month", self.data_root / "scraped_speaking" / "La_tache_2"),
            ("tache3_topic", self.data_root / "scraped_speaking" / "La_tache_3")
        ]
        
        for category, speaking_dir in speaking_dirs:
            if not speaking_dir.exists():
                logger.warning(f"❌ Speaking {category} directory not found: {speaking_dir}")
                continue
                
            for json_file in speaking_dir.glob("*.json"):
                try:
                    test_identifier = json_file.stem
                    logger.info(f"  🎤 Processing speaking {category} {test_identifier}")
                    
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Handle different speaking test structures
                    if category == "tache2_month":
                        # Task 2: Month-based scenario tests
                        parties = data.get('parties', [])
                        for partie in parties:
                            partie_num = partie.get('partie_number')
                            taches = partie.get('taches', [])
                            
                            for i, tache in enumerate(taches, 1):
                                question_num = f"{partie_num}_{i}"
                                
                                # Prepare scenario data
                                scenario_data = {
                                    'month_year': data.get('month_year'),
                                    'partie_number': partie_num,
                                    'scenario': tache.get('scenario'),
                                    'example_questions': tache.get('example_questions', [])
                                }
                                
                                # Separate content
                                free_content, paid_content, content_flags = self.separate_speaking_content(scenario_data, category)
                                
                                self._upsert_question_improved(
                                    test_type="speaking",
                                    test_category=category,
                                    test_identifier=test_identifier,
                                    question_number=question_num,
                                    free_content=free_content,
                                    paid_content=paid_content,
                                    content_flags=content_flags
                                )
                                
                    else:  # tache3_topic
                        # Task 3: Topic-based discussions
                        taches = data.get('taches', [])
                        for tache in taches:
                            task_num = tache.get('task_number')
                            question_num = str(task_num)
                            
                            # Prepare topic data
                            topic_data = {
                                'topic': data.get('topic'),
                                'task_number': task_num,
                                'question': tache.get('question'),
                                'word_count': tache.get('word_count'),
                                'exemplary_answer': tache.get('exemplary_answer', {})
                            }
                            
                            # Separate content
                            free_content, paid_content, content_flags = self.separate_speaking_content(topic_data, category)
                            
                            self._upsert_question_improved(
                                test_type="speaking",
                                test_category=category,
                                test_identifier=test_identifier,
                                question_number=question_num,
                                free_content=free_content,
                                paid_content=paid_content,
                                content_flags=content_flags
                            )
                        
                except Exception as e:
                    logger.error(f"❌ Error processing {json_file}: {e}")
                    self.stats['errors'] += 1

    def build_transcript_from_chunks(self, chunks) -> str:
        """Build complete transcript text from chunks array"""
        if not chunks or not isinstance(chunks, list):
            return ""
        
        transcript_parts = []
        for chunk in chunks:
            if isinstance(chunk, dict) and 'text' in chunk:
                text = chunk['text'].strip()
                if text:
                    transcript_parts.append(text)
        
        return ' '.join(transcript_parts)

    def fetch_transcript_from_source(self, test_type: str, location: str) -> str:
        """Fetch transcript from source test if group test transcript is incomplete"""
        try:
            # Parse location string like "test4 Q8"
            location_match = re.match(r'test(\d+)\s+Q(\d+)', location)
            if not location_match:
                logger.debug(f"Could not parse location: {location}")
                return ""

            source_test_id = f"test{location_match.group(1)}"
            source_question = int(location_match.group(2))

            # Try premium first (since group tests are premium content)
            for category in ['premium', 'free']:
                response = self.supabase.table('test_questions').select('question_data').eq(
                    'test_type', test_type
                ).eq(
                    'test_category', category
                ).eq(
                    'test_identifier', source_test_id
                ).eq(
                    'question_number', source_question
                ).execute()

                if response.data:
                    source_data = response.data[0].get('question_data', {})
                    source_transcript = source_data.get('extracted_text', '')
                    if source_transcript:
                        logger.debug(f"Fetched transcript from {location} ({category}): {len(source_transcript)} chars")
                        return source_transcript

        except Exception as e:
            logger.debug(f"Error fetching transcript from {location}: {e}")

        return ""

    def sync_group_tests(self) -> None:
        """Sync group tests with complete asset and transcript data"""
        logger.info("🔄 Syncing group tests with enhanced asset and transcript support...")
        
        if not self.deduplicated_root.exists():
            logger.warning("❌ Deduplicated data directory not found")
            return
        
        for section in ['reading', 'listening']:
            section_dir = self.deduplicated_root / section
            if not section_dir.exists():
                logger.warning(f"❌ Section directory not found: {section_dir}")
                continue
                
            for group_file in section_dir.glob(f"{section}_group*.json"):
                group_id = group_file.stem.split('_group')[1]
                logger.info(f"  📚 Processing {section}_group{group_id}")
                
                try:
                    # Load group questions
                    with open(group_file, 'r', encoding='utf-8') as f:
                        questions = json.load(f)
                    
                    # Get answers for this group
                    group_answers_data = self.group_answers.get(section, {}).get(f'group{group_id}', {})
                    
                    # Sync each question
                    for i, question in enumerate(questions, 1):
                        question_number = str(i)
                        
                        # Build complete transcript - prioritize local file data (user preference)
                        extracted_text = ""

                        # FIRST PRIORITY: Use local file data (respects user preference for local files to take precedence)
                        local_text = question.get('extracted_text', '')
                        if local_text:
                            extracted_text = local_text

                        # SECOND PRIORITY: Only if local text is missing or too short, try chunks
                        if len(extracted_text) < 50 and question.get('chunks'):
                            chunks_text = self.build_transcript_from_chunks(question['chunks'])
                            if chunks_text and len(chunks_text) > len(extracted_text):
                                extracted_text = chunks_text

                        # LAST RESORT: Only if both local and chunks are insufficient, fetch from source
                        if len(extracted_text) < 50 and question.get('locations'):
                            for location in question['locations']:
                                source_transcript = self.fetch_transcript_from_source(section, location)
                                if source_transcript and len(source_transcript) > len(extracted_text):
                                    extracted_text = source_transcript
                                    break
                        
                        # Ensure chunks are properly loaded for listening tests
                        chunks = question.get('chunks', [])
                        if section == 'listening':
                            if chunks:
                                logger.debug(f"✅ Found {len(chunks)} chunks for listening group{group_id} question {question_number}")
                            else:
                                logger.warning(f"⚠️ No chunks found for listening group{group_id} question {question_number}")

                        # Log audio/image paths for debugging
                        audio_path = question.get('audio_path', '')
                        image_path = question.get('image_path', '')
                        if audio_path:
                            logger.debug(f"🎵 Audio path: {audio_path}")
                        if image_path:
                            logger.debug(f"🖼️ Image path: {image_path}")

                        # Update question with enhanced extracted_text and ensure all fields are present
                        enhanced_question = {
                            **question,  # Start with original question data
                            'extracted_text': extracted_text,  # Use enhanced extracted text
                            'chunks': chunks,  # Ensure chunks are included
                            'difficulty_group': int(group_id),  # Add group metadata
                        }

                        # Get correct answer using the same method as regular tests
                        # Group tests are premium content, so is_free=False
                        correct_answer = self._get_correct_answer(section, False, f'group{group_id}', question_number)

                        # Group tests content separation (EXACTLY same as regular reading/listening tests)
                        # Group tests are premium content - put all content in free_content for premium access
                        # Remove question_number and correct_answer from content (they have separate columns)
                        free_content = {k: v for k, v in enhanced_question.items()
                                      if k not in ['question_number', 'correct_answer']}
                        paid_content = {}
                        content_flags = {'has_paid_content': False, 'paid_fields': []}

                        # Create metadata (consistent with normal tests but with group-specific info)
                        metadata = {
                            'difficulty_group': int(group_id),
                            'has_audio': bool(enhanced_question.get('audio_path')),
                            'has_image': bool(enhanced_question.get('image_path')),
                            'has_chunks': bool(chunks),
                            'transcript_length': len(extracted_text),
                            'synced_from': 'group_files_unified_format',
                            'is_group_test': True
                        }

                        # Use the improved upsert method (EXACTLY same format as normal tests)
                        self._upsert_question_improved(
                            test_type=section,
                            test_category='difficulty',
                            test_identifier=f'group{group_id}',
                            question_number=question_number,  # Use string format like normal tests
                            free_content=free_content,
                            paid_content=paid_content,
                            content_flags=content_flags,
                            correct_answer=correct_answer,
                            metadata=metadata
                        )
                    
                    logger.info(f"    ✅ Synced {len(questions)} questions for {section}_group{group_id}")
                    
                except Exception as e:
                    logger.error(f"❌ Error processing {group_file}: {e}")
                    self.stats['errors'] += 1

# Usage example and CLI
def main():
    parser = argparse.ArgumentParser(description='Sync TCF Canada test data to Supabase')
    parser.add_argument('--type', choices=['reading', 'listening', 'writing', 'speaking', 'group', 'all'],
                       default='all', help='Test type to sync (all includes group tests)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be synced without actually syncing')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--force', action='store_true', help='Force update all records (local files override database)')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    syncer = ComprehensiveTestDataSyncer(force_update=args.force)
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No data will be modified")
    
    logger.info("🚀 Starting comprehensive sync with enhanced group test support...")
    logger.info(f"📁 Data root: {syncer.data_root}")
    logger.info(f"📁 Deduplicated root: {syncer.deduplicated_root}")
    logger.info(f"📁 Answer root: {syncer.answer_root}")

    if args.force:
        logger.info("🔄 FORCE MODE: Local files will override all database content")
    else:
        logger.info("📊 NORMAL MODE: Only changed data will be updated")
    
    # Sync based on type
    if args.type == 'all':
        if not args.dry_run:
            syncer.sync_reading_tests()
            syncer.sync_listening_tests()
            syncer.sync_writing_tests()
            syncer.sync_speaking_tests()
            syncer.sync_group_tests()
    else:
        if not args.dry_run:
            if args.type == 'reading':
                syncer.sync_reading_tests()
            elif args.type == 'listening':
                syncer.sync_listening_tests()
            elif args.type == 'writing':
                syncer.sync_writing_tests()
            elif args.type == 'speaking':
                syncer.sync_speaking_tests()
            elif args.type == 'group':
                syncer.sync_group_tests()
    
    logger.info("\n" + "="*60)
    logger.info("🎉 COMPREHENSIVE SYNC COMPLETED!")
    logger.info("="*60)
    logger.info(f"📊 Total processed: {syncer.stats['total_processed']}")
    logger.info(f"➕ Inserted: {syncer.stats['total_inserted']}")
    logger.info(f"✏️  Updated: {syncer.stats['total_updated']}")
    logger.info(f"⏭️  Skipped: {syncer.stats['total_skipped']}")
    logger.info(f"❌ Errors: {syncer.stats['errors']}")
    logger.info("="*60)
    
    if syncer.stats['errors'] > 0:
        logger.warning("⚠️  Some errors occurred. Check the log file for details.")
    
    # Show answer loading summary
    logger.info("📋 Answer Loading Summary:")
    for answer_type, answers in syncer.answers.items():
        logger.info(f"  {answer_type}: {len(answers)} items")
    
    # Show group answer summary
    logger.info("📚 Group Answer Summary:")
    for section, groups in syncer.group_answers.items():
        logger.info(f"  {section}: {len(groups)} groups")

if __name__ == "__main__":
    main() 