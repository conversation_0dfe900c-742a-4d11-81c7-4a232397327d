# TCF Canada Database Management

This directory contains all database-related scripts and configurations for the TCF Canada application. The system manages comprehensive test data including reading, listening, writing, speaking tests, and difficulty-based group tests.

## 📁 Directory Structure

```
database/
├── sync_to_supabase.py      # Main sync script for all test types
├── test_data_service.py     # Database service layer for accessing test data
├── grading_service.py       # Test grading and scoring service
├── sync.sh                  # Shell script for automated sync workflows
├── schema.sql               # Complete database schema definition
├── enable_rls_policies.sql  # Row Level Security policies
├── manage_rls.sql           # RLS management utilities
├── reset_test_questions_table.sql  # Table reset script
└── README.md               # This documentation
```

## 🚀 Quick Start

### 1. Sync All Test Data
```bash
# Sync all test types (regular tests + group tests)
python3 sync_to_supabase.py

# Sync specific test type only
python3 sync_to_supabase.py --type listening

# Dry run to see what would be synced
python3 sync_to_supabase.py --dry-run --verbose
```

### 2. Using the Automated Sync Script
```bash
# Run the complete sync workflow
./sync.sh
```

## 📚 Test Data Types

### Regular Tests
- **Reading Tests**: Text comprehension with multiple choice questions
  - Free tests: `test1`, `test2` (publicly available)
  - Premium tests: `test3+` (requires membership)

- **Listening Tests**: Audio comprehension with transcripts
  - Free tests: `test1`, `test2` (publicly available)  
  - Premium tests: `test3+` (requires membership)

- **Writing Tests**: Expression écrite by month
  - All writing tests require membership
  - Organized by month (e.g., `janvier-2025`)

- **Speaking Tests**: Expression orale with two task types
  - Task 2: Scenario-based (`tache2_month`)
  - Task 3: Topic-based discussions (`tache3_topic`)
  - All speaking tests require membership

### Group Tests (Difficulty-Based)
- **Purpose**: Tests organized by difficulty level instead of chronologically
- **Categories**: `group1` (easiest) to `group5` (hardest)
- **Features**:
  - Complete transcripts extracted from chunks and source tests
  - Consistent asset path format with normal tests
  - Unified path structure: `listening_asset/media_test12/Q1.mp3`

## 🔧 Core Scripts

### sync_to_supabase.py

The main synchronization script with comprehensive functionality:

**Features:**
- Syncs all test types with proper content separation
- Enhanced group test support with complete asset management
- Transcript extraction from multiple sources
- Proper answer handling and security
- Comprehensive error handling and logging

**Usage:**
```bash
# Sync all data
python3 sync_to_supabase.py

# Sync specific type
python3 sync_to_supabase.py --type group

# Available types: reading, listening, writing, speaking, group, all
```

**Options:**
- `--type`: Choose specific test type or 'all'
- `--dry-run`: Preview changes without applying them
- `--verbose`: Enable detailed logging

### test_data_service.py

Database service layer providing:
- Secure test data access with membership validation
- Group test data with complete asset paths
- Access control for free vs premium content
- Available test listing with proper filtering

**Key Methods:**
- `get_test_data()`: Retrieve regular test questions
- `get_group_test_data()`: Retrieve difficulty-based group tests
- `get_available_tests()`: List accessible tests per user type

### grading_service.py

Test grading and assessment functionality:
- Automated grading for multiple choice questions
- Score calculation and performance tracking
- Support for different test types and formats

## 🗄️ Database Schema

### Core Tables

**test_questions**: Main table storing all test data
```sql
- id: Primary key
- test_type: reading/listening/writing/speaking
- test_category: free/premium/difficulty/month/tache2_month/tache3_topic
- test_identifier: test1, group1, janvier-2025, etc.
- question_number: Question sequence number
- question_data: Complete question content (JSONB)
- metadata: Additional tracking information (JSONB)
- correct_answer: Encrypted correct answers (JSONB)
- created_at/updated_at: Timestamps
```

### Data Organization

**Test Categories:**
- `free`: Publicly available tests (reading/listening test1-2)
- `premium`: Membership-required tests (reading/listening test3+)
- `difficulty`: Group tests organized by difficulty (group1-5)
- `month`: Monthly writing tests (janvier-2025, etc.)
- `tache2_month`: Speaking task 2 by month
- `tache3_topic`: Speaking task 3 by topic

## 🔒 Security & Access Control

### Row Level Security (RLS)
- Enabled on all sensitive tables
- User-based access control for premium content
- Secure answer storage with restricted access

### Content Separation
- Free content: Available to all users
- Premium content: Requires active membership
- Answers: Stored separately with admin-only access

## 📊 Asset Management

### Unified Asset Paths
All tests (regular and group) use consistent path format:
```
{test_type}_asset/media_test{id}/Q{number}.mp3
{test_type}_asset/media_test{id}/Q{number}.webp
```

Examples:
- `listening_asset/media_test12/Q1.mp3`
- `reading_asset/media_test5/Q15.webp`

### Audio Transcripts
- Built from chunks array when available
- Fallback to source test transcripts
- Complete transcripts for all group test questions

## 📝 Logging & Monitoring

### Sync Logging
- Comprehensive logging to `sync_to_supabase.log`
- Real-time console output with progress indicators
- Error tracking and statistics

### Performance Metrics
- Questions processed/inserted/updated/skipped
- Error count and details
- Answer loading summary
- Processing time tracking

## 🔄 Data Flow

1. **Source Data**: JSON files in `/data/scraped/` and `/data/deduplicated/`
2. **Answer Files**: Correct answers in `/data/scraped/scraped_answer/`
3. **Sync Process**: `sync_to_supabase.py` processes and uploads
4. **Database Storage**: Supabase with proper categorization
5. **API Access**: `test_data_service.py` provides secure access
6. **Asset Serving**: Unified path handling for all test types

## 🛠️ Maintenance Tasks

### Database Reset
```bash
# Reset test questions table (CAUTION: Destructive)
psql -f reset_test_questions_table.sql
```

### RLS Management
```bash
# Enable/disable Row Level Security
psql -f manage_rls.sql
```

### Schema Updates
```bash
# Apply latest schema
psql -f schema.sql
```

## 🐛 Troubleshooting

### Common Issues

**Sync Failures:**
- Check Supabase credentials in environment
- Verify source data file paths
- Review logs for specific error messages

**Missing Transcripts:**
- Ensure chunks data is available in source files
- Check source test references in locations array
- Verify database connectivity for transcript fetching

**Asset Path Issues:**
- Verify asset file existence in source directories
- Check path format consistency across test types
- Ensure asset serving endpoint can handle unified paths

### Debug Commands
```bash
# Verbose sync with detailed logging
python3 sync_to_supabase.py --verbose --dry-run

# Test specific group
python3 sync_to_supabase.py --type group --verbose
```

## 📈 Statistics & Monitoring

The sync process provides comprehensive statistics:
- **Total Questions**: Complete count across all test types
- **Asset Coverage**: Audio/image availability tracking  
- **Transcript Quality**: Character count and completeness
- **Error Tracking**: Failed operations and reasons
- **Performance Metrics**: Processing speed and efficiency

## 🔮 Future Enhancements

- Automated sync scheduling
- Enhanced error recovery
- Performance optimization for large datasets
- Advanced analytics and reporting
- Real-time sync monitoring dashboard

---

For technical support or questions about the database system, please refer to the application documentation or contact the development team.