#!/bin/bash

# TCF Canada - Complete Test Data Sync Script
# Handles both regular tests and group tests with RLS management

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Function to show usage
show_usage() {
    echo -e "${GREEN}Usage:${NC}"
    echo "  $0 [options]"
    echo ""
    echo -e "${GREEN}Sync Options:${NC}"
    echo "  --all                  # Sync all data (regular + groups) [DEFAULT]"
    echo "  --regular              # Sync only regular tests (reading, listening, writing, speaking)"
    echo "  --groups               # Sync only group tests (difficulty-based)"
    echo "  --analysis             # Sync only analysis data for reading questions"
    echo "  --type <types>         # Sync specific test types: reading, listening, writing, speaking"
    echo ""
    echo -e "${GREEN}Control Options:${NC}"
    echo "  --dry-run              # Show what would be synced without actually syncing"
    echo "  --verbose              # Enable detailed logging"
    echo "  --skip-rls             # Skip RLS disable/enable (use if RLS already disabled)"
    echo "  --help                 # Show this help message"
    echo ""
    echo -e "${GREEN}Examples:${NC}"
    echo "  $0                     # Sync all data (regular + groups)"
    echo "  $0 --regular           # Sync only regular tests"
    echo "  $0 --groups            # Sync only group tests"
    echo "  $0 --analysis          # Sync only analysis data"
    echo "  $0 --type reading      # Sync only reading tests"
    echo "  $0 --type writing speaking  # Sync writing and speaking tests"
    echo "  $0 --groups --verbose  # Sync groups with detailed logging"
    echo "  $0 --dry-run           # Preview what would be synced"
}

# Function to check dependencies
check_dependencies() {
    print_step "Checking dependencies..."
    
    if [ ! -f "$BACKEND_DIR/app/config.py" ]; then
        print_error "Could not find backend app directory"
        exit 1
    fi
    
    if ! command -v python3 &> /dev/null; then
        print_error "python3 is required but not installed"
        exit 1
    fi
    
    # Check if virtual environment is activated
    if [ -z "$VIRTUAL_ENV" ]; then
        print_warning "No virtual environment detected"
        print_warning "It's recommended to run this in the backend virtual environment"
        echo ""
    fi
    
    print_success "Dependencies check completed"
}

# Function to manage RLS
manage_rls() {
    local action=$1  # "disable" or "enable"
    
    if [ "$SKIP_RLS" = true ]; then
        print_warning "Skipping RLS $action (--skip-rls flag used)"
        return 0
    fi
    
    # Capitalize first letter (compatible with older bash)
    local action_capitalized
    if [ "$action" = "disable" ]; then
        action_capitalized="Disabling"
    else
        action_capitalized="Enabling"
    fi
    
    print_step "$action_capitalized Row Level Security..."
    
    local sql_command
    if [ "$action" = "disable" ]; then
        sql_command="ALTER TABLE test_questions DISABLE ROW LEVEL SECURITY;"
    else
        sql_command="ALTER TABLE test_questions ENABLE ROW LEVEL SECURITY;"
    fi
    
    print_warning "Please run this SQL command in your Supabase SQL Editor:"
    echo -e "${CYAN}"
    echo "$sql_command"
    echo -e "${NC}"
    
    read -p "Press Enter after running the SQL command above..."
    print_success "RLS $action completed"
}

# Function to sync regular tests
sync_regular_tests() {
    print_step "Syncing regular test data..."
    
    local args=()
    
    if [ ${#SPECIFIC_TYPES[@]} -gt 0 ]; then
        # Sync each type separately for better control
        for type in "${SPECIFIC_TYPES[@]}"; do
            local type_args=(--type "$type")
            
            if [ "$VERBOSE" = true ]; then
                type_args+=(--verbose)
            fi
            
            if [ "$DRY_RUN" = true ]; then
                type_args+=(--dry-run)
            fi
            
            print_status "Running: python3 database/sync_to_supabase.py ${type_args[*]}"
            
            if ! python3 database/sync_to_supabase.py "${type_args[@]}"; then
                print_error "Sync failed for test type: $type"
                return 1
            fi
        done
    else
        # Sync all regular test types (excluding group)
        for type in reading listening writing speaking; do
            local type_args=(--type "$type")
            
            if [ "$VERBOSE" = true ]; then
                type_args+=(--verbose)
            fi
            
            if [ "$DRY_RUN" = true ]; then
                type_args+=(--dry-run)
            fi
            
            print_status "Running: python3 database/sync_to_supabase.py ${type_args[*]}"
            
            if ! python3 database/sync_to_supabase.py "${type_args[@]}"; then
                print_error "Sync failed for test type: $type"
                return 1
            fi
        done
    fi
    
    print_success "Regular tests synced successfully"

    # Sync classified writing tasks only if writing is being synced
    local should_sync_classified_writing=false
    
    if [ ${#SPECIFIC_TYPES[@]} -gt 0 ]; then
        # Check if writing is in the specific types
        for type in "${SPECIFIC_TYPES[@]}"; do
            if [ "$type" = "writing" ]; then
                should_sync_classified_writing=true
                break
            fi
        done
    else
        # If no specific types, we're syncing all regular types (includes writing)
        should_sync_classified_writing=true
    fi
    
    if [ "$should_sync_classified_writing" = true ]; then
        print_step "Syncing classified writing tasks..."
        local classified_args=()

        if [ "$VERBOSE" = true ]; then
            classified_args+=(--verbose)
        fi

        if [ "$DRY_RUN" = true ]; then
            classified_args+=(--dry-run)
        fi

        print_status "Running: python3 database/sync_classified_writing.py ${classified_args[*]}"

        if ! python3 database/sync_classified_writing.py "${classified_args[@]}"; then
            print_error "Sync failed for classified writing tasks"
            return 1
        fi

        print_success "Classified writing tasks synced successfully"
    fi
    return 0
}

# Function to sync group tests
sync_group_tests() {
    print_step "Syncing group test data..."
    
    local args=(--type group)
    
    if [ "$VERBOSE" = true ]; then
        args+=(--verbose)
    fi
    
    if [ "$DRY_RUN" = true ]; then
        args+=(--dry-run)
    fi
    
    print_status "Running: python3 database/sync_to_supabase.py ${args[*]}"
    
    if python3 database/sync_to_supabase.py "${args[@]}"; then
        print_success "Group tests synced successfully"
        return 0
    else
        print_error "Group tests sync failed"
        return 1
    fi
}

# Function to sync analysis data
sync_analysis() {
    print_step "Syncing analysis data for reading questions..."
    
    local args=()
    
    if [ "$VERBOSE" = true ]; then
        args+=(--verbose)
    fi
    
    if [ "$DRY_RUN" = true ]; then
        args+=(--dry-run)
    fi
    
    print_status "Running: python3 database/sync_analysis.py ${args[*]}"
    
    if python3 database/sync_analysis.py "${args[@]}"; then
        print_success "Analysis data synced successfully"
        return 0
    else
        print_error "Analysis sync failed"
        return 1
    fi
}

# Function to verify sync results
verify_sync() {
    print_step "Verifying sync results..."
    
    cat > /tmp/verify_sync.py << 'EOF'
#!/usr/bin/env python3
import os
import sys
from dotenv import load_dotenv
from supabase import create_client

load_dotenv()

try:
    supabase = create_client(os.environ['SUPABASE_URL'], os.environ['SUPABASE_KEY'])
    
    # Check total questions
    result = supabase.table('test_questions').select('test_type, test_category, test_identifier').execute()
    total = len(result.data)
    
    # Count by type and category
    types = {}
    categories = {}
    
    for record in result.data:
        test_type = record['test_type']
        test_category = record['test_category']
        
        types[test_type] = types.get(test_type, 0) + 1
        categories[test_category] = categories.get(test_category, 0) + 1
    
    print(f"📊 Total questions in database: {total}")
    print("\n📋 By test type:")
    for test_type, count in sorted(types.items()):
        print(f"  - {test_type}: {count} questions")
    
    print("\n🏷️  By category:")
    for category, count in sorted(categories.items()):
        print(f"  - {category}: {count} questions")
    
    # Check for group tests specifically
    if 'difficulty' in categories:
        print(f"\n🎯 Group tests found: {categories['difficulty']} questions")
    else:
        print("\n❌ No group tests found")
    
except Exception as e:
    print(f"❌ Verification failed: {e}")
    sys.exit(1)
EOF
    
    if python3 /tmp/verify_sync.py; then
        rm -f /tmp/verify_sync.py
        print_success "Sync verification completed"
        return 0
    else
        rm -f /tmp/verify_sync.py
        print_error "Verification failed"
        return 1
    fi
}

# Parse command line arguments
SYNC_REGULAR=false
SYNC_GROUPS=false
SYNC_ALL=false
SYNC_ANALYSIS=false
DRY_RUN=false
VERBOSE=false
SKIP_RLS=false
SPECIFIC_TYPES=()

if [ $# -eq 0 ]; then
    SYNC_ALL=true
fi

while [[ $# -gt 0 ]]; do
    case $1 in
        --all)
            SYNC_ALL=true
            shift
            ;;
        --regular)
            SYNC_REGULAR=true
            shift
            ;;
        --groups)
            SYNC_GROUPS=true
            shift
            ;;
        --analysis)
            SYNC_ANALYSIS=true
            shift
            ;;
        --type)
            SYNC_REGULAR=true
            shift
            while [[ $# -gt 0 && ! "$1" =~ ^-- ]]; do
                case $1 in
                    reading|listening|writing|speaking)
                        SPECIFIC_TYPES+=("$1")
                        ;;
                    *)
                        print_error "Invalid test type: $1"
                        print_error "Valid types: reading, listening, writing, speaking"
                        exit 1
                        ;;
                esac
                shift
            done
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --skip-rls)
            SKIP_RLS=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Determine what to sync
if [ "$SYNC_ALL" = true ]; then
    SYNC_REGULAR=true
    SYNC_GROUPS=true
fi

# Main execution
main() {
    echo -e "${CYAN}"
    echo "============================================================"
    echo "🚀 TCF Canada Complete Test Data Sync"
    echo "============================================================"
    echo -e "${NC}"
    
    # Show what will be synced
    echo -e "${YELLOW}Sync Plan:${NC}"
    if [ "$SYNC_REGULAR" = true ]; then
        if [ ${#SPECIFIC_TYPES[@]} -gt 0 ]; then
            echo "  📚 Regular tests: ${SPECIFIC_TYPES[*]}"
        else
            echo "  📚 Regular tests: all types (reading, listening, writing, speaking)"
        fi
    fi
    if [ "$SYNC_GROUPS" = true ]; then
        echo "  🎯 Group tests: difficulty-based"
    fi
    if [ "$SYNC_ANALYSIS" = true ]; then
        echo "  📝 Analysis data: reading questions (en, cn, fr)"
    fi
    if [ "$DRY_RUN" = true ]; then
        echo "  🔍 Mode: DRY RUN (preview only)"
    fi
    if [ "$VERBOSE" = true ]; then
        echo "  📝 Logging: verbose"
    fi
    echo ""
    
    # Confirm with user
    if [ "$DRY_RUN" != true ]; then
        read -p "🤔 Do you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Sync cancelled by user"
            exit 0
        fi
    fi
    
    # Change to backend directory
    cd "$BACKEND_DIR"
    
    # Run checks
    check_dependencies
    
    # Disable RLS if needed
    if [ "$DRY_RUN" != true ] && ([ "$SYNC_REGULAR" = true ] || [ "$SYNC_GROUPS" = true ] || [ "$SYNC_ANALYSIS" = true ]); then
        manage_rls "disable"
    fi
    
    # Sync operations
    SYNC_ERRORS=0
    
    if [ "$SYNC_REGULAR" = true ]; then
        sync_regular_tests || SYNC_ERRORS=$((SYNC_ERRORS + 1))
    fi
    
    if [ "$SYNC_GROUPS" = true ]; then
        sync_group_tests || SYNC_ERRORS=$((SYNC_ERRORS + 1))
    fi
    
    if [ "$SYNC_ANALYSIS" = true ]; then
        sync_analysis || SYNC_ERRORS=$((SYNC_ERRORS + 1))
    fi
    
    # Re-enable RLS if needed
    if [ "$DRY_RUN" != true ] && ([ "$SYNC_REGULAR" = true ] || [ "$SYNC_GROUPS" = true ] || [ "$SYNC_ANALYSIS" = true ]); then
        manage_rls "enable"
    fi
    
    # Verify results
    if [ "$DRY_RUN" != true ]; then
        verify_sync || SYNC_ERRORS=$((SYNC_ERRORS + 1))
    fi
    
    # Final status
    echo ""
    echo -e "${CYAN}"
    echo "============================================================"
    if [ "$DRY_RUN" = true ]; then
        echo -e "${BLUE}🔍 DRY RUN COMPLETED - No actual changes made${NC}"
    elif [ $SYNC_ERRORS -eq 0 ]; then
        echo -e "${GREEN}🎉 ALL SYNC OPERATIONS COMPLETED SUCCESSFULLY!${NC}"
    else
        echo -e "${YELLOW}⚠️  SYNC COMPLETED WITH $SYNC_ERRORS ERROR(S)${NC}"
    fi
    echo "============================================================"
    echo -e "${NC}"
}

# Run main function
main "$@" 