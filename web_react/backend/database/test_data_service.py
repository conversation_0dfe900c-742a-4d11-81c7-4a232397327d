"""
Test Data Service for Supabase Integration
This service provides test data from Supabase with proper access control
"""

import json
from typing import Dict, List, Any, Optional
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class TestDataService:
    """Service for accessing test data from Supabase"""
    
    def __init__(self, supabase_client):
        self.supabase = supabase_client
    
    def get_test_data(self, test_type: str, test_identifier: str, is_free: bool = False, 
                     user_is_premium: bool = False) -> Optional[List[Dict[str, Any]]]:
        """
        Get test data with proper access control
        
        Args:
            test_type: reading, listening, writing, speaking
            test_identifier: test1, test2, janvier-2025, Culture, etc.
            is_free: whether to fetch free content specifically
            user_is_premium: whether user has premium membership
        
        Returns:
            List of questions with appropriate content based on membership
        """
        try:
            # Determine category based on test type and access level
            if test_type in ['reading', 'listening']:
                if is_free:
                    category = 'free'
                else:
                    category = 'premium'
            elif test_type == 'writing':
                category = 'month'
            elif test_type == 'speaking':
                # Try both speaking categories
                return self._get_speaking_data(test_identifier, user_is_premium)
            else:
                logger.error(f"Unknown test type: {test_type}")
                return None
            
            # Query database (we'll sort client-side due to Supabase limitations)
            response = self.supabase.table('test_questions').select('*').eq(
                'test_type', test_type
            ).eq(
                'test_category', category
            ).eq(
                'test_identifier', test_identifier
            ).execute()  # Remove order() - we'll sort client-side
            
            if not response.data:
                logger.warning(f"No data found for {test_type}/{category}/{test_identifier}")
                return None
            
            # Sort by question_number numerically (client-side)
            sorted_records = sorted(response.data, key=lambda x: int(x.get('question_number', '0') or '0'))
            
            # Process questions with access control
            questions = []
            for record in sorted_records:
                question_data = self._build_question_content(record, user_is_premium, test_type)
                if question_data:  # Only add if user has access
                    questions.append(question_data)
            
            logger.info(f"Retrieved {len(questions)} questions for {test_type}/{test_identifier} (premium: {user_is_premium})")
            return questions
            
        except Exception as e:
            logger.error(f"Error fetching test data {test_type}/{test_identifier}: {e}")
            return None
    
    def get_group_test_data(self, test_type: str, group_id: int, user_is_premium: bool = False) -> Optional[List[Dict[str, Any]]]:
        """
        Get group test data (difficulty-based) - requires membership
        
        Args:
            test_type: reading or listening
            group_id: 1, 2, 3, 4, or 5
            user_is_premium: whether user has premium membership
        
        Returns:
            List of questions if user has access, None otherwise
        """
        try:
            # Group tests require membership
            if not user_is_premium:
                logger.warning(f"User without membership tried to access group test {test_type}/group{group_id}")
                return None
            
            # Query database for group test (client-side sorting)
            response = self.supabase.table('test_questions').select('*').eq(
                'test_type', test_type
            ).eq(
                'test_category', 'difficulty'
            ).eq(
                'test_identifier', f'group{group_id}'
            ).execute()  # Remove order() - we'll sort client-side
            
            if not response.data:
                logger.warning(f"No group data found for {test_type}/group{group_id}")
                return None
            
            # Sort by question_number numerically (client-side)
            sorted_records = sorted(response.data, key=lambda x: int(x.get('question_number', '0') or '0'))
            
            # Process questions - enhance with proper asset paths and complete data
            questions = []
            for i, record in enumerate(sorted_records):  # Use sorted_records instead of response.data
                question_data = record.get('question_data', {})
                
                # Add metadata
                metadata = record.get('metadata', {})
                if metadata:
                    question_data.update(metadata)
                
                # Essential: Add question number for frontend reference
                question_number = i + 1  # 1-indexed question numbers
                question_data['question_number'] = question_number
                
                # Add question ID for bookmarking
                question_data['question_id'] = record.get('id')
                
                # Note: audio_path and image_path are already set correctly in the database
                # No need to override them here - use the values from the database as-is
                
                # Ensure extracted_text (transcript) is available
                # If not present or empty, try to fetch from source test
                if not question_data.get('extracted_text') and question_data.get('locations'):
                    try:
                        # Try to get transcript from source test
                        location = question_data['locations'][0]  # Use first location
                        import re
                        location_match = re.match(r'test(\d+)\s+Q(\d+)', location)
                        if location_match:
                            source_test_id = f"test{location_match.group(1)}"
                            source_question = int(location_match.group(2))
                            
                            # Query source test for transcript
                            source_response = self.supabase.table('test_questions').select('question_data').eq(
                                'test_type', test_type
                            ).eq(
                                'test_identifier', source_test_id
                            ).eq(
                                'question_number', source_question
                            ).execute()
                            
                            if source_response.data:
                                source_data = source_response.data[0].get('question_data', {})
                                source_transcript = source_data.get('extracted_text', '')
                                if source_transcript:
                                    question_data['extracted_text'] = source_transcript
                                    logger.debug(f"Fetched transcript from {location} for group{group_id} Q{question_number}")
                    except Exception as e:
                        logger.warning(f"Could not fetch transcript from source for group{group_id} Q{question_number}: {e}")
                
                # Ensure we have all the standard fields that normal listening tests have
                standard_fields = {
                    'chunks': question_data.get('chunks', []),
                    'choices': question_data.get('choices', {}),
                    'extracted_text': question_data.get('extracted_text', ''),
                    'question_text': question_data.get('question_text', ''),
                    'locations': question_data.get('locations', []),
                    'difficulty_group': question_data.get('difficulty_group', group_id)
                }
                
                # Merge all fields
                final_question_data = {**question_data, **standard_fields}
                
                logger.debug(f"Group {group_id} Q{question_number}: audio={final_question_data.get('audio_path', 'N/A')}, image={final_question_data.get('image_path', 'N/A')}, transcript_length={len(final_question_data.get('extracted_text', ''))}")
                
                questions.append(final_question_data)
            
            logger.info(f"Retrieved {len(questions)} group questions for {test_type}/group{group_id} with complete asset paths and transcripts")
            return questions
            
        except Exception as e:
            logger.error(f"Error fetching group test data {test_type}/group{group_id}: {e}")
            return None
    
    def _build_question_content(self, record: Dict[str, Any], user_is_premium: bool, test_type: str) -> Optional[Dict[str, Any]]:
        """Build question content based on user's membership level"""
        try:
            question_data = record.get('question_data', {})
            metadata = record.get('metadata', {})
            free_content = record.get('free_content', {})
            paid_content = record.get('paid_content', {})
            
            # For free tests, return as-is (no access control needed)
            if record.get('test_category') == 'free':
                result = dict(question_data)
                if metadata:
                    result.update(metadata)
                # Add question ID for bookmarking
                result['question_id'] = record.get('id')
                
                # Add cross-reference locations for regular tests
                if not result.get('locations'):
                    result['locations'] = self._get_question_locations(
                        test_type, record.get('test_identifier'), record.get('question_number')
                    )
                
                return result
            
            # Build content based on membership level
            content = {}
            
            # Always include free content
            if free_content:
                content.update(free_content)
            
            # Add paid content for premium members
            if user_is_premium and paid_content:
                content.update(paid_content)
            
            # Add question_data as base
            if question_data:
                # Start with question_data, then overlay free/paid content
                result = dict(question_data)
                result.update(content)
            else:
                result = content
            
            # Add metadata
            if metadata:
                result.update(metadata)
                
            # Add question ID for bookmarking
            result['question_id'] = record.get('id')
            
            # Add cross-reference locations if not already present
            if not result.get('locations'):
                result['locations'] = self._get_question_locations(
                    test_type, record.get('test_identifier'), record.get('question_number')
                )
            
            # Only return if there's accessible content
            return result if content or question_data else None
            
        except Exception as e:
            logger.error(f"Error building question content: {e}")
            return None
    
    def _get_question_locations(self, test_type: str, current_test_id: str, question_number: int) -> List[str]:
        """Get cross-reference locations where this question appears in other tests"""
        try:
            # For now, return empty list for regular tests as locations are mainly used for group tests
            # In the future, this could be enhanced to find similar questions across tests
            if current_test_id and current_test_id.startswith('test'):
                return [f"{current_test_id} Q{question_number}"]
            return []
        except Exception as e:
            logger.error(f"Error getting question locations: {e}")
            return []
    
    def _get_speaking_data(self, test_identifier: str, user_is_premium: bool) -> Optional[List[Dict[str, Any]]]:
        """Get speaking test data with proper free/paid content separation"""
        try:
            # Try task 2 (month-based) first (client-side sorting)
            response = self.supabase.table('test_questions').select('*').eq(
                'test_type', 'speaking'
            ).eq(
                'test_category', 'tache2_month'
            ).eq(
                'test_identifier', test_identifier
            ).execute()  # Remove order() - we'll sort client-side
            
            if response.data:
                # Sort by question_number numerically (client-side)
                sorted_records = sorted(response.data, key=lambda x: int(x.get('question_number', '0') or '0'))
                
                questions = []
                for record in sorted_records:
                    # Use the proper access control method that handles free/paid content
                    question_content = self._build_question_content(record, user_is_premium, 'speaking')
                    if question_content:
                        questions.append(question_content)
                return questions if questions else None
            
            # Try task 3 (topic-based) (client-side sorting)
            response = self.supabase.table('test_questions').select('*').eq(
                'test_type', 'speaking'
            ).eq(
                'test_category', 'tache3_topic'
            ).eq(
                'test_identifier', test_identifier
            ).execute()  # Remove order() - we'll sort client-side
            
            if response.data:
                # Sort by question_number numerically (client-side)
                sorted_records = sorted(response.data, key=lambda x: int(x.get('question_number', '0') or '0'))
                
                questions = []
                for record in sorted_records:
                    # Use the proper access control method that handles free/paid content
                    question_content = self._build_question_content(record, user_is_premium, 'speaking')
                    if question_content:
                        questions.append(question_content)
                return questions if questions else None
            
            logger.warning(f"No speaking data found for {test_identifier}")
            return None
            
        except Exception as e:
            logger.error(f"Error fetching speaking data {test_identifier}: {e}")
            return None
    
    def get_writing_test_summary(self, test_identifier: str) -> Dict[str, Any]:
        """Get summary statistics for a writing test (fast, for list cards)"""
        try:
            # Get basic stats from database without loading full content
            # Use the same query structure as get_test_data for consistency
            response = self.supabase.table('test_questions').select(
                'free_content,paid_content,question_data,metadata'
            ).eq(
                'test_type', 'writing'
            ).eq(
                'test_identifier', test_identifier
            ).eq(
                'test_category', 'month'
            ).execute()
            
            if not response.data:
                return {
                    'month_year': test_identifier,
                    'total_tasks': 0,
                    'total_combinations': 0,
                    'has_corrections': False
                }
            
            # Count unique combinations and tasks by parsing the actual data structure
            combinations = set()
            total_tasks = 0
            has_corrections = False
            month_year = test_identifier
            
            for record in response.data:
                free_content = record.get('free_content', {})
                paid_content = record.get('paid_content', {})
                question_data = record.get('question_data', {})
                metadata = record.get('metadata', {})
                
                # Get combination_number from various possible locations
                combination_number = (
                    free_content.get('combination_number') or
                    question_data.get('combination_number') or
                    metadata.get('combination_number') or
                    '1'
                )
                
                combinations.add(str(combination_number))
                total_tasks += 1
                
                # Get month_year from the most likely source
                month_year = (
                    free_content.get('month_year') or
                    question_data.get('month_year') or
                    metadata.get('month_year') or
                    test_identifier
                )
                
                # Check if any corrections exist
                correction_content = (
                    paid_content.get('correction_content') if paid_content else None or
                    question_data.get('correction_content') if question_data else None
                )
                if correction_content:
                    has_corrections = True
            
            print(f"📝 Summary for {test_identifier}: {total_tasks} tasks, {len(combinations)} combinations, corrections: {has_corrections}")
            
            return {
                'month_year': month_year,
                'total_tasks': total_tasks,
                'total_combinations': len(combinations),
                'has_corrections': has_corrections
            }
            
        except Exception as e:
            logger.error(f"Error getting writing test summary for {test_identifier}: {e}")
            return {
                'month_year': test_identifier,
                'total_tasks': 0,
                'total_combinations': 0,
                'has_corrections': False
            }
    
    def get_speaking_test_summary(self, test_identifier: str) -> Dict[str, Any]:
        """Get summary statistics for a speaking test (fast, for list cards)"""
        try:
            # Try both speaking categories
            for category in ['tache2_month', 'tache3_topic']:
                response = self.supabase.table('test_questions').select(
                    'free_content,paid_content,question_data,metadata'
                ).eq(
                    'test_type', 'speaking'
                ).eq(
                    'test_identifier', test_identifier
                ).eq(
                    'test_category', category
                ).execute()
                
                if response.data:
                    # Found data for this category
                    total_scenarios = len(response.data)
                    total_questions = 0
                    total_parties = 0
                    month_year = test_identifier
                    
                    # Determine task type
                    task_type = 'tache3' if category == 'tache3_topic' else 'tache2'
                    
                    if category == 'tache2_month':
                        # Task 2: Count parties and questions
                        parties = set()
                        for record in response.data:
                            free_content = record.get('free_content', {})
                            paid_content = record.get('paid_content', {})
                            question_data = record.get('question_data', {})
                            metadata = record.get('metadata', {})
                            
                            # Get month_year
                            month_year = (
                                free_content.get('month_year') or
                                question_data.get('month_year') or
                                metadata.get('month_year') or
                                test_identifier
                            )
                            
                            # Count partie numbers
                            partie_num = (
                                free_content.get('partie_number') or
                                question_data.get('partie_number') or
                                metadata.get('partie_number')
                            )
                            if partie_num:
                                parties.add(str(partie_num))
                            
                            # Count example questions
                            example_questions = (
                                free_content.get('example_questions') or
                                paid_content.get('example_questions') or
                                question_data.get('example_questions') or
                                []
                            )
                            total_questions += len(example_questions) if example_questions else 0
                        
                        total_parties = len(parties)
                        
                    else:
                        # Task 3: Count questions and tasks
                        for record in response.data:
                            free_content = record.get('free_content', {})
                            question_data = record.get('question_data', {})
                            metadata = record.get('metadata', {})
                            
                            # Get topic/month_year
                            month_year = (
                                free_content.get('topic') or
                                question_data.get('topic') or
                                metadata.get('topic') or
                                test_identifier
                            )
                            
                            # For Task 3, each record is typically one question
                            total_questions += 1
                    
                    print(f"🎤 Summary for {test_identifier} ({category}): {total_scenarios} items, {total_parties} parties, {total_questions} questions")
                    
                    return {
                        'month_year': month_year,
                        'total_scenarios': total_scenarios,
                        'total_parties': total_parties,
                        'total_questions': total_questions,
                        'task_type': task_type
                    }
            
            # If no data found in either category
            return {
                'month_year': test_identifier,
                'total_scenarios': 0,
                'total_parties': 0,
                'total_questions': 0,
                'task_type': 'tache2'
            }
            
        except Exception as e:
            logger.error(f"Error getting speaking test summary for {test_identifier}: {e}")
            return {
                'month_year': test_identifier,
                'total_scenarios': 0,
                'total_parties': 0,
                'total_questions': 0,
                'task_type': 'tache2'
            }
    
    def get_available_tests(self, test_type: str, user_is_premium: bool = False) -> List[Dict[str, Any]]:
        """Get list of available tests for a test type - now shows all tests regardless of membership for marketing purposes"""
        try:
            available_tests = []
            
            def sort_test_identifiers(identifiers):
                """Sort test identifiers numerically instead of lexicographically"""
                def extract_number(identifier):
                    # Extract number from test identifier (e.g., "test1" -> 1, "1" -> 1)
                    import re
                    match = re.search(r'(\d+)', identifier)
                    return int(match.group(1)) if match else 999999
                
                return sorted(identifiers, key=extract_number)
            
            if test_type in ['reading', 'listening']:
                # Free tests (always available and accessible)
                response = self.supabase.table('test_questions').select('test_identifier').eq(
                    'test_type', test_type
                ).eq(
                    'test_category', 'free'
                ).limit(2000).execute()
                
                free_identifiers = list(set(record['test_identifier'] for record in response.data))
                for identifier in sort_test_identifiers(free_identifiers):
                    available_tests.append({
                        'id': identifier,
                        'title': f'Test de Compréhension {"Orale" if test_type == "listening" else "Écrite"} {identifier}',
                        'type': test_type,
                        'free': True
                    })
                
                # Premium tests (always shown, but access controlled at UI level)
                # Use pagination to get all premium tests
                all_premium_identifiers = set()
                page_size = 1000
                start = 0
                max_iterations = 10
                iteration = 0
                
                while iteration < max_iterations:
                    response = self.supabase.table('test_questions').select('test_identifier').eq(
                        'test_type', test_type
                    ).eq(
                        'test_category', 'premium'
                    ).range(start, start + page_size - 1).execute()
                    
                    if not response.data:
                        break
                    
                    page_identifiers = set(record['test_identifier'] for record in response.data)
                    all_premium_identifiers.update(page_identifiers)
                    
                    start += page_size
                    iteration += 1
                    
                    if len(response.data) < page_size and len(all_premium_identifiers) >= 30:
                        break
                
                # Add all premium tests regardless of user membership status
                premium_identifiers = sort_test_identifiers(all_premium_identifiers)
                for identifier in premium_identifiers:
                    available_tests.append({
                        'id': identifier,
                        'title': f'Test de Compréhension {"Orale" if test_type == "listening" else "Écrite"} {identifier}',
                        'type': test_type,
                        'free': False
                    })
            
            elif test_type == 'writing':
                # Writing tests are available to everyone (tasks are free, corrections require premium)
                response = self.supabase.table('test_questions').select('test_identifier').eq(
                    'test_type', test_type
                ).eq(
                    'test_category', 'month'
                ).limit(2000).execute()
                
                identifiers = list(set(record['test_identifier'] for record in response.data))
                for identifier in sort_test_identifiers(identifiers):
                    available_tests.append({
                        'id': identifier,
                        'title': f'Test d\'Expression Écrite - {identifier}',
                        'type': test_type,
                        'free': False
                    })
            
            elif test_type == 'speaking':
                # Speaking tests are available to everyone (parties/questions are free, sample answers require premium)
                for category in ['tache2_month', 'tache3_topic']:
                    all_speaking_identifiers = set()
                    page_size = 1000
                    start = 0
                    max_iterations = 10
                    iteration = 0
                    
                    while iteration < max_iterations:
                        response = self.supabase.table('test_questions').select('test_identifier').eq(
                            'test_type', test_type
                        ).eq(
                            'test_category', category
                        ).range(start, start + page_size - 1).execute()
                        
                        if not response.data:
                            break
                        
                        page_identifiers = set(record['test_identifier'] for record in response.data)
                        all_speaking_identifiers.update(page_identifiers)
                        
                        start += page_size
                        iteration += 1
                        
                        if len(response.data) < page_size:
                            break
                    
                    identifiers = sort_test_identifiers(all_speaking_identifiers)
                    for identifier in identifiers:
                        task_type = "Tâche 2" if category == 'tache2_month' else "Tâche 3"
                        available_tests.append({
                            'id': identifier,
                            'title': f'Test d\'Expression Orale - {task_type} - {identifier}',
                            'type': test_type,
                            'free': False
                        })
            
            return available_tests
            
        except Exception as e:
            logger.error(f"Error fetching available tests for {test_type}: {e}")
            return []

# Convenience functions for use in Flask routes
def get_test_data_service():
    """Get test data service instance with current app's Supabase client"""
    return TestDataService(current_app.supabase)

def get_test_questions_with_access_control(test_type: str, test_identifier: str, 
                                         is_free: bool = False, user_is_premium: bool = False) -> Optional[List[Dict[str, Any]]]:
    """Convenience function to get test data with access control"""
    service = get_test_data_service()
    return service.get_test_data(test_type, test_identifier, is_free, user_is_premium) 