# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

TCF Canada is a comprehensive web application for Test de Connaissance du Français (TCF) preparation. It consists of:
- **Frontend**: React 18 + TypeScript with Mantine UI
- **Backend**: Flask Python API with Supabase database
- **Data Processing Pipeline**: Python modules for scraping, analysis, and content generation

## Common Development Commands

### Frontend Development (React)
```bash
cd web_react/frontend
npm install                    # Install dependencies
npm run dev                    # Start development server (port 5173)
npm run build                  # Build for production
npm run lint                   # Run ESLint
npm run preview               # Preview production build
```

### Backend Development (Flask)
```bash
cd web_react/backend
pip install -r requirements.txt  # Install dependencies
python3 run.py                   # Start development server (port 5001)
# For production: gunicorn --workers 3 --bind 127.0.0.1:5001 run:app
```

### Data Processing Pipeline
```bash
# Install root dependencies
pip install -r requirements.txt

# Audio processing
cd src/transcript
python audio_processing.py      # Process audio files
python chunking.py              # Segment transcripts

# Content analysis
cd src/analysis
python reading_analysis.py      # Analyze reading comprehension
python listening_analysis.py    # Analyze listening comprehension

# Web scraping
cd src/scrape
python scrape_reading_llm.py    # Scrape reading content
python scrape_listening.py      # Scrape listening content
```

### Testing
```bash
# Backend tests
cd web_react/backend
python -m pytest

# Frontend tests
cd web_react/frontend
npm test
```

## Architecture Overview

### Frontend Structure
- **Routing**: React Router with protected routes
- **State Management**: Zustand for auth, React Query for server state
- **UI Components**: Mantine UI component library
- **Internationalization**: i18next (English, French, Chinese)
- **Authentication**: Session-based with Supabase integration

### Backend Structure
- **API Framework**: Flask with environment-based configuration
- **Database**: Supabase (PostgreSQL) with real-time capabilities
- **Authentication**: Session-based with secure cookie handling
- **File Storage**: Local files with Git LFS for audio assets
- **Payment Processing**: Stripe integration
- **Email Service**: SendGrid for notifications

### Data Processing Pipeline
The `src/` directory contains specialized modules:

1. **scrape/**: Web scraping with OCR capabilities using GPT-4 Vision
2. **transcript/**: Audio processing pipeline with Whisper ASR
3. **analysis/**: Content analysis using Qwen API for multilingual explanations
4. **classify/**: Content classification with human-in-the-loop validation
5. **answer_check/**: Quality assurance using LLM-based answer validation

## Key Configuration Files

### Environment Variables
- `web_react/backend/.env`: Backend configuration (Supabase, Stripe, SendGrid)
- Frontend config in `web_react/frontend/src/config/`

### Important Paths
- Audio assets: `data/assets/listening_asset/`
- Test data: `data/scraped/` and `data/analysis/`
- Static files: `web_react/frontend/public/`

## Development Guidelines

### Backend Development
- Follow Flask application factory pattern
- Use environment-based configuration (development/production)
- Implement proper error handling and logging
- Use Supabase client for database operations

### Frontend Development
- Use TypeScript for all components
- Follow React hooks patterns
- Use Mantine components for consistent UI
- Implement proper loading states and error handling

### Data Processing
- Use UTF-8 encoding for all JSON files
- Follow naming conventions (test1.json, test2.json, etc.)
- Implement proper error handling for API calls
- Use modular design for pipeline components

## API Integration

### LLM APIs
- **OpenAI**: Used for OCR and answer checking
- **Qwen**: Primary LLM for content generation and analysis
- **Anthropic**: Alternative for answer validation

### Third-party Services
- **Supabase**: Database and authentication
- **Stripe**: Payment processing
- **SendGrid**: Email notifications

## File Structure Patterns

### Test Data Organization
```
data/
├── assets/                    # Audio and image files (Git LFS)
├── scraped/                   # Raw scraped content
├── analysis/                  # Generated explanations
└── deduplicated/              # Processed and cleaned data
```

### Code Organization
```
src/
├── analysis/                  # Content analysis modules
├── scrape/                    # Web scraping modules
├── transcript/                # Audio processing pipeline
├── classify/                  # Content classification
└── utils/                     # Shared utilities
```

## Important Notes

- Audio files are managed with Git LFS - run `git lfs pull` after cloning
- The application supports three languages: English, French, and Chinese
- All API keys and sensitive data should be in environment variables
- Use proper error handling for all external API calls
- Follow the existing code style and patterns
- The system is designed for cost-efficient LLM API usage

## Production Deployment

The application is designed for EC2 deployment with:
- Nginx as reverse proxy
- Gunicorn for Flask application
- SSL/TLS with Let's Encrypt
- SystemD for process management

See README.md for detailed deployment instructions.

## Supabase Database Structure

The application uses Supabase with the following main tables:
- `users` - User accounts and authentication
- `test_questions` - Test questions with free/paid content separation
- `test_history` - User test history and progress
- `collection_book` - User bookmarked questions
- `writing_submissions` - User writing task submissions
- `mock_exams` - Generated mock exams
- `notebook_notes` - User notes with isolation
- `highlights` - Text highlighting data
- `promo_codes` - Promotional codes
- `promo_usage_log` - Promo usage tracking

## Content Access Control

The application implements a sophisticated content access control system:
- **Free Content**: Available to all users
- **Premium Content**: Requires membership subscription
- **Writing Tasks**: Free to access, corrections require premium
- **Speaking Content**: Parties/questions free, sample answers require premium

Content is stored in the `test_questions` table with fields:
- `free_content`: Always accessible content
- `paid_content`: Premium-only content
- `question_data`: Base question data
- `correct_answer`: Secure answer data (not sent to frontend)

## Test Data Structure

Test questions are organized by:
- **test_type**: reading, listening, writing, speaking
- **test_category**: free, premium, month, tache2_month, tache3_topic, difficulty
- **test_identifier**: test1, janvier-2025, group1, etc.
- **question_number**: 1, 2, 3, ... (1-indexed)

## Grading System

The backend implements a comprehensive grading system:
- **Regular Tests**: Weighted scoring based on TCF difficulty levels
- **Group Tests**: Difficulty-based practice with adaptive scoring
- **Mock Exams**: Custom exam generation with realistic scoring
- **Collection Book**: User bookmarked questions with custom grading

## Writing and Speaking Features

The application includes specialized features for:
- **Writing Practice**: Monthly themed tasks with correction examples
- **Speaking Practice**: Two task types (Tâche 2 and Tâche 3) with sample answers
- **Classified Writing**: Organized by task type and topic for focused practice

## Security Features

The application includes robust security measures:
- Anti-scraping protection for test content
- Rate limiting and IP blocking
- Session-based authentication with secure cookies
- Input validation and sanitization
- Content Security Policy (CSP) headers