

# TCF Canada - Test de Connaissance du Français

A comprehensive web application for TCF (Test de Connaissance du Français) preparation with reading, listening, writing, and speaking practice modules.

## 🌟 Features

- **Multi-language Support**: English, French, and Chinese interfaces
- **Complete Test Sections**: Reading, Listening, Writing, and Speaking practice
- **Mock Exams**: Full-length practice tests with realistic timing
- **Progress Tracking**: Detailed analytics and performance monitoring
- **User Notebooks**: Rich text note-taking with user isolation
- **Membership System**: Free and premium tiers with Stripe integration
- **Audio Playback**: High-quality audio for listening comprehension
- **Highlighting System**: Text highlighting for reading practice
- **Responsive Design**: Mobile-friendly interface

## 🏗️ Architecture

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Mantine UI** for modern component library
- **TanStack Query** for data fetching and caching
- **React Router** for navigation
- **i18next** for internationalization

### Backend
- **Flask** Python web framework
- **Supabase** for database and authentication
- **Stripe** for payment processing
- **SendGrid** for email services
- **Git LFS** for large audio assets

## 📋 Prerequisites

- **Python 3.9+**
- **Node.js 18+**
- **Git LFS** (for audio assets)
- **Supabase Account** (for database)
- **Stripe Account** (for payments)
- **SendGrid Account** (for emails)

## 🚀 Quick Start (Development)

### 1. Clone Repository
```bash
git clone <repository-url>
cd TCF-Canada
git lfs pull  # Download audio assets
```

### 2. Backend Setup
```bash
cd web_react/backend
pip install -r requirements.txt
cp env.example .env
# Edit .env with your configuration
python3 run.py
# if running web_react/start_dev.sh, do the following
# Set up Python virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### 3. Frontend Setup
Install Node.js from https://nodejs.org/en/download/
```bash
cd web_react/frontend
npm install
npm run dev
```

### 4. Access Application
- Frontend: http://localhost:5173
- Backend API: http://localhost:5001

## 🌐 Production Deployment (EC2)

### EC2 Instance Requirements
- **Instance Type**: t3.medium or larger (2 vCPU, 4GB RAM minimum)
- **Storage**: 20GB+ SSD (for audio assets)
- **OS**: Ubuntu 22.04 LTS
- **Security Groups**: 
  - Port 80 (HTTP)
  - Port 443 (HTTPS)
  - Port 22 (SSH)

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python 3.9+
sudo apt install python3 python3-pip python3-venv -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Git LFS
sudo apt-get install git-lfs -y

# Install Nginx
sudo apt install nginx -y

# Install Certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

### 2. Application Deployment

```bash
# Clone repository
git clone <repository-url> /var/www/tcf-canada
cd /var/www/tcf-canada
git lfs pull

# Set up Python virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Build frontend
cd web_react/frontend
npm install
npm run build

# Copy built files to serve directory
sudo cp -r dist/* /var/www/html/
```

### 3. Environment Configuration

```bash
# Backend environment
cd /var/www/tcf-canada/web_react/backend
sudo cp env.example .env
sudo nano .env
```

Configure the following variables:
```env
# Database
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Security
SECRET_KEY=your_secret_key_here

# Email
MAIL_PASSWORD=your_sendgrid_api_key

# Payments
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Frontend URL
FRONTEND_URL=https://yourdomain.com
```

### 4. Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/tcf-canada
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Frontend (React app)
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Handle CORS
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
        
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/tcf-canada /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 5. SSL Certificate

```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
```

### 6. Process Management

Create systemd service for the backend:

```bash
sudo nano /etc/systemd/system/tcf-backend.service
```

```ini
[Unit]
Description=TCF Canada Backend
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/tcf-canada/web_react/backend
Environment=PATH=/var/www/tcf-canada/venv/bin
ExecStart=/var/www/tcf-canada/venv/bin/gunicorn --workers 3 --bind 127.0.0.1:5001 run:app
Restart=always

[Install]
WantedBy=multi-user.target
```

```bash
sudo systemctl daemon-reload
sudo systemctl enable tcf-backend
sudo systemctl start tcf-backend
sudo systemctl status tcf-backend
```

### 7. Database Setup

Run the database migration scripts:

```bash
cd /var/www/tcf-canada
source venv/bin/activate

# Run database setup scripts (if needed)
# python3 scripts/setup_database.py
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
SECRET_KEY=your-secret-key
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
MAIL_PASSWORD=your-sendgrid-api-key
FRONTEND_URL=https://yourdomain.com
```

### Database Schema

The application uses Supabase with the following main tables:
- `users` - User accounts and authentication
- `notebook_notes` - User notes with isolation
- `highlights` - Text highlighting data
- `promo_codes` - Promotional codes
- `promo_usage_log` - Promo usage tracking

## 📁 Project Structure

```
TCF-Canada/
├── data/                          # Audio and test assets (Git LFS)
│   └── assets/
│       ├── listening_asset/       # Audio files for listening tests
│       └── reading_asset/         # Reading test materials
├── web_react/
│   ├── backend/                   # Flask API server
│   │   ├── app/                   # Application modules
│   │   ├── database/              # Database scripts and schemas
│   │   ├── .env.example           # Environment template
│   │   └── run.py                 # Application entry point
│   └── frontend/                  # React application
│       ├── src/                   # Source code
│       ├── public/                # Static assets
│       ├── package.json           # Node.js dependencies
│       └── vite.config.ts         # Vite configuration
├── requirements.txt               # Python dependencies
└── README.md                      # This file
```

## 🛠️ Development

### Running Tests
```bash
# Backend tests
cd web_react/backend
python -m pytest

# Frontend tests
cd web_react/frontend
npm test
```

### Code Quality
```bash
# Python formatting
black web_react/backend/

# TypeScript checking
cd web_react/frontend
npm run type-check
```

## 🔒 Security

- **Authentication**: Session-based with Supabase
- **Authorization**: Role-based access control
- **Data Isolation**: User-specific data separation
- **HTTPS**: SSL/TLS encryption in production
- **CORS**: Configured for cross-origin requests
- **Input Validation**: Server-side validation
- **SQL Injection**: Protected via Supabase ORM

## 📊 Monitoring

### Health Checks
- Backend: `GET /api/health`
- Frontend: Check if main page loads

### Logs
```bash
# Backend logs
sudo journalctl -u tcf-backend -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## 🚨 Troubleshooting

### Common Issues

1. **Audio files not loading**
   - Ensure Git LFS is installed and files are pulled
   - Check file permissions: `sudo chown -R www-data:www-data /var/www/tcf-canada/data`

2. **Database connection errors**
   - Verify Supabase credentials in .env
   - Check network connectivity to Supabase

3. **Frontend build errors**
   - Clear node_modules: `rm -rf node_modules package-lock.json && npm install`
   - Check Node.js version: `node --version`

4. **Backend not starting**
   - Check Python virtual environment is activated
   - Verify all dependencies are installed
   - Check logs: `sudo journalctl -u tcf-backend -f`

### Performance Optimization

1. **Enable Gzip compression** in Nginx
2. **Configure caching** for static assets
3. **Use CDN** for audio files (optional)
4. **Database indexing** for frequently queried fields
5. **Connection pooling** for database connections

## 📝 License

[Add your license information here]

## 🤝 Contributing

[Add contribution guidelines here]

## 📞 Support

For technical support or questions:
- Email: [<EMAIL>]
- Documentation: [link-to-docs]
- Issues: [link-to-issues]

---

**Note**: This application handles user data and payments. Ensure compliance with relevant data protection regulations (GDPR, CCPA, etc.) and PCI DSS standards for payment processing.
